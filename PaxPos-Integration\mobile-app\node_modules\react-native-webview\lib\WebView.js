var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=exports.WebView=void 0;var _react=_interopRequireDefault(require("react"));var _reactNative=require("react-native");var _WebView=_interopRequireDefault(require("./WebView.styles"));var _jsxRuntime=require("react/jsx-runtime");var _this=this,_jsxFileName="/home/<USER>/code/src/WebView.tsx";var WebView=exports.WebView=function WebView(){return(0,_jsxRuntime.jsx)(_reactNative.View,{style:_WebView.default.flexStart,children:(0,_jsxRuntime.jsx)(_reactNative.Text,{style:_WebView.default.colorRed,children:"React Native WebView does not support this platform."})});};var _default=exports.default=WebView;