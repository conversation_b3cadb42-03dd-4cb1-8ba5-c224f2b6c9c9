{"version": 3, "sources": ["../../../../../src/start/server/metro/fetchRouterManifest.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport resolveFrom from 'resolve-from';\n\nimport { getRoutePaths } from './router';\n\nexport type ExpoRouterServerManifestV1Route<TRegex = string> = {\n  file: string;\n  page: string;\n  routeKeys: Record<string, string>;\n  namedRegex: TRegex;\n  generated?: boolean;\n};\n\nexport type ExpoRouterServerManifestV1<TRegex = string> = {\n  apiRoutes: ExpoRouterServerManifestV1Route<TRegex>[];\n  htmlRoutes: ExpoRouterServerManifestV1Route<TRegex>[];\n  notFoundRoutes: ExpoRouterServerManifestV1Route<TRegex>[];\n  redirects: ExpoRouterServerManifestV1Route<TRegex>[];\n  rewrites: ExpoRouterServerManifestV1Route<TRegex>[];\n};\n\nfunction getExpoRouteManifestBuilderAsync(projectRoot: string) {\n  return require(resolveFrom(projectRoot, 'expo-router/build/routes-manifest'))\n    .createRoutesManifest as typeof import('expo-router/build/routes-manifest').createRoutesManifest;\n}\n\n// TODO: Simplify this now that we use Node.js directly, no need for the Metro bundler caching layer.\nexport async function fetchManifest<TRegex = string>(\n  projectRoot: string,\n  options: {\n    asJson?: boolean;\n    appDir: string;\n  } & import('expo-router/build/routes-manifest').Options\n): Promise<ExpoRouterServerManifestV1<TRegex> | null> {\n  const getManifest = getExpoRouteManifestBuilderAsync(projectRoot);\n  const paths = getRoutePaths(options.appDir);\n  // Get the serialized manifest\n  const jsonManifest = getManifest(paths, options);\n\n  if (!jsonManifest) {\n    return null;\n  }\n\n  if (!jsonManifest.htmlRoutes || !jsonManifest.apiRoutes) {\n    throw new Error('Routes manifest is malformed: ' + JSON.stringify(jsonManifest, null, 2));\n  }\n\n  if (!options.asJson) {\n    // @ts-expect-error\n    return inflateManifest(jsonManifest);\n  }\n  // @ts-expect-error\n  return jsonManifest;\n}\n\n// Convert the serialized manifest to a usable format\nexport function inflateManifest(\n  json: ExpoRouterServerManifestV1<string>\n): ExpoRouterServerManifestV1<RegExp> {\n  return {\n    ...json,\n    htmlRoutes: json.htmlRoutes?.map((value) => {\n      return {\n        ...value,\n        namedRegex: new RegExp(value.namedRegex),\n      };\n    }),\n    apiRoutes: json.apiRoutes?.map((value) => {\n      return {\n        ...value,\n        namedRegex: new RegExp(value.namedRegex),\n      };\n    }),\n    notFoundRoutes: json.notFoundRoutes?.map((value) => {\n      return {\n        ...value,\n        namedRegex: new RegExp(value.namedRegex),\n      };\n    }),\n    redirects: json.redirects?.map((value: any) => {\n      return {\n        ...value,\n        namedRegex: new RegExp(value.namedRegex),\n      };\n    }),\n    rewrites: json.rewrites?.map((value: any) => {\n      return {\n        ...value,\n        namedRegex: new RegExp(value.namedRegex),\n      };\n    }),\n  };\n}\n"], "names": ["fetchManifest", "inflateManifest", "getExpoRouteManifestBuilderAsync", "projectRoot", "require", "resolveFrom", "createRoutesManifest", "options", "getManifest", "paths", "getRoutePaths", "appDir", "jsonManifest", "htmlRoutes", "apiRoutes", "Error", "JSON", "stringify", "as<PERSON><PERSON>", "json", "map", "value", "namedRegex", "RegExp", "notFoundRoutes", "redirects", "rewrites"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IA2BqBA,aAAa;eAAbA;;IA6BNC,eAAe;eAAfA;;;;gEAvDQ;;;;;;wBAEM;;;;;;AAkB9B,SAASC,iCAAiCC,WAAmB;IAC3D,OAAOC,QAAQC,IAAAA,sBAAW,EAACF,aAAa,sCACrCG,oBAAoB;AACzB;AAGO,eAAeN,cACpBG,WAAmB,EACnBI,OAGuD;IAEvD,MAAMC,cAAcN,iCAAiCC;IACrD,MAAMM,QAAQC,IAAAA,qBAAa,EAACH,QAAQI,MAAM;IAC1C,8BAA8B;IAC9B,MAAMC,eAAeJ,YAAYC,OAAOF;IAExC,IAAI,CAACK,cAAc;QACjB,OAAO;IACT;IAEA,IAAI,CAACA,aAAaC,UAAU,IAAI,CAACD,aAAaE,SAAS,EAAE;QACvD,MAAM,IAAIC,MAAM,mCAAmCC,KAAKC,SAAS,CAACL,cAAc,MAAM;IACxF;IAEA,IAAI,CAACL,QAAQW,MAAM,EAAE;QACnB,mBAAmB;QACnB,OAAOjB,gBAAgBW;IACzB;IACA,mBAAmB;IACnB,OAAOA;AACT;AAGO,SAASX,gBACdkB,IAAwC;QAI1BA,kBAMDA,iBAMKA,sBAMLA,iBAMDA;IA1BZ,OAAO;QACL,GAAGA,IAAI;QACPN,UAAU,GAAEM,mBAAAA,KAAKN,UAAU,qBAAfM,iBAAiBC,GAAG,CAAC,CAACC;YAChC,OAAO;gBACL,GAAGA,KAAK;gBACRC,YAAY,IAAIC,OAAOF,MAAMC,UAAU;YACzC;QACF;QACAR,SAAS,GAAEK,kBAAAA,KAAKL,SAAS,qBAAdK,gBAAgBC,GAAG,CAAC,CAACC;YAC9B,OAAO;gBACL,GAAGA,KAAK;gBACRC,YAAY,IAAIC,OAAOF,MAAMC,UAAU;YACzC;QACF;QACAE,cAAc,GAAEL,uBAAAA,KAAKK,cAAc,qBAAnBL,qBAAqBC,GAAG,CAAC,CAACC;YACxC,OAAO;gBACL,GAAGA,KAAK;gBACRC,YAAY,IAAIC,OAAOF,MAAMC,UAAU;YACzC;QACF;QACAG,SAAS,GAAEN,kBAAAA,KAAKM,SAAS,qBAAdN,gBAAgBC,GAAG,CAAC,CAACC;YAC9B,OAAO;gBACL,GAAGA,KAAK;gBACRC,YAAY,IAAIC,OAAOF,MAAMC,UAAU;YACzC;QACF;QACAI,QAAQ,GAAEP,iBAAAA,KAAKO,QAAQ,qBAAbP,eAAeC,GAAG,CAAC,CAACC;YAC5B,OAAO;gBACL,GAAGA,KAAK;gBACRC,YAAY,IAAIC,OAAOF,MAAMC,UAAU;YACzC;QACF;IACF;AACF"}