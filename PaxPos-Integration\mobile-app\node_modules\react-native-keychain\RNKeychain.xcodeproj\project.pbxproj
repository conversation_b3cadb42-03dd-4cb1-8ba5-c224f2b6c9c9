// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		4F9F3CC426B8FD3700F34E8C /* RNKeychainManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 5D82368C1B0CE2A6005A9EF3 /* RNKeychainManager.m */; };
		5D82368F1B0CE3CB005A9EF3 /* RNKeychainManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 5D82368C1B0CE2A6005A9EF3 /* RNKeychainManager.m */; };
		5D8236911B0CE3D6005A9EF3 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5D8236901B0CE3D6005A9EF3 /* Security.framework */; };
		5DE632D52043423E004F9598 /* LocalAuthentication.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5DE632D42043423E004F9598 /* LocalAuthentication.framework */; };
		5DE632DB204342AE004F9598 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5DE632DA204342AE004F9598 /* Security.framework */; };
		6478986B1F38BFA100DA1C12 /* RNKeychainManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 5D82368C1B0CE2A6005A9EF3 /* RNKeychainManager.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		5D82366D1B0CE05B005A9EF3 /* Copy Headers */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			name = "Copy Headers";
			runOnlyForDeploymentPostprocessing = 0;
		};
		6478985D1F38BF9100DA1C12 /* Copy Headers */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			name = "Copy Headers";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		4F9F3CBB26B8FD1000F34E8C /* libRNKeychain-macOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libRNKeychain-macOS.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		5D82366F1B0CE05B005A9EF3 /* libRNKeychain.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNKeychain.a; sourceTree = BUILT_PRODUCTS_DIR; };
		5D82368B1B0CE2A6005A9EF3 /* RNKeychainManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RNKeychainManager.h; sourceTree = "<group>"; };
		5D82368C1B0CE2A6005A9EF3 /* RNKeychainManager.m */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 2; lastKnownFileType = sourcecode.c.objc; path = RNKeychainManager.m; sourceTree = "<group>"; tabWidth = 2; };
		5D8236901B0CE3D6005A9EF3 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		5DE632D42043423E004F9598 /* LocalAuthentication.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = LocalAuthentication.framework; path = System/Library/Frameworks/LocalAuthentication.framework; sourceTree = SDKROOT; };
		5DE632DA204342AE004F9598 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS11.2.sdk/System/Library/Frameworks/Security.framework; sourceTree = DEVELOPER_DIR; };
		6478985F1F38BF9100DA1C12 /* libRNKeychain.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNKeychain.a; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		4F9F3CB926B8FD1000F34E8C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5D82366C1B0CE05B005A9EF3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5DE632D52043423E004F9598 /* LocalAuthentication.framework in Frameworks */,
				5D8236911B0CE3D6005A9EF3 /* Security.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6478985C1F38BF9100DA1C12 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5DE632DB204342AE004F9598 /* Security.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		5D8236661B0CE05B005A9EF3 = {
			isa = PBXGroup;
			children = (
				5D82368A1B0CE2A6005A9EF3 /* RNKeychainManager */,
				5D8236701B0CE05B005A9EF3 /* Products */,
				647898681F38BF9C00DA1C12 /* Frameworks */,
			);
			sourceTree = "<group>";
			wrapsLines = 0;
		};
		5D8236701B0CE05B005A9EF3 /* Products */ = {
			isa = PBXGroup;
			children = (
				5D82366F1B0CE05B005A9EF3 /* libRNKeychain.a */,
				6478985F1F38BF9100DA1C12 /* libRNKeychain.a */,
				4F9F3CBB26B8FD1000F34E8C /* libRNKeychain-macOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		5D82368A1B0CE2A6005A9EF3 /* RNKeychainManager */ = {
			isa = PBXGroup;
			children = (
				5D82368B1B0CE2A6005A9EF3 /* RNKeychainManager.h */,
				5D82368C1B0CE2A6005A9EF3 /* RNKeychainManager.m */,
			);
			path = RNKeychainManager;
			sourceTree = "<group>";
		};
		647898681F38BF9C00DA1C12 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5DE632DA204342AE004F9598 /* Security.framework */,
				5DE632D42043423E004F9598 /* LocalAuthentication.framework */,
				5D8236901B0CE3D6005A9EF3 /* Security.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		4F9F3CB726B8FD1000F34E8C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5DE632D820434281004F9598 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5DE632DD204342BC004F9598 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		4F9F3CBA26B8FD1000F34E8C /* RNKeychain-macOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4F9F3CC326B8FD1000F34E8C /* Build configuration list for PBXNativeTarget "RNKeychain-macOS" */;
			buildPhases = (
				4F9F3CB726B8FD1000F34E8C /* Headers */,
				4F9F3CB826B8FD1000F34E8C /* Sources */,
				4F9F3CB926B8FD1000F34E8C /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "RNKeychain-macOS";
			productName = "RNKeychain-macOS";
			productReference = 4F9F3CBB26B8FD1000F34E8C /* libRNKeychain-macOS.a */;
			productType = "com.apple.product-type.library.static";
		};
		5D82366E1B0CE05B005A9EF3 /* RNKeychain */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5D8236831B0CE05B005A9EF3 /* Build configuration list for PBXNativeTarget "RNKeychain" */;
			buildPhases = (
				5D82366B1B0CE05B005A9EF3 /* Sources */,
				5D82366C1B0CE05B005A9EF3 /* Frameworks */,
				5DE632D820434281004F9598 /* Headers */,
				5D82366D1B0CE05B005A9EF3 /* Copy Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNKeychain;
			productName = RNKeychain;
			productReference = 5D82366F1B0CE05B005A9EF3 /* libRNKeychain.a */;
			productType = "com.apple.product-type.library.static";
		};
		6478985E1F38BF9100DA1C12 /* RNKeychain-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 647898671F38BF9100DA1C12 /* Build configuration list for PBXNativeTarget "RNKeychain-tvOS" */;
			buildPhases = (
				6478985B1F38BF9100DA1C12 /* Sources */,
				6478985C1F38BF9100DA1C12 /* Frameworks */,
				5DE632DD204342BC004F9598 /* Headers */,
				6478985D1F38BF9100DA1C12 /* Copy Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "RNKeychain-tvOS";
			productName = "RNKeychain-tvOS";
			productReference = 6478985F1F38BF9100DA1C12 /* libRNKeychain.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		5D8236671B0CE05B005A9EF3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0630;
				ORGANIZATIONNAME = "Joel Arvidsson";
				TargetAttributes = {
					4F9F3CBA26B8FD1000F34E8C = {
						CreatedOnToolsVersion = 12.5.1;
						ProvisioningStyle = Automatic;
					};
					5D82366E1B0CE05B005A9EF3 = {
						CreatedOnToolsVersion = 6.3.1;
					};
					6478985E1F38BF9100DA1C12 = {
						CreatedOnToolsVersion = 8.3.3;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 5D82366A1B0CE05B005A9EF3 /* Build configuration list for PBXProject "RNKeychain" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 5D8236661B0CE05B005A9EF3;
			productRefGroup = 5D8236701B0CE05B005A9EF3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				5D82366E1B0CE05B005A9EF3 /* RNKeychain */,
				6478985E1F38BF9100DA1C12 /* RNKeychain-tvOS */,
				4F9F3CBA26B8FD1000F34E8C /* RNKeychain-macOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		4F9F3CB826B8FD1000F34E8C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4F9F3CC426B8FD3700F34E8C /* RNKeychainManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5D82366B1B0CE05B005A9EF3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5D82368F1B0CE3CB005A9EF3 /* RNKeychainManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6478985B1F38BF9100DA1C12 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6478986B1F38BFA100DA1C12 /* RNKeychainManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		4F9F3CC126B8FD1000F34E8C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				MACOSX_DEPLOYMENT_TARGET = 11.3;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		4F9F3CC226B8FD1000F34E8C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				MACOSX_DEPLOYMENT_TARGET = 11.3;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		5D8236811B0CE05B005A9EF3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		5D8236821B0CE05B005A9EF3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		5D8236841B0CE05B005A9EF3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		5D8236851B0CE05B005A9EF3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		647898651F38BF9100DA1C12 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNKeychain;
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TVOS_DEPLOYMENT_TARGET = 10.2;
			};
			name = Debug;
		};
		647898661F38BF9100DA1C12 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNKeychain;
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TVOS_DEPLOYMENT_TARGET = 10.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4F9F3CC326B8FD1000F34E8C /* Build configuration list for PBXNativeTarget "RNKeychain-macOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4F9F3CC126B8FD1000F34E8C /* Debug */,
				4F9F3CC226B8FD1000F34E8C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5D82366A1B0CE05B005A9EF3 /* Build configuration list for PBXProject "RNKeychain" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5D8236811B0CE05B005A9EF3 /* Debug */,
				5D8236821B0CE05B005A9EF3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5D8236831B0CE05B005A9EF3 /* Build configuration list for PBXNativeTarget "RNKeychain" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5D8236841B0CE05B005A9EF3 /* Debug */,
				5D8236851B0CE05B005A9EF3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		647898671F38BF9100DA1C12 /* Build configuration list for PBXNativeTarget "RNKeychain-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				647898651F38BF9100DA1C12 /* Debug */,
				647898661F38BF9100DA1C12 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 5D8236671B0CE05B005A9EF3 /* Project object */;
}
