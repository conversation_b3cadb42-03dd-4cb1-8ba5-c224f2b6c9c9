{"version": 3, "file": "Sourcer.js", "sourceRoot": "", "sources": ["../../src/sourcer/Sourcer.ts"], "names": [], "mappings": ";;;;;AA4BA,kDAsFC;AAlHD,kDAA0B;AAC1B,oDAA4B;AAE5B,iCAQgB;AAChB,iCAMgB;AAChB,8CAAmD;AACnD,kDAAgE;AAChE,yCAA2D;AAC3D,iDAA6D;AAE7D,8CAA2C;AAE3C,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,kCAAkC,CAAC,CAAC;AAE5D,KAAK,UAAU,mBAAmB,CACvC,WAAmB,EACnB,OAA0B;IAE1B,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,MAAM,IAAA,+BAAkB,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAE7F,MAAM,sBAAsB,GAAG,IAAA,4CAA6B,EAAC,WAAW,CAAC,IAAI,OAAO,CAAC;IACrF,MAAM,4BAA4B;IAChC,4FAA4F;IAC5F,4FAA4F;IAC5F,6DAA6D;IAC7D,OAAO,OAAO,CAAC,4BAA4B,KAAK,SAAS;QACvD,CAAC,CAAC,OAAO,CAAC,4BAA4B;QACtC,CAAC,CAAC,gBAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;IAEnD,mHAAmH;IACnH,oJAAoJ;IACpJ,IAAI,6BAA6B,GAAa,EAAE,CAAC;IACjD,IACE,OAAO,CAAC,kBAAkB,CAAC,OAAO;QAClC,UAAU,EAAE,GAAG,CAAC,OAAO,EAAE,iBAAiB;QAC1C,4BAA4B;QAC5B,gBAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,QAAQ,CAAC,EAC5C,CAAC;QACD,6BAA6B,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAChE,CAAC;IAED,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAChC,OAAO;QACP,IAAA,iBAAO,EAAC,OAAO,EAAE,4CAAqC,CAAC,CACrD,WAAW,EACX,OAAO,EACP,sBAAsB,CACvB;QACD,IAAA,iBAAO,EAAC,OAAO,EAAE,wCAAiC,CAAC,CACjD,WAAW,EACX,OAAO,EACP,sBAAsB,CACvB;QACD,IAAA,iBAAO,EAAC,OAAO,EAAE,gCAAyB,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,CAAC;QAC5F,IAAA,iBAAO,EAAC,OAAO,EAAE,8BAAuB,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;QAC/D,IAAA,iBAAO,EAAC,OAAO,EAAE,kCAA2B,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;QAEnE,qBAAqB;QACrB,IAAA,iBAAO,EAAC,OAAO,EAAE,+BAAwB,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;QAChE,IAAA,iBAAO,EAAC,OAAO,EAAE,uCAAgC,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;QAExE,oBAAoB;QACpB,IAAA,iBAAO,EAAC,OAAO,EAAE,iCAA0B,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;QAClE,IAAA,iBAAO,EAAC,OAAO,EAAE,6BAAsB,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;QAE9D,gCAAgC;QAChC,IAAA,iBAAO,EAAC,OAAO,EAAE,+CAAwC,CAAC,CACxD,WAAW,EACX,OAAO,EACP,6BAA6B,EAC7B,4BAA4B,CAC7B;QACD,IAAA,iBAAO,EAAC,OAAO,EAAE,2CAAoC,CAAC,CACpD,WAAW,EACX,OAAO,EACP,4BAA4B,CAC7B;QACD,IAAA,iBAAO,EAAC,OAAO,EAAE,+CAAwC,CAAC,CACxD,WAAW,EACX,OAAO,EACP,4BAA4B,CAC7B;QAED,gBAAgB;QAChB,IAAA,iBAAO,EAAC,OAAO,EAAE,0CAA2B,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;QAEnE,6CAA6C;QAC7C,IAAA,iBAAO,EAAC,OAAO,EAAE,wCAA6B,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;KACtE,CAAC,CAAC;IAEH,gBAAgB;IAChB,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;QACzB,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YAC1C,KAAK,CAAC,yBAAyB,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QACtE,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACrC,CAAC;IAED,kBAAkB;IAClB,OAAQ,EAAmB,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC;AACjD,CAAC"}