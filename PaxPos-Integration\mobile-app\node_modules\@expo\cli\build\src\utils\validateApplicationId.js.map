{"version": 3, "sources": ["../../../src/utils/validateApplicationId.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport { env } from './env';\nimport { memoize } from './fn';\nimport { learnMore } from './link';\nimport { isUrlAvailableAsync } from './url';\nimport { Log } from '../log';\n\nconst debug = require('debug')('expo:utils:validateApplicationId') as typeof console.log;\n\n// TODO: Adjust to indicate that the bundle identifier must start with a letter, period, or hyphen.\nconst IOS_BUNDLE_ID_REGEX = /^[a-zA-Z0-9-.]+$/;\nconst ANDROID_PACKAGE_REGEX = /^[a-zA-Z][a-zA-Z0-9_]*(\\.[a-zA-Z][a-zA-Z0-9_]*)+$/;\n\n/** Validate an iOS bundle identifier. */\nexport function validateBundleId(value: string): boolean {\n  return IOS_BUNDLE_ID_REGEX.test(value);\n}\n\n/** Validate an Android package name. */\nexport function validatePackage(value: string): boolean {\n  return validatePackageWithWarning(value) === true;\n}\n\n/** Validate an Android package name and return the reason if invalid. */\nexport function validatePackageWithWarning(value: string): true | string {\n  const parts = value.split('.');\n  for (const segment of parts) {\n    if (RESERVED_ANDROID_PACKAGE_NAME_SEGMENTS.includes(segment)) {\n      return `\"${segment}\" is a reserved Java keyword.`;\n    }\n  }\n  if (parts.length < 2) {\n    return `Package name must contain more than one segment, separated by \".\", e.g. com.${value}`;\n  }\n  if (!ANDROID_PACKAGE_REGEX.test(value)) {\n    return 'Invalid characters in Android package name. Only alphanumeric characters, \".\" and \"_\" are allowed, and each segment start with a letter.';\n  }\n\n  return true;\n}\n\nexport function getSanitizedPackage(value: string) {\n  // It's common to use dashes in your node project name, strip them from the suggested package name.\n  let output = value\n    // Oracle recommends package names are \"legalized\" by converting hyphen to an underscore and removing unsupported characters\n    // https://docs.oracle.com/javase/tutorial/java/package/namingpkgs.html\n    // However, life is much nicer when the bundle identifier and package name are the same and iOS has the inverse rule— converting underscores to hyphens.\n    // So we'll simply remove hyphens and illegal characters for Android.\n    .replace(/[^a-zA-Z0-9_.]/g, '')\n    // Prevent multiple '.' in a row (e.g. no zero-length segments).\n    .replace(/\\.+/g, '.')\n    // Prevent '.' from the start or end.\n    .replace(/^\\.|\\.$/g, '');\n\n  output ||= 'app';\n\n  // Prepend extra segments\n  let segments = output.split('.').length;\n  while (segments < 2) {\n    output = `com.${output}`;\n    segments += 1;\n  }\n\n  // Ensure each dot has a letter or number after it\n  output = output\n    .split('.')\n    .map((segment) => {\n      segment = /^[a-zA-Z]/.test(segment) ? segment : 'x' + segment;\n\n      if (RESERVED_ANDROID_PACKAGE_NAME_SEGMENTS.includes(segment)) {\n        segment = 'x' + segment;\n      }\n      return segment;\n    })\n    .join('.');\n\n  return output;\n}\n\nexport function getSanitizedBundleIdentifier(value: string) {\n  // According to the behavior observed when using the UI in Xcode.\n  // Must start with a letter, period, or hyphen (not number).\n  // Can only contain alphanumeric characters, periods, and hyphens.\n  // Can have empty segments (e.g. com.example..app).\n  return value.replace(/(^[^a-zA-Z.-]|[^a-zA-Z0-9-.])/g, '-');\n}\n\n// https://en.wikipedia.org/wiki/List_of_Java_keywords\n// Running the following in the console and pruning the \"Reserved Identifiers\" section:\n// [...document.querySelectorAll('dl > dt > code')].map(node => node.innerText)\nconst RESERVED_ANDROID_PACKAGE_NAME_SEGMENTS = [\n  // List of Java keywords\n  '_',\n  'abstract',\n  'assert',\n  'boolean',\n  'break',\n  'byte',\n  'case',\n  'catch',\n  'char',\n  'class',\n  'const',\n  'continue',\n  'default',\n  'do',\n  'double',\n  'else',\n  'enum',\n  'extends',\n  'final',\n  'finally',\n  'float',\n  'for',\n  'goto',\n  'if',\n  'implements',\n  'import',\n  'instanceof',\n  'int',\n  'interface',\n  'long',\n  'native',\n  'new',\n  'package',\n  'private',\n  'protected',\n  'public',\n  'return',\n  'short',\n  'static',\n  'super',\n  'switch',\n  'synchronized',\n  'this',\n  'throw',\n  'throws',\n  'transient',\n  'try',\n  'void',\n  'volatile',\n  'while',\n  // Reserved words for literal values\n  'true',\n  'false',\n  'null',\n  // Unused\n  'const',\n  'goto',\n  'strictfp',\n];\n\nexport function assertValidBundleId(value: string) {\n  assert.match(\n    value,\n    IOS_BUNDLE_ID_REGEX,\n    `The ios.bundleIdentifier defined in your Expo config is not formatted properly. Only alphanumeric characters, '.', '-', and '_' are allowed, and each '.' must be followed by a letter.`\n  );\n}\n\nexport function assertValidPackage(value: string) {\n  assert.match(\n    value,\n    ANDROID_PACKAGE_REGEX,\n    `Invalid format of Android package name. Only alphanumeric characters, '.' and '_' are allowed, and each '.' must be followed by a letter. Reserved Java keywords are not allowed.`\n  );\n}\n\n/** @private */\nexport async function getBundleIdWarningInternalAsync(bundleId: string): Promise<string | null> {\n  if (env.EXPO_OFFLINE) {\n    Log.warn('Skipping Apple bundle identifier reservation validation in offline-mode.');\n    return null;\n  }\n\n  if (!(await isUrlAvailableAsync('itunes.apple.com'))) {\n    debug(\n      `Couldn't connect to iTunes Store to check bundle ID ${bundleId}. itunes.apple.com may be down.`\n    );\n    // If no network, simply skip the warnings since they'll just lead to more confusion.\n    return null;\n  }\n\n  const url = `http://itunes.apple.com/lookup?bundleId=${bundleId}`;\n  try {\n    debug(`Checking iOS bundle ID '${bundleId}' at: ${url}`);\n    const response = await fetch(url);\n    const json: any = await response.json();\n    if (json.resultCount > 0) {\n      const firstApp = json.results[0];\n      return formatInUseWarning(firstApp.trackName, firstApp.sellerName, bundleId);\n    }\n  } catch (error: any) {\n    debug(`Error checking bundle ID ${bundleId}: ${error.message}`);\n    // Error fetching itunes data.\n  }\n  return null;\n}\n\n/** Returns a warning message if an iOS bundle identifier is potentially already in use. */\nexport const getBundleIdWarningAsync = memoize(getBundleIdWarningInternalAsync);\n\n/** @private */\nexport async function getPackageNameWarningInternalAsync(\n  packageName: string\n): Promise<string | null> {\n  if (env.EXPO_OFFLINE) {\n    Log.warn('Skipping Android package name reservation validation in offline-mode.');\n    return null;\n  }\n\n  if (!(await isUrlAvailableAsync('play.google.com'))) {\n    debug(\n      `Couldn't connect to Play Store to check package name ${packageName}. play.google.com may be down.`\n    );\n    // If no network, simply skip the warnings since they'll just lead to more confusion.\n    return null;\n  }\n\n  const url = `https://play.google.com/store/apps/details?id=${packageName}`;\n  try {\n    debug(`Checking Android package name '${packageName}' at: ${url}`);\n    const response = await fetch(url);\n    // If the page exists, then warn the user.\n    if (response.status === 200) {\n      // There is no JSON API for the Play Store so we can't concisely\n      // locate the app name and developer to match the iOS warning.\n      return `⚠️  The package ${chalk.bold(packageName)} is already in use. ${chalk.dim(\n        learnMore(url)\n      )}`;\n    }\n  } catch (error: any) {\n    // Error fetching play store data or the page doesn't exist.\n    debug(`Error checking package name ${packageName}: ${error.message}`);\n  }\n  return null;\n}\n\nfunction formatInUseWarning(appName: string, author: string, id: string): string {\n  return `⚠️  The app ${chalk.bold(appName)} by ${chalk.italic(\n    author\n  )} is already using ${chalk.bold(id)}`;\n}\n\n/** Returns a warning message if an Android package name is potentially already in use. */\nexport const getPackageNameWarningAsync = memoize(getPackageNameWarningInternalAsync);\n"], "names": ["assertValidBundleId", "assertValidPackage", "getBundleIdWarningAsync", "getBundleIdWarningInternalAsync", "getPackageNameWarningAsync", "getPackageNameWarningInternalAsync", "getSanitizedBundleIdentifier", "getSanitizedPackage", "validateBundleId", "validatePackage", "validatePackageWithWarning", "debug", "require", "IOS_BUNDLE_ID_REGEX", "ANDROID_PACKAGE_REGEX", "value", "test", "parts", "split", "segment", "RESERVED_ANDROID_PACKAGE_NAME_SEGMENTS", "includes", "length", "output", "replace", "segments", "map", "join", "assert", "match", "bundleId", "env", "EXPO_OFFLINE", "Log", "warn", "isUrlAvailableAsync", "url", "response", "fetch", "json", "resultCount", "firstApp", "results", "formatInUseWarning", "trackName", "sellerName", "error", "message", "memoize", "packageName", "status", "chalk", "bold", "dim", "learnMore", "appName", "author", "id", "italic"], "mappings": ";;;;;;;;;;;IA0JgBA,mBAAmB;eAAnBA;;IAQAC,kBAAkB;eAAlBA;;IAwCHC,uBAAuB;eAAvBA;;IA/BSC,+BAA+B;eAA/BA;;IA4ETC,0BAA0B;eAA1BA;;IA1CSC,kCAAkC;eAAlCA;;IA5HNC,4BAA4B;eAA5BA;;IAtCAC,mBAAmB;eAAnBA;;IA3BAC,gBAAgB;eAAhBA;;IAKAC,eAAe;eAAfA;;IAKAC,0BAA0B;eAA1BA;;;;gEA1BG;;;;;;;gEACD;;;;;;qBAEE;oBACI;sBACE;qBACU;qBAChB;;;;;;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,mGAAmG;AACnG,MAAMC,sBAAsB;AAC5B,MAAMC,wBAAwB;AAGvB,SAASN,iBAAiBO,KAAa;IAC5C,OAAOF,oBAAoBG,IAAI,CAACD;AAClC;AAGO,SAASN,gBAAgBM,KAAa;IAC3C,OAAOL,2BAA2BK,WAAW;AAC/C;AAGO,SAASL,2BAA2BK,KAAa;IACtD,MAAME,QAAQF,MAAMG,KAAK,CAAC;IAC1B,KAAK,MAAMC,WAAWF,MAAO;QAC3B,IAAIG,uCAAuCC,QAAQ,CAACF,UAAU;YAC5D,OAAO,CAAC,CAAC,EAAEA,QAAQ,6BAA6B,CAAC;QACnD;IACF;IACA,IAAIF,MAAMK,MAAM,GAAG,GAAG;QACpB,OAAO,CAAC,4EAA4E,EAAEP,OAAO;IAC/F;IACA,IAAI,CAACD,sBAAsBE,IAAI,CAACD,QAAQ;QACtC,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAASR,oBAAoBQ,KAAa;IAC/C,mGAAmG;IACnG,IAAIQ,SAASR,KACX,4HAA4H;IAC5H,uEAAuE;IACvE,wJAAwJ;IACxJ,qEAAqE;KACpES,OAAO,CAAC,mBAAmB,GAC5B,gEAAgE;KAC/DA,OAAO,CAAC,QAAQ,IACjB,qCAAqC;KACpCA,OAAO,CAAC,YAAY;IAEvBD,WAAW;IAEX,yBAAyB;IACzB,IAAIE,WAAWF,OAAOL,KAAK,CAAC,KAAKI,MAAM;IACvC,MAAOG,WAAW,EAAG;QACnBF,SAAS,CAAC,IAAI,EAAEA,QAAQ;QACxBE,YAAY;IACd;IAEA,kDAAkD;IAClDF,SAASA,OACNL,KAAK,CAAC,KACNQ,GAAG,CAAC,CAACP;QACJA,UAAU,YAAYH,IAAI,CAACG,WAAWA,UAAU,MAAMA;QAEtD,IAAIC,uCAAuCC,QAAQ,CAACF,UAAU;YAC5DA,UAAU,MAAMA;QAClB;QACA,OAAOA;IACT,GACCQ,IAAI,CAAC;IAER,OAAOJ;AACT;AAEO,SAASjB,6BAA6BS,KAAa;IACxD,iEAAiE;IACjE,4DAA4D;IAC5D,kEAAkE;IAClE,mDAAmD;IACnD,OAAOA,MAAMS,OAAO,CAAC,kCAAkC;AACzD;AAEA,sDAAsD;AACtD,uFAAuF;AACvF,+EAA+E;AAC/E,MAAMJ,yCAAyC;IAC7C,wBAAwB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oCAAoC;IACpC;IACA;IACA;IACA,SAAS;IACT;IACA;IACA;CACD;AAEM,SAASpB,oBAAoBe,KAAa;IAC/Ca,iBAAM,CAACC,KAAK,CACVd,OACAF,qBACA,CAAC,uLAAuL,CAAC;AAE7L;AAEO,SAASZ,mBAAmBc,KAAa;IAC9Ca,iBAAM,CAACC,KAAK,CACVd,OACAD,uBACA,CAAC,iLAAiL,CAAC;AAEvL;AAGO,eAAeX,gCAAgC2B,QAAgB;IACpE,IAAIC,QAAG,CAACC,YAAY,EAAE;QACpBC,QAAG,CAACC,IAAI,CAAC;QACT,OAAO;IACT;IAEA,IAAI,CAAE,MAAMC,IAAAA,wBAAmB,EAAC,qBAAsB;QACpDxB,MACE,CAAC,oDAAoD,EAAEmB,SAAS,+BAA+B,CAAC;QAElG,qFAAqF;QACrF,OAAO;IACT;IAEA,MAAMM,MAAM,CAAC,wCAAwC,EAAEN,UAAU;IACjE,IAAI;QACFnB,MAAM,CAAC,wBAAwB,EAAEmB,SAAS,MAAM,EAAEM,KAAK;QACvD,MAAMC,WAAW,MAAMC,MAAMF;QAC7B,MAAMG,OAAY,MAAMF,SAASE,IAAI;QACrC,IAAIA,KAAKC,WAAW,GAAG,GAAG;YACxB,MAAMC,WAAWF,KAAKG,OAAO,CAAC,EAAE;YAChC,OAAOC,mBAAmBF,SAASG,SAAS,EAAEH,SAASI,UAAU,EAAEf;QACrE;IACF,EAAE,OAAOgB,OAAY;QACnBnC,MAAM,CAAC,yBAAyB,EAAEmB,SAAS,EAAE,EAAEgB,MAAMC,OAAO,EAAE;IAC9D,8BAA8B;IAChC;IACA,OAAO;AACT;AAGO,MAAM7C,0BAA0B8C,IAAAA,WAAO,EAAC7C;AAGxC,eAAeE,mCACpB4C,WAAmB;IAEnB,IAAIlB,QAAG,CAACC,YAAY,EAAE;QACpBC,QAAG,CAACC,IAAI,CAAC;QACT,OAAO;IACT;IAEA,IAAI,CAAE,MAAMC,IAAAA,wBAAmB,EAAC,oBAAqB;QACnDxB,MACE,CAAC,qDAAqD,EAAEsC,YAAY,8BAA8B,CAAC;QAErG,qFAAqF;QACrF,OAAO;IACT;IAEA,MAAMb,MAAM,CAAC,8CAA8C,EAAEa,aAAa;IAC1E,IAAI;QACFtC,MAAM,CAAC,+BAA+B,EAAEsC,YAAY,MAAM,EAAEb,KAAK;QACjE,MAAMC,WAAW,MAAMC,MAAMF;QAC7B,0CAA0C;QAC1C,IAAIC,SAASa,MAAM,KAAK,KAAK;YAC3B,gEAAgE;YAChE,8DAA8D;YAC9D,OAAO,CAAC,gBAAgB,EAAEC,gBAAK,CAACC,IAAI,CAACH,aAAa,oBAAoB,EAAEE,gBAAK,CAACE,GAAG,CAC/EC,IAAAA,eAAS,EAAClB,OACT;QACL;IACF,EAAE,OAAOU,OAAY;QACnB,4DAA4D;QAC5DnC,MAAM,CAAC,4BAA4B,EAAEsC,YAAY,EAAE,EAAEH,MAAMC,OAAO,EAAE;IACtE;IACA,OAAO;AACT;AAEA,SAASJ,mBAAmBY,OAAe,EAAEC,MAAc,EAAEC,EAAU;IACrE,OAAO,CAAC,YAAY,EAAEN,gBAAK,CAACC,IAAI,CAACG,SAAS,IAAI,EAAEJ,gBAAK,CAACO,MAAM,CAC1DF,QACA,kBAAkB,EAAEL,gBAAK,CAACC,IAAI,CAACK,KAAK;AACxC;AAGO,MAAMrD,6BAA6B4C,IAAAA,WAAO,EAAC3C"}