{"version": 3, "file": "switchRegex.js", "sourceRoot": "", "sources": ["../src/switchRegex.ts"], "names": [], "mappings": ";;AAAA,kCAkBC;AAlBD,SAAgB,WAAW,CACzB,IAAY,EACZ,KAAsE,EACtE,QAAiB,KAAK;IAEtB,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9B,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,GAAG,IAAI,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC", "sourcesContent": ["export function switchRegex(\n  text: string,\n  cases: [RegExp | null, (matches: RegExpMatchArray) => string | void][],\n  isAll: boolean = false\n): string {\n  for (const [reg, callback] of cases) {\n    if (!reg) {\n      return callback(['']) || '';\n    }\n    const results = text.match(reg);\n    if (results) {\n      const res = callback(results);\n      if (!isAll) {\n        return res || '';\n      }\n    }\n  }\n  return '';\n}\n"]}