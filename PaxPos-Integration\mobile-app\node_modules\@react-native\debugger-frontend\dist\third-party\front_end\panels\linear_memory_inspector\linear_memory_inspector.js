import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as r from"../../core/sdk/sdk.js";import*as n from"../../models/bindings/bindings.js";import*as s from"../../ui/legacy/legacy.js";import*as i from"./components/components.js";import*as o from"../../ui/visual_logging/visual_logging.js";const a={noOpenInspections:"No open inspections"},d=t.i18n.registerUIStrings("panels/linear_memory_inspector/LinearMemoryInspectorPane.ts",a),c=t.i18n.getLocalizedString.bind(void 0,d);let l;class h extends(e.ObjectWrapper.eventMixin(s.Widget.VBox)){#e;constructor(){super(!1),this.element.setAttribute("jslog",`${o.panel("linear-memory-inspector").track({resize:!0})}`);const e=document.createElement("div");e.textContent=c(a.noOpenInspections),e.style.display="flex",this.#e=new s.TabbedPane.TabbedPane,this.#e.setPlaceholderElement(e),this.#e.setCloseableTabs(!0),this.#e.setAllowTabReorder(!0,!0),this.#e.addEventListener(s.TabbedPane.Events.TabClosed,this.#t,this),this.#e.show(this.contentElement),this.#e.headerElement().setAttribute("jslog",`${o.toolbar().track({keydown:"ArrowUp|ArrowLeft|ArrowDown|ArrowRight|Enter|Space"})}`)}static instance(){return l||(l=new h),l}#r(e){const t=this.#e.tabView(e);if(null===t)throw new Error(`No linear memory inspector view for the given tab id: ${e}`);return t}create(e,t,r,n){const s=new g(r,n,e);this.#e.appendTab(e,t,s,void 0,!1,!0),this.#e.selectTab(e)}close(e){this.#e.closeTab(e,!1)}reveal(e,t){const r=this.#r(e);void 0!==t&&r.updateAddress(t),this.refreshView(e),this.#e.selectTab(e)}refreshView(e){this.#r(e).refreshData()}#t(e){const{tabId:t}=e.data;this.dispatchEventToListeners("ViewClosed",t)}}class g extends s.Widget.VBox{#n;#s;#i;#o;firstTimeOpen;constructor(e,t=0,r){if(super(!1),t<0||t>=e.length())throw new Error("Requested address is out of bounds.");this.#n=e,this.#s=t,this.#i=r,this.#o=new i.LinearMemoryInspector.LinearMemoryInspector,this.#o.addEventListener(i.LinearMemoryInspector.MemoryRequestEvent.eventName,(e=>{this.#a(e)})),this.#o.addEventListener(i.LinearMemoryInspector.AddressChangedEvent.eventName,(e=>{this.updateAddress(e.data)})),this.#o.addEventListener(i.LinearMemoryInspector.SettingsChangedEvent.eventName,(e=>{e.stopPropagation(),this.saveSettings(e.data)})),this.#o.addEventListener(i.LinearMemoryHighlightChipList.DeleteMemoryHighlightEvent.eventName,(e=>{M.instance().removeHighlight(this.#i,e.data),this.refreshData()})),this.contentElement.appendChild(this.#o),this.firstTimeOpen=!0}wasShown(){this.refreshData()}saveSettings(e){M.instance().saveSettings(e)}updateAddress(e){if(e<0||e>=this.#n.length())throw new Error("Requested address is out of bounds.");this.#s=e}refreshData(){M.getMemoryForAddress(this.#n,this.#s).then((({memory:e,offset:t})=>{let r,n,s;if(this.firstTimeOpen){const e=M.instance().loadSettings();r=e.valueTypes,n=e.modes,s=e.endianness,this.firstTimeOpen=!1}this.#o.data={memory:e,address:this.#s,memoryOffset:t,outerMemoryLength:this.#n.length(),valueTypes:r,valueTypeModes:n,endianness:s,highlightInfo:this.#d()}}))}#a(e){const{start:t,end:r,address:n}=e.data;if(n<t||n>=r)throw new Error("Requested address is out of bounds.");M.getMemoryRange(this.#n,t,r).then((e=>{this.#o.data={memory:e,address:n,memoryOffset:t,outerMemoryLength:this.#n.length(),highlightInfo:this.#d()}}))}#d(){const e=M.instance().getHighlightInfo(this.#i);if(void 0!==e){if(e.startAddress<0||e.startAddress>=this.#n.length())throw new Error("HighlightInfo start address is out of bounds.");if(e.size<=0)throw new Error("Highlight size must be a positive number.")}return e}}var m=Object.freeze({__proto__:null,LinearMemoryInspectorPane:h});const u={couldNotOpenLinearMemory:"Could not open linear memory inspector: failed locating buffer.",revealInMemoryInspectorPanel:"Reveal in Memory inspector panel"},p=t.i18n.registerUIStrings("panels/linear_memory_inspector/LinearMemoryInspectorController.ts",u),f=t.i18n.getLocalizedString.bind(void 0,p),b=1e3;let y;class v{#c;constructor(e){this.#c=e}length(){return this.#c.byteLength()}async getRange(e,t){const r=Math.min(t,this.length());if(e<0||e>r)return console.error(`Requesting invalid range of memory: (${e}, ${t})`),new Uint8Array(0);const n=await this.#c.bytes(e,r);return new Uint8Array(n)}}class M extends r.TargetManager.SDKModelObserver{#l=h.instance();#h=new Map;#g=new Map;#m;constructor(){super(),r.TargetManager.TargetManager.instance().observeModels(r.RuntimeModel.RuntimeModel,this),r.TargetManager.TargetManager.instance().addModelListener(r.DebuggerModel.DebuggerModel,r.DebuggerModel.Events.GlobalObjectCleared,this.#u,this),this.#l.addEventListener("ViewClosed",this.#p.bind(this)),r.TargetManager.TargetManager.instance().addModelListener(r.DebuggerModel.DebuggerModel,r.DebuggerModel.Events.DebuggerPaused,this.#f,this);const t=i.ValueInterpreterDisplayUtils.getDefaultValueTypeMapping(),n={valueTypes:Array.from(t.keys()),valueTypeModes:Array.from(t),endianness:"Little Endian"};this.#m=e.Settings.Settings.instance().createSetting("lmi-interpreter-settings",n)}static instance(){return y||(y=new M,y)}static async getMemoryForAddress(e,t){const r=Math.max(0,t-500),n=r+b;return{memory:await e.getRange(r,n),offset:r}}static async getMemoryRange(e,t,r){if(t<0||t>r||t>=e.length())throw new Error("Requested range is out of bounds.");const n=Math.max(r,t+b);return await e.getRange(t,n)}async evaluateExpression(e,t){const r=await e.evaluate({expression:t});if("error"in r)console.error(`Tried to evaluate the expression '${t}' but got an error: ${r.error}`);else{if(!("exceptionDetails"in r)||!r?.exceptionDetails?.text)return r.object;console.error(`Tried to evaluate the expression '${t}' but got an exception: ${r.exceptionDetails.text}`)}}saveSettings(e){const t=Array.from(e.valueTypes),r=[...e.modes];this.#m.set({valueTypes:t,valueTypeModes:r,endianness:e.endianness})}loadSettings(){const e=this.#m.get();return{valueTypes:new Set(e.valueTypes),modes:new Map(e.valueTypeModes),endianness:e.endianness}}getHighlightInfo(e){return this.#g.get(e)}removeHighlight(e,t){this.getHighlightInfo(e)===t&&this.#g.delete(e)}setHighlightInfo(e,t){this.#g.set(e,t)}#b(e){this.#g.delete(e)}static async retrieveDWARFMemoryObjectAndAddress(t){if(!(t instanceof n.DebuggerLanguagePlugins.ExtensionRemoteObject))return;const r=t,s=t.linearMemoryAddress;if(void 0===s)return;const i=r.callFrame,o=await t.debuggerModel().agent.invoke_evaluateOnCallFrame({callFrameId:i.id,expression:"memories[0]"}),a=o.getError();a&&(console.error(a),e.Console.Console.instance().error(f(u.couldNotOpenLinearMemory)));return{obj:t.debuggerModel().runtimeModel().createRemoteObject(o.result),address:s}}static extractObjectSize(e){return e.linearMemorySize??0}static extractObjectTypeDescription(e){const t=e.description;if(!t)return"";const r=t.charAt(t.length-1),n=t.charAt(t.length-2);return"*"===r||"&"===r?" "===n?t.slice(0,t.length-2):t.slice(0,t.length-1):t}static extractObjectName(e,t){const r=e.description?.charAt(e.description.length-1);return"*"===r?"*"+t:t}async reveal({object:e,expression:t},n){const i=await M.retrieveDWARFMemoryObjectAndAddress(e);let o,a=e;void 0!==i&&(o=i.address,a=i.obj);const d=await async function(e){const t=await e.runtimeModel().agent.invoke_callFunctionOn({objectId:e.objectId,functionDeclaration:"function() { return this instanceof ArrayBuffer || (typeof SharedArrayBuffer !== 'undefined' && this instanceof SharedArrayBuffer) ? this : this.buffer; }",silent:!0,objectGroup:"linear-memory-inspector"}),n=t.getError();if(n)throw new Error(`Remote object representing ArrayBuffer could not be retrieved: ${n}`);return e=e.runtimeModel().createRemoteObject(t.result),new r.RemoteObject.RemoteArrayBuffer(e)}(a),{internalProperties:c}=await d.object().getOwnProperties(!1),l=c?.find((({name:e})=>"[[ArrayBufferData]]"===e)),h=l?.value?.value;if(!h)throw new Error("Unable to find backing store id for array buffer");const g=c?.find((({name:e})=>"[[WebAssemblyMemory]]"===e)),m=g?.value,u=M.extractHighlightInfo(e,t);if(u?this.setHighlightInfo(h,u):this.#b(h),this.#h.has(h))return this.#l.reveal(h,o),void s.ViewManager.ViewManager.instance().showView("linear-memory-inspector",n);const p=String(m?m.description:d.object().description);this.#h.set(h,d.object());const f=new v(d);this.#l.create(h,p,f,o),s.ViewManager.ViewManager.instance().showView("linear-memory-inspector",n)}appendApplicableItems(e,t,n){if(n.property.value?.isLinearMemoryInspectable()){const e=n.path(),s=n.property.value;t.debugSection().appendItem(f(u.revealInMemoryInspectorPanel),this.reveal.bind(this,new r.RemoteObject.LinearMemoryInspectable(s,e)),{jslogContext:"reveal-in-memory-inspector"})}}static extractHighlightInfo(e,t){if(!(e instanceof n.DebuggerLanguagePlugins.ExtensionRemoteObject))return;const r=e.linearMemoryAddress??0;let s;try{s={startAddress:r,size:M.extractObjectSize(e),name:t?M.extractObjectName(e,t):t,type:M.extractObjectTypeDescription(e)}}catch(e){s=void 0}return s}modelRemoved(e){for(const[t,r]of this.#h)e===r.runtimeModel()&&(this.#h.delete(t),this.#b(t),this.#l.close(t))}#f(e){const t=e.data;for(const[e,r]of this.#h)if(t.runtimeModel()===r.runtimeModel()){const r=t.debuggerPausedDetails()?.callFrames[0];r?this.updateHighlightedMemory(e,r).then((()=>this.#l.refreshView(e))):(this.#b(e),this.#l.refreshView(e))}}#u(e){this.modelRemoved(e.data.runtimeModel())}#p({data:e}){const t=this.#h.get(e);t&&t.release(),this.#h.delete(e),this.#b(e)}async updateHighlightedMemory(e,t){const r=this.getHighlightInfo(e),n=r?.name;if(!r||!n)return void this.#b(e);const s=await this.evaluateExpression(t,n);if(!s)return void this.#b(e);const i=M.extractHighlightInfo(s,n);i&&this.#y(i,r)?this.setHighlightInfo(e,i):this.#b(e)}#y(e,t){return e.type===t.type&&e.startAddress===t.startAddress}}var I=Object.freeze({__proto__:null,RemoteArrayBufferWrapper:v,isDWARFMemoryObject:function(e){return e instanceof n.DebuggerLanguagePlugins.ExtensionRemoteObject&&void 0!==e.linearMemoryAddress},LinearMemoryInspectorController:M});export{I as LinearMemoryInspectorController,m as LinearMemoryInspectorPane};
