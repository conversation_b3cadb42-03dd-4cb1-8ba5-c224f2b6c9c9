{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/getDebuggerType.ts"], "sourcesContent": ["/** Known compatible debuggers that require specific workarounds */\nexport type DebuggerType = 'chrome' | 'vscode' | 'unknown';\n\n// Patterns to test against user agents\nconst CHROME_USER_AGENT = /chrome/i;\nconst VSCODE_USER_AGENT = /vscode/i;\n\n/**\n * Determine the debugger type based on the known user agent.\n */\nexport function getDebuggerType(userAgent?: string | null): DebuggerType {\n  if (userAgent && CHROME_USER_AGENT.test(userAgent)) return 'chrome';\n  if (userAgent && VSCODE_USER_AGENT.test(userAgent)) return 'vscode';\n  return 'unknown';\n}\n"], "names": ["getDebuggerType", "CHROME_USER_AGENT", "VSCODE_USER_AGENT", "userAgent", "test"], "mappings": "AAAA,iEAAiE;;;;+BAUjDA;;;eAAAA;;;AAPhB,uCAAuC;AACvC,MAAMC,oBAAoB;AAC1B,MAAMC,oBAAoB;AAKnB,SAASF,gBAAgBG,SAAyB;IACvD,IAAIA,aAAaF,kBAAkBG,IAAI,CAACD,YAAY,OAAO;IAC3D,IAAIA,aAAaD,kBAAkBE,IAAI,CAACD,YAAY,OAAO;IAC3D,OAAO;AACT"}