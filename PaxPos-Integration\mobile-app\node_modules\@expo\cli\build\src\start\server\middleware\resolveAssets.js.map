{"version": 3, "sources": ["../../../../../src/start/server/middleware/resolveAssets.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nimport { getAssetSchemasAsync } from '../../../api/getExpoSchema';\nimport { BundleAssetWithFileHashes } from '../../../export/saveAssets';\nimport * as Log from '../../../log';\nimport { fileExistsAsync } from '../../../utils/dir';\nimport { CommandError } from '../../../utils/errors';\nimport { get, set } from '../../../utils/obj';\nimport { validateUrl } from '../../../utils/url';\n\ntype ManifestAsset = { fileHashes: string[]; files: string[]; hash: string };\n\nexport type Asset = ManifestAsset | BundleAssetWithFileHashes;\n\ntype ManifestResolutionError = Error & {\n  localAssetPath?: string;\n  manifestField?: string;\n};\n\n/** Inline the contents of each platform's `googleServicesFile` so runtimes can access them. */\nexport async function resolveGoogleServicesFile(\n  projectRoot: string,\n  manifest: Partial<Pick<ExpoConfig, 'android' | 'ios'>>\n) {\n  if (manifest.android?.googleServicesFile) {\n    try {\n      const contents = await fs.readFile(\n        path.resolve(projectRoot, manifest.android.googleServicesFile),\n        'utf8'\n      );\n      manifest.android.googleServicesFile = contents;\n    } catch {\n      Log.warn(\n        `Could not parse Expo config: android.googleServicesFile: \"${manifest.android.googleServicesFile}\"`\n      );\n      // Delete the field so Expo Go doesn't attempt to read it.\n      delete manifest.android.googleServicesFile;\n    }\n  }\n  if (manifest.ios?.googleServicesFile) {\n    try {\n      const contents = await fs.readFile(\n        path.resolve(projectRoot, manifest.ios.googleServicesFile),\n        'base64'\n      );\n      manifest.ios.googleServicesFile = contents;\n    } catch {\n      Log.warn(\n        `Could not parse Expo config: ios.googleServicesFile: \"${manifest.ios.googleServicesFile}\"`\n      );\n      // Delete the field so Expo Go doesn't attempt to read it.\n      delete manifest.ios.googleServicesFile;\n    }\n  }\n  return manifest;\n}\n\n/**\n * Get all fields in the manifest that match assets, then filter the ones that aren't set.\n *\n * @param manifest\n * @returns Asset fields that the user has set like [\"icon\", \"splash.image\", ...]\n */\nexport async function getAssetFieldPathsForManifestAsync(manifest: ExpoConfig): Promise<string[]> {\n  // String array like [\"icon\", \"notification.icon\", \"loading.icon\", \"loading.backgroundImage\", \"ios.icon\", ...]\n  const sdkAssetFieldPaths = await getAssetSchemasAsync(manifest.sdkVersion);\n  return sdkAssetFieldPaths.filter((assetSchema) => get(manifest, assetSchema));\n}\n\n/** Resolve all assets in the app.json inline. */\nexport async function resolveManifestAssets(\n  projectRoot: string,\n  {\n    manifest,\n    resolver,\n    strict,\n  }: {\n    manifest: ExpoConfig;\n    resolver: (assetPath: string) => Promise<string>;\n    strict?: boolean;\n  }\n) {\n  try {\n    // Asset fields that the user has set like [\"icon\", \"splash.image\"]\n    const assetSchemas = await getAssetFieldPathsForManifestAsync(manifest);\n    // Get the URLs\n    const urls = await Promise.all(\n      assetSchemas.map(async (manifestField) => {\n        const pathOrURL = get(manifest, manifestField);\n        // URL\n        if (validateUrl(pathOrURL, { requireProtocol: true })) {\n          return pathOrURL;\n        }\n\n        // File path\n        if (await fileExistsAsync(path.resolve(projectRoot, pathOrURL))) {\n          return await resolver(pathOrURL);\n        }\n\n        // Unknown\n        const err: ManifestResolutionError = new CommandError(\n          'MANIFEST_ASSET',\n          'Could not resolve local asset: ' + pathOrURL\n        );\n        err.localAssetPath = pathOrURL;\n        err.manifestField = manifestField;\n        throw err;\n      })\n    );\n\n    // Set the corresponding URL fields\n    assetSchemas.forEach((manifestField, index: number) =>\n      set(manifest, `${manifestField}Url`, urls[index])\n    );\n  } catch (error: any) {\n    if (error.localAssetPath) {\n      Log.warn(\n        `Unable to resolve asset \"${error.localAssetPath}\" from \"${error.manifestField}\" in your app.json or app.config.js`\n      );\n    } else {\n      Log.warn(\n        `Warning: Unable to resolve manifest assets. Icons and fonts might not work. ${error.message}.`\n      );\n    }\n\n    if (strict) {\n      throw new CommandError(\n        'MANIFEST_ASSET',\n        'Failed to export manifest assets: ' + error.message\n      );\n    }\n  }\n}\n"], "names": ["getAssetFieldPathsForManifestAsync", "resolveGoogleServicesFile", "resolveManifestAssets", "projectRoot", "manifest", "android", "googleServicesFile", "contents", "fs", "readFile", "path", "resolve", "Log", "warn", "ios", "sdkAssetFieldPaths", "getAssetSchemasAsync", "sdkVersion", "filter", "assetSchema", "get", "resolver", "strict", "assetSchemas", "urls", "Promise", "all", "map", "manifestField", "pathOrURL", "validateUrl", "requireProtocol", "fileExistsAsync", "err", "CommandError", "localAssetPath", "for<PERSON>ach", "index", "set", "error", "message"], "mappings": ";;;;;;;;;;;IAiEsBA,kCAAkC;eAAlCA;;IA3CAC,yBAAyB;eAAzBA;;IAkDAC,qBAAqB;eAArBA;;;;gEAvEP;;;;;;;gEACE;;;;;;+BAEoB;6DAEhB;qBACW;wBACH;qBACJ;qBACG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB,eAAeD,0BACpBE,WAAmB,EACnBC,QAAsD;QAElDA,mBAeAA;IAfJ,KAAIA,oBAAAA,SAASC,OAAO,qBAAhBD,kBAAkBE,kBAAkB,EAAE;QACxC,IAAI;YACF,MAAMC,WAAW,MAAMC,mBAAE,CAACC,QAAQ,CAChCC,eAAI,CAACC,OAAO,CAACR,aAAaC,SAASC,OAAO,CAACC,kBAAkB,GAC7D;YAEFF,SAASC,OAAO,CAACC,kBAAkB,GAAGC;QACxC,EAAE,OAAM;YACNK,KAAIC,IAAI,CACN,CAAC,0DAA0D,EAAET,SAASC,OAAO,CAACC,kBAAkB,CAAC,CAAC,CAAC;YAErG,0DAA0D;YAC1D,OAAOF,SAASC,OAAO,CAACC,kBAAkB;QAC5C;IACF;IACA,KAAIF,gBAAAA,SAASU,GAAG,qBAAZV,cAAcE,kBAAkB,EAAE;QACpC,IAAI;YACF,MAAMC,WAAW,MAAMC,mBAAE,CAACC,QAAQ,CAChCC,eAAI,CAACC,OAAO,CAACR,aAAaC,SAASU,GAAG,CAACR,kBAAkB,GACzD;YAEFF,SAASU,GAAG,CAACR,kBAAkB,GAAGC;QACpC,EAAE,OAAM;YACNK,KAAIC,IAAI,CACN,CAAC,sDAAsD,EAAET,SAASU,GAAG,CAACR,kBAAkB,CAAC,CAAC,CAAC;YAE7F,0DAA0D;YAC1D,OAAOF,SAASU,GAAG,CAACR,kBAAkB;QACxC;IACF;IACA,OAAOF;AACT;AAQO,eAAeJ,mCAAmCI,QAAoB;IAC3E,8GAA8G;IAC9G,MAAMW,qBAAqB,MAAMC,IAAAA,mCAAoB,EAACZ,SAASa,UAAU;IACzE,OAAOF,mBAAmBG,MAAM,CAAC,CAACC,cAAgBC,IAAAA,QAAG,EAAChB,UAAUe;AAClE;AAGO,eAAejB,sBACpBC,WAAmB,EACnB,EACEC,QAAQ,EACRiB,QAAQ,EACRC,MAAM,EAKP;IAED,IAAI;QACF,mEAAmE;QACnE,MAAMC,eAAe,MAAMvB,mCAAmCI;QAC9D,eAAe;QACf,MAAMoB,OAAO,MAAMC,QAAQC,GAAG,CAC5BH,aAAaI,GAAG,CAAC,OAAOC;YACtB,MAAMC,YAAYT,IAAAA,QAAG,EAAChB,UAAUwB;YAChC,MAAM;YACN,IAAIE,IAAAA,gBAAW,EAACD,WAAW;gBAAEE,iBAAiB;YAAK,IAAI;gBACrD,OAAOF;YACT;YAEA,YAAY;YACZ,IAAI,MAAMG,IAAAA,oBAAe,EAACtB,eAAI,CAACC,OAAO,CAACR,aAAa0B,aAAa;gBAC/D,OAAO,MAAMR,SAASQ;YACxB;YAEA,UAAU;YACV,MAAMI,MAA+B,IAAIC,oBAAY,CACnD,kBACA,oCAAoCL;YAEtCI,IAAIE,cAAc,GAAGN;YACrBI,IAAIL,aAAa,GAAGA;YACpB,MAAMK;QACR;QAGF,mCAAmC;QACnCV,aAAaa,OAAO,CAAC,CAACR,eAAeS,QACnCC,IAAAA,QAAG,EAAClC,UAAU,GAAGwB,cAAc,GAAG,CAAC,EAAEJ,IAAI,CAACa,MAAM;IAEpD,EAAE,OAAOE,OAAY;QACnB,IAAIA,MAAMJ,cAAc,EAAE;YACxBvB,KAAIC,IAAI,CACN,CAAC,yBAAyB,EAAE0B,MAAMJ,cAAc,CAAC,QAAQ,EAAEI,MAAMX,aAAa,CAAC,mCAAmC,CAAC;QAEvH,OAAO;YACLhB,KAAIC,IAAI,CACN,CAAC,4EAA4E,EAAE0B,MAAMC,OAAO,CAAC,CAAC,CAAC;QAEnG;QAEA,IAAIlB,QAAQ;YACV,MAAM,IAAIY,oBAAY,CACpB,kBACA,uCAAuCK,MAAMC,OAAO;QAExD;IACF;AACF"}