{"version": 3, "sources": ["../../../../../src/start/server/metro/getCssModulesFromBundler.ts"], "sourcesContent": ["import { MetroConfig } from '@expo/metro-config';\nimport crypto from 'crypto';\nimport type { Module } from 'metro';\nimport { getJsOutput, isJsModule } from 'metro/src/DeltaBundler/Serializers/helpers/js.js';\nimport type { ReadOnlyDependencies } from 'metro/src/DeltaBundler/types';\nimport type IncrementalBundler from 'metro/src/IncrementalBundler';\nimport splitBundleOptions from 'metro/src/lib/splitBundleOptions';\nimport path from 'path';\n\ntype Options = {\n  processModuleFilter: (modules: Module) => boolean;\n  assetPlugins: readonly string[];\n  platform?: string | null;\n  projectRoot: string;\n  publicPath: string;\n};\n\ntype MetroModuleCSSMetadata = {\n  code: string;\n  lineCount: number;\n  map: any[];\n};\n\nexport type CSSAsset = {\n  // 'styles.css'\n  originFilename: string;\n  // '_expo/static/css/bc6aa0a69dcebf8e8cac1faa76705756.css'\n  filename: string;\n  // '\\ndiv {\\n    background: cyan;\\n}\\n\\n'\n  source: string;\n};\n\n// s = static\nconst STATIC_EXPORT_DIRECTORY = '_expo/static/css';\n\n/** @returns the static CSS assets used in a given bundle. CSS assets are only enabled if the `@expo/metro-config` `transformerPath` is used. */\nexport async function getCssModulesFromBundler(\n  config: MetroConfig,\n  incrementalBundler: IncrementalBundler,\n  options: any\n): Promise<CSSAsset[]> {\n  // Static CSS is a web-only feature.\n  if (options.platform !== 'web') {\n    return [];\n  }\n\n  const { entryFile, onProgress, resolverOptions, transformOptions } = splitBundleOptions(options);\n\n  const dependencies = await incrementalBundler.getDependencies(\n    [entryFile],\n    transformOptions,\n    resolverOptions,\n    { onProgress, shallow: false, lazy: false }\n  );\n\n  return getCssModules(dependencies, {\n    processModuleFilter: config.serializer.processModuleFilter,\n    assetPlugins: config.transformer.assetPlugins,\n    platform: transformOptions.platform,\n    projectRoot: config.server.unstable_serverRoot ?? config.projectRoot,\n    publicPath: config.transformer.publicPath,\n  });\n}\n\nfunction hashString(str: string) {\n  return crypto.createHash('md5').update(str).digest('hex');\n}\n\nfunction getCssModules(\n  dependencies: ReadOnlyDependencies,\n  { processModuleFilter, projectRoot }: Options\n) {\n  const promises = [];\n\n  for (const module of dependencies.values()) {\n    if (\n      isJsModule(module) &&\n      processModuleFilter(module) &&\n      getJsOutput(module).type === 'js/module' &&\n      path.relative(projectRoot, module.path) !== 'package.json'\n    ) {\n      const cssMetadata = getCssMetadata(module);\n      if (cssMetadata) {\n        const contents = cssMetadata.code;\n        const filename = path.join(\n          // Consistent location\n          STATIC_EXPORT_DIRECTORY,\n          // Hashed file contents + name for caching\n          getFileName(module.path) + '-' + hashString(module.path + contents) + '.css'\n        );\n        promises.push({\n          originFilename: path.relative(projectRoot, module.path),\n          filename,\n          source: contents,\n        });\n      }\n    }\n  }\n\n  return promises;\n}\n\nfunction getCssMetadata(module: Module): MetroModuleCSSMetadata | null {\n  const data = module.output[0]?.data;\n  if (data && typeof data === 'object' && 'css' in data) {\n    if (typeof data.css !== 'object' || !('code' in (data as any).css)) {\n      throw new Error(\n        `Unexpected CSS metadata in Metro module (${module.path}): ${JSON.stringify(data.css)}`\n      );\n    }\n    return data.css as MetroModuleCSSMetadata;\n  }\n  return null;\n}\n\nexport function getFileName(module: string) {\n  return path.basename(module).replace(/\\.[^.]+$/, '');\n}\n"], "names": ["getCssModulesFromBundler", "getFileName", "STATIC_EXPORT_DIRECTORY", "config", "incrementalBundler", "options", "platform", "entryFile", "onProgress", "resolverOptions", "transformOptions", "splitBundleOptions", "dependencies", "getDependencies", "shallow", "lazy", "getCssModules", "processModuleFilter", "serializer", "assetPlugins", "transformer", "projectRoot", "server", "unstable_serverRoot", "publicPath", "hashString", "str", "crypto", "createHash", "update", "digest", "promises", "module", "values", "isJsModule", "getJsOutput", "type", "path", "relative", "cssMetadata", "getCssMetadata", "contents", "code", "filename", "join", "push", "originFilename", "source", "data", "output", "css", "Error", "JSON", "stringify", "basename", "replace"], "mappings": ";;;;;;;;;;;IAoCsBA,wBAAwB;eAAxBA;;IA+ENC,WAAW;eAAXA;;;;gEAlHG;;;;;;;yBAEqB;;;;;;;gEAGT;;;;;;;gEACd;;;;;;;;;;;AAyBjB,aAAa;AACb,MAAMC,0BAA0B;AAGzB,eAAeF,yBACpBG,MAAmB,EACnBC,kBAAsC,EACtCC,OAAY;IAEZ,oCAAoC;IACpC,IAAIA,QAAQC,QAAQ,KAAK,OAAO;QAC9B,OAAO,EAAE;IACX;IAEA,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,eAAe,EAAEC,gBAAgB,EAAE,GAAGC,IAAAA,6BAAkB,EAACN;IAExF,MAAMO,eAAe,MAAMR,mBAAmBS,eAAe,CAC3D;QAACN;KAAU,EACXG,kBACAD,iBACA;QAAED;QAAYM,SAAS;QAAOC,MAAM;IAAM;IAG5C,OAAOC,cAAcJ,cAAc;QACjCK,qBAAqBd,OAAOe,UAAU,CAACD,mBAAmB;QAC1DE,cAAchB,OAAOiB,WAAW,CAACD,YAAY;QAC7Cb,UAAUI,iBAAiBJ,QAAQ;QACnCe,aAAalB,OAAOmB,MAAM,CAACC,mBAAmB,IAAIpB,OAAOkB,WAAW;QACpEG,YAAYrB,OAAOiB,WAAW,CAACI,UAAU;IAC3C;AACF;AAEA,SAASC,WAAWC,GAAW;IAC7B,OAAOC,iBAAM,CAACC,UAAU,CAAC,OAAOC,MAAM,CAACH,KAAKI,MAAM,CAAC;AACrD;AAEA,SAASd,cACPJ,YAAkC,EAClC,EAAEK,mBAAmB,EAAEI,WAAW,EAAW;IAE7C,MAAMU,WAAW,EAAE;IAEnB,KAAK,MAAMC,UAAUpB,aAAaqB,MAAM,GAAI;QAC1C,IACEC,IAAAA,gBAAU,EAACF,WACXf,oBAAoBe,WACpBG,IAAAA,iBAAW,EAACH,QAAQI,IAAI,KAAK,eAC7BC,eAAI,CAACC,QAAQ,CAACjB,aAAaW,OAAOK,IAAI,MAAM,gBAC5C;YACA,MAAME,cAAcC,eAAeR;YACnC,IAAIO,aAAa;gBACf,MAAME,WAAWF,YAAYG,IAAI;gBACjC,MAAMC,WAAWN,eAAI,CAACO,IAAI,CACxB,sBAAsB;gBACtB1C,yBACA,0CAA0C;gBAC1CD,YAAY+B,OAAOK,IAAI,IAAI,MAAMZ,WAAWO,OAAOK,IAAI,GAAGI,YAAY;gBAExEV,SAASc,IAAI,CAAC;oBACZC,gBAAgBT,eAAI,CAACC,QAAQ,CAACjB,aAAaW,OAAOK,IAAI;oBACtDM;oBACAI,QAAQN;gBACV;YACF;QACF;IACF;IAEA,OAAOV;AACT;AAEA,SAASS,eAAeR,MAAc;QACvBA;IAAb,MAAMgB,QAAOhB,kBAAAA,OAAOiB,MAAM,CAAC,EAAE,qBAAhBjB,gBAAkBgB,IAAI;IACnC,IAAIA,QAAQ,OAAOA,SAAS,YAAY,SAASA,MAAM;QACrD,IAAI,OAAOA,KAAKE,GAAG,KAAK,YAAY,CAAE,CAAA,UAAU,AAACF,KAAaE,GAAG,AAAD,GAAI;YAClE,MAAM,IAAIC,MACR,CAAC,yCAAyC,EAAEnB,OAAOK,IAAI,CAAC,GAAG,EAAEe,KAAKC,SAAS,CAACL,KAAKE,GAAG,GAAG;QAE3F;QACA,OAAOF,KAAKE,GAAG;IACjB;IACA,OAAO;AACT;AAEO,SAASjD,YAAY+B,MAAc;IACxC,OAAOK,eAAI,CAACiB,QAAQ,CAACtB,QAAQuB,OAAO,CAAC,YAAY;AACnD"}