{"version": 3, "sources": ["../../../../../src/start/server/metro/instantiateMetro.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport { getDefaultConfig, LoadOptions } from '@expo/metro-config';\nimport chalk from 'chalk';\nimport http from 'http';\nimport type Metro from 'metro';\nimport { ReadOnlyGraph } from 'metro';\nimport Bundler from 'metro/src/Bundler';\nimport type { TransformOptions } from 'metro/src/DeltaBundler/Worker';\nimport MetroHmrServer from 'metro/src/HmrServer';\nimport RevisionNotFoundError from 'metro/src/IncrementalBundler/RevisionNotFoundError';\nimport formatBundlingError from 'metro/src/lib/formatBundlingError';\nimport { loadConfig, resolveConfig, ConfigT } from 'metro-config';\nimport { Terminal } from 'metro-core';\nimport util from 'node:util';\nimport path from 'path';\n\nimport { createDevToolsPluginWebsocketEndpoint } from './DevToolsPluginWebsocketEndpoint';\nimport { MetroBundlerDevServer } from './MetroBundlerDevServer';\nimport { MetroTerminalReporter } from './MetroTerminalReporter';\nimport { attachAtlasAsync } from './debugging/attachAtlas';\nimport { createDebugMiddleware } from './debugging/createDebugMiddleware';\nimport { createMetroMiddleware } from './dev-server/createMetroMiddleware';\nimport { runServer } from './runServer-fork';\nimport { withMetroMultiPlatformAsync } from './withMetroMultiPlatform';\nimport { Log } from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { createCorsMiddleware } from '../middleware/CorsMiddleware';\nimport { createJsInspectorMiddleware } from '../middleware/inspector/createJsInspectorMiddleware';\nimport { prependMiddleware } from '../middleware/mutations';\nimport { getPlatformBundlers } from '../platformBundlers';\n\n// From expo/dev-server but with ability to use custom logger.\ntype MessageSocket = {\n  broadcast: (method: string, params?: Record<string, any> | undefined) => void;\n};\n\n// Wrap terminal and polyfill console.log so we can log during bundling without breaking the indicator.\nclass LogRespectingTerminal extends Terminal {\n  constructor(stream: import('node:net').Socket | import('node:stream').Writable) {\n    super(stream);\n\n    const sendLog = (...args: any[]) => {\n      this._logLines.push(\n        // format args like console.log\n        util.format(...args)\n      );\n      this._scheduleUpdate();\n\n      // Flush the logs to the terminal immediately so logs at the end of the process are not lost.\n      this.flush();\n    };\n\n    console.log = sendLog;\n    console.info = sendLog;\n  }\n}\n\n// Share one instance of Terminal for all instances of Metro.\nconst terminal = new LogRespectingTerminal(process.stdout);\n\nexport async function loadMetroConfigAsync(\n  projectRoot: string,\n  options: LoadOptions,\n  {\n    exp,\n    isExporting,\n    getMetroBundler,\n  }: { exp: ExpoConfig; isExporting: boolean; getMetroBundler: () => Bundler }\n) {\n  let reportEvent: ((event: any) => void) | undefined;\n\n  const serverActionsEnabled =\n    exp.experiments?.reactServerFunctions ?? env.EXPO_UNSTABLE_SERVER_FUNCTIONS;\n\n  if (serverActionsEnabled) {\n    process.env.EXPO_UNSTABLE_SERVER_FUNCTIONS = '1';\n  }\n\n  // NOTE: Enable all the experimental Metro flags when RSC is enabled.\n  if (exp.experiments?.reactServerComponentRoutes || serverActionsEnabled) {\n    process.env.EXPO_USE_METRO_REQUIRE = '1';\n    process.env.EXPO_USE_FAST_RESOLVER = '1';\n  }\n\n  const isReactCanaryEnabled =\n    (exp.experiments?.reactServerComponentRoutes ||\n      serverActionsEnabled ||\n      exp.experiments?.reactCanary) ??\n    false;\n\n  if (isReactCanaryEnabled) {\n    // The fast resolver is required for React canary to work as it can switch the node_modules location for react imports.\n    process.env.EXPO_USE_FAST_RESOLVER = '1';\n  }\n\n  const serverRoot = getMetroServerRoot(projectRoot);\n  const terminalReporter = new MetroTerminalReporter(serverRoot, terminal);\n\n  const hasConfig = await resolveConfig(options.config, projectRoot);\n  let config: ConfigT = {\n    ...(await loadConfig(\n      { cwd: projectRoot, projectRoot, ...options },\n      // If the project does not have a metro.config.js, then we use the default config.\n      hasConfig.isEmpty ? getDefaultConfig(projectRoot) : undefined\n    )),\n    reporter: {\n      update(event: any) {\n        terminalReporter.update(event);\n        if (reportEvent) {\n          reportEvent(event);\n        }\n      },\n    },\n  };\n\n  // @ts-expect-error: Set the global require cycle ignore patterns for SSR bundles. This won't work with custom global prefixes, but we don't use those.\n  globalThis.__requireCycleIgnorePatterns = config.resolver?.requireCycleIgnorePatterns;\n\n  if (isExporting) {\n    // This token will be used in the asset plugin to ensure the path is correct for writing locally.\n    // @ts-expect-error: typed as readonly.\n    config.transformer.publicPath = `/assets?export_path=${\n      (exp.experiments?.baseUrl ?? '') + '/assets'\n    }`;\n  } else {\n    // @ts-expect-error: typed as readonly\n    config.transformer.publicPath = '/assets/?unstable_path=.';\n  }\n\n  const platformBundlers = getPlatformBundlers(projectRoot, exp);\n\n  if (exp.experiments?.reactCompiler) {\n    Log.warn(`Experimental React Compiler is enabled.`);\n  }\n\n  if (env.EXPO_UNSTABLE_TREE_SHAKING && !env.EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH) {\n    throw new CommandError(\n      'EXPO_UNSTABLE_TREE_SHAKING requires EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH to be enabled.'\n    );\n  }\n\n  if (env.EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH) {\n    Log.warn(`Experimental bundle optimization is enabled.`);\n  }\n  if (env.EXPO_UNSTABLE_TREE_SHAKING) {\n    Log.warn(`Experimental tree shaking is enabled.`);\n  }\n\n  if (serverActionsEnabled) {\n    Log.warn(\n      `React Server Functions (beta) are enabled. Route rendering mode: ${exp.experiments?.reactServerComponentRoutes ? 'server' : 'client'}`\n    );\n  }\n\n  config = await withMetroMultiPlatformAsync(projectRoot, {\n    config,\n    exp,\n    platformBundlers,\n    isTsconfigPathsEnabled: exp.experiments?.tsconfigPaths ?? true,\n    isFastResolverEnabled: env.EXPO_USE_FAST_RESOLVER,\n    isExporting,\n    isReactCanaryEnabled,\n    isNamedRequiresEnabled: env.EXPO_USE_METRO_REQUIRE,\n    isReactServerComponentsEnabled: !!exp.experiments?.reactServerComponentRoutes,\n    getMetroBundler,\n  });\n\n  return {\n    config,\n    setEventReporter: (logger: (event: any) => void) => (reportEvent = logger),\n    reporter: terminalReporter,\n  };\n}\n\n/** The most generic possible setup for Metro bundler. */\nexport async function instantiateMetroAsync(\n  metroBundler: MetroBundlerDevServer,\n  options: Omit<LoadOptions, 'logger'>,\n  {\n    isExporting,\n    exp = getConfig(metroBundler.projectRoot, {\n      skipSDKVersionRequirement: true,\n    }).exp,\n  }: { isExporting: boolean; exp?: ExpoConfig }\n): Promise<{\n  metro: Metro.Server;\n  hmrServer: MetroHmrServer | null;\n  server: http.Server;\n  middleware: any;\n  messageSocket: MessageSocket;\n}> {\n  const projectRoot = metroBundler.projectRoot;\n\n  const {\n    config: metroConfig,\n    setEventReporter,\n    reporter,\n  } = await loadMetroConfigAsync(projectRoot, options, {\n    exp,\n    isExporting,\n    getMetroBundler() {\n      return metro.getBundler().getBundler();\n    },\n  });\n\n  // Create the core middleware stack for Metro, including websocket listeners\n  const { middleware, messagesSocket, eventsSocket, websocketEndpoints } =\n    createMetroMiddleware(metroConfig);\n\n  if (!isExporting) {\n    // Enable correct CORS headers for Expo Router features\n    prependMiddleware(middleware, createCorsMiddleware(exp));\n\n    // Enable debug middleware for CDP-related debugging\n    const { debugMiddleware, debugWebsocketEndpoints } = createDebugMiddleware(\n      metroBundler,\n      reporter\n    );\n    Object.assign(websocketEndpoints, debugWebsocketEndpoints);\n    middleware.use(debugMiddleware);\n    middleware.use('/_expo/debugger', createJsInspectorMiddleware());\n\n    // TODO(cedric): `enhanceMiddleware` is deprecated, but is currently used to unify the middleware stacks\n    // See: https://github.com/facebook/metro/commit/22e85fde85ec454792a1b70eba4253747a2587a9\n    // See: https://github.com/facebook/metro/commit/d0d554381f119bb80ab09dbd6a1d310b54737e52\n    const customEnhanceMiddleware = metroConfig.server.enhanceMiddleware;\n    // @ts-expect-error: can't mutate readonly config\n    metroConfig.server.enhanceMiddleware = (metroMiddleware: any, server: Metro.Server) => {\n      if (customEnhanceMiddleware) {\n        metroMiddleware = customEnhanceMiddleware(metroMiddleware, server);\n      }\n      return middleware.use(metroMiddleware);\n    };\n  }\n\n  // Attach Expo Atlas if enabled\n  await attachAtlasAsync({\n    isExporting,\n    exp,\n    projectRoot,\n    middleware,\n    metroConfig,\n    // NOTE(cedric): reset the Atlas file once, and reuse it for static exports\n    resetAtlasFile: isExporting,\n  });\n\n  const { server, hmrServer, metro } = await runServer(\n    metroBundler,\n    metroConfig,\n    {\n      websocketEndpoints: {\n        ...websocketEndpoints,\n        ...createDevToolsPluginWebsocketEndpoint(),\n      },\n      watch: !isExporting && isWatchEnabled(),\n    },\n    {\n      mockServer: isExporting,\n    }\n  );\n\n  // Patch transform file to remove inconvenient customTransformOptions which are only used in single well-known files.\n  const originalTransformFile = metro\n    .getBundler()\n    .getBundler()\n    .transformFile.bind(metro.getBundler().getBundler());\n\n  metro.getBundler().getBundler().transformFile = async function (\n    filePath: string,\n    transformOptions: TransformOptions,\n    fileBuffer?: Buffer\n  ) {\n    return originalTransformFile(\n      filePath,\n      pruneCustomTransformOptions(\n        filePath,\n        // Clone the options so we don't mutate the original.\n        {\n          ...transformOptions,\n          customTransformOptions: {\n            __proto__: null,\n            ...transformOptions.customTransformOptions,\n          },\n        }\n      ),\n      fileBuffer\n    );\n  };\n\n  setEventReporter(eventsSocket.reportMetroEvent);\n\n  // This function ensures that modules in source maps are sorted in the same\n  // order as in a plain JS bundle.\n  metro._getSortedModules = function (this: Metro.Server, graph: ReadOnlyGraph) {\n    const modules = [...graph.dependencies.values()];\n\n    const ctx = {\n      platform: graph.transformOptions.platform,\n      environment: graph.transformOptions.customTransformOptions?.environment,\n    };\n    // Assign IDs to modules in a consistent order\n    for (const module of modules) {\n      // @ts-expect-error\n      this._createModuleId(module.path, ctx);\n    }\n    // Sort by IDs\n    return modules.sort(\n      // @ts-expect-error\n      (a, b) => this._createModuleId(a.path, ctx) - this._createModuleId(b.path, ctx)\n    );\n  };\n\n  if (hmrServer) {\n    let hmrJSBundle: typeof import('@expo/metro-config/build/serializer/fork/hmrJSBundle').default;\n\n    try {\n      hmrJSBundle = require('@expo/metro-config/build/serializer/fork/hmrJSBundle').default;\n    } catch {\n      // Add fallback for monorepo tests up until the fork is merged.\n      Log.warn('Failed to load HMR serializer from @expo/metro-config, using fallback version.');\n      hmrJSBundle = require('metro/src/DeltaBundler/Serializers/hmrJSBundle');\n    }\n\n    // Patch HMR Server to send more info to the `_createModuleId` function for deterministic module IDs and add support for serializing HMR updates the same as all other bundles.\n    hmrServer._prepareMessage = async function (this: MetroHmrServer, group, options, changeEvent) {\n      // Fork of https://github.com/facebook/metro/blob/3b3e0aaf725cfa6907bf2c8b5fbc0da352d29efe/packages/metro/src/HmrServer.js#L327-L393\n      // with patch for `_createModuleId`.\n      const logger = !options.isInitialUpdate ? changeEvent?.logger : null;\n      try {\n        const revPromise = this._bundler.getRevision(group.revisionId);\n        if (!revPromise) {\n          return {\n            type: 'error',\n            body: formatBundlingError(new RevisionNotFoundError(group.revisionId)),\n          };\n        }\n        logger?.point('updateGraph_start');\n        const { revision, delta } = await this._bundler.updateGraph(await revPromise, false);\n        logger?.point('updateGraph_end');\n        this._clientGroups.delete(group.revisionId);\n        group.revisionId = revision.id;\n        for (const client of group.clients) {\n          client.revisionIds = client.revisionIds.filter(\n            (revisionId) => revisionId !== group.revisionId\n          );\n          client.revisionIds.push(revision.id);\n        }\n        this._clientGroups.set(group.revisionId, group);\n        logger?.point('serialize_start');\n        // NOTE(EvanBacon): This is the patch\n        const moduleIdContext = {\n          platform: revision.graph.transformOptions.platform,\n          environment: revision.graph.transformOptions.customTransformOptions?.environment,\n        };\n        const hmrUpdate = hmrJSBundle(delta, revision.graph, {\n          clientUrl: group.clientUrl,\n          // NOTE(EvanBacon): This is also the patch\n          createModuleId: (moduleId: string) => {\n            // @ts-expect-error\n            return this._createModuleId(moduleId, moduleIdContext);\n          },\n          includeAsyncPaths: group.graphOptions.lazy,\n          projectRoot: this._config.projectRoot,\n          serverRoot: this._config.server.unstable_serverRoot ?? this._config.projectRoot,\n        });\n        logger?.point('serialize_end');\n        return {\n          type: 'update',\n          body: {\n            revisionId: revision.id,\n            isInitialUpdate: options.isInitialUpdate,\n            ...hmrUpdate,\n          },\n        };\n      } catch (error: any) {\n        const formattedError = formatBundlingError(error);\n        this._config.reporter.update({\n          type: 'bundling_error',\n          error,\n        });\n        return {\n          type: 'error',\n          body: formattedError,\n        };\n      }\n    };\n  }\n\n  return {\n    metro,\n    hmrServer,\n    server,\n    middleware,\n    messageSocket: messagesSocket,\n  };\n}\n\n// TODO: Fork the entire transform function so we can simply regex the file contents for keywords instead.\nfunction pruneCustomTransformOptions(\n  filePath: string,\n  transformOptions: TransformOptions\n): TransformOptions {\n  // Normalize the filepath for cross platform checking.\n  filePath = filePath.split(path.sep).join('/');\n\n  if (\n    transformOptions.customTransformOptions?.dom &&\n    // The only generated file that needs the dom root is `expo/dom/entry.js`\n    !filePath.match(/expo\\/dom\\/entry\\.js$/)\n  ) {\n    // Clear the dom root option if we aren't transforming the magic entry file, this ensures\n    // that cached artifacts from other DOM component bundles can be reused.\n    transformOptions.customTransformOptions.dom = 'true';\n  }\n\n  if (\n    transformOptions.customTransformOptions?.routerRoot &&\n    // The router root is used all over expo-router (`process.env.EXPO_ROUTER_ABS_APP_ROOT`, `process.env.EXPO_ROUTER_APP_ROOT`) so we'll just ignore the entire package.\n    !(filePath.match(/\\/expo-router\\/_ctx/) || filePath.match(/\\/expo-router\\/build\\//))\n  ) {\n    // Set to the default value.\n    transformOptions.customTransformOptions.routerRoot = 'app';\n  }\n  if (\n    transformOptions.customTransformOptions?.asyncRoutes &&\n    // The async routes settings are also used in `expo-router/_ctx.ios.js` (and other platform variants) via `process.env.EXPO_ROUTER_IMPORT_MODE`\n    !(filePath.match(/\\/expo-router\\/_ctx/) || filePath.match(/\\/expo-router\\/build\\//))\n  ) {\n    delete transformOptions.customTransformOptions.asyncRoutes;\n  }\n\n  if (\n    transformOptions.customTransformOptions?.clientBoundaries &&\n    // The client boundaries are only used in `expo/virtual/rsc.js` for production RSC exports.\n    !filePath.match(/\\/expo\\/virtual\\/rsc\\.js$/)\n  ) {\n    delete transformOptions.customTransformOptions.clientBoundaries;\n  }\n\n  return transformOptions;\n}\n\n/**\n * Simplify and communicate if Metro is running without watching file updates,.\n * Exposed for testing.\n */\nexport function isWatchEnabled() {\n  if (env.CI) {\n    Log.log(\n      chalk`Metro is running in CI mode, reloads are disabled. Remove {bold CI=true} to enable watch mode.`\n    );\n  }\n\n  return !env.CI;\n}\n"], "names": ["instantiateMetroAsync", "isWatchEnabled", "loadMetroConfigAsync", "LogRespectingTerminal", "Terminal", "constructor", "stream", "sendLog", "args", "_logLines", "push", "util", "format", "_scheduleUpdate", "flush", "console", "log", "info", "terminal", "process", "stdout", "projectRoot", "options", "exp", "isExporting", "getMetroBundler", "config", "reportEvent", "serverActionsEnabled", "experiments", "reactServerFunctions", "env", "EXPO_UNSTABLE_SERVER_FUNCTIONS", "reactServerComponentRoutes", "EXPO_USE_METRO_REQUIRE", "EXPO_USE_FAST_RESOLVER", "isReactCanaryEnabled", "reactCanary", "serverRoot", "getMetroServerRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MetroTerminalReporter", "hasConfig", "resolveConfig", "loadConfig", "cwd", "isEmpty", "getDefaultConfig", "undefined", "reporter", "update", "event", "globalThis", "__requireCycleIgnorePatterns", "resolver", "requireCycleIgnorePatterns", "transformer", "publicPath", "baseUrl", "platformBundlers", "getPlatformBundlers", "reactCompiler", "Log", "warn", "EXPO_UNSTABLE_TREE_SHAKING", "EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH", "CommandError", "withMetroMultiPlatformAsync", "isTsconfigPathsEnabled", "tsconfigPaths", "isFastResolverEnabled", "isNamedRequiresEnabled", "isReactServerComponentsEnabled", "setEventReporter", "logger", "metroBundler", "getConfig", "skipSDKVersionRequirement", "metroConfig", "metro", "getBundler", "middleware", "messagesSocket", "eventsSocket", "websocketEndpoints", "createMetroMiddleware", "prependMiddleware", "createCorsMiddleware", "debugMiddleware", "debugWebsocketEndpoints", "createDebugMiddleware", "Object", "assign", "use", "createJsInspectorMiddleware", "customEnhanceMiddleware", "server", "enhanceMiddleware", "metroMiddleware", "attachAtlasAsync", "resetAtlasFile", "hmrServer", "runServer", "createDevToolsPluginWebsocketEndpoint", "watch", "mockServer", "originalTransformFile", "transformFile", "bind", "filePath", "transformOptions", "fileBuffer", "pruneCustomTransformOptions", "customTransformOptions", "__proto__", "reportMetroEvent", "_getSortedModules", "graph", "modules", "dependencies", "values", "ctx", "platform", "environment", "module", "_createModuleId", "path", "sort", "a", "b", "hmrJSBundle", "require", "default", "_prepareMessage", "group", "changeEvent", "isInitialUpdate", "revision", "revPromise", "_bundler", "getRevision", "revisionId", "type", "body", "formatBundlingError", "RevisionNotFoundError", "point", "delta", "updateGraph", "_clientGroups", "delete", "id", "client", "clients", "revisionIds", "filter", "set", "moduleIdContext", "hmrUpdate", "clientUrl", "createModuleId", "moduleId", "includeAsyncPaths", "graphOptions", "lazy", "_config", "unstable_serverRoot", "error", "formattedError", "messageSocket", "split", "sep", "join", "dom", "match", "routerRoot", "asyncRoutes", "clientBoundaries", "CI", "chalk"], "mappings": ";;;;;;;;;;;IAiLsBA,qBAAqB;eAArBA;;IA+QNC,cAAc;eAAdA;;IAlYMC,oBAAoB;eAApBA;;;;yBA9DgB;;;;;;;yBACH;;;;;;;yBACW;;;;;;;gEAC5B;;;;;;;gEAOgB;;;;;;;gEACF;;;;;;;yBACmB;;;;;;;yBAC1B;;;;;;;gEACR;;;;;;;gEACA;;;;;;iDAEqC;uCAEhB;6BACL;uCACK;uCACA;+BACZ;wCACkB;qBACxB;qBACA;wBACS;gCACQ;6CACO;2BACV;kCACE;;;;;;AAOpC,uGAAuG;AACvG,MAAMC,8BAA8BC,qBAAQ;IAC1CC,YAAYC,MAAkE,CAAE;QAC9E,KAAK,CAACA;QAEN,MAAMC,UAAU,CAAC,GAAGC;YAClB,IAAI,CAACC,SAAS,CAACC,IAAI,CACjB,+BAA+B;YAC/BC,mBAAI,CAACC,MAAM,IAAIJ;YAEjB,IAAI,CAACK,eAAe;YAEpB,6FAA6F;YAC7F,IAAI,CAACC,KAAK;QACZ;QAEAC,QAAQC,GAAG,GAAGT;QACdQ,QAAQE,IAAI,GAAGV;IACjB;AACF;AAEA,6DAA6D;AAC7D,MAAMW,WAAW,IAAIf,sBAAsBgB,QAAQC,MAAM;AAElD,eAAelB,qBACpBmB,WAAmB,EACnBC,OAAoB,EACpB,EACEC,GAAG,EACHC,WAAW,EACXC,eAAe,EAC2D;QAK1EF,kBAOEA,mBAMDA,mBAECA,mBA6BsCG,kBAetCH,mBA2BsBA,mBAKUA;IA9FpC,IAAII;IAEJ,MAAMC,uBACJL,EAAAA,mBAAAA,IAAIM,WAAW,qBAAfN,iBAAiBO,oBAAoB,KAAIC,QAAG,CAACC,8BAA8B;IAE7E,IAAIJ,sBAAsB;QACxBT,QAAQY,GAAG,CAACC,8BAA8B,GAAG;IAC/C;IAEA,qEAAqE;IACrE,IAAIT,EAAAA,oBAAAA,IAAIM,WAAW,qBAAfN,kBAAiBU,0BAA0B,KAAIL,sBAAsB;QACvET,QAAQY,GAAG,CAACG,sBAAsB,GAAG;QACrCf,QAAQY,GAAG,CAACI,sBAAsB,GAAG;IACvC;IAEA,MAAMC,uBACJ,AAACb,CAAAA,EAAAA,oBAAAA,IAAIM,WAAW,qBAAfN,kBAAiBU,0BAA0B,KAC1CL,0BACAL,oBAAAA,IAAIM,WAAW,qBAAfN,kBAAiBc,WAAW,CAAD,KAC7B;IAEF,IAAID,sBAAsB;QACxB,uHAAuH;QACvHjB,QAAQY,GAAG,CAACI,sBAAsB,GAAG;IACvC;IAEA,MAAMG,aAAaC,IAAAA,2BAAkB,EAAClB;IACtC,MAAMmB,mBAAmB,IAAIC,4CAAqB,CAACH,YAAYpB;IAE/D,MAAMwB,YAAY,MAAMC,IAAAA,6BAAa,EAACrB,QAAQI,MAAM,EAAEL;IACtD,IAAIK,SAAkB;QACpB,GAAI,MAAMkB,IAAAA,0BAAU,EAClB;YAAEC,KAAKxB;YAAaA;YAAa,GAAGC,OAAO;QAAC,GAC5C,kFAAkF;QAClFoB,UAAUI,OAAO,GAAGC,IAAAA,+BAAgB,EAAC1B,eAAe2B,UACrD;QACDC,UAAU;YACRC,QAAOC,KAAU;gBACfX,iBAAiBU,MAAM,CAACC;gBACxB,IAAIxB,aAAa;oBACfA,YAAYwB;gBACd;YACF;QACF;IACF;IAEA,uJAAuJ;IACvJC,WAAWC,4BAA4B,IAAG3B,mBAAAA,OAAO4B,QAAQ,qBAAf5B,iBAAiB6B,0BAA0B;IAErF,IAAI/B,aAAa;YAIZD;QAHH,iGAAiG;QACjG,uCAAuC;QACvCG,OAAO8B,WAAW,CAACC,UAAU,GAAG,CAAC,oBAAoB,EACnD,AAAClC,CAAAA,EAAAA,oBAAAA,IAAIM,WAAW,qBAAfN,kBAAiBmC,OAAO,KAAI,EAAC,IAAK,WACnC;IACJ,OAAO;QACL,sCAAsC;QACtChC,OAAO8B,WAAW,CAACC,UAAU,GAAG;IAClC;IAEA,MAAME,mBAAmBC,IAAAA,qCAAmB,EAACvC,aAAaE;IAE1D,KAAIA,oBAAAA,IAAIM,WAAW,qBAAfN,kBAAiBsC,aAAa,EAAE;QAClCC,QAAG,CAACC,IAAI,CAAC,CAAC,uCAAuC,CAAC;IACpD;IAEA,IAAIhC,QAAG,CAACiC,0BAA0B,IAAI,CAACjC,QAAG,CAACkC,kCAAkC,EAAE;QAC7E,MAAM,IAAIC,oBAAY,CACpB;IAEJ;IAEA,IAAInC,QAAG,CAACkC,kCAAkC,EAAE;QAC1CH,QAAG,CAACC,IAAI,CAAC,CAAC,4CAA4C,CAAC;IACzD;IACA,IAAIhC,QAAG,CAACiC,0BAA0B,EAAE;QAClCF,QAAG,CAACC,IAAI,CAAC,CAAC,qCAAqC,CAAC;IAClD;IAEA,IAAInC,sBAAsB;YAE8CL;QADtEuC,QAAG,CAACC,IAAI,CACN,CAAC,iEAAiE,EAAExC,EAAAA,oBAAAA,IAAIM,WAAW,qBAAfN,kBAAiBU,0BAA0B,IAAG,WAAW,UAAU;IAE3I;IAEAP,SAAS,MAAMyC,IAAAA,mDAA2B,EAAC9C,aAAa;QACtDK;QACAH;QACAoC;QACAS,wBAAwB7C,EAAAA,oBAAAA,IAAIM,WAAW,qBAAfN,kBAAiB8C,aAAa,KAAI;QAC1DC,uBAAuBvC,QAAG,CAACI,sBAAsB;QACjDX;QACAY;QACAmC,wBAAwBxC,QAAG,CAACG,sBAAsB;QAClDsC,gCAAgC,CAAC,GAACjD,oBAAAA,IAAIM,WAAW,qBAAfN,kBAAiBU,0BAA0B;QAC7ER;IACF;IAEA,OAAO;QACLC;QACA+C,kBAAkB,CAACC,SAAkC/C,cAAc+C;QACnEzB,UAAUT;IACZ;AACF;AAGO,eAAexC,sBACpB2E,YAAmC,EACnCrD,OAAoC,EACpC,EACEE,WAAW,EACXD,MAAMqD,IAAAA,mBAAS,EAACD,aAAatD,WAAW,EAAE;IACxCwD,2BAA2B;AAC7B,GAAGtD,GAAG,EACqC;IAQ7C,MAAMF,cAAcsD,aAAatD,WAAW;IAE5C,MAAM,EACJK,QAAQoD,WAAW,EACnBL,gBAAgB,EAChBxB,QAAQ,EACT,GAAG,MAAM/C,qBAAqBmB,aAAaC,SAAS;QACnDC;QACAC;QACAC;YACE,OAAOsD,MAAMC,UAAU,GAAGA,UAAU;QACtC;IACF;IAEA,4EAA4E;IAC5E,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,YAAY,EAAEC,kBAAkB,EAAE,GACpEC,IAAAA,4CAAqB,EAACP;IAExB,IAAI,CAACtD,aAAa;QAChB,uDAAuD;QACvD8D,IAAAA,4BAAiB,EAACL,YAAYM,IAAAA,oCAAoB,EAAChE;QAEnD,oDAAoD;QACpD,MAAM,EAAEiE,eAAe,EAAEC,uBAAuB,EAAE,GAAGC,IAAAA,4CAAqB,EACxEf,cACA1B;QAEF0C,OAAOC,MAAM,CAACR,oBAAoBK;QAClCR,WAAWY,GAAG,CAACL;QACfP,WAAWY,GAAG,CAAC,mBAAmBC,IAAAA,wDAA2B;QAE7D,wGAAwG;QACxG,yFAAyF;QACzF,yFAAyF;QACzF,MAAMC,0BAA0BjB,YAAYkB,MAAM,CAACC,iBAAiB;QACpE,iDAAiD;QACjDnB,YAAYkB,MAAM,CAACC,iBAAiB,GAAG,CAACC,iBAAsBF;YAC5D,IAAID,yBAAyB;gBAC3BG,kBAAkBH,wBAAwBG,iBAAiBF;YAC7D;YACA,OAAOf,WAAWY,GAAG,CAACK;QACxB;IACF;IAEA,+BAA+B;IAC/B,MAAMC,IAAAA,6BAAgB,EAAC;QACrB3E;QACAD;QACAF;QACA4D;QACAH;QACA,2EAA2E;QAC3EsB,gBAAgB5E;IAClB;IAEA,MAAM,EAAEwE,MAAM,EAAEK,SAAS,EAAEtB,KAAK,EAAE,GAAG,MAAMuB,IAAAA,wBAAS,EAClD3B,cACAG,aACA;QACEM,oBAAoB;YAClB,GAAGA,kBAAkB;YACrB,GAAGmB,IAAAA,sEAAqC,GAAE;QAC5C;QACAC,OAAO,CAAChF,eAAevB;IACzB,GACA;QACEwG,YAAYjF;IACd;IAGF,qHAAqH;IACrH,MAAMkF,wBAAwB3B,MAC3BC,UAAU,GACVA,UAAU,GACV2B,aAAa,CAACC,IAAI,CAAC7B,MAAMC,UAAU,GAAGA,UAAU;IAEnDD,MAAMC,UAAU,GAAGA,UAAU,GAAG2B,aAAa,GAAG,eAC9CE,QAAgB,EAChBC,gBAAkC,EAClCC,UAAmB;QAEnB,OAAOL,sBACLG,UACAG,4BACEH,UACA,qDAAqD;QACrD;YACE,GAAGC,gBAAgB;YACnBG,wBAAwB;gBACtBC,WAAW;gBACX,GAAGJ,iBAAiBG,sBAAsB;YAC5C;QACF,IAEFF;IAEJ;IAEAtC,iBAAiBU,aAAagC,gBAAgB;IAE9C,2EAA2E;IAC3E,iCAAiC;IACjCpC,MAAMqC,iBAAiB,GAAG,SAA8BC,KAAoB;YAK3DA;QAJf,MAAMC,UAAU;eAAID,MAAME,YAAY,CAACC,MAAM;SAAG;QAEhD,MAAMC,MAAM;YACVC,UAAUL,MAAMP,gBAAgB,CAACY,QAAQ;YACzCC,WAAW,GAAEN,iDAAAA,MAAMP,gBAAgB,CAACG,sBAAsB,qBAA7CI,+CAA+CM,WAAW;QACzE;QACA,8CAA8C;QAC9C,KAAK,MAAMC,UAAUN,QAAS;YAC5B,mBAAmB;YACnB,IAAI,CAACO,eAAe,CAACD,OAAOE,IAAI,EAAEL;QACpC;QACA,cAAc;QACd,OAAOH,QAAQS,IAAI,CACjB,mBAAmB;QACnB,CAACC,GAAGC,IAAM,IAAI,CAACJ,eAAe,CAACG,EAAEF,IAAI,EAAEL,OAAO,IAAI,CAACI,eAAe,CAACI,EAAEH,IAAI,EAAEL;IAE/E;IAEA,IAAIpB,WAAW;QACb,IAAI6B;QAEJ,IAAI;YACFA,cAAcC,QAAQ,wDAAwDC,OAAO;QACvF,EAAE,OAAM;YACN,+DAA+D;YAC/DtE,QAAG,CAACC,IAAI,CAAC;YACTmE,cAAcC,QAAQ;QACxB;QAEA,+KAA+K;QAC/K9B,UAAUgC,eAAe,GAAG,eAAsCC,KAAK,EAAEhH,OAAO,EAAEiH,WAAW;YAC3F,oIAAoI;YACpI,oCAAoC;YACpC,MAAM7D,SAAS,CAACpD,QAAQkH,eAAe,GAAGD,+BAAAA,YAAa7D,MAAM,GAAG;YAChE,IAAI;oBAwBa+D;gBAvBf,MAAMC,aAAa,IAAI,CAACC,QAAQ,CAACC,WAAW,CAACN,MAAMO,UAAU;gBAC7D,IAAI,CAACH,YAAY;oBACf,OAAO;wBACLI,MAAM;wBACNC,MAAMC,IAAAA,8BAAmB,EAAC,IAAIC,CAAAA,wBAAoB,SAAC,CAACX,MAAMO,UAAU;oBACtE;gBACF;gBACAnE,0BAAAA,OAAQwE,KAAK,CAAC;gBACd,MAAM,EAAET,QAAQ,EAAEU,KAAK,EAAE,GAAG,MAAM,IAAI,CAACR,QAAQ,CAACS,WAAW,CAAC,MAAMV,YAAY;gBAC9EhE,0BAAAA,OAAQwE,KAAK,CAAC;gBACd,IAAI,CAACG,aAAa,CAACC,MAAM,CAAChB,MAAMO,UAAU;gBAC1CP,MAAMO,UAAU,GAAGJ,SAASc,EAAE;gBAC9B,KAAK,MAAMC,UAAUlB,MAAMmB,OAAO,CAAE;oBAClCD,OAAOE,WAAW,GAAGF,OAAOE,WAAW,CAACC,MAAM,CAC5C,CAACd,aAAeA,eAAeP,MAAMO,UAAU;oBAEjDW,OAAOE,WAAW,CAAChJ,IAAI,CAAC+H,SAASc,EAAE;gBACrC;gBACA,IAAI,CAACF,aAAa,CAACO,GAAG,CAACtB,MAAMO,UAAU,EAAEP;gBACzC5D,0BAAAA,OAAQwE,KAAK,CAAC;gBACd,qCAAqC;gBACrC,MAAMW,kBAAkB;oBACtBnC,UAAUe,SAASpB,KAAK,CAACP,gBAAgB,CAACY,QAAQ;oBAClDC,WAAW,GAAEc,0DAAAA,SAASpB,KAAK,CAACP,gBAAgB,CAACG,sBAAsB,qBAAtDwB,wDAAwDd,WAAW;gBAClF;gBACA,MAAMmC,YAAY5B,YAAYiB,OAAOV,SAASpB,KAAK,EAAE;oBACnD0C,WAAWzB,MAAMyB,SAAS;oBAC1B,0CAA0C;oBAC1CC,gBAAgB,CAACC;wBACf,mBAAmB;wBACnB,OAAO,IAAI,CAACpC,eAAe,CAACoC,UAAUJ;oBACxC;oBACAK,mBAAmB5B,MAAM6B,YAAY,CAACC,IAAI;oBAC1C/I,aAAa,IAAI,CAACgJ,OAAO,CAAChJ,WAAW;oBACrCiB,YAAY,IAAI,CAAC+H,OAAO,CAACrE,MAAM,CAACsE,mBAAmB,IAAI,IAAI,CAACD,OAAO,CAAChJ,WAAW;gBACjF;gBACAqD,0BAAAA,OAAQwE,KAAK,CAAC;gBACd,OAAO;oBACLJ,MAAM;oBACNC,MAAM;wBACJF,YAAYJ,SAASc,EAAE;wBACvBf,iBAAiBlH,QAAQkH,eAAe;wBACxC,GAAGsB,SAAS;oBACd;gBACF;YACF,EAAE,OAAOS,OAAY;gBACnB,MAAMC,iBAAiBxB,IAAAA,8BAAmB,EAACuB;gBAC3C,IAAI,CAACF,OAAO,CAACpH,QAAQ,CAACC,MAAM,CAAC;oBAC3B4F,MAAM;oBACNyB;gBACF;gBACA,OAAO;oBACLzB,MAAM;oBACNC,MAAMyB;gBACR;YACF;QACF;IACF;IAEA,OAAO;QACLzF;QACAsB;QACAL;QACAf;QACAwF,eAAevF;IACjB;AACF;AAEA,0GAA0G;AAC1G,SAAS8B,4BACPH,QAAgB,EAChBC,gBAAkC;QAMhCA,0CAUAA,2CAQAA,2CAQAA;IA9BF,sDAAsD;IACtDD,WAAWA,SAAS6D,KAAK,CAAC5C,eAAI,CAAC6C,GAAG,EAAEC,IAAI,CAAC;IAEzC,IACE9D,EAAAA,2CAAAA,iBAAiBG,sBAAsB,qBAAvCH,yCAAyC+D,GAAG,KAC5C,yEAAyE;IACzE,CAAChE,SAASiE,KAAK,CAAC,0BAChB;QACA,yFAAyF;QACzF,wEAAwE;QACxEhE,iBAAiBG,sBAAsB,CAAC4D,GAAG,GAAG;IAChD;IAEA,IACE/D,EAAAA,4CAAAA,iBAAiBG,sBAAsB,qBAAvCH,0CAAyCiE,UAAU,KACnD,qKAAqK;IACrK,CAAElE,CAAAA,SAASiE,KAAK,CAAC,0BAA0BjE,SAASiE,KAAK,CAAC,yBAAwB,GAClF;QACA,4BAA4B;QAC5BhE,iBAAiBG,sBAAsB,CAAC8D,UAAU,GAAG;IACvD;IACA,IACEjE,EAAAA,4CAAAA,iBAAiBG,sBAAsB,qBAAvCH,0CAAyCkE,WAAW,KACpD,+IAA+I;IAC/I,CAAEnE,CAAAA,SAASiE,KAAK,CAAC,0BAA0BjE,SAASiE,KAAK,CAAC,yBAAwB,GAClF;QACA,OAAOhE,iBAAiBG,sBAAsB,CAAC+D,WAAW;IAC5D;IAEA,IACElE,EAAAA,4CAAAA,iBAAiBG,sBAAsB,qBAAvCH,0CAAyCmE,gBAAgB,KACzD,2FAA2F;IAC3F,CAACpE,SAASiE,KAAK,CAAC,8BAChB;QACA,OAAOhE,iBAAiBG,sBAAsB,CAACgE,gBAAgB;IACjE;IAEA,OAAOnE;AACT;AAMO,SAAS7G;IACd,IAAI8B,QAAG,CAACmJ,EAAE,EAAE;QACVpH,QAAG,CAAC9C,GAAG,CACLmK,IAAAA,gBAAK,CAAA,CAAC,8FAA8F,CAAC;IAEzG;IAEA,OAAO,CAACpJ,QAAG,CAACmJ,EAAE;AAChB"}