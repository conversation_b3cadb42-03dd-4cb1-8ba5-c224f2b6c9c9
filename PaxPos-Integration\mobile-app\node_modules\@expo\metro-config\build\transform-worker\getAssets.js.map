{"version": 3, "file": "getAssets.js", "sourceRoot": "", "sources": ["../../src/transform-worker/getAssets.ts"], "names": [], "mappings": ";;;;;AA8FA,sDAyBC;AAID,4BA2BC;AA9ID,6CAAgD;AAChD,4EAA2F;AAC3F,8DAAiC;AACjC,sDAAyB;AACzB,0DAA6B;AAI7B,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAuB,CAAC;AAcjF,SAAS,aAAa,CAAC,IAAc;IACnC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,IAAI,GAAG,qBAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAY;IAC1C,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC7C,MAAM,MAAM,GAAG,qBAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,iBAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACxC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QACxC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAC,KAAoB;IAC7C,IAAI,YAAY,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;QAC7D,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,KAAK,UAAU,yBAAyB,CAAC,KAAoB;IAC3D,kFAAkF;IAClF,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,KAAK,CAAC,oDAAoD,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QACzE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC;IAC1E,wDAAwD;IAExD,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC;IAE1B,4EAA4E;IAC5E,oIAAoI;IACpI,4NAA4N;IAC5N,IAAI,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;QACvD,wCAAwC;QACxC,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB;aAChD,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;aAC9B,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED,yFAAyF;IACzF,0FAA0F;IAC1F,MAAM,uBAAuB,GAAG,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAC5D,oCAAoC,CACrC,CAAC;IACF,IAAI,uBAAuB,IAAI,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1D,MAAM,SAAS,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAC7C,wCAAwC;QACxC,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,OAAO,CACzD,SAAS,EACT,kBAAkB,CAAC,SAAS,CAAC,CAC9B,CAAC;IACJ,CAAC;IAED,OAAO,KAAwB,CAAC;AAClC,CAAC;AAEM,KAAK,UAAU,qBAAqB,CACzC,SAAiB,EACjB,SAAiB,EACjB,gBAAmC,EACnC,QAAmC,EACnC,UAAkB;IAElB,MAAM,cAAc,GAAG,MAAM,IAAA,qBAAY,EACvC,SAAS,EACT,SAAS,EACT,gBAAgB,EAChB,QAAQ,EACR,UAAU,CACX,CAAC;IACF,MAAM,IAAI,GAAG,MAAM,yBAAyB,CAAC,cAAc,CAAC,CAAC;IAE7D,+GAA+G;IAC/G,IAAI,QAAQ,KAAK,KAAK,IAAI,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;QAC/D,uIAAuI;QACvI,0EAA0E;QAC1E,+CAA+C;QAC/C,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;IAC/D,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAIc,KAAK,UAAU,SAAS,CACrC,YAAkC,EAClC,OAAgB;IAEhB,MAAM,QAAQ,GAA+B,EAAE,CAAC;IAChD,MAAM,EAAE,mBAAmB,EAAE,GAAG,OAAO,CAAC;IAExC,KAAK,MAAM,MAAM,IAAI,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;QAC3C,IACE,IAAA,kBAAU,EAAC,MAAM,CAAC;YAClB,mBAAmB,CAAC,MAAM,CAAC;YAC3B,IAAA,mBAAW,EAAC,MAAM,CAAC,CAAC,IAAI,KAAK,iBAAiB;YAC9C,mBAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,cAAc,EAClE,CAAC;YACD,QAAQ,CAAC,IAAI,CACX,qBAAqB,CACnB,MAAM,CAAC,IAAI,EACX,mBAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,EAC/C,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,UAAU,CACnB,CACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACrC,CAAC"}