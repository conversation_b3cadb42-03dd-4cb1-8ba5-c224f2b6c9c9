{"version": 3, "sources": ["../../../../src/export/embed/resolveOptions.ts"], "sourcesContent": ["import { resolveEntryPoint } from '@expo/config/paths';\nimport arg from 'arg';\nimport type { OutputOptions } from 'metro/src/shared/types';\nimport canonicalize from 'metro-core/src/canonicalize';\nimport os from 'os';\nimport path from 'path';\n\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { resolveCustomBooleanArgsAsync } from '../../utils/resolveArgs';\nimport { isAndroidUsingHermes, isIosUsingHermes } from '../exportHermes';\n\nexport interface Options {\n  assetsDest?: string;\n  assetCatalogDest?: string;\n  entryFile: string;\n  resetCache: boolean;\n  transformer?: string;\n  minify?: boolean;\n  config?: string;\n  platform: string;\n  dev: boolean;\n  bundleOutput: string;\n  bundleEncoding?: OutputOptions['bundleEncoding'];\n  maxWorkers?: number;\n  sourcemapOutput?: string;\n  sourcemapSourcesRoot?: string;\n  sourcemapUseAbsolutePath: boolean;\n  verbose: boolean;\n  unstableTransformProfile?: string;\n  eager?: boolean;\n  bytecode?: boolean;\n}\n\nfunction assertIsBoolean(val: any): asserts val is boolean {\n  if (typeof val !== 'boolean') {\n    throw new CommandError(`Expected boolean, got ${typeof val}`);\n  }\n}\n\nfunction getBundleEncoding(encoding: string | undefined): OutputOptions['bundleEncoding'] {\n  return encoding === 'utf8' || encoding === 'utf16le' || encoding === 'ascii'\n    ? encoding\n    : undefined;\n}\n\nexport function resolveOptions(\n  projectRoot: string,\n  args: arg.Result<arg.Spec>,\n  parsed: Awaited<ReturnType<typeof resolveCustomBooleanArgsAsync>>\n): Options {\n  const dev = parsed.args['--dev'] ?? true;\n  assertIsBoolean(dev);\n\n  const platform = args['--platform'];\n  if (!platform) {\n    throw new CommandError(`Missing required argument: --platform`);\n  }\n\n  const bundleOutput = args['--bundle-output'];\n\n  const commonOptions = {\n    entryFile: args['--entry-file'] ?? resolveEntryPoint(projectRoot, { platform }),\n    assetCatalogDest: args['--asset-catalog-dest'],\n    platform,\n    transformer: args['--transformer'],\n    // TODO: Support `--dev false`\n    //   dev: false,\n    bundleOutput,\n    bundleEncoding: getBundleEncoding(args['--bundle-encoding']) ?? 'utf8',\n    maxWorkers: args['--max-workers'],\n    sourcemapOutput: args['--sourcemap-output'],\n    sourcemapSourcesRoot: args['--sourcemap-sources-root'],\n    sourcemapUseAbsolutePath: !!parsed.args['--sourcemap-use-absolute-path'],\n    assetsDest: args['--assets-dest'],\n    unstableTransformProfile: args['--unstable-transform-profile'],\n    resetCache: !!parsed.args['--reset-cache'],\n    verbose: args['--verbose'] ?? env.EXPO_DEBUG,\n    config: args['--config'] ? path.resolve(args['--config']) : undefined,\n    dev,\n    minify: parsed.args['--minify'] as boolean | undefined,\n    eager: !!parsed.args['--eager'],\n    bytecode: parsed.args['--bytecode'] as boolean | undefined,\n  };\n\n  if (commonOptions.eager) {\n    return resolveEagerOptionsAsync(projectRoot, commonOptions);\n  }\n\n  // Perform extra assertions after the eager options are resolved.\n\n  if (!bundleOutput) {\n    throw new CommandError(`Missing required argument: --bundle-output`);\n  }\n\n  const minify = parsed.args['--minify'] ?? !dev;\n  assertIsBoolean(minify);\n\n  return { ...commonOptions, minify, bundleOutput };\n}\n\nfunction getTemporaryPath() {\n  return path.join(os.tmpdir(), Math.random().toString(36).substring(2));\n}\n\n/** Best effort guess of which options will be used for the export:embed invocation that is called from the native build scripts. */\nexport function resolveEagerOptionsAsync(\n  projectRoot: string,\n  {\n    dev,\n    platform,\n    assetsDest,\n    bundleOutput,\n    minify,\n    ...options\n  }: Partial<Omit<Options, 'platform' | 'dev'>> & {\n    platform: string;\n    dev: boolean;\n  }\n): Options {\n  // If the minify prop is undefined, then check if the project is using hermes.\n  minify ??= !(platform === 'android'\n    ? isAndroidUsingHermes(projectRoot)\n    : isIosUsingHermes(projectRoot));\n\n  let destination: string | undefined;\n\n  if (!assetsDest) {\n    destination ??= getTemporaryPath();\n    assetsDest = path.join(destination, 'assets');\n  }\n\n  if (!bundleOutput) {\n    destination ??= getTemporaryPath();\n    bundleOutput =\n      platform === 'ios'\n        ? path.join(destination, 'main.jsbundle')\n        : path.join(destination, 'index.js');\n  }\n\n  return {\n    ...options,\n    eager: options.eager ?? true,\n    bundleOutput,\n    assetsDest,\n    entryFile: options.entryFile ?? resolveEntryPoint(projectRoot, { platform }),\n    resetCache: !!options.resetCache,\n    platform,\n    minify,\n    dev,\n    bundleEncoding: 'utf8',\n    sourcemapUseAbsolutePath: false,\n    verbose: env.EXPO_DEBUG,\n  };\n}\n\nexport function getExportEmbedOptionsKey({\n  // Extract all values that won't change the Metro results.\n  resetCache,\n  assetsDest,\n  bundleOutput,\n  verbose,\n  maxWorkers,\n  eager,\n  ...options\n}: Options) {\n  // Create a sorted key for the options, removing values that won't change the Metro results.\n  return JSON.stringify(options, canonicalize);\n}\n\nexport function deserializeEagerKey(key: string) {\n  return JSON.parse(key) as { options: Options; key: string };\n}\n"], "names": ["deserializeEagerKey", "getExportEmbedOptionsKey", "resolveEagerOptionsAsync", "resolveOptions", "assertIsBoolean", "val", "CommandError", "getBundleEncoding", "encoding", "undefined", "projectRoot", "args", "parsed", "dev", "platform", "bundleOutput", "commonOptions", "entryFile", "resolveEntryPoint", "assetCatalogDest", "transformer", "bundleEncoding", "maxWorkers", "sourcemapOutput", "sourcemapSourcesRoot", "sourcemapUseAbsolutePath", "assetsDest", "unstableTransformProfile", "resetCache", "verbose", "env", "EXPO_DEBUG", "config", "path", "resolve", "minify", "eager", "bytecode", "getTemporaryPath", "join", "os", "tmpdir", "Math", "random", "toString", "substring", "options", "isAndroidUsingHermes", "isIosUsingHermes", "destination", "JSON", "stringify", "canonicalize", "key", "parse"], "mappings": ";;;;;;;;;;;IA0KgBA,mBAAmB;eAAnBA;;IAdAC,wBAAwB;eAAxBA;;IAlDAC,wBAAwB;eAAxBA;;IA5DAC,cAAc;eAAdA;;;;yBA9CkB;;;;;;;gEAGT;;;;;;;gEACV;;;;;;;gEACE;;;;;;qBAEG;wBACS;8BAE0B;;;;;;AAwBvD,SAASC,gBAAgBC,GAAQ;IAC/B,IAAI,OAAOA,QAAQ,WAAW;QAC5B,MAAM,IAAIC,oBAAY,CAAC,CAAC,sBAAsB,EAAE,OAAOD,KAAK;IAC9D;AACF;AAEA,SAASE,kBAAkBC,QAA4B;IACrD,OAAOA,aAAa,UAAUA,aAAa,aAAaA,aAAa,UACjEA,WACAC;AACN;AAEO,SAASN,eACdO,WAAmB,EACnBC,IAA0B,EAC1BC,MAAiE;IAEjE,MAAMC,MAAMD,OAAOD,IAAI,CAAC,QAAQ,IAAI;IACpCP,gBAAgBS;IAEhB,MAAMC,WAAWH,IAAI,CAAC,aAAa;IACnC,IAAI,CAACG,UAAU;QACb,MAAM,IAAIR,oBAAY,CAAC,CAAC,qCAAqC,CAAC;IAChE;IAEA,MAAMS,eAAeJ,IAAI,CAAC,kBAAkB;IAE5C,MAAMK,gBAAgB;QACpBC,WAAWN,IAAI,CAAC,eAAe,IAAIO,IAAAA,0BAAiB,EAACR,aAAa;YAAEI;QAAS;QAC7EK,kBAAkBR,IAAI,CAAC,uBAAuB;QAC9CG;QACAM,aAAaT,IAAI,CAAC,gBAAgB;QAClC,8BAA8B;QAC9B,gBAAgB;QAChBI;QACAM,gBAAgBd,kBAAkBI,IAAI,CAAC,oBAAoB,KAAK;QAChEW,YAAYX,IAAI,CAAC,gBAAgB;QACjCY,iBAAiBZ,IAAI,CAAC,qBAAqB;QAC3Ca,sBAAsBb,IAAI,CAAC,2BAA2B;QACtDc,0BAA0B,CAAC,CAACb,OAAOD,IAAI,CAAC,gCAAgC;QACxEe,YAAYf,IAAI,CAAC,gBAAgB;QACjCgB,0BAA0BhB,IAAI,CAAC,+BAA+B;QAC9DiB,YAAY,CAAC,CAAChB,OAAOD,IAAI,CAAC,gBAAgB;QAC1CkB,SAASlB,IAAI,CAAC,YAAY,IAAImB,QAAG,CAACC,UAAU;QAC5CC,QAAQrB,IAAI,CAAC,WAAW,GAAGsB,eAAI,CAACC,OAAO,CAACvB,IAAI,CAAC,WAAW,IAAIF;QAC5DI;QACAsB,QAAQvB,OAAOD,IAAI,CAAC,WAAW;QAC/ByB,OAAO,CAAC,CAACxB,OAAOD,IAAI,CAAC,UAAU;QAC/B0B,UAAUzB,OAAOD,IAAI,CAAC,aAAa;IACrC;IAEA,IAAIK,cAAcoB,KAAK,EAAE;QACvB,OAAOlC,yBAAyBQ,aAAaM;IAC/C;IAEA,iEAAiE;IAEjE,IAAI,CAACD,cAAc;QACjB,MAAM,IAAIT,oBAAY,CAAC,CAAC,0CAA0C,CAAC;IACrE;IAEA,MAAM6B,SAASvB,OAAOD,IAAI,CAAC,WAAW,IAAI,CAACE;IAC3CT,gBAAgB+B;IAEhB,OAAO;QAAE,GAAGnB,aAAa;QAAEmB;QAAQpB;IAAa;AAClD;AAEA,SAASuB;IACP,OAAOL,eAAI,CAACM,IAAI,CAACC,aAAE,CAACC,MAAM,IAAIC,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC;AACrE;AAGO,SAAS3C,yBACdQ,WAAmB,EACnB,EACEG,GAAG,EACHC,QAAQ,EACRY,UAAU,EACVX,YAAY,EACZoB,MAAM,EACN,GAAGW,SAIJ;IAED,8EAA8E;IAC9EX,WAAW,CAAErB,CAAAA,aAAa,YACtBiC,IAAAA,kCAAoB,EAACrC,eACrBsC,IAAAA,8BAAgB,EAACtC,YAAW;IAEhC,IAAIuC;IAEJ,IAAI,CAACvB,YAAY;QACfuB,gBAAgBX;QAChBZ,aAAaO,eAAI,CAACM,IAAI,CAACU,aAAa;IACtC;IAEA,IAAI,CAAClC,cAAc;QACjBkC,gBAAgBX;QAChBvB,eACED,aAAa,QACTmB,eAAI,CAACM,IAAI,CAACU,aAAa,mBACvBhB,eAAI,CAACM,IAAI,CAACU,aAAa;IAC/B;IAEA,OAAO;QACL,GAAGH,OAAO;QACVV,OAAOU,QAAQV,KAAK,IAAI;QACxBrB;QACAW;QACAT,WAAW6B,QAAQ7B,SAAS,IAAIC,IAAAA,0BAAiB,EAACR,aAAa;YAAEI;QAAS;QAC1Ec,YAAY,CAAC,CAACkB,QAAQlB,UAAU;QAChCd;QACAqB;QACAtB;QACAQ,gBAAgB;QAChBI,0BAA0B;QAC1BI,SAASC,QAAG,CAACC,UAAU;IACzB;AACF;AAEO,SAAS9B,yBAAyB,EACvC,0DAA0D;AAC1D2B,UAAU,EACVF,UAAU,EACVX,YAAY,EACZc,OAAO,EACPP,UAAU,EACVc,KAAK,EACL,GAAGU,SACK;IACR,4FAA4F;IAC5F,OAAOI,KAAKC,SAAS,CAACL,SAASM,uBAAY;AAC7C;AAEO,SAASpD,oBAAoBqD,GAAW;IAC7C,OAAOH,KAAKI,KAAK,CAACD;AACpB"}