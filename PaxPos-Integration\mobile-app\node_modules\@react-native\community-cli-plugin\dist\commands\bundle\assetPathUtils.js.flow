/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

export type PackagerAsset = $ReadOnly<{
  httpServerLocation: string,
  name: string,
  type: string,
  ...
}>;

/**
 * FIXME: using number to represent discrete scale numbers is fragile in essence because of
 * floating point numbers imprecision.
 */
declare function getAndroidAssetSuffix(scale: number): string;

declare function getAndroidResourceFolderName(
  asset: PackagerAsset,
  scale: number
): string;

declare function getResourceIdentifier(asset: PackagerAsset): string;

declare function getBasePath(asset: PackagerAsset): string;

declare export default {
  getAndroidAssetSuffix: getAndroidAssetSuffix,
  getAndroidResourceFolderName: getAndroidResourceFolderName,
  getResourceIdentifier: getResourceIdentifier,
  getBasePath: getBasePath,
};
