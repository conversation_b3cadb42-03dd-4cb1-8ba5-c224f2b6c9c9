/**
 * Unit test for Viva Webhook Service
 * Tests the webhook service functionality without requiring a running server
 */

// Mock environment variables
process.env.VIVA_MERCHANT_ID = '30481af3-63d9-42cd-93ea-1937a972b76d';
process.env.VIVA_API_KEY = 'SothunZ2FxVRMkq666sbxbxB6VNbJG';
process.env.VIVA_CLIENT_ID = '00pp9ggt8otvtzyfy3sv7y0d5u56oleukdkd7mma293z8.apps.vivapayments.com';
process.env.VIVA_CLIENT_SECRET = 'test_client_secret';
process.env.VIVA_SOURCE_CODE = 'Default';
process.env.VIVA_ENVIRONMENT = 'demo';
process.env.VIVA_WEBHOOK_VERIFICATION_KEY = 'test_verification_key';

async function testWebhookService() {
  try {
    console.log('🔍 Testing Viva Webhook Service...\n');

    // Import the service after setting environment variables
    const { vivaWebhookService, VIVA_WEBHOOK_EVENTS } = require('./dist/services/vivaWebhookService.js');

    // Test 1: Event type descriptions
    console.log('📋 Test 1: Event Type Descriptions');
    const eventTypes = [
      VIVA_WEBHOOK_EVENTS.TRANSACTION_PAYMENT_CREATED,
      VIVA_WEBHOOK_EVENTS.TRANSACTION_FAILED,
      VIVA_WEBHOOK_EVENTS.TRANSACTION_REVERSAL_CREATED
    ];

    eventTypes.forEach(eventType => {
      const description = vivaWebhookService.getEventTypeDescription(eventType);
      console.log(`  Event ${eventType}: ${description}`);
    });
    console.log('✅ Event type descriptions working correctly\n');

    // Test 2: Webhook event validation
    console.log('📋 Test 2: Webhook Event Validation');
    
    const validEvent = {
      Url: "https://demo.vivapayments.com/api/messages/events/1796",
      EventData: {
        Amount: 1000,
        MerchantId: "30481af3-63d9-42cd-93ea-1937a972b76d",
        OrderCode: ****************,
        StatusId: "F",
        TransactionId: "test-transaction-123",
        InsDate: new Date().toISOString()
      },
      Created: new Date().toISOString(),
      CorrelationId: "test-correlation-123",
      EventTypeId: 1796,
      RetryCount: 0,
      MessageId: "test-message-123",
      RecipientId: "test-recipient",
      MessageTypeId: 512
    };

    try {
      const validatedEvent = vivaWebhookService.validateWebhookEvent(validEvent);
      console.log('  ✅ Valid event structure accepted');
      console.log(`  Transaction ID: ${validatedEvent.EventData.TransactionId}`);
      console.log(`  Order Code: ${validatedEvent.EventData.OrderCode}`);
      console.log(`  Amount: €${(validatedEvent.EventData.Amount / 100).toFixed(2)}`);
    } catch (error) {
      console.log('  ❌ Valid event rejected:', error.message);
    }

    // Test invalid event
    const invalidEvent = {
      EventTypeId: 1796,
      // Missing required fields
    };

    try {
      vivaWebhookService.validateWebhookEvent(invalidEvent);
      console.log('  ❌ Invalid event incorrectly accepted');
    } catch (error) {
      console.log('  ✅ Invalid event correctly rejected');
    }

    console.log('\n📋 Test 3: Webhook Authenticity Verification');
    
    // Test webhook verification (basic implementation)
    const testPayload = JSON.stringify(validEvent);
    const testHeaders = {
      'content-type': 'application/json',
      'user-agent': 'Viva-Webhook/1.0'
    };

    const isValid = vivaWebhookService.verifyWebhookAuthenticity(testPayload, testHeaders);
    console.log(`  Webhook authenticity check: ${isValid ? '✅ Valid' : '❌ Invalid'}`);

    console.log('\n🎉 All webhook service tests completed successfully!');
    
    return true;
  } catch (error) {
    console.error('💥 Webhook service test failed:', error);
    return false;
  }
}

// Test receipt service integration
async function testReceiptService() {
  try {
    console.log('\n🔍 Testing Receipt Service Integration...\n');

    // Mock transaction data
    const mockTransaction = {
      _id: 'test-transaction-id',
      amount: 1000,
      currency: 'EUR',
      status: 'success',
      paymentMethod: 'card',
      paymentProvider: 'viva',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        order_code: '****************',
        payment_provider: 'viva'
      }
    };

    // Mock Viva event data
    const mockVivaData = {
      TransactionId: 'viva-transaction-123',
      OrderCode: ****************,
      Amount: 1000,
      StatusId: 'F',
      MerchantId: '30481af3-63d9-42cd-93ea-1937a972b76d',
      CardNumber: '414746XXXXXX0133',
      AuthorizationId: '471543',
      ResponseCode: '00',
      InsDate: new Date().toISOString(),
      CurrencyCode: '978',
      MerchantTrns: 'Test payment transaction',
      CustomerTrns: 'Payment for order #123',
      CardIssuingBank: 'Test Bank',
      ReferenceNumber: 471543,
      RetrievalReferenceNumber: '************'
    };

    // Import receipt service
    const { ReceiptService } = require('./dist/services/receiptService.js');

    console.log('📋 Test 1: Generate Viva Customer Receipt');
    try {
      const customerReceipt = ReceiptService.generateVivaCustomerReceipt(mockTransaction, mockVivaData);
      console.log('✅ Customer receipt generated successfully');
      console.log('Receipt preview (first 200 chars):');
      console.log(customerReceipt.substring(0, 200) + '...\n');
    } catch (error) {
      console.log('❌ Customer receipt generation failed:', error.message);
    }

    console.log('📋 Test 2: Generate Viva Merchant Receipt');
    try {
      const merchantReceipt = ReceiptService.generateVivaMerchantReceipt(mockTransaction, mockVivaData);
      console.log('✅ Merchant receipt generated successfully');
      console.log('Receipt preview (first 200 chars):');
      console.log(merchantReceipt.substring(0, 200) + '...\n');
    } catch (error) {
      console.log('❌ Merchant receipt generation failed:', error.message);
    }

    console.log('🎉 Receipt service tests completed successfully!');
    return true;
  } catch (error) {
    console.error('💥 Receipt service test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Viva Webhook Integration Unit Tests\n');
  console.log('=' .repeat(60));
  
  const webhookResult = await testWebhookService();
  console.log('=' .repeat(60));
  
  const receiptResult = await testReceiptService();
  console.log('=' .repeat(60));
  
  console.log('\n📊 Final Test Results:');
  console.log(`Webhook Service: ${webhookResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Receipt Service: ${receiptResult ? '✅ PASS' : '❌ FAIL'}`);
  
  if (webhookResult && receiptResult) {
    console.log('\n🎉 All unit tests passed! The Viva webhook integration is ready for deployment.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.');
  }
  
  return webhookResult && receiptResult;
}

// Handle command line execution
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testWebhookService,
  testReceiptService,
  runAllTests
};
