/**
 * WebSocket Service
 * 
 * Handles real-time communication between backend and frontend
 * Broadcasts payment events, transaction updates, and other real-time data
 */

import { WebSocketServer, WebSocket } from 'ws';
import { createChildLogger } from '../config/logger';
import { ITransaction } from '../models/Transaction.mongo';

const wsLogger = createChildLogger({ module: 'websocket' });

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export interface PaymentEventData {
  transactionId: string;
  orderCode?: string | number;
  status: string;
  amount?: number;
  currency?: string;
  paymentProvider: string;
  eventType: string;
  metadata?: Record<string, any>;
}

export interface ReceiptEventData {
  transactionId: string;
  receiptType: 'customer' | 'merchant';
  receiptContent: string;
  format: 'text' | 'html' | 'pdf';
}

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private clients: Set<WebSocket> = new Set();

  /**
   * Initialize WebSocket server
   */
  initialize(server: any): void {
    try {
      this.wss = new WebSocketServer({ 
        server,
        path: '/ws',
        perMessageDeflate: false
      });

      this.wss.on('connection', (ws: WebSocket, request) => {
        const clientId = this.generateClientId();
        wsLogger.info('WebSocket client connected', { 
          clientId,
          origin: request.headers.origin,
          userAgent: request.headers['user-agent']
        });

        // Add client to active connections
        this.clients.add(ws);

        // Set up client event handlers
        this.setupClientHandlers(ws, clientId);

        // Send welcome message
        this.sendToClient(ws, {
          type: 'connection',
          data: { 
            clientId,
            message: 'Connected to payment system WebSocket',
            timestamp: new Date().toISOString()
          },
          timestamp: new Date().toISOString()
        });
      });

      this.wss.on('error', (error) => {
        wsLogger.error('WebSocket server error', error);
      });

      wsLogger.info('WebSocket server initialized successfully');
    } catch (error) {
      wsLogger.error('Failed to initialize WebSocket server', error);
      throw error;
    }
  }

  /**
   * Set up event handlers for a WebSocket client
   */
  private setupClientHandlers(ws: WebSocket, clientId: string): void {
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        wsLogger.debug('Received message from client', { clientId, message });

        // Handle different message types
        this.handleClientMessage(ws, clientId, message);
      } catch (error) {
        wsLogger.error('Error parsing client message', { clientId, error });
      }
    });

    ws.on('close', (code, reason) => {
      wsLogger.info('WebSocket client disconnected', { 
        clientId, 
        code, 
        reason: reason.toString() 
      });
      this.clients.delete(ws);
    });

    ws.on('error', (error) => {
      wsLogger.error('WebSocket client error', { clientId, error });
      this.clients.delete(ws);
    });

    // Set up ping/pong for connection health
    ws.on('pong', () => {
      wsLogger.debug('Received pong from client', { clientId });
    });
  }

  /**
   * Handle messages from WebSocket clients
   */
  private handleClientMessage(ws: WebSocket, clientId: string, message: any): void {
    switch (message.type) {
      case 'ping':
        this.sendToClient(ws, {
          type: 'pong',
          data: { timestamp: new Date().toISOString() },
          timestamp: new Date().toISOString()
        });
        break;

      case 'subscribe':
        // Handle subscription to specific events
        wsLogger.info('Client subscribed to events', { 
          clientId, 
          events: message.data?.events 
        });
        break;

      case 'unsubscribe':
        // Handle unsubscription from events
        wsLogger.info('Client unsubscribed from events', { 
          clientId, 
          events: message.data?.events 
        });
        break;

      default:
        wsLogger.warn('Unknown message type from client', { clientId, type: message.type });
    }
  }

  /**
   * Send message to a specific client
   */
  private sendToClient(ws: WebSocket, message: WebSocketMessage): void {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    } catch (error) {
      wsLogger.error('Error sending message to client', error);
    }
  }

  /**
   * Broadcast message to all connected clients
   */
  broadcast(message: WebSocketMessage): void {
    if (!this.wss || this.clients.size === 0) {
      wsLogger.debug('No WebSocket clients to broadcast to');
      return;
    }

    wsLogger.info('Broadcasting message to clients', { 
      type: message.type, 
      clientCount: this.clients.size 
    });

    const deadClients: WebSocket[] = [];

    this.clients.forEach((client) => {
      try {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify(message));
        } else {
          deadClients.push(client);
        }
      } catch (error) {
        wsLogger.error('Error broadcasting to client', error);
        deadClients.push(client);
      }
    });

    // Clean up dead connections
    deadClients.forEach((client) => {
      this.clients.delete(client);
    });
  }

  /**
   * Broadcast payment event to all clients
   */
  broadcastPaymentEvent(eventData: PaymentEventData): void {
    this.broadcast({
      type: 'payment_event',
      data: eventData,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Broadcast transaction update to all clients
   */
  broadcastTransactionUpdate(transaction: ITransaction): void {
    this.broadcast({
      type: 'transaction_update',
      data: {
        transactionId: transaction._id?.toString(),
        status: transaction.status,
        amount: transaction.amount,
        currency: transaction.currency,
        paymentMethod: transaction.paymentMethod,
        paymentProvider: transaction.paymentProvider,
        metadata: transaction.metadata,
        updatedAt: transaction.updatedAt
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Broadcast receipt generation event to all clients
   */
  broadcastReceiptEvent(eventData: ReceiptEventData): void {
    this.broadcast({
      type: 'receipt_event',
      data: eventData,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Broadcast Viva webhook event to all clients
   */
  broadcastVivaWebhookEvent(eventData: {
    eventType: number;
    eventDescription: string;
    transactionId: string;
    orderCode: number;
    amount: number;
    statusId: string;
    processed: boolean;
  }): void {
    this.broadcast({
      type: 'viva_webhook_event',
      data: eventData,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send health check ping to all clients
   */
  pingClients(): void {
    if (this.clients.size === 0) return;

    wsLogger.debug('Sending ping to all clients', { clientCount: this.clients.size });

    const deadClients: WebSocket[] = [];

    this.clients.forEach((client) => {
      try {
        if (client.readyState === WebSocket.OPEN) {
          client.ping();
        } else {
          deadClients.push(client);
        }
      } catch (error) {
        wsLogger.error('Error pinging client', error);
        deadClients.push(client);
      }
    });

    // Clean up dead connections
    deadClients.forEach((client) => {
      this.clients.delete(client);
    });
  }

  /**
   * Get current connection statistics
   */
  getStats(): { connectedClients: number; serverRunning: boolean } {
    return {
      connectedClients: this.clients.size,
      serverRunning: this.wss !== null
    };
  }

  /**
   * Generate unique client ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Close WebSocket server
   */
  close(): void {
    if (this.wss) {
      wsLogger.info('Closing WebSocket server');
      this.wss.close();
      this.wss = null;
      this.clients.clear();
    }
  }
}

export const websocketService = new WebSocketService();
