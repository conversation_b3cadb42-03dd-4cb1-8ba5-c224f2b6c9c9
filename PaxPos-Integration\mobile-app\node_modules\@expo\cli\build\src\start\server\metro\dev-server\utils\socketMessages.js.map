{"version": 3, "sources": ["../../../../../../../src/start/server/metro/dev-server/utils/socketMessages.ts"], "sourcesContent": ["import type { RawData as WebSocketRawData } from 'ws';\n\nconst debug = require('debug')('expo:metro:dev-server:messages') as typeof console.log;\n\n/** The current websocket-based communication between Metro, CLI, and client devices */\nconst PROTOCOL_VERSION = 2;\n\n/**\n * Parse the incoming raw message data and return the parsed object.\n * This returns null if the protocol version did not match expected version.\n */\nexport function parseRawMessage<T = Record<string, any>>(\n  data: WebSocketRawData,\n  isBinary: boolean\n): null | T {\n  if (isBinary) return null;\n\n  try {\n    const { version, ...message } = JSON.parse(data.toString()) ?? {};\n    if (version === PROTOCOL_VERSION) {\n      return message;\n    }\n\n    debug(\n      `Received message protocol version did not match supported \"${PROTOCOL_VERSION}\", received: ${message.version}`\n    );\n  } catch (error) {\n    debug(`Failed to parse message: ${error}`);\n  }\n\n  return null;\n}\n\n/**\n * Serialize any of the messages to send over websockets.\n * This adds the protocol version to the message.\n */\nexport function serializeMessage(message: Record<string, any>): string {\n  return JSON.stringify({ ...message, version: PROTOCOL_VERSION });\n}\n"], "names": ["parseRawMessage", "serializeMessage", "debug", "require", "PROTOCOL_VERSION", "data", "isBinary", "version", "message", "JSON", "parse", "toString", "error", "stringify"], "mappings": ";;;;;;;;;;;IAWgBA,eAAe;eAAfA;;IA0BAC,gBAAgB;eAAhBA;;;AAnChB,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,qFAAqF,GACrF,MAAMC,mBAAmB;AAMlB,SAASJ,gBACdK,IAAsB,EACtBC,QAAiB;IAEjB,IAAIA,UAAU,OAAO;IAErB,IAAI;QACF,MAAM,EAAEC,OAAO,EAAE,GAAGC,SAAS,GAAGC,KAAKC,KAAK,CAACL,KAAKM,QAAQ,OAAO,CAAC;QAChE,IAAIJ,YAAYH,kBAAkB;YAChC,OAAOI;QACT;QAEAN,MACE,CAAC,2DAA2D,EAAEE,iBAAiB,aAAa,EAAEI,QAAQD,OAAO,EAAE;IAEnH,EAAE,OAAOK,OAAO;QACdV,MAAM,CAAC,yBAAyB,EAAEU,OAAO;IAC3C;IAEA,OAAO;AACT;AAMO,SAASX,iBAAiBO,OAA4B;IAC3D,OAAOC,KAAKI,SAAS,CAAC;QAAE,GAAGL,OAAO;QAAED,SAASH;IAAiB;AAChE"}