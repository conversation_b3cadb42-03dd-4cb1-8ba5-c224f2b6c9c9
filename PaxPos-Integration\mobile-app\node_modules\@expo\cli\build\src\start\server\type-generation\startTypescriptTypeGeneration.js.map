{"version": 3, "sources": ["../../../../../src/start/server/type-generation/startTypescriptTypeGeneration.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport fs from 'fs/promises';\nimport { Server } from 'metro';\nimport path from 'path';\n\nimport { removeExpoEnvDTS, writeExpoEnvDTS } from './expo-env';\nimport { setupTypedRoutes } from './routes';\nimport { forceRemovalTSConfig, forceUpdateTSConfig } from './tsconfig';\nimport { upsertGitIgnoreContents } from '../../../utils/mergeGitIgnorePaths';\nimport { ensureDotExpoProjectDirectoryInitialized } from '../../project/dotExpo';\nimport { ServerLike } from '../BundlerDevServer';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\n\nexport interface TypeScriptTypeGenerationOptions {\n  server?: ServerLike;\n  metro?: Server | null;\n  projectRoot: string;\n}\n\nconst debug = require('debug')('expo:typed-routes') as typeof console.log;\n\n/** Setup all requisite features for statically typed routes in Expo Router v2 / SDK +49. */\nexport async function startTypescriptTypeGenerationAsync({\n  metro,\n  projectRoot,\n  server,\n}: TypeScriptTypeGenerationOptions) {\n  const { exp } = getConfig(projectRoot);\n\n  // If typed routes are disabled, remove any files that were added.\n  if (!exp.experiments?.typedRoutes) {\n    debug('Removing typed routes side-effects (experiments.typedRoutes: false)');\n    await Promise.all([forceRemovalTSConfig(projectRoot), removeExpoEnvDTS(projectRoot)]);\n  } else {\n    const dotExpoDir = ensureDotExpoProjectDirectoryInitialized(projectRoot);\n    const typesDirectory = path.resolve(dotExpoDir, './types');\n    debug(\n      'Ensuring typed routes side-effects are setup (experiments.typedRoutes: true, typesDirectory: %s)',\n      typesDirectory\n    );\n\n    // Ensure the types directory exists.\n    await fs.mkdir(typesDirectory, { recursive: true });\n\n    await Promise.all([\n      upsertGitIgnoreContents(path.join(projectRoot, '.gitignore'), 'expo-env.d.ts'),\n      writeExpoEnvDTS(projectRoot),\n      forceUpdateTSConfig(projectRoot),\n      setupTypedRoutes({\n        metro,\n        server,\n        typesDirectory,\n        projectRoot,\n        routerDirectory: path.join(\n          projectRoot,\n          getRouterDirectoryModuleIdWithManifest(projectRoot, exp)\n        ),\n        plugin: exp?.extra?.router,\n      }),\n    ]);\n  }\n}\n"], "names": ["startTypescriptTypeGenerationAsync", "debug", "require", "metro", "projectRoot", "server", "exp", "getConfig", "experiments", "typedRoutes", "Promise", "all", "forceRemovalTSConfig", "removeExpoEnvDTS", "dotExpoDir", "ensureDotExpoProjectDirectoryInitialized", "typesDirectory", "path", "resolve", "fs", "mkdir", "recursive", "upsertGitIgnoreContents", "join", "writeExpoEnvDTS", "forceUpdateTSConfig", "setupTypedRoutes", "routerDirectory", "getRouterDirectoryModuleIdWithManifest", "plugin", "extra", "router"], "mappings": ";;;;+BAsBsBA;;;eAAAA;;;;yBAtBI;;;;;;;gEACX;;;;;;;gEAEE;;;;;;yBAEiC;wBACjB;0BACyB;qCAClB;yBACiB;wBAEF;;;;;;AAQvD,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,eAAeF,mCAAmC,EACvDG,KAAK,EACLC,WAAW,EACXC,MAAM,EAC0B;QAI3BC;IAHL,MAAM,EAAEA,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAACH;IAE1B,kEAAkE;IAClE,IAAI,GAACE,mBAAAA,IAAIE,WAAW,qBAAfF,iBAAiBG,WAAW,GAAE;QACjCR,MAAM;QACN,MAAMS,QAAQC,GAAG,CAAC;YAACC,IAAAA,8BAAoB,EAACR;YAAcS,IAAAA,yBAAgB,EAACT;SAAa;IACtF,OAAO;YAwBOE;QAvBZ,MAAMQ,aAAaC,IAAAA,iDAAwC,EAACX;QAC5D,MAAMY,iBAAiBC,eAAI,CAACC,OAAO,CAACJ,YAAY;QAChDb,MACE,oGACAe;QAGF,qCAAqC;QACrC,MAAMG,mBAAE,CAACC,KAAK,CAACJ,gBAAgB;YAAEK,WAAW;QAAK;QAEjD,MAAMX,QAAQC,GAAG,CAAC;YAChBW,IAAAA,4CAAuB,EAACL,eAAI,CAACM,IAAI,CAACnB,aAAa,eAAe;YAC9DoB,IAAAA,wBAAe,EAACpB;YAChBqB,IAAAA,6BAAmB,EAACrB;YACpBsB,IAAAA,wBAAgB,EAAC;gBACfvB;gBACAE;gBACAW;gBACAZ;gBACAuB,iBAAiBV,eAAI,CAACM,IAAI,CACxBnB,aACAwB,IAAAA,8CAAsC,EAACxB,aAAaE;gBAEtDuB,MAAM,EAAEvB,wBAAAA,aAAAA,IAAKwB,KAAK,qBAAVxB,WAAYyB,MAAM;YAC5B;SACD;IACH;AACF"}