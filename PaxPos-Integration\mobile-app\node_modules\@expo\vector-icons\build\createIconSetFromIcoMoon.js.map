{"version": 3, "file": "createIconSetFromIcoMoon.js", "sourceRoot": "", "sources": ["../src/createIconSetFromIcoMoon.ts"], "names": [], "mappings": "AAAA,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAE5C,MAAM,CAAC,OAAO,WAAW,MAAM,EAAE,YAAY,EAAE,WAAW;IACxD,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACtD,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,YAAY,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;IAEnF,OAAO,aAAa,CAAiB,QAAQ,EAAE,UAAU,EAAE,WAAW,IAAI,GAAG,UAAU,MAAM,CAAC,CAAC;AACjG,CAAC", "sourcesContent": ["import createIconSet from './createIconSet';\n\nexport default function (config, expoFontName, expoAssetId) {\n  const glyphMap = {};\n  config.icons.forEach((icon) => {\n    icon.properties.name.split(/\\s*,\\s*/g).forEach((name) => {\n      glyphMap[name] = icon.properties.code;\n    });\n  });\n\n  const fontFamily = expoFontName || config.preferences.fontPref.metadata.fontFamily;\n\n  return createIconSet<string, string>(glyphMap, fontFamily, expoAssetId || `${fontFamily}.ttf`);\n}\n"]}