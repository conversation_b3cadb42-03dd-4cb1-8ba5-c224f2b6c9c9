/**
 * React Hook for WebSocket Management
 * 
 * Provides easy-to-use WebSocket functionality for React components
 * Handles connection management, event listening, and cleanup
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { websocketService, PaymentEventData, TransactionUpdateData, ReceiptEventData, VivaWebhookEventData } from '../services/websocketService';

export interface UseWebSocketOptions {
  autoConnect?: boolean;
  onPaymentEvent?: (data: PaymentEventData) => void;
  onTransactionUpdate?: (data: TransactionUpdateData) => void;
  onReceiptEvent?: (data: ReceiptEventData) => void;
  onVivaWebhookEvent?: (data: VivaWebhookEventData) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => void;
  send: (type: string, data: any) => void;
  ping: () => void;
  subscribe: (events: string[]) => void;
  unsubscribe: (events: string[]) => void;
}

export function useWebSocket(options: UseWebSocketOptions = {}): UseWebSocketReturn {
  const {
    autoConnect = true,
    onPaymentEvent,
    onTransactionUpdate,
    onReceiptEvent,
    onVivaWebhookEvent,
    onConnect,
    onDisconnect,
    onError
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const handlersRef = useRef<Map<string, Function>>(new Map());

  // Update connection status
  const updateConnectionStatus = useCallback(() => {
    const connected = websocketService.isConnected();
    setIsConnected(connected);
    
    if (connected && onConnect) {
      onConnect();
    } else if (!connected && onDisconnect) {
      onDisconnect();
    }
  }, [onConnect, onDisconnect]);

  // Connect to WebSocket
  const connect = useCallback(async () => {
    try {
      await websocketService.connect();
      updateConnectionStatus();
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      if (onError) {
        onError(error);
      }
    }
  }, [updateConnectionStatus, onError]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    websocketService.disconnect();
    updateConnectionStatus();
  }, [updateConnectionStatus]);

  // Send message
  const send = useCallback((type: string, data: any) => {
    websocketService.send({
      type,
      data,
      timestamp: new Date().toISOString()
    });
  }, []);

  // Ping server
  const ping = useCallback(() => {
    websocketService.ping();
  }, []);

  // Subscribe to events
  const subscribe = useCallback((events: string[]) => {
    websocketService.subscribe(events);
  }, []);

  // Unsubscribe from events
  const unsubscribe = useCallback((events: string[]) => {
    websocketService.unsubscribe(events);
  }, []);

  // Set up event handlers
  useEffect(() => {
    const handlers = new Map<string, Function>();

    // Payment event handler
    if (onPaymentEvent) {
      const handler = (data: PaymentEventData) => onPaymentEvent(data);
      websocketService.on('payment_event', handler);
      handlers.set('payment_event', handler);
    }

    // Transaction update handler
    if (onTransactionUpdate) {
      const handler = (data: TransactionUpdateData) => onTransactionUpdate(data);
      websocketService.on('transaction_update', handler);
      handlers.set('transaction_update', handler);
    }

    // Receipt event handler
    if (onReceiptEvent) {
      const handler = (data: ReceiptEventData) => onReceiptEvent(data);
      websocketService.on('receipt_event', handler);
      handlers.set('receipt_event', handler);
    }

    // Viva webhook event handler
    if (onVivaWebhookEvent) {
      const handler = (data: VivaWebhookEventData) => onVivaWebhookEvent(data);
      websocketService.on('viva_webhook_event', handler);
      handlers.set('viva_webhook_event', handler);
    }

    handlersRef.current = handlers;

    // Cleanup function
    return () => {
      handlers.forEach((handler, eventType) => {
        websocketService.off(eventType, handler as any);
      });
    };
  }, [onPaymentEvent, onTransactionUpdate, onReceiptEvent, onVivaWebhookEvent]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Cleanup on unmount
    return () => {
      if (autoConnect) {
        disconnect();
      }
    };
  }, [autoConnect, connect, disconnect]);

  // Monitor connection status
  useEffect(() => {
    const interval = setInterval(updateConnectionStatus, 1000);
    return () => clearInterval(interval);
  }, [updateConnectionStatus]);

  return {
    isConnected,
    connect,
    disconnect,
    send,
    ping,
    subscribe,
    unsubscribe
  };
}

/**
 * Hook specifically for Viva payment events
 */
export function useVivaWebhookEvents(
  onVivaEvent?: (data: VivaWebhookEventData) => void,
  onTransactionUpdate?: (data: TransactionUpdateData) => void,
  onReceiptGenerated?: (data: ReceiptEventData) => void
) {
  return useWebSocket({
    onVivaWebhookEvent: onVivaEvent,
    onTransactionUpdate,
    onReceiptEvent: onReceiptGenerated,
    autoConnect: true
  });
}

/**
 * Hook for payment-related events
 */
export function usePaymentEvents(
  onPaymentEvent?: (data: PaymentEventData) => void,
  onTransactionUpdate?: (data: TransactionUpdateData) => void
) {
  return useWebSocket({
    onPaymentEvent,
    onTransactionUpdate,
    autoConnect: true
  });
}

/**
 * Hook for receipt events
 */
export function useReceiptEvents(
  onReceiptEvent?: (data: ReceiptEventData) => void
) {
  return useWebSocket({
    onReceiptEvent,
    autoConnect: true
  });
}
