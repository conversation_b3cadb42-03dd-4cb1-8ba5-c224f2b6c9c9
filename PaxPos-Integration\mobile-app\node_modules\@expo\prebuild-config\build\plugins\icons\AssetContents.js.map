{"version": 3, "file": "AssetContents.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "e", "__esModule", "default", "createContentsJsonItem", "item", "writeContentsJsonAsync", "directory", "images", "fs", "promises", "mkdir", "recursive", "writeFile", "join", "JSON", "stringify", "info", "version", "author"], "sources": ["../../../src/plugins/icons/AssetContents.ts"], "sourcesContent": ["import fs from 'fs';\nimport { join } from 'path';\n\nexport type ContentsJsonImageIdiom =\n  | 'iphone'\n  | 'ipad'\n  | 'watchos'\n  | 'ios'\n  | 'ios-marketing'\n  | 'universal';\n\nexport type ContentsJsonImageAppearanceLuminosityType = 'light' | 'dark' | 'tinted';\n\nexport type ContentsJsonAppearance = {\n  appearance: 'luminosity';\n  value: ContentsJsonImageAppearanceLuminosityType;\n};\n\nexport type ContentsJsonImageScale = '1x' | '2x' | '3x';\n\nexport interface ContentsJsonImage {\n  appearances?: ContentsJsonAppearance[];\n  idiom: ContentsJsonImageIdiom;\n  size?: string;\n  scale?: ContentsJsonImageScale;\n  filename?: string;\n  platform?: ContentsJsonImageIdiom;\n}\n\nexport interface ContentsJsonColor {\n  appearances?: ContentsJsonAppearance[];\n  idiom: ContentsJsonImageIdiom;\n  color: {\n    'color-space': 'srgb';\n    components: {\n      alpha: string;\n      blue: string;\n      green: string;\n      red: string;\n    };\n  };\n}\n\nexport interface ContentsJson {\n  images: ContentsJsonImage[];\n  colors: ContentsJsonColor[];\n  info: {\n    version: number;\n    author: string;\n  };\n}\n\nexport function createContentsJsonItem(item: ContentsJsonImage): ContentsJsonImage {\n  return item;\n}\n\n/**\n * Writes the Config.json which is used to assign images to their respective platform, dpi, and idiom.\n *\n * @param directory path to add the Contents.json to.\n * @param contents image json data\n */\nexport async function writeContentsJsonAsync(\n  directory: string,\n  { images }: Pick<ContentsJson, 'images'>\n): Promise<void> {\n  await fs.promises.mkdir(directory, { recursive: true });\n  await fs.promises.writeFile(\n    join(directory, 'Contents.json'),\n    JSON.stringify(\n      {\n        images,\n        info: {\n          version: 1,\n          // common practice is for the tool that generated the icons to be the \"author\"\n          author: 'expo',\n        },\n      },\n      null,\n      2\n    )\n  );\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAC,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAmDrB,SAASG,sBAAsBA,CAACC,IAAuB,EAAqB;EACjF,OAAOA,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,eAAeC,sBAAsBA,CAC1CC,SAAiB,EACjB;EAAEC;AAAqC,CAAC,EACzB;EACf,MAAMC,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACJ,SAAS,EAAE;IAAEK,SAAS,EAAE;EAAK,CAAC,CAAC;EACvD,MAAMH,aAAE,CAACC,QAAQ,CAACG,SAAS,CACzB,IAAAC,YAAI,EAACP,SAAS,EAAE,eAAe,CAAC,EAChCQ,IAAI,CAACC,SAAS,CACZ;IACER,MAAM;IACNS,IAAI,EAAE;MACJC,OAAO,EAAE,CAAC;MACV;MACAC,MAAM,EAAE;IACV;EACF,CAAC,EACD,IAAI,EACJ,CACF,CACF,CAAC;AACH", "ignoreList": []}