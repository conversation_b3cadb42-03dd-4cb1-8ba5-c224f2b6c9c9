{"version": 3, "sources": ["../../../src/utils/filePath.ts"], "sourcesContent": ["import fs from 'fs';\n\nconst REGEXP_REPLACE_SLASHES = /\\\\/g;\n\n/**\n * This is a workaround for Metro not resolving entry file paths to their real location.\n * When running exports through `eas build --local` on macOS, the `/var/folders` path is used instead of `/private/var/folders`.\n *\n * See: https://github.com/expo/expo/issues/28890\n */\nexport function resolveRealEntryFilePath(projectRoot: string, entryFile: string): string {\n  if (projectRoot.startsWith('/private/var') && entryFile.startsWith('/var')) {\n    return fs.realpathSync(entryFile);\n  }\n\n  return entryFile;\n}\n\n/**\n * Convert any platform-specific path to a POSIX path.\n */\nexport function toPosixPath(filePath: string): string {\n  return filePath.replace(REGEXP_REPLACE_SLASHES, '/');\n}\n"], "names": ["resolveRealEntryFilePath", "toPosixPath", "REGEXP_REPLACE_SLASHES", "projectRoot", "entryFile", "startsWith", "fs", "realpathSync", "filePath", "replace"], "mappings": ";;;;;;;;;;;IAUgBA,wBAAwB;eAAxBA;;IAWAC,WAAW;eAAXA;;;;gEArBD;;;;;;;;;;;AAEf,MAAMC,yBAAyB;AAQxB,SAASF,yBAAyBG,WAAmB,EAAEC,SAAiB;IAC7E,IAAID,YAAYE,UAAU,CAAC,mBAAmBD,UAAUC,UAAU,CAAC,SAAS;QAC1E,OAAOC,aAAE,CAACC,YAAY,CAACH;IACzB;IAEA,OAAOA;AACT;AAKO,SAASH,YAAYO,QAAgB;IAC1C,OAAOA,SAASC,OAAO,CAACP,wBAAwB;AAClD"}