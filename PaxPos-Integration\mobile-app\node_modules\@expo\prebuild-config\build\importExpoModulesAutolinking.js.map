{"version": 3, "file": "importExpoModulesAutolinking.js", "names": ["_resolveFrom", "data", "_interopRequireDefault", "require", "e", "__esModule", "default", "importExpoModulesAutolinking", "projectRoot", "autolinking", "tryRequireExpoModulesAutolinking", "assertAutolinkingCompatibility", "expoPackageRoot", "resolveFrom", "silent", "autolinkingExportsPath", "Error"], "sources": ["../src/importExpoModulesAutolinking.ts"], "sourcesContent": ["import resolveFrom from 'resolve-from';\n\nexport type SearchResults = {\n  [moduleName: string]: object;\n};\n\nexport type SearchOptions = {\n  searchPaths: string[];\n  platform: 'ios' | 'android' | 'web';\n  silent?: boolean;\n};\n\ntype AutolinkingModule = typeof import('expo-modules-autolinking/exports');\n\n/**\n * Imports the `expo-modules-autolinking` package installed in the project at the given path.\n */\nexport function importExpoModulesAutolinking(projectRoot: string): AutolinkingModule {\n  const autolinking = tryRequireExpoModulesAutolinking(projectRoot);\n  assertAutolinkingCompatibility(autolinking);\n  return autolinking;\n}\n\nfunction tryRequireExpoModulesAutolinking(projectRoot: string): AutolinkingModule {\n  const expoPackageRoot = resolveFrom.silent(projectRoot, 'expo/package.json');\n  const autolinkingExportsPath = resolveFrom.silent(\n    expoPackageRoot ?? projectRoot,\n    'expo-modules-autolinking/exports'\n  );\n  if (!autolinkingExportsPath) {\n    throw new Error(\n      \"Cannot find 'expo-modules-autolinking' package in your project, make sure that you have 'expo' package installed\"\n    );\n  }\n  return require(autolinkingExportsPath);\n}\n\nfunction assertAutolinkingCompatibility(autolinking: AutolinkingModule): void {\n  if ('resolveSearchPathsAsync' in autolinking && 'findModulesAsync' in autolinking) {\n    return;\n  }\n  throw new Error(\n    \"The 'expo-modules-autolinking' package has been found, but it seems to be incompatible with '@expo/prebuild-config'\"\n  );\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,aAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,YAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAC,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAcvC;AACA;AACA;AACO,SAASG,4BAA4BA,CAACC,WAAmB,EAAqB;EACnF,MAAMC,WAAW,GAAGC,gCAAgC,CAACF,WAAW,CAAC;EACjEG,8BAA8B,CAACF,WAAW,CAAC;EAC3C,OAAOA,WAAW;AACpB;AAEA,SAASC,gCAAgCA,CAACF,WAAmB,EAAqB;EAChF,MAAMI,eAAe,GAAGC,sBAAW,CAACC,MAAM,CAACN,WAAW,EAAE,mBAAmB,CAAC;EAC5E,MAAMO,sBAAsB,GAAGF,sBAAW,CAACC,MAAM,CAC/CF,eAAe,IAAIJ,WAAW,EAC9B,kCACF,CAAC;EACD,IAAI,CAACO,sBAAsB,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,kHACF,CAAC;EACH;EACA,OAAOb,OAAO,CAACY,sBAAsB,CAAC;AACxC;AAEA,SAASJ,8BAA8BA,CAACF,WAA8B,EAAQ;EAC5E,IAAI,yBAAyB,IAAIA,WAAW,IAAI,kBAAkB,IAAIA,WAAW,EAAE;IACjF;EACF;EACA,MAAM,IAAIO,KAAK,CACb,qHACF,CAAC;AACH", "ignoreList": []}