/* PAX A920 POS System Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.pos-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.pos-header {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left h1 {
    color: #2c3e50;
    margin-bottom: 8px;
}

.header-left h1 i {
    color: #3498db;
    margin-right: 10px;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.connection-status.connected i {
    color: #27ae60;
}

.connection-status.disconnected i {
    color: #e74c3c;
}

.connection-status.checking i {
    color: #f39c12;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.header-right {
    display: flex;
    gap: 10px;
}

/* Button Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-large {
    padding: 15px 25px;
    font-size: 16px;
    width: 100%;
    margin-bottom: 10px;
}

/* Main Layout */
.pos-main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.left-panel, .right-panel {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Product Entry */
.product-entry {
    margin-bottom: 30px;
}

.product-entry h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.input-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #3498db;
}

/* Shopping Cart */
.shopping-cart h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

.cart-items {
    min-height: 200px;
    margin-bottom: 20px;
}

.empty-cart {
    text-align: center;
    color: #95a5a6;
    padding: 40px 20px;
}

.empty-cart i {
    font-size: 48px;
    margin-bottom: 10px;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #ecf0f1;
    border-radius: 8px;
    margin-bottom: 10px;
    background: #f8f9fa;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 500;
    color: #2c3e50;
}

.cart-item-details {
    font-size: 12px;
    color: #7f8c8d;
}

.cart-item-price {
    font-weight: 600;
    color: #27ae60;
}

.cart-item-remove {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
}

.cart-total {
    border-top: 2px solid #ecf0f1;
    padding-top: 15px;
}

.total-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.total-final {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    border-top: 1px solid #ecf0f1;
    padding-top: 8px;
    margin-top: 8px;
}

/* Payment Section */
.payment-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

.amount-display {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.amount-label {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 5px;
}

.amount-value {
    font-size: 36px;
    font-weight: 700;
}

.payment-types h4 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.payment-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 30px;
}

.payment-btn {
    padding: 15px;
    border: 2px solid #ecf0f1;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.payment-btn:hover {
    border-color: #3498db;
    background: #f8f9fa;
}

.payment-btn.active {
    border-color: #3498db;
    background: #3498db;
    color: white;
}

.payment-btn i {
    display: block;
    font-size: 24px;
    margin-bottom: 8px;
}

/* Transaction Status */
.transaction-status {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.transaction-status h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.status-content {
    font-size: 14px;
    color: #555;
}

/* Transaction History */
.transaction-history {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.transaction-history h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

.no-transactions {
    text-align: center;
    color: #95a5a6;
    padding: 40px 20px;
}

.no-transactions i {
    font-size: 48px;
    margin-bottom: 10px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1001;
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toast.success .toast-icon {
    color: #27ae60;
}

.toast.error .toast-icon {
    color: #e74c3c;
}

.toast.warning .toast-icon {
    color: #f39c12;
}

.toast.info .toast-icon {
    color: #3498db;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pos-main {
        grid-template-columns: 1fr;
    }
    
    .payment-buttons {
        grid-template-columns: 1fr;
    }
    
    .header-right {
        flex-direction: column;
    }
}
