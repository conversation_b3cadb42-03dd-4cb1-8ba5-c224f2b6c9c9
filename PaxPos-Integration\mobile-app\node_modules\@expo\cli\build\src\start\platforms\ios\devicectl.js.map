{"version": 3, "sources": ["../../../../../src/start/platforms/ios/devicectl.ts"], "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport JsonFile from '@expo/json-file';\nimport spawnAsync, { SpawnOptions, SpawnResult } from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport { spawn, execSync } from 'child_process';\nimport fs from 'fs';\nimport assert from 'node:assert';\nimport { Ora } from 'ora';\nimport { EOL } from 'os';\nimport path from 'path';\n\nimport { xcrunAsync } from './xcrun';\nimport { getExpoHomeDirectory } from '../../../api/user/UserSettings';\nimport * as Log from '../../../log';\nimport { createTempFilePath } from '../../../utils/createTempPath';\nimport { CommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\nimport { isInteractive } from '../../../utils/interactive';\nimport { ora } from '../../../utils/ora';\nimport { confirmAsync } from '../../../utils/prompts';\n\nconst DEVICE_CTL_EXISTS_PATH = path.join(getExpoHomeDirectory(), 'devicectl-exists');\n\nconst debug = require('debug')('expo:devicectl') as typeof console.log;\n\ntype AnyEnum<T extends string = string> = T | (string & object);\n\ntype DeviceCtlDevice = {\n  capabilities: DeviceCtlDeviceCapability[];\n  connectionProperties: DeviceCtlConnectionProperties;\n  deviceProperties: DeviceCtlDeviceProperties;\n  hardwareProperties: DeviceCtlHardwareProperties;\n  /** \"A1A1AAA1-0011-1AA1-11A1-10A1111AA11A\" */\n  identifier: string;\n  visibilityClass: AnyEnum<'default'>;\n};\n\ntype DeviceCtlHardwareProperties = {\n  cpuType: DeviceCtlCpuType;\n  deviceType: AnyEnum<'iPhone'>;\n  /** 1114404411111111 */\n  ecid: number;\n  /** \"D74AP\" */\n  hardwareModel: string;\n  /** 512000000000 */\n  internalStorageCapacity: number;\n  /** true */\n  isProductionFused: boolean;\n  /** \"iPhone 14 Pro Max\" */\n  marketingName: string;\n  /** \"iOS\" */\n  platform: AnyEnum<'iOS' | 'xrOS'>;\n  /** \"iPhone15,3\" */\n  productType: AnyEnum<'iPhone13,4' | 'iPhone15,3'>;\n  reality: AnyEnum<'physical'>;\n  /** \"X2X1CC1XXX\" */\n  serialNumber: string;\n  supportedCPUTypes: DeviceCtlCpuType[];\n  /** [1] */\n  supportedDeviceFamilies: number[];\n  thinningProductType: AnyEnum<'iPhone15,3'>;\n  /** \"00001110-001111110110101A\" */\n  udid: string;\n};\n\ntype DeviceCtlDeviceProperties = {\n  /** true */\n  bootedFromSnapshot: boolean;\n  /** \"com.apple.os.update-AD0CF111ACFF11A11111A76A3D1262AE42A3F56F305AF5AE1135393A7A14A7D1\" */\n  bootedSnapshotName: string;\n  /** false */\n  ddiServicesAvailable: boolean;\n\n  developerModeStatus: AnyEnum<'enabled'>;\n  /** false */\n  hasInternalOSBuild: boolean;\n  /** \"Evan's phone\" */\n  name: string;\n  /** \"21E236\" */\n  osBuildUpdate: string;\n  /** \"17.4.1\" */\n  osVersionNumber: string;\n  /** false */\n  rootFileSystemIsWritable: boolean;\n};\n\ntype DeviceCtlDeviceCapability =\n  | {\n      name: AnyEnum;\n      featureIdentifier: AnyEnum;\n    }\n  | {\n      featureIdentifier: 'com.apple.coredevice.feature.connectdevice';\n      name: 'Connect to Device';\n    }\n  | {\n      featureIdentifier: 'com.apple.coredevice.feature.unpairdevice';\n      name: 'Unpair Device';\n    }\n  | {\n      featureIdentifier: 'com.apple.coredevice.feature.acquireusageassertion';\n      name: 'Acquire Usage Assertion';\n    };\n\ntype DeviceCtlConnectionProperties = {\n  authenticationType: AnyEnum<'manualPairing'>;\n  isMobileDeviceOnly: boolean;\n  /** \"2024-04-20T22:50:04.244Z\" */\n  lastConnectionDate: string;\n  pairingState: AnyEnum<'paired'>;\n  /** [\"00001111-001111110110101A.coredevice.local\", \"A1A1AAA1-0011-1AA1-11A1-10A1111AA11A.coredevice.local\"] */\n  potentialHostnames: string[];\n  transportType: AnyEnum<'localNetwork' | 'wired'>;\n  tunnelState: AnyEnum<'disconnected' | 'unavailable'>;\n  tunnelTransportProtocol: AnyEnum<'tcp'>;\n};\n\ntype DeviceCtlCpuType = {\n  name: AnyEnum<'arm64e' | 'arm64' | 'arm64_32'>;\n  subType: number;\n  /** 16777228 */\n  type: number;\n};\n\n/** Run a `devicectl` command. */\nexport async function devicectlAsync(\n  args: (string | undefined)[],\n  options?: SpawnOptions\n): Promise<SpawnResult> {\n  try {\n    return await xcrunAsync(['devicectl', ...args], options);\n  } catch (error: any) {\n    if (error instanceof CommandError) {\n      throw error;\n    }\n    if ('stderr' in error) {\n      const errorCodes = getDeviceCtlErrorCodes(error.stderr);\n      if (errorCodes.includes('Locked')) {\n        throw new CommandError('APPLE_DEVICE_LOCKED', 'Device is locked, unlock and try again.');\n      }\n    }\n    throw error;\n  }\n}\n\nexport async function getConnectedAppleDevicesAsync() {\n  if (!hasDevicectlEverBeenInstalled()) {\n    debug('devicectl not found, skipping remote Apple devices.');\n    return [];\n  }\n\n  const tmpPath = createTempFilePath();\n  const devices = await devicectlAsync([\n    'list',\n    'devices',\n    '--json-output',\n    tmpPath,\n    // Give two seconds before timing out: between 5 and 9223372036854775807\n    '--timeout',\n    '5',\n  ]);\n  debug(devices.stdout);\n  const devicesJson = await JsonFile.readAsync(tmpPath);\n\n  if ((devicesJson as any)?.info?.jsonVersion !== 2) {\n    Log.warn(\n      'Unexpected devicectl JSON version output from devicectl. Connecting to physical Apple devices may not work as expected.'\n    );\n  }\n\n  assertDevicesJson(devicesJson);\n\n  return devicesJson.result.devices as DeviceCtlDevice[];\n}\n\nfunction assertDevicesJson(\n  results: any\n): asserts results is { result: { devices: DeviceCtlDevice[] } } {\n  assert(\n    results != null && 'result' in results && Array.isArray(results?.result?.devices),\n    'Malformed JSON output from devicectl: ' + JSON.stringify(results, null, 2)\n  );\n}\n\nexport async function launchBinaryOnMacAsync(\n  bundleId: string,\n  appBinaryPath: string\n): Promise<void> {\n  const args = ['-b', bundleId, appBinaryPath];\n  try {\n    await spawnAsync('open', args);\n  } catch (error: any) {\n    if ('code' in error) {\n      if (error.code === 1) {\n        throw new CommandError(\n          'MACOS_LAUNCH',\n          'Failed to launch the compatible binary on macOS: open ' +\n            args.join(' ') +\n            '\\n\\n' +\n            error.message\n        );\n      }\n    }\n    throw error;\n  }\n}\n\nasync function installAppWithDeviceCtlAsync(\n  uuid: string,\n  bundleIdOrAppPath: string,\n  onProgress: (event: { status: string; isComplete: boolean; progress: number }) => void\n): Promise<void> {\n  // 𝝠 xcrun devicectl device install app --device 00001110-001111110110101A /Users/<USER>/Library/Developer/Xcode/DerivedData/Router-hgbqaxzhrhkiftfweydvhgttadvn/Build/Products/Debug-iphoneos/Router.app --verbose\n  return new Promise((resolve, reject) => {\n    const args: string[] = [\n      'devicectl',\n      'device',\n      'install',\n      'app',\n      '--device',\n      uuid,\n      bundleIdOrAppPath,\n    ];\n    const childProcess = spawn('xcrun', args);\n    debug('xcrun ' + args.join(' '));\n\n    let currentProgress = 0;\n    let hasStarted = false;\n\n    function updateProgress(progress: number) {\n      hasStarted = true;\n      if (progress <= currentProgress) {\n        return;\n      }\n      currentProgress = progress;\n      onProgress({\n        progress,\n        isComplete: progress === 100,\n        status: 'Installing',\n      });\n    }\n\n    childProcess.stdout.on('data', (data: Buffer) => {\n      // Sometimes more than one chunk comes at a time, here we split by system newline,\n      // then trim and filter.\n      const strings = data\n        .toString()\n        .split(EOL)\n        .map((value) => value.trim());\n\n      strings.forEach((str) => {\n        // Match the progress percentage:\n        // - '34%... 35%...' -> 34\n        // - '31%...' -> 31\n        // - 'Complete!' -> 100\n\n        const match = str.match(/(\\d+)%\\.\\.\\./);\n        if (match) {\n          updateProgress(parseInt(match[1], 10));\n        } else if (hasStarted) {\n          updateProgress(100);\n        }\n      });\n\n      debug('[stdout]:', strings);\n    });\n\n    childProcess.on('close', (code) => {\n      debug('[close]: ' + code);\n      if (code === 0) {\n        resolve();\n      } else {\n        const stderr = childProcess.stderr.read();\n        const err = new Error(stderr);\n        (err as any).code = code;\n        detach(err);\n      }\n    });\n\n    const detach = async (err?: Error) => {\n      off?.();\n      if (childProcess) {\n        return new Promise<void>((resolve) => {\n          childProcess?.on('close', resolve);\n          childProcess?.kill();\n          // childProcess = null;\n          reject(err ?? new CommandError('detached'));\n        });\n      }\n    };\n\n    const off = installExitHooks(() => detach());\n  });\n}\n\nexport async function launchAppWithDeviceCtl(deviceId: string, bundleId: string) {\n  await devicectlAsync(['device', 'process', 'launch', '--device', deviceId, bundleId]);\n}\n\n/** Find all error codes from the output log */\nfunction getDeviceCtlErrorCodes(log: string): string[] {\n  return [...log.matchAll(/BSErrorCodeDescription\\s+=\\s+(.*)$/gim)].map(([_line, code]) => code);\n}\n\nlet hasEverBeenInstalled: boolean | undefined;\n\nexport function hasDevicectlEverBeenInstalled() {\n  if (hasEverBeenInstalled) return hasEverBeenInstalled;\n  // It doesn't appear possible for devicectl to ever be uninstalled. We can just check once and store this result forever\n  // to prevent cold boots of devicectl from slowing down all invocations of `expo run ios`\n  if (fs.existsSync(DEVICE_CTL_EXISTS_PATH)) {\n    hasEverBeenInstalled = true;\n    return true;\n  }\n\n  const isInstalled = isDevicectlInstalled();\n\n  if (isInstalled) {\n    fs.writeFileSync(DEVICE_CTL_EXISTS_PATH, '1');\n  }\n  hasEverBeenInstalled = isInstalled;\n  return isInstalled;\n}\n\nfunction isDevicectlInstalled() {\n  try {\n    execSync('xcrun devicectl --version', { stdio: 'ignore' });\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Wraps the apple device method for installing and running an app,\n * adds indicator and retry loop for when the device is locked.\n */\nexport async function installAndLaunchAppAsync(props: {\n  bundle: string;\n  bundleIdentifier: string;\n  udid: string;\n  deviceName: string;\n}): Promise<void> {\n  debug('Running on device:', props);\n  const { bundle, bundleIdentifier, udid, deviceName } = props;\n  let indicator: Ora | undefined;\n\n  try {\n    if (!indicator) {\n      indicator = ora(`Connecting to: ${props.deviceName}`).start();\n    }\n\n    await installAppWithDeviceCtlAsync(\n      udid,\n      bundle,\n      ({\n        status,\n        isComplete,\n        progress,\n      }: {\n        status: string;\n        isComplete: boolean;\n        progress: number;\n      }) => {\n        if (!indicator) {\n          indicator = ora(status).start();\n        }\n        indicator.text = `${chalk.bold(status)} ${progress}%`;\n        if (isComplete) {\n          indicator.succeed();\n        }\n      }\n    );\n  } catch (error: any) {\n    if (indicator) {\n      indicator.fail();\n    }\n    throw error;\n  }\n\n  async function launchAppOptionally() {\n    try {\n      await launchAppWithDeviceCtl(udid, bundleIdentifier);\n    } catch (error: any) {\n      if (indicator) {\n        indicator.fail();\n      }\n      if (error.code === 'APPLE_DEVICE_LOCKED') {\n        // Get the app name from the binary path.\n        const appName = path.basename(bundle).split('.')[0] ?? 'app';\n        if (\n          isInteractive() &&\n          (await confirmAsync({\n            message: `Cannot launch ${appName} because the device is locked. Unlock ${deviceName} to continue...`,\n            initial: true,\n          }))\n        ) {\n          return launchAppOptionally();\n        }\n        throw new CommandError(\n          `Cannot launch ${appName} on ${deviceName} because the device is locked.`\n        );\n      }\n      throw error;\n    }\n  }\n\n  await launchAppOptionally();\n}\n"], "names": ["devicectlAsync", "getConnectedAppleDevicesAsync", "hasDevicectlEverBeenInstalled", "installAndLaunchAppAsync", "launchAppWithDeviceCtl", "launchBinaryOnMacAsync", "DEVICE_CTL_EXISTS_PATH", "path", "join", "getExpoHomeDirectory", "debug", "require", "args", "options", "xcrunAsync", "error", "CommandError", "errorCodes", "getDeviceCtlErrorCodes", "stderr", "includes", "tmpPath", "createTempFilePath", "devices", "stdout", "devicesJson", "JsonFile", "readAsync", "info", "jsonVersion", "Log", "warn", "assertDevicesJson", "result", "results", "assert", "Array", "isArray", "JSON", "stringify", "bundleId", "appBinaryPath", "spawnAsync", "code", "message", "installAppWithDeviceCtlAsync", "uuid", "bundleIdOrAppPath", "onProgress", "Promise", "resolve", "reject", "childProcess", "spawn", "currentProgress", "hasStarted", "updateProgress", "progress", "isComplete", "status", "on", "data", "strings", "toString", "split", "EOL", "map", "value", "trim", "for<PERSON>ach", "str", "match", "parseInt", "read", "err", "Error", "detach", "off", "kill", "installExitHooks", "deviceId", "log", "matchAll", "_line", "hasEverBeenInstalled", "fs", "existsSync", "isInstalled", "isDevicectlInstalled", "writeFileSync", "execSync", "stdio", "props", "bundle", "bundleIdentifier", "udid", "deviceName", "indicator", "ora", "start", "text", "chalk", "bold", "succeed", "fail", "launchAppOptionally", "appName", "basename", "isInteractive", "<PERSON><PERSON><PERSON>", "initial"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IA8HqBA,cAAc;eAAdA;;IAoBAC,6BAA6B;eAA7BA;;IAiKNC,6BAA6B;eAA7BA;;IA+BMC,wBAAwB;eAAxBA;;IA1CAC,sBAAsB;eAAtBA;;IA/GAC,sBAAsB;eAAtBA;;;;gEAvLD;;;;;;;gEACiC;;;;;;;gEACpC;;;;;;;yBACc;;;;;;;gEACjB;;;;;;;gEACI;;;;;;;yBAEC;;;;;;;gEACH;;;;;;uBAEU;8BACU;6DAChB;gCACc;wBACN;sBACI;6BACH;qBACV;yBACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,yBAAyBC,eAAI,CAACC,IAAI,CAACC,IAAAA,kCAAoB,KAAI;AAEjE,MAAMC,QAAQC,QAAQ,SAAS;AAsGxB,eAAeX,eACpBY,IAA4B,EAC5BC,OAAsB;IAEtB,IAAI;QACF,OAAO,MAAMC,IAAAA,iBAAU,EAAC;YAAC;eAAgBF;SAAK,EAAEC;IAClD,EAAE,OAAOE,OAAY;QACnB,IAAIA,iBAAiBC,oBAAY,EAAE;YACjC,MAAMD;QACR;QACA,IAAI,YAAYA,OAAO;YACrB,MAAME,aAAaC,uBAAuBH,MAAMI,MAAM;YACtD,IAAIF,WAAWG,QAAQ,CAAC,WAAW;gBACjC,MAAM,IAAIJ,oBAAY,CAAC,uBAAuB;YAChD;QACF;QACA,MAAMD;IACR;AACF;AAEO,eAAed;QAmBhB;IAlBJ,IAAI,CAACC,iCAAiC;QACpCQ,MAAM;QACN,OAAO,EAAE;IACX;IAEA,MAAMW,UAAUC,IAAAA,kCAAkB;IAClC,MAAMC,UAAU,MAAMvB,eAAe;QACnC;QACA;QACA;QACAqB;QACA,wEAAwE;QACxE;QACA;KACD;IACDX,MAAMa,QAAQC,MAAM;IACpB,MAAMC,cAAc,MAAMC,mBAAQ,CAACC,SAAS,CAACN;IAE7C,IAAI,CAACI,gCAAD,oBAAA,AAACA,YAAqBG,IAAI,qBAA1B,kBAA4BC,WAAW,MAAK,GAAG;QACjDC,KAAIC,IAAI,CACN;IAEJ;IAEAC,kBAAkBP;IAElB,OAAOA,YAAYQ,MAAM,CAACV,OAAO;AACnC;AAEA,SAASS,kBACPE,OAAY;QAG8CA;IAD1DC,IAAAA,qBAAM,EACJD,WAAW,QAAQ,YAAYA,WAAWE,MAAMC,OAAO,CAACH,4BAAAA,kBAAAA,QAASD,MAAM,qBAAfC,gBAAiBX,OAAO,GAChF,2CAA2Ce,KAAKC,SAAS,CAACL,SAAS,MAAM;AAE7E;AAEO,eAAe7B,uBACpBmC,QAAgB,EAChBC,aAAqB;IAErB,MAAM7B,OAAO;QAAC;QAAM4B;QAAUC;KAAc;IAC5C,IAAI;QACF,MAAMC,IAAAA,qBAAU,EAAC,QAAQ9B;IAC3B,EAAE,OAAOG,OAAY;QACnB,IAAI,UAAUA,OAAO;YACnB,IAAIA,MAAM4B,IAAI,KAAK,GAAG;gBACpB,MAAM,IAAI3B,oBAAY,CACpB,gBACA,2DACEJ,KAAKJ,IAAI,CAAC,OACV,SACAO,MAAM6B,OAAO;YAEnB;QACF;QACA,MAAM7B;IACR;AACF;AAEA,eAAe8B,6BACbC,IAAY,EACZC,iBAAyB,EACzBC,UAAsF;IAEtF,uNAAuN;IACvN,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3B,MAAMvC,OAAiB;YACrB;YACA;YACA;YACA;YACA;YACAkC;YACAC;SACD;QACD,MAAMK,eAAeC,IAAAA,sBAAK,EAAC,SAASzC;QACpCF,MAAM,WAAWE,KAAKJ,IAAI,CAAC;QAE3B,IAAI8C,kBAAkB;QACtB,IAAIC,aAAa;QAEjB,SAASC,eAAeC,QAAgB;YACtCF,aAAa;YACb,IAAIE,YAAYH,iBAAiB;gBAC/B;YACF;YACAA,kBAAkBG;YAClBT,WAAW;gBACTS;gBACAC,YAAYD,aAAa;gBACzBE,QAAQ;YACV;QACF;QAEAP,aAAa5B,MAAM,CAACoC,EAAE,CAAC,QAAQ,CAACC;YAC9B,kFAAkF;YAClF,wBAAwB;YACxB,MAAMC,UAAUD,KACbE,QAAQ,GACRC,KAAK,CAACC,SAAG,EACTC,GAAG,CAAC,CAACC,QAAUA,MAAMC,IAAI;YAE5BN,QAAQO,OAAO,CAAC,CAACC;gBACf,iCAAiC;gBACjC,0BAA0B;gBAC1B,mBAAmB;gBACnB,uBAAuB;gBAEvB,MAAMC,QAAQD,IAAIC,KAAK,CAAC;gBACxB,IAAIA,OAAO;oBACTf,eAAegB,SAASD,KAAK,CAAC,EAAE,EAAE;gBACpC,OAAO,IAAIhB,YAAY;oBACrBC,eAAe;gBACjB;YACF;YAEA9C,MAAM,aAAaoD;QACrB;QAEAV,aAAaQ,EAAE,CAAC,SAAS,CAACjB;YACxBjC,MAAM,cAAciC;YACpB,IAAIA,SAAS,GAAG;gBACdO;YACF,OAAO;gBACL,MAAM/B,SAASiC,aAAajC,MAAM,CAACsD,IAAI;gBACvC,MAAMC,MAAM,IAAIC,MAAMxD;gBACrBuD,IAAY/B,IAAI,GAAGA;gBACpBiC,OAAOF;YACT;QACF;QAEA,MAAME,SAAS,OAAOF;YACpBG,uBAAAA;YACA,IAAIzB,cAAc;gBAChB,OAAO,IAAIH,QAAc,CAACC;oBACxBE,gCAAAA,aAAcQ,EAAE,CAAC,SAASV;oBAC1BE,gCAAAA,aAAc0B,IAAI;oBAClB,uBAAuB;oBACvB3B,OAAOuB,OAAO,IAAI1D,oBAAY,CAAC;gBACjC;YACF;QACF;QAEA,MAAM6D,MAAME,IAAAA,sBAAgB,EAAC,IAAMH;IACrC;AACF;AAEO,eAAexE,uBAAuB4E,QAAgB,EAAExC,QAAgB;IAC7E,MAAMxC,eAAe;QAAC;QAAU;QAAW;QAAU;QAAYgF;QAAUxC;KAAS;AACtF;AAEA,6CAA6C,GAC7C,SAAStB,uBAAuB+D,GAAW;IACzC,OAAO;WAAIA,IAAIC,QAAQ,CAAC;KAAyC,CAAChB,GAAG,CAAC,CAAC,CAACiB,OAAOxC,KAAK,GAAKA;AAC3F;AAEA,IAAIyC;AAEG,SAASlF;IACd,IAAIkF,sBAAsB,OAAOA;IACjC,wHAAwH;IACxH,yFAAyF;IACzF,IAAIC,aAAE,CAACC,UAAU,CAAChF,yBAAyB;QACzC8E,uBAAuB;QACvB,OAAO;IACT;IAEA,MAAMG,cAAcC;IAEpB,IAAID,aAAa;QACfF,aAAE,CAACI,aAAa,CAACnF,wBAAwB;IAC3C;IACA8E,uBAAuBG;IACvB,OAAOA;AACT;AAEA,SAASC;IACP,IAAI;QACFE,IAAAA,yBAAQ,EAAC,6BAA6B;YAAEC,OAAO;QAAS;QACxD,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,eAAexF,yBAAyByF,KAK9C;IACClF,MAAM,sBAAsBkF;IAC5B,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,IAAI,EAAEC,UAAU,EAAE,GAAGJ;IACvD,IAAIK;IAEJ,IAAI;QACF,IAAI,CAACA,WAAW;YACdA,YAAYC,IAAAA,QAAG,EAAC,CAAC,eAAe,EAAEN,MAAMI,UAAU,EAAE,EAAEG,KAAK;QAC7D;QAEA,MAAMtD,6BACJkD,MACAF,QACA,CAAC,EACClC,MAAM,EACND,UAAU,EACVD,QAAQ,EAKT;YACC,IAAI,CAACwC,WAAW;gBACdA,YAAYC,IAAAA,QAAG,EAACvC,QAAQwC,KAAK;YAC/B;YACAF,UAAUG,IAAI,GAAG,GAAGC,gBAAK,CAACC,IAAI,CAAC3C,QAAQ,CAAC,EAAEF,SAAS,CAAC,CAAC;YACrD,IAAIC,YAAY;gBACduC,UAAUM,OAAO;YACnB;QACF;IAEJ,EAAE,OAAOxF,OAAY;QACnB,IAAIkF,WAAW;YACbA,UAAUO,IAAI;QAChB;QACA,MAAMzF;IACR;IAEA,eAAe0F;QACb,IAAI;YACF,MAAMrG,uBAAuB2F,MAAMD;QACrC,EAAE,OAAO/E,OAAY;YACnB,IAAIkF,WAAW;gBACbA,UAAUO,IAAI;YAChB;YACA,IAAIzF,MAAM4B,IAAI,KAAK,uBAAuB;gBACxC,yCAAyC;gBACzC,MAAM+D,UAAUnG,eAAI,CAACoG,QAAQ,CAACd,QAAQ7B,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBACvD,IACE4C,IAAAA,0BAAa,OACZ,MAAMC,IAAAA,qBAAY,EAAC;oBAClBjE,SAAS,CAAC,cAAc,EAAE8D,QAAQ,sCAAsC,EAAEV,WAAW,eAAe,CAAC;oBACrGc,SAAS;gBACX,IACA;oBACA,OAAOL;gBACT;gBACA,MAAM,IAAIzF,oBAAY,CACpB,CAAC,cAAc,EAAE0F,QAAQ,IAAI,EAAEV,WAAW,8BAA8B,CAAC;YAE7E;YACA,MAAMjF;QACR;IACF;IAEA,MAAM0F;AACR"}