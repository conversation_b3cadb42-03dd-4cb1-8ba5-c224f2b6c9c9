{"version": 3, "file": "ExpoFontUtils.web.js", "sourceRoot": "", "sources": ["../src/ExpoFontUtils.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIzF,MAAM,aAAc,SAAQ,YAAY;IACtC,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,OAA8B;QACrE,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;IACnE,CAAC;CACF;AAED,eAAe,iBAAiB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { NativeModule, registerWebModule, UnavailabilityError } from 'expo-modules-core';\n\nimport { RenderToImageOptions } from './FontUtils.types';\n\nclass ExpoFontUtils extends NativeModule {\n  async renderToImageAsync(glyphs: string, options?: RenderToImageOptions): Promise<string> {\n    throw new UnavailabilityError('expo-font', 'renderToImageAsync');\n  }\n}\n\nexport default registerWebModule(ExpoFontUtils, 'ExpoFontUtils');\n"]}