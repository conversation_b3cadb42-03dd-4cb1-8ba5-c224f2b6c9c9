{"version": 3, "sources": ["index.ts"], "names": ["useCallback", "useEffect", "useState", "Dimensions", "NativeEventEmitter", "NativeModules", "Platform", "useOnEvent", "useOnMount", "devicesWithDynamicIsland", "devicesWithNotch", "RNDeviceInfo", "getSupportedPlatformInfoAsync", "getSupportedPlatformInfoFunctions", "getSupportedPlatformInfoSync", "getUniqueId", "getUniqueIdSync", "memoKey", "supportedPlatforms", "getter", "syncGetter", "defaultValue", "uniqueId", "syncUniqueId", "OS", "getInstanceId", "getInstanceIdSync", "getSerialNumber", "getSerialNumberSync", "getAndroidId", "getAndroidIdSync", "getIpAddress", "getIpAddressSync", "isCameraPresent", "isCameraPresentSync", "getMacAddress", "getMacAddressSync", "getDeviceId", "deviceId", "getManufacturer", "getManufacturerSync", "Promise", "resolve", "getSystemManufacturer", "getSystemManufacturerSync", "getModel", "model", "get<PERSON>rand", "brand", "getSystemName", "select", "ios", "systemName", "android", "windows", "default", "getSystemVersion", "systemVersion", "getBuildId", "getBuildIdSync", "getApiLevel", "getApiLevelSync", "getBundleId", "bundleId", "getInstallerPackageName", "getInstallerPackageNameSync", "getApplicationName", "appName", "getBuildNumber", "buildNumber", "getVersion", "appVersion", "getReadableVersion", "getDeviceName", "getDeviceNameSync", "getUsed<PERSON><PERSON><PERSON>", "getUsedMemorySync", "getUserAgent", "getUserAgentSync", "getFontScale", "getFontScaleSync", "getBootloader", "getBootloaderSync", "getDevice", "getDeviceSync", "getDisplay", "getDisplaySync", "getFingerprint", "getFingerprintSync", "getHardware", "getHardwareSync", "getHost", "getHostSync", "getHostNames", "getHostNamesSync", "getProduct", "getProductSync", "getTags", "getTagsSync", "getType", "getTypeSync", "getBaseOs", "getBaseOsSync", "getPreviewSdkInt", "getPreviewSdkIntSync", "getSecurityPatch", "getSecurityPatchSync", "getCodename", "getCodenameSync", "getIncremental", "getIncrementalSync", "isEmulator", "isEmulatorSync", "isTablet", "isLowRamDevice", "isDisplayZoomed", "isPinOrFingerprintSet", "isPinOrFingerprintSetSync", "notch", "hasNotch", "undefined", "_brand", "_model", "findIndex", "item", "toLowerCase", "dynamicIsland", "hasDynamicIsland", "hasGms", "hasGmsSync", "hasHms", "hasHmsSync", "getFirstInstallTime", "getFirstInstallTimeSync", "getInstallReferrer", "getInstallReferrerSync", "getLastUpdateTime", "getLastUpdateTimeSync", "getPhoneNumber", "getPhoneNumberSync", "get<PERSON>arrier", "getCarrierSync", "getTotalMemory", "getTotalMemorySync", "getMaxMemory", "getMaxMemorySync", "getTotalDiskCapacity", "getTotalDiskCapacitySync", "getTotalDiskCapacityOld", "getTotalDiskCapacityOldSync", "getFreeDiskStorage", "getFreeDiskStorageSync", "getFreeDiskStorageOld", "getFreeDiskStorageOldSync", "getBatteryLevel", "getBatteryLevelSync", "getPowerState", "getPowerStateSync", "isBatteryCharging", "isBatteryChargingSync", "isLandscape", "isLandscapeSync", "height", "width", "get", "isAirplaneMode", "isAirplaneModeSync", "getDeviceType", "deviceType", "getDeviceTypeSync", "supportedAbis", "supportedAbisSync", "getSupportedAbis", "getSupportedAbisSync", "supported32BitAbis", "supported32BitAbisSync", "getSupported32BitAbis", "getSupported32BitAbisSync", "supported64BitAbis", "supported64BitAbisSync", "getSupported64BitAbis", "getSupported64BitAbisSync", "hasSystemFeature", "feature", "hasSystemFeatureSync", "isLowBatteryLevel", "level", "getSystemAvailableFeatures", "getSystemAvailableFeaturesSync", "isLocationEnabled", "isLocationEnabledSync", "isHeadphonesConnected", "isHeadphonesConnectedSync", "isWiredHeadphonesConnected", "isWiredHeadphonesConnectedSync", "isBluetoothHeadphonesConnected", "isBluetoothHeadphonesConnectedSync", "isMouseConnected", "isMouseConnectedSync", "isKeyboardConnected", "isKeyboardConnectedSync", "getSupportedMediaTypeList", "getSupportedMediaTypeListSync", "isTabletMode", "getAvailableLocationProviders", "getAvailableLocationProvidersSync", "getBrightness", "getBrightnessSync", "getDeviceToken", "deviceInfoEmitter", "useBatteryLevel", "batteryLevel", "setBatteryLevel", "setInitialValue", "initialValue", "onChange", "subscription", "addListener", "remove", "useBatteryLevelIsLow", "batteryLevelIsLow", "setBatteryLevelIsLow", "usePowerState", "powerState", "setPowerState", "state", "useIsHeadphonesConnected", "useIsWiredHeadphonesConnected", "useIsBluetoothHeadphonesConnected", "useFirstInstallTime", "useDeviceName", "useHasSystemFeature", "asyncGetter", "useIsEmulator", "useManufacturer", "useBrightness", "brightness", "setBrightness", "value", "DeviceInfo"], "mappings": "AAAA,SAASA,WAAT,EAAsBC,SAAtB,EAAiCC,QAAjC,QAAiD,OAAjD;AACA,SAASC,UAAT,EAAqBC,kBAArB,EAAyCC,aAAzC,EAAwDC,QAAxD,QAAwE,cAAxE;AACA,SAASC,UAAT,EAAqBC,UAArB,QAAuC,8BAAvC;AACA,OAAOC,wBAAP,MAAqC,qCAArC;AACA,OAAOC,gBAAP,MAA6B,6BAA7B;AACA,OAAOC,YAAP,MAAyB,4BAAzB;AACA,SACEC,6BADF,EAEEC,iCAFF,EAGEC,4BAHF,QAIO,oCAJP;AAaA,OAAO,MAAM,CAACC,WAAD,EAAcC,eAAd,IAAiCH,iCAAiC,CAAC;AAC9EI,EAAAA,OAAO,EAAE,UADqE;AAE9EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAF0D;AAG9EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACI,WAAb,EAHgE;AAI9EK,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACK,eAAb,EAJ4D;AAK9EK,EAAAA,YAAY,EAAE;AALgE,CAAD,CAAxE;AAQP,IAAIC,QAAJ;AACA,OAAO,eAAeC,YAAf,GAA8B;AACnC,MAAIjB,QAAQ,CAACkB,EAAT,KAAgB,KAApB,EAA2B;AACzBF,IAAAA,QAAQ,GAAG,MAAMX,YAAY,CAACY,YAAb,EAAjB;AACD,GAFD,MAEO;AACLD,IAAAA,QAAQ,GAAG,MAAMP,WAAW,EAA5B;AACD;;AACD,SAAOO,QAAP;AACD;AAED,OAAO,MAAM,CAACG,aAAD,EAAgBC,iBAAhB,IAAqCb,iCAAiC,CAAC;AAClFI,EAAAA,OAAO,EAAE,YADyE;AAElFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF8D;AAGlFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACc,aAAb,EAHoE;AAIlFL,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACe,iBAAb,EAJgE;AAKlFL,EAAAA,YAAY,EAAE;AALoE,CAAD,CAA5E;AAQP,OAAO,MAAM,CAACM,eAAD,EAAkBC,mBAAlB,IAAyCf,iCAAiC,CAAC;AACtFI,EAAAA,OAAO,EAAE,cAD6E;AAEtFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,CAFkE;AAGtFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACgB,eAAb,EAHwE;AAItFP,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACiB,mBAAb,EAJoE;AAKtFP,EAAAA,YAAY,EAAE;AALwE,CAAD,CAAhF;AAQP,OAAO,MAAM,CAACQ,YAAD,EAAeC,gBAAf,IAAmCjB,iCAAiC,CAAC;AAChFI,EAAAA,OAAO,EAAE,WADuE;AAEhFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF4D;AAGhFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACkB,YAAb,EAHkE;AAIhFT,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACmB,gBAAb,EAJ8D;AAKhFT,EAAAA,YAAY,EAAE;AALkE,CAAD,CAA1E;AAQP,OAAO,MAAM,CAACU,YAAD,EAAeC,gBAAf,IAAmCnB,iCAAiC,CAAC;AAChFK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAD4D;AAEhFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACoB,YAAb,EAFkE;AAGhFX,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACqB,gBAAb,EAH8D;AAIhFX,EAAAA,YAAY,EAAE;AAJkE,CAAD,CAA1E;AAOP,OAAO,MAAM,CAACY,eAAD,EAAkBC,mBAAlB,IAAyCrB,iCAAiC,CAAC;AACtFK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,KAAvB,CADkE;AAEtFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACsB,eAAb,EAFwE;AAGtFb,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACuB,mBAAb,EAHoE;AAItFb,EAAAA,YAAY,EAAE;AAJwE,CAAD,CAAhF;AAOP,OAAO,eAAec,aAAf,GAA+B;AACpC,MAAI7B,QAAQ,CAACkB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOb,YAAY,CAACwB,aAAb,EAAP;AACD,GAFD,MAEO,IAAI7B,QAAQ,CAACkB,EAAT,KAAgB,KAApB,EAA2B;AAChC,WAAO,mBAAP;AACD;;AACD,SAAO,SAAP;AACD;AAED,OAAO,SAASY,iBAAT,GAA6B;AAClC,MAAI9B,QAAQ,CAACkB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOb,YAAY,CAACyB,iBAAb,EAAP;AACD,GAFD,MAEO,IAAI9B,QAAQ,CAACkB,EAAT,KAAgB,KAApB,EAA2B;AAChC,WAAO,mBAAP;AACD;;AACD,SAAO,SAAP;AACD;AAED,OAAO,MAAMa,WAAW,GAAG,MACzBvB,4BAA4B,CAAC;AAC3BO,EAAAA,YAAY,EAAE,SADa;AAE3BJ,EAAAA,OAAO,EAAE,UAFkB;AAG3BE,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC2B,QAHA;AAI3BpB,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB;AAJO,CAAD,CADvB;AAQP,OAAO,MAAM,CAACqB,eAAD,EAAkBC,mBAAlB,IAAyC3B,iCAAiC,CAAC;AACtFI,EAAAA,OAAO,EAAE,cAD6E;AAEtFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFkE;AAGtFC,EAAAA,MAAM,EAAE,MACNb,QAAQ,CAACkB,EAAT,IAAe,KAAf,GAAuBiB,OAAO,CAACC,OAAR,CAAgB,OAAhB,CAAvB,GAAkD/B,YAAY,CAACgC,qBAAb,EAJkC;AAKtFvB,EAAAA,UAAU,EAAE,MAAOd,QAAQ,CAACkB,EAAT,IAAe,KAAf,GAAuB,OAAvB,GAAiCb,YAAY,CAACiC,yBAAb,EALkC;AAMtFvB,EAAAA,YAAY,EAAE;AANwE,CAAD,CAAhF;AASP,OAAO,MAAMwB,QAAQ,GAAG,MACtB/B,4BAA4B,CAAC;AAC3BG,EAAAA,OAAO,EAAE,OADkB;AAE3BI,EAAAA,YAAY,EAAE,SAFa;AAG3BH,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,SAAR,EAAmB,SAAnB,CAHO;AAI3BC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACmC;AAJA,CAAD,CADvB;AAQP,OAAO,MAAMC,QAAQ,GAAG,MACtBjC,4BAA4B,CAAC;AAC3BG,EAAAA,OAAO,EAAE,OADkB;AAE3BC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFO;AAG3BG,EAAAA,YAAY,EAAE,SAHa;AAI3BF,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACqC;AAJA,CAAD,CADvB;AAQP,OAAO,MAAMC,aAAa,GAAG,MAC3BnC,4BAA4B,CAAC;AAC3BO,EAAAA,YAAY,EAAE,SADa;AAE3BH,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,SAAR,EAAmB,SAAnB,CAFO;AAG3BD,EAAAA,OAAO,EAAE,YAHkB;AAI3BE,EAAAA,MAAM,EAAE,MACNb,QAAQ,CAAC4C,MAAT,CAAgB;AACdC,IAAAA,GAAG,EAAExC,YAAY,CAACyC,UADJ;AAEdC,IAAAA,OAAO,EAAE,SAFK;AAGdC,IAAAA,OAAO,EAAE,SAHK;AAIdC,IAAAA,OAAO,EAAE;AAJK,GAAhB;AALyB,CAAD,CADvB;AAcP,OAAO,MAAMC,gBAAgB,GAAG,MAC9B1C,4BAA4B,CAAC;AAC3BO,EAAAA,YAAY,EAAE,SADa;AAE3BF,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC8C,aAFA;AAG3BvC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAHO;AAI3BD,EAAAA,OAAO,EAAE;AAJkB,CAAD,CADvB;AAQP,OAAO,MAAM,CAACyC,UAAD,EAAaC,cAAb,IAA+B9C,iCAAiC,CAAC;AAC5EI,EAAAA,OAAO,EAAE,SADmE;AAE5EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFwD;AAG5EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC+C,UAAb,EAH8D;AAI5EtC,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACgD,cAAb,EAJ0D;AAK5EtC,EAAAA,YAAY,EAAE;AAL8D,CAAD,CAAtE;AAQP,OAAO,MAAM,CAACuC,WAAD,EAAcC,eAAd,IAAiChD,iCAAiC,CAAC;AAC9EI,EAAAA,OAAO,EAAE,UADqE;AAE9EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF0D;AAG9EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACiD,WAAb,EAHgE;AAI9ExC,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACkD,eAAb,EAJ4D;AAK9ExC,EAAAA,YAAY,EAAE,CAAC;AAL+D,CAAD,CAAxE;AAQP,OAAO,MAAMyC,WAAW,GAAG,MACzBhD,4BAA4B,CAAC;AAC3BG,EAAAA,OAAO,EAAE,UADkB;AAE3BC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFO;AAG3BG,EAAAA,YAAY,EAAE,SAHa;AAI3BF,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACoD;AAJA,CAAD,CADvB;AAQP,OAAO,MAAM,CACXC,uBADW,EAEXC,2BAFW,IAGTpD,iCAAiC,CAAC;AACpCI,EAAAA,OAAO,EAAE,sBAD2B;AAEpCC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,KAAvB,CAFgB;AAGpCC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACqD,uBAAb,EAHsB;AAIpC5C,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACsD,2BAAb,EAJkB;AAKpC5C,EAAAA,YAAY,EAAE;AALsB,CAAD,CAH9B;AAWP,OAAO,MAAM6C,kBAAkB,GAAG,MAChCpD,4BAA4B,CAAC;AAC3BG,EAAAA,OAAO,EAAE,SADkB;AAE3BI,EAAAA,YAAY,EAAE,SAFa;AAG3BF,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACwD,OAHA;AAI3BjD,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB;AAJO,CAAD,CADvB;AAQP,OAAO,MAAMkD,cAAc,GAAG,MAC5BtD,4BAA4B,CAAC;AAC3BG,EAAAA,OAAO,EAAE,aADkB;AAE3BC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFO;AAG3BC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC0D,WAHA;AAI3BhD,EAAAA,YAAY,EAAE;AAJa,CAAD,CADvB;AAQP,OAAO,MAAMiD,UAAU,GAAG,MACxBxD,4BAA4B,CAAC;AAC3BG,EAAAA,OAAO,EAAE,SADkB;AAE3BI,EAAAA,YAAY,EAAE,SAFa;AAG3BH,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAHO;AAI3BC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC4D;AAJA,CAAD,CADvB;AAQP,OAAO,SAASC,kBAAT,GAA8B;AACnC,SAAOF,UAAU,KAAK,GAAf,GAAqBF,cAAc,EAA1C;AACD;AAED,OAAO,MAAM,CAACK,aAAD,EAAgBC,iBAAhB,IAAqC7D,iCAAiC,CAAC;AAClFK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAD8D;AAElFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC8D,aAAb,EAFoE;AAGlFrD,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC+D,iBAAb,EAHgE;AAIlFrD,EAAAA,YAAY,EAAE;AAJoE,CAAD,CAA5E;AAOP,OAAO,MAAM,CAACsD,aAAD,EAAgBC,iBAAhB,IAAqC/D,iCAAiC,CAAC;AAClFK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CAD8D;AAElFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACgE,aAAb,EAFoE;AAGlFvD,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACiE,iBAAb,EAHgE;AAIlFvD,EAAAA,YAAY,EAAE,CAAC;AAJmE,CAAD,CAA5E;AAOP,OAAO,MAAMwD,YAAY,GAAG,MAC1BjE,6BAA6B,CAAC;AAC5BK,EAAAA,OAAO,EAAE,WADmB;AAE5BI,EAAAA,YAAY,EAAE,SAFc;AAG5BH,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,KAAnB,CAHQ;AAI5BC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACkE,YAAb;AAJc,CAAD,CADxB;AAQP,OAAO,MAAMC,gBAAgB,GAAG,MAC9BhE,4BAA4B,CAAC;AAC3BG,EAAAA,OAAO,EAAE,eADkB;AAE3BI,EAAAA,YAAY,EAAE,SAFa;AAG3BH,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CAHO;AAI3BC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACmE,gBAAb;AAJa,CAAD,CADvB;AAQP,OAAO,MAAM,CAACC,YAAD,EAAeC,gBAAf,IAAmCnE,iCAAiC,CAAC;AAChFK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAD4D;AAEhFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACoE,YAAb,EAFkE;AAGhF3D,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACqE,gBAAb,EAH8D;AAIhF3D,EAAAA,YAAY,EAAE,CAAC;AAJiE,CAAD,CAA1E;AAOP,OAAO,MAAM,CAAC4D,aAAD,EAAgBC,iBAAhB,IAAqCrE,iCAAiC,CAAC;AAClFI,EAAAA,OAAO,EAAE,YADyE;AAElFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF8D;AAGlFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACsE,aAAb,EAHoE;AAIlF7D,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACuE,iBAAb,EAJgE;AAKlF7D,EAAAA,YAAY,EAAE;AALoE,CAAD,CAA5E;AAQP,OAAO,MAAM,CAAC8D,SAAD,EAAYC,aAAZ,IAA6BvE,iCAAiC,CAAC;AAC1EM,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACwE,SAAb,EAD4D;AAE1E/D,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACyE,aAAb,EAFwD;AAG1E/D,EAAAA,YAAY,EAAE,SAH4D;AAI1EJ,EAAAA,OAAO,EAAE,QAJiE;AAK1EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD;AALsD,CAAD,CAApE;AAQP,OAAO,MAAM,CAACmE,UAAD,EAAaC,cAAb,IAA+BzE,iCAAiC,CAAC;AAC5EI,EAAAA,OAAO,EAAE,SADmE;AAE5EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFwD;AAG5EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC0E,UAAb,EAH8D;AAI5EjE,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC2E,cAAb,EAJ0D;AAK5EjE,EAAAA,YAAY,EAAE;AAL8D,CAAD,CAAtE;AAQP,OAAO,MAAM,CAACkE,cAAD,EAAiBC,kBAAjB,IAAuC3E,iCAAiC,CAAC;AACpFI,EAAAA,OAAO,EAAE,aAD2E;AAEpFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFgE;AAGpFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC4E,cAAb,EAHsE;AAIpFnE,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC6E,kBAAb,EAJkE;AAKpFnE,EAAAA,YAAY,EAAE;AALsE,CAAD,CAA9E;AAQP,OAAO,MAAM,CAACoE,WAAD,EAAcC,eAAd,IAAiC7E,iCAAiC,CAAC;AAC9EI,EAAAA,OAAO,EAAE,UADqE;AAE9EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF0D;AAG9EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC8E,WAAb,EAHgE;AAI9ErE,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC+E,eAAb,EAJ4D;AAK9ErE,EAAAA,YAAY,EAAE;AALgE,CAAD,CAAxE;AAQP,OAAO,MAAM,CAACsE,OAAD,EAAUC,WAAV,IAAyB/E,iCAAiC,CAAC;AACtEI,EAAAA,OAAO,EAAE,MAD6D;AAEtEC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,CAFkD;AAGtEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACgF,OAAb,EAHwD;AAItEvE,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACiF,WAAb,EAJoD;AAKtEvE,EAAAA,YAAY,EAAE;AALwD,CAAD,CAAhE;AAQP,OAAO,MAAM,CAACwE,YAAD,EAAeC,gBAAf,IAAmCjF,iCAAiC,CAAC;AAChFI,EAAAA,OAAO,EAAE,WADuE;AAEhFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF4D;AAGhFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACkF,YAAb,EAHkE;AAIhFzE,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACmF,gBAAb,EAJ8D;AAKhFzE,EAAAA,YAAY,EAAE;AALkE,CAAD,CAA1E;AAQP,OAAO,MAAM,CAAC0E,UAAD,EAAaC,cAAb,IAA+BnF,iCAAiC,CAAC;AAC5EI,EAAAA,OAAO,EAAE,SADmE;AAE5EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFwD;AAG5EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACoF,UAAb,EAH8D;AAI5E3E,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACqF,cAAb,EAJ0D;AAK5E3E,EAAAA,YAAY,EAAE;AAL8D,CAAD,CAAtE;AAQP,OAAO,MAAM,CAAC4E,OAAD,EAAUC,WAAV,IAAyBrF,iCAAiC,CAAC;AACtEI,EAAAA,OAAO,EAAE,MAD6D;AAEtEC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFkD;AAGtEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACsF,OAAb,EAHwD;AAItE7E,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACuF,WAAb,EAJoD;AAKtE7E,EAAAA,YAAY,EAAE;AALwD,CAAD,CAAhE;AAQP,OAAO,MAAM,CAAC8E,OAAD,EAAUC,WAAV,IAAyBvF,iCAAiC,CAAC;AACtEI,EAAAA,OAAO,EAAE,MAD6D;AAEtEC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFkD;AAGtEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACwF,OAAb,EAHwD;AAItE/E,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACyF,WAAb,EAJoD;AAKtE/E,EAAAA,YAAY,EAAE;AALwD,CAAD,CAAhE;AAQP,OAAO,MAAM,CAACgF,SAAD,EAAYC,aAAZ,IAA6BzF,iCAAiC,CAAC;AAC1EI,EAAAA,OAAO,EAAE,QADiE;AAE1EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFsD;AAG1EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC0F,SAAb,EAH4D;AAI1EjF,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC2F,aAAb,EAJwD;AAK1EjF,EAAAA,YAAY,EAAE;AAL4D,CAAD,CAApE;AAQP,OAAO,MAAM,CAACkF,gBAAD,EAAmBC,oBAAnB,IAA2C3F,iCAAiC,CAAC;AACxFI,EAAAA,OAAO,EAAE,eAD+E;AAExFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFoE;AAGxFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC4F,gBAAb,EAH0E;AAIxFnF,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC6F,oBAAb,EAJsE;AAKxFnF,EAAAA,YAAY,EAAE,CAAC;AALyE,CAAD,CAAlF;AAQP,OAAO,MAAM,CAACoF,gBAAD,EAAmBC,oBAAnB,IAA2C7F,iCAAiC,CAAC;AACxFI,EAAAA,OAAO,EAAE,eAD+E;AAExFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFoE;AAGxFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC8F,gBAAb,EAH0E;AAIxFrF,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC+F,oBAAb,EAJsE;AAKxFrF,EAAAA,YAAY,EAAE;AAL0E,CAAD,CAAlF;AAQP,OAAO,MAAM,CAACsF,WAAD,EAAcC,eAAd,IAAiC/F,iCAAiC,CAAC;AAC9EI,EAAAA,OAAO,EAAE,UADqE;AAE9EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF0D;AAG9EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACgG,WAAb,EAHgE;AAI9EvF,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACiG,eAAb,EAJ4D;AAK9EvF,EAAAA,YAAY,EAAE;AALgE,CAAD,CAAxE;AAQP,OAAO,MAAM,CAACwF,cAAD,EAAiBC,kBAAjB,IAAuCjG,iCAAiC,CAAC;AACpFI,EAAAA,OAAO,EAAE,aAD2E;AAEpFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFgE;AAGpFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACkG,cAAb,EAHsE;AAIpFzF,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACmG,kBAAb,EAJkE;AAKpFzF,EAAAA,YAAY,EAAE;AALsE,CAAD,CAA9E;AAQP,OAAO,MAAM,CAAC0F,UAAD,EAAaC,cAAb,IAA+BnG,iCAAiC,CAAC;AAC5EI,EAAAA,OAAO,EAAE,UADmE;AAE5EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFwD;AAG5EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACoG,UAAb,EAH8D;AAI5E3F,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACqG,cAAb,EAJ0D;AAK5E3F,EAAAA,YAAY,EAAE;AAL8D,CAAD,CAAtE;AAQP,OAAO,MAAM4F,QAAQ,GAAG,MACtBnG,4BAA4B,CAAC;AAC3BO,EAAAA,YAAY,EAAE,KADa;AAE3BH,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFO;AAG3BD,EAAAA,OAAO,EAAE,QAHkB;AAI3BE,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACsG;AAJA,CAAD,CADvB;AAQP,OAAO,MAAMC,cAAc,GAAG,MAC5BpG,4BAA4B,CAAC;AAC3BO,EAAAA,YAAY,EAAE,KADa;AAE3BH,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFO;AAG3BD,EAAAA,OAAO,EAAE,QAHkB;AAI3BE,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACuG;AAJA,CAAD,CADvB;AAQP,OAAO,MAAMC,eAAe,GAAG,MAC7BrG,4BAA4B,CAAC;AAC3BO,EAAAA,YAAY,EAAE,KADa;AAE3BH,EAAAA,kBAAkB,EAAE,CAAC,KAAD,CAFO;AAG3BD,EAAAA,OAAO,EAAE,QAHkB;AAI3BE,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACwG;AAJA,CAAD,CADvB;AAQP,OAAO,MAAM,CAACC,qBAAD,EAAwBC,yBAAxB,IAAqDxG,iCAAiC,CACjG;AACEK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACyG,qBAAb,EAFhB;AAGEhG,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC0G,yBAAb,EAHpB;AAIEhG,EAAAA,YAAY,EAAE;AAJhB,CADiG,CAA5F;AASP,IAAIiG,KAAJ;AACA,OAAO,SAASC,QAAT,GAAoB;AACzB,MAAID,KAAK,KAAKE,SAAd,EAAyB;AACvB,QAAIC,MAAM,GAAG1E,QAAQ,EAArB;;AACA,QAAI2E,MAAM,GAAG7E,QAAQ,EAArB;;AACAyE,IAAAA,KAAK,GACH5G,gBAAgB,CAACiH,SAAjB,CACGC,IAAD,IACEA,IAAI,CAAC5E,KAAL,CAAW6E,WAAX,OAA6BJ,MAAM,CAACI,WAAP,EAA7B,IACAD,IAAI,CAAC9E,KAAL,CAAW+E,WAAX,OAA6BH,MAAM,CAACG,WAAP,EAHjC,MAIM,CAAC,CALT;AAMD;;AACD,SAAOP,KAAP;AACD;AAED,IAAIQ,aAAJ;AACA,OAAO,SAASC,gBAAT,GAA4B;AACjC,MAAID,aAAa,KAAKN,SAAtB,EAAiC;AAC/B,QAAIC,MAAM,GAAG1E,QAAQ,EAArB;;AACA,QAAI2E,MAAM,GAAG7E,QAAQ,EAArB;;AACAiF,IAAAA,aAAa,GACXrH,wBAAwB,CAACkH,SAAzB,CACGC,IAAD,IACEA,IAAI,CAAC5E,KAAL,CAAW6E,WAAX,OAA6BJ,MAAM,CAACI,WAAP,EAA7B,IACAD,IAAI,CAAC9E,KAAL,CAAW+E,WAAX,OAA6BH,MAAM,CAACG,WAAP,EAHjC,MAIM,CAAC,CALT;AAMD;;AACD,SAAOC,aAAP;AACD;AAED,OAAO,MAAM,CAACE,MAAD,EAASC,UAAT,IAAuBpH,iCAAiC,CAAC;AACpEK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADgD;AAEpEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACqH,MAAb,EAFsD;AAGpE5G,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACsH,UAAb,EAHkD;AAIpE5G,EAAAA,YAAY,EAAE;AAJsD,CAAD,CAA9D;AAOP,OAAO,MAAM,CAAC6G,MAAD,EAASC,UAAT,IAAuBtH,iCAAiC,CAAC;AACpEK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADgD;AAEpEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACuH,MAAb,EAFsD;AAGpE9G,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACwH,UAAb,EAHkD;AAIpE9G,EAAAA,YAAY,EAAE;AAJsD,CAAD,CAA9D;AAOP,OAAO,MAAM,CAAC+G,mBAAD,EAAsBC,uBAAtB,IAAiDxH,iCAAiC,CAAC;AAC9FI,EAAAA,OAAO,EAAE,kBADqF;AAE9FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAF0E;AAG9FC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACyH,mBAAb,EAHgF;AAI9FhH,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC0H,uBAAb,EAJ4E;AAK9FhH,EAAAA,YAAY,EAAE,CAAC;AAL+E,CAAD,CAAxF;AAQP,OAAO,MAAM,CAACiH,kBAAD,EAAqBC,sBAArB,IAA+C1H,iCAAiC,CAAC;AAC5FI,EAAAA,OAAO,EAAE,iBADmF;AAE5FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,KAAvB,CAFwE;AAG5FC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC2H,kBAAb,EAH8E;AAI5FlH,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC4H,sBAAb,EAJ0E;AAK5FlH,EAAAA,YAAY,EAAE;AAL8E,CAAD,CAAtF;AAQP,OAAO,MAAM,CAACmH,iBAAD,EAAoBC,qBAApB,IAA6C5H,iCAAiC,CAAC;AAC1FI,EAAAA,OAAO,EAAE,gBADiF;AAE1FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFsE;AAG1FC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC6H,iBAAb,EAH4E;AAI1FpH,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC8H,qBAAb,EAJwE;AAK1FpH,EAAAA,YAAY,EAAE,CAAC;AAL2E,CAAD,CAApF;AAQP,OAAO,MAAM,CAACqH,cAAD,EAAiBC,kBAAjB,IAAuC9H,iCAAiC,CAAC;AACpFK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADgE;AAEpFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC+H,cAAb,EAFsE;AAGpFtH,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACgI,kBAAb,EAHkE;AAIpFtH,EAAAA,YAAY,EAAE;AAJsE,CAAD,CAA9E;AAOP,OAAO,MAAM,CAACuH,UAAD,EAAaC,cAAb,IAA+BhI,iCAAiC,CAAC;AAC5EK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADwD;AAE5EC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACiI,UAAb,EAF8D;AAG5ExH,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACkI,cAAb,EAH0D;AAI5ExH,EAAAA,YAAY,EAAE;AAJ8D,CAAD,CAAtE;AAOP,OAAO,MAAM,CAACyH,cAAD,EAAiBC,kBAAjB,IAAuClI,iCAAiC,CAAC;AACpFI,EAAAA,OAAO,EAAE,aAD2E;AAEpFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CAFgE;AAGpFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACmI,cAAb,EAHsE;AAIpF1H,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACoI,kBAAb,EAJkE;AAKpF1H,EAAAA,YAAY,EAAE,CAAC;AALqE,CAAD,CAA9E;AAQP,OAAO,MAAM,CAAC2H,YAAD,EAAeC,gBAAf,IAAmCpI,iCAAiC,CAAC;AAChFI,EAAAA,OAAO,EAAE,WADuE;AAEhFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,KAAvB,CAF4D;AAGhFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACqI,YAAb,EAHkE;AAIhF5H,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACsI,gBAAb,EAJ8D;AAKhF5H,EAAAA,YAAY,EAAE,CAAC;AALiE,CAAD,CAA1E;AAQP,OAAO,MAAM,CAAC6H,oBAAD,EAAuBC,wBAAvB,IAAmDtI,iCAAiC,CAAC;AAChGK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CAD4E;AAEhGC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACuI,oBAAb,EAFkF;AAGhG9H,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACwI,wBAAb,EAH8E;AAIhG9H,EAAAA,YAAY,EAAE,CAAC;AAJiF,CAAD,CAA1F;AAOP,OAAO,eAAe+H,uBAAf,GAAyC;AAC9C,MAAI9I,QAAQ,CAACkB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOb,YAAY,CAACyI,uBAAb,EAAP;AACD;;AACD,MAAI9I,QAAQ,CAACkB,EAAT,KAAgB,KAAhB,IAAyBlB,QAAQ,CAACkB,EAAT,KAAgB,SAAzC,IAAsDlB,QAAQ,CAACkB,EAAT,KAAgB,KAA1E,EAAiF;AAC/E,WAAO0H,oBAAoB,EAA3B;AACD;;AAED,SAAO,CAAC,CAAR;AACD;AAED,OAAO,SAASG,2BAAT,GAAuC;AAC5C,MAAI/I,QAAQ,CAACkB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOb,YAAY,CAAC0I,2BAAb,EAAP;AACD;;AACD,MAAI/I,QAAQ,CAACkB,EAAT,KAAgB,KAAhB,IAAyBlB,QAAQ,CAACkB,EAAT,KAAgB,SAAzC,IAAsDlB,QAAQ,CAACkB,EAAT,KAAgB,KAA1E,EAAiF;AAC/E,WAAO2H,wBAAwB,EAA/B;AACD;;AAED,SAAO,CAAC,CAAR;AACD;AAED,OAAO,MAAM,CAACG,kBAAD,EAAqBC,sBAArB,IAA+C1I,iCAAiC,CAAC;AAC5FK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CADwE;AAE5FC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC2I,kBAAb,EAF8E;AAG5FlI,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC4I,sBAAb,EAH0E;AAI5FlI,EAAAA,YAAY,EAAE,CAAC;AAJ6E,CAAD,CAAtF;AAOP,OAAO,eAAemI,qBAAf,GAAuC;AAC5C,MAAIlJ,QAAQ,CAACkB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOb,YAAY,CAAC6I,qBAAb,EAAP;AACD;;AACD,MAAIlJ,QAAQ,CAACkB,EAAT,KAAgB,KAAhB,IAAyBlB,QAAQ,CAACkB,EAAT,KAAgB,SAAzC,IAAsDlB,QAAQ,CAACkB,EAAT,KAAgB,KAA1E,EAAiF;AAC/E,WAAO8H,kBAAkB,EAAzB;AACD;;AAED,SAAO,CAAC,CAAR;AACD;AAED,OAAO,SAASG,yBAAT,GAAqC;AAC1C,MAAInJ,QAAQ,CAACkB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOb,YAAY,CAAC8I,yBAAb,EAAP;AACD;;AACD,MAAInJ,QAAQ,CAACkB,EAAT,KAAgB,KAAhB,IAAyBlB,QAAQ,CAACkB,EAAT,KAAgB,SAAzC,IAAsDlB,QAAQ,CAACkB,EAAT,KAAgB,KAA1E,EAAiF;AAC/E,WAAO+H,sBAAsB,EAA7B;AACD;;AAED,SAAO,CAAC,CAAR;AACD;AAED,OAAO,MAAM,CAACG,eAAD,EAAkBC,mBAAlB,IAAyC9I,iCAAiC,CAAC;AACtFK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CADkE;AAEtFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC+I,eAAb,EAFwE;AAGtFtI,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACgJ,mBAAb,EAHoE;AAItFtI,EAAAA,YAAY,EAAE,CAAC;AAJuE,CAAD,CAAhF;AAOP,OAAO,MAAM,CAACuI,aAAD,EAAgBC,iBAAhB,IAAqChJ,iCAAiC,CAEjF;AACAK,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,SAAR,EAAmB,SAAnB,EAA8B,KAA9B,CADpB;AAEAC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACiJ,aAAb,EAFd;AAGAxI,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACkJ,iBAAb,EAHlB;AAIAxI,EAAAA,YAAY,EAAE;AAJd,CAFiF,CAA5E;AASP,OAAO,MAAM,CAACyI,iBAAD,EAAoBC,qBAApB,IAA6ClJ,iCAAiC,CAAC;AAC1FK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CADsE;AAE1FC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACmJ,iBAAb,EAF4E;AAG1F1I,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACoJ,qBAAb,EAHwE;AAI1F1I,EAAAA,YAAY,EAAE;AAJ4E,CAAD,CAApF;AAOP,OAAO,eAAe2I,WAAf,GAA6B;AAClC,SAAOvH,OAAO,CAACC,OAAR,CAAgBuH,eAAe,EAA/B,CAAP;AACD;AAED,OAAO,SAASA,eAAT,GAA2B;AAChC,QAAM;AAAEC,IAAAA,MAAF;AAAUC,IAAAA;AAAV,MAAoBhK,UAAU,CAACiK,GAAX,CAAe,QAAf,CAA1B;AACA,SAAOD,KAAK,IAAID,MAAhB;AACD;AAED,OAAO,MAAM,CAACG,cAAD,EAAiBC,kBAAjB,IAAuCzJ,iCAAiC,CAAC;AACpFK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADgE;AAEpFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC0J,cAAb,EAFsE;AAGpFjJ,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC2J,kBAAb,EAHkE;AAIpFjJ,EAAAA,YAAY,EAAE;AAJsE,CAAD,CAA9E;AAOP,OAAO,MAAMkJ,aAAa,GAAG,MAAM;AACjC,SAAOzJ,4BAA4B,CAAC;AAClCG,IAAAA,OAAO,EAAE,YADyB;AAElCC,IAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFc;AAGlCG,IAAAA,YAAY,EAAE,SAHoB;AAIlCF,IAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC6J;AAJO,GAAD,CAAnC;AAMD,CAPM;AASP,OAAO,MAAMC,iBAAiB,GAAG,MAAM;AACrC,SAAO3J,4BAA4B,CAAC;AAClCG,IAAAA,OAAO,EAAE,YADyB;AAElCC,IAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFc;AAGlCG,IAAAA,YAAY,EAAE,SAHoB;AAIlCF,IAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC6J;AAJO,GAAD,CAAnC;AAMD,CAPM;AASP,OAAO,MAAM,CAACE,aAAD,EAAgBC,iBAAhB,IAAqC9J,iCAAiC,CAAC;AAClFI,EAAAA,OAAO,EAAE,gBADyE;AAElFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAF8D;AAGlFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACiK,gBAAb,EAHoE;AAIlFxJ,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACkK,oBAAb,EAJgE;AAKlFxJ,EAAAA,YAAY,EAAE;AALoE,CAAD,CAA5E;AAQP,OAAO,MAAM,CAACyJ,kBAAD,EAAqBC,sBAArB,IAA+ClK,iCAAiC,CAAC;AAC5FI,EAAAA,OAAO,EAAE,qBADmF;AAE5FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFwE;AAG5FC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACqK,qBAAb,EAH8E;AAI5F5J,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACsK,yBAAb,EAJ0E;AAK5F5J,EAAAA,YAAY,EAAE;AAL8E,CAAD,CAAtF;AAQP,OAAO,MAAM,CAAC6J,kBAAD,EAAqBC,sBAArB,IAA+CtK,iCAAiC,CAAC;AAC5FI,EAAAA,OAAO,EAAE,qBADmF;AAE5FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFwE;AAG5FC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACyK,qBAAb,EAH8E;AAI5FhK,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC0K,yBAAb,EAJ0E;AAK5FhK,EAAAA,YAAY,EAAE;AAL8E,CAAD,CAAtF;AAQP,OAAO,eAAeiK,gBAAf,CAAgCC,OAAhC,EAAiD;AACtD,MAAIjL,QAAQ,CAACkB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOb,YAAY,CAAC2K,gBAAb,CAA8BC,OAA9B,CAAP;AACD;;AACD,SAAO,KAAP;AACD;AAED,OAAO,SAASC,oBAAT,CAA8BD,OAA9B,EAA+C;AACpD,MAAIjL,QAAQ,CAACkB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOb,YAAY,CAAC6K,oBAAb,CAAkCD,OAAlC,CAAP;AACD;;AACD,SAAO,KAAP;AACD;AAED,OAAO,SAASE,iBAAT,CAA2BC,KAA3B,EAAmD;AACxD,MAAIpL,QAAQ,CAACkB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOkK,KAAK,GAAG,IAAf;AACD;;AACD,SAAOA,KAAK,GAAG,GAAf;AACD;AAED,OAAO,MAAM,CACXC,0BADW,EAEXC,8BAFW,IAGT/K,iCAAiC,CAAC;AACpCK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADgB;AAEpCC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACgL,0BAAb,EAFsB;AAGpCvK,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACiL,8BAAb,EAHkB;AAIpCvK,EAAAA,YAAY,EAAE;AAJsB,CAAD,CAH9B;AAUP,OAAO,MAAM,CAACwK,iBAAD,EAAoBC,qBAApB,IAA6CjL,iCAAiC,CAAC;AAC1FK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,KAAnB,CADsE;AAE1FC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACkL,iBAAb,EAF4E;AAG1FzK,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACmL,qBAAb,EAHwE;AAI1FzK,EAAAA,YAAY,EAAE;AAJ4E,CAAD,CAApF;AAOP,OAAO,MAAM,CAAC0K,qBAAD,EAAwBC,yBAAxB,IAAqDnL,iCAAiC,CACjG;AACEK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACoL,qBAAb,EAFhB;AAGE3K,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACqL,yBAAb,EAHpB;AAIE3K,EAAAA,YAAY,EAAE;AAJhB,CADiG,CAA5F;AASP,OAAO,MAAM,CAAC4K,0BAAD,EAA6BC,8BAA7B,IAA+DrL,iCAAiC,CAC3G;AACEK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACsL,0BAAb,EAFhB;AAGE7K,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACuL,8BAAb,EAHpB;AAIE7K,EAAAA,YAAY,EAAE;AAJhB,CAD2G,CAAtG;AASP,OAAO,MAAM,CAAC8K,8BAAD,EAAiCC,kCAAjC,IAAuEvL,iCAAiC,CACnH;AACEK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACwL,8BAAb,EAFhB;AAGE/K,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACyL,kCAAb,EAHpB;AAIE/K,EAAAA,YAAY,EAAE;AAJhB,CADmH,CAA9G;AASP,OAAO,MAAM,CAACgL,gBAAD,EAAmBC,oBAAnB,IAA2CzL,iCAAiC,CAAC;AACxFK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADoE;AAExFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC0L,gBAAb,EAF0E;AAGxFjL,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC2L,oBAAb,EAHsE;AAIxFjL,EAAAA,YAAY,EAAE;AAJ0E,CAAD,CAAlF;AAOP,OAAO,MAAM,CAACkL,mBAAD,EAAsBC,uBAAtB,IAAiD3L,iCAAiC,CAAC;AAC9FK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAD0E;AAE9FC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC4L,mBAAb,EAFgF;AAG9FnL,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC6L,uBAAb,EAH4E;AAI9FnL,EAAAA,YAAY,EAAE;AAJgF,CAAD,CAAxF;AAOP,OAAO,MAAM,CAACoL,yBAAD,EAA4BC,6BAA5B,IAA6D7L,iCAAiC,CACzG;AACEK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAAC8L,yBAAb,EAFhB;AAGErL,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAAC+L,6BAAb,EAHpB;AAIErL,EAAAA,YAAY,EAAE;AAJhB,CADyG,CAApG;AAUP,OAAO,MAAMsL,YAAY,GAAG,MAC1B/L,6BAA6B,CAAC;AAC5BM,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADQ;AAE5BC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACgM,YAAb,EAFc;AAG5BtL,EAAAA,YAAY,EAAE;AAHc,CAAD,CADxB;AAOP,OAAO,MAAM,CACXuL,6BADW,EAEXC,iCAFW,IAGThM,iCAAiC,CAAC;AACpCK,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADgB;AAEpCC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACiM,6BAAb,EAFsB;AAGpCxL,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACkM,iCAAb,EAHkB;AAIpCxL,EAAAA,YAAY,EAAE;AAJsB,CAAD,CAH9B;AAUP,OAAO,MAAM,CAACyL,aAAD,EAAgBC,iBAAhB,IAAqClM,iCAAiC,CAAC;AAClFK,EAAAA,kBAAkB,EAAE,CAAC,KAAD,CAD8D;AAElFC,EAAAA,MAAM,EAAE,MAAMR,YAAY,CAACmM,aAAb,EAFoE;AAGlF1L,EAAAA,UAAU,EAAE,MAAMT,YAAY,CAACoM,iBAAb,EAHgE;AAIlF1L,EAAAA,YAAY,EAAE,CAAC;AAJmE,CAAD,CAA5E;AAOP,OAAO,eAAe2L,cAAf,GAAgC;AACrC,MAAI1M,QAAQ,CAACkB,EAAT,KAAgB,KAApB,EAA2B;AACzB,WAAOb,YAAY,CAACqM,cAAb,EAAP;AACD;;AACD,SAAO,SAAP;AACD;AAED,MAAMC,iBAAiB,GAAG,IAAI7M,kBAAJ,CAAuBC,aAAa,CAACM,YAArC,CAA1B;AACA,OAAO,SAASuM,eAAT,GAA0C;AAC/C,QAAM,CAACC,YAAD,EAAeC,eAAf,IAAkClN,QAAQ,CAAgB,IAAhB,CAAhD;AAEAD,EAAAA,SAAS,CAAC,MAAM;AACd,UAAMoN,eAAe,GAAG,YAAY;AAClC,YAAMC,YAAoB,GAAG,MAAM5D,eAAe,EAAlD;AACA0D,MAAAA,eAAe,CAACE,YAAD,CAAf;AACD,KAHD;;AAKA,UAAMC,QAAQ,GAAI7B,KAAD,IAAmB;AAClC0B,MAAAA,eAAe,CAAC1B,KAAD,CAAf;AACD,KAFD;;AAIA2B,IAAAA,eAAe;AAEf,UAAMG,YAAY,GAAGP,iBAAiB,CAACQ,WAAlB,CACnB,oCADmB,EAEnBF,QAFmB,CAArB;AAKA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAlBQ,EAkBN,EAlBM,CAAT;AAoBA,SAAOP,YAAP;AACD;AAED,OAAO,SAASQ,oBAAT,GAA+C;AACpD,QAAM,CAACC,iBAAD,EAAoBC,oBAApB,IAA4C3N,QAAQ,CAAgB,IAAhB,CAA1D;AAEAD,EAAAA,SAAS,CAAC,MAAM;AACd,UAAMoN,eAAe,GAAG,YAAY;AAClC,YAAMC,YAAoB,GAAG,MAAM5D,eAAe,EAAlD;AACA+B,MAAAA,iBAAiB,CAAC6B,YAAD,CAAjB,IAAmCO,oBAAoB,CAACP,YAAD,CAAvD;AACD,KAHD;;AAKAD,IAAAA,eAAe;;AAEf,UAAME,QAAQ,GAAI7B,KAAD,IAAmB;AAClCmC,MAAAA,oBAAoB,CAACnC,KAAD,CAApB;AACD,KAFD;;AAIA,UAAM8B,YAAY,GAAGP,iBAAiB,CAACQ,WAAlB,CAA8B,gCAA9B,EAAgEF,QAAhE,CAArB;AAEA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAfQ,EAeN,EAfM,CAAT;AAiBA,SAAOE,iBAAP;AACD;AAED,OAAO,SAASE,aAAT,GAA8C;AACnD,QAAM,CAACC,UAAD,EAAaC,aAAb,IAA8B9N,QAAQ,CAAsB,EAAtB,CAA5C;AAEAD,EAAAA,SAAS,CAAC,MAAM;AACd,UAAMoN,eAAe,GAAG,YAAY;AAClC,YAAMC,YAAiC,GAAG,MAAM1D,aAAa,EAA7D;AACAoE,MAAAA,aAAa,CAACV,YAAD,CAAb;AACD,KAHD;;AAKA,UAAMC,QAAQ,GAAIU,KAAD,IAAuB;AACtCD,MAAAA,aAAa,CAACC,KAAD,CAAb;AACD,KAFD;;AAIAZ,IAAAA,eAAe;AAEf,UAAMG,YAAY,GAAGP,iBAAiB,CAACQ,WAAlB,CACnB,kCADmB,EAEnBF,QAFmB,CAArB;AAKA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAlBQ,EAkBN,EAlBM,CAAT;AAoBA,SAAOK,UAAP;AACD;AAED,OAAO,SAASG,wBAAT,GAA8D;AACnE,SAAO3N,UAAU,CAAC,2CAAD,EAA8CwL,qBAA9C,EAAqE,KAArE,CAAjB;AACD;AAED,OAAO,SAASoC,6BAAT,GAAmE;AACxE,SAAO5N,UAAU,CAAC,gDAAD,EAAmD0L,0BAAnD,EAA+E,KAA/E,CAAjB;AACD;AAED,OAAO,SAASmC,iCAAT,GAAuE;AAC5E,SAAO7N,UAAU,CAAC,oDAAD,EAAuD4L,8BAAvD,EAAuF,KAAvF,CAAjB;AACD;AAED,OAAO,SAASkC,mBAAT,GAAwD;AAC7D,SAAO7N,UAAU,CAAC4H,mBAAD,EAAsB,CAAC,CAAvB,CAAjB;AACD;AAED,OAAO,SAASkG,aAAT,GAAkD;AACvD,SAAO9N,UAAU,CAACiE,aAAD,EAAgB,SAAhB,CAAjB;AACD;AAED,OAAO,SAAS8J,mBAAT,CAA6BhD,OAA7B,EAAwE;AAC7E,QAAMiD,WAAW,GAAGxO,WAAW,CAAC,MAAMsL,gBAAgB,CAACC,OAAD,CAAvB,EAAkC,CAACA,OAAD,CAAlC,CAA/B;AACA,SAAO/K,UAAU,CAACgO,WAAD,EAAc,KAAd,CAAjB;AACD;AAED,OAAO,SAASC,aAAT,GAAmD;AACxD,SAAOjO,UAAU,CAACuG,UAAD,EAAa,KAAb,CAAjB;AACD;AAED,OAAO,SAAS2H,eAAT,GAAoD;AACzD,SAAOlO,UAAU,CAAC+B,eAAD,EAAkB,SAAlB,CAAjB;AACD;AAED,OAAO,SAASoM,aAAT,GAAwC;AAC7C,QAAM,CAACC,UAAD,EAAaC,aAAb,IAA8B3O,QAAQ,CAAgB,IAAhB,CAA5C;AAEAD,EAAAA,SAAS,CAAC,MAAM;AACd,UAAMoN,eAAe,GAAG,YAAY;AAClC,YAAMC,YAAoB,GAAG,MAAMR,aAAa,EAAhD;AACA+B,MAAAA,aAAa,CAACvB,YAAD,CAAb;AACD,KAHD;;AAKA,UAAMC,QAAQ,GAAIuB,KAAD,IAAmB;AAClCD,MAAAA,aAAa,CAACC,KAAD,CAAb;AACD,KAFD;;AAIAzB,IAAAA,eAAe;AAEf,UAAMG,YAAY,GAAGP,iBAAiB,CAACQ,WAAlB,CACnB,kCADmB,EAEnBF,QAFmB,CAArB;AAKA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAlBQ,EAkBN,EAlBM,CAAT;AAoBA,SAAOkB,UAAP;AACD;AAID,MAAMG,UAA4B,GAAG;AACnClN,EAAAA,YADmC;AAEnCC,EAAAA,gBAFmC;AAGnC8B,EAAAA,WAHmC;AAInCC,EAAAA,eAJmC;AAKnCK,EAAAA,kBALmC;AAMnC0I,EAAAA,6BANmC;AAOnCC,EAAAA,iCAPmC;AAQnCxG,EAAAA,SARmC;AASnCC,EAAAA,aATmC;AAUnCoD,EAAAA,eAVmC;AAWnCC,EAAAA,mBAXmC;AAYnC1E,EAAAA,aAZmC;AAanCC,EAAAA,iBAbmC;AAcnCnC,EAAAA,QAdmC;AAenCW,EAAAA,UAfmC;AAgBnCC,EAAAA,cAhBmC;AAiBnCS,EAAAA,cAjBmC;AAkBnCN,EAAAA,WAlBmC;AAmBnC8E,EAAAA,UAnBmC;AAoBnCC,EAAAA,cApBmC;AAqBnClC,EAAAA,WArBmC;AAsBnCC,EAAAA,eAtBmC;AAuBnCzB,EAAAA,SAvBmC;AAwBnC9C,EAAAA,WAxBmC;AAyBnCoC,EAAAA,aAzBmC;AA0BnCC,EAAAA,iBA1BmC;AA2BnCU,EAAAA,aA3BmC;AA4BnC4H,EAAAA,cA5BmC;AA6BnCzC,EAAAA,aA7BmC;AA8BnClF,EAAAA,UA9BmC;AA+BnCC,EAAAA,cA/BmC;AAgCnCC,EAAAA,cAhCmC;AAiCnCC,EAAAA,kBAjCmC;AAkCnC4C,EAAAA,mBAlCmC;AAmCnCC,EAAAA,uBAnCmC;AAoCnCtD,EAAAA,YApCmC;AAqCnCC,EAAAA,gBArCmC;AAsCnCsE,EAAAA,kBAtCmC;AAuCnCE,EAAAA,qBAvCmC;AAwCnCD,EAAAA,sBAxCmC;AAyCnCE,EAAAA,yBAzCmC;AA0CnChE,EAAAA,WA1CmC;AA2CnCC,EAAAA,eA3CmC;AA4CnCC,EAAAA,OA5CmC;AA6CnCC,EAAAA,WA7CmC;AA8CnCC,EAAAA,YA9CmC;AA+CnCC,EAAAA,gBA/CmC;AAgDnCe,EAAAA,cAhDmC;AAiDnCC,EAAAA,kBAjDmC;AAkDnC9C,EAAAA,uBAlDmC;AAmDnCC,EAAAA,2BAnDmC;AAoDnCqE,EAAAA,kBApDmC;AAqDnCC,EAAAA,sBArDmC;AAsDnC9G,EAAAA,aAtDmC;AAuDnCC,EAAAA,iBAvDmC;AAwDnCK,EAAAA,YAxDmC;AAyDnCC,EAAAA,gBAzDmC;AA0DnCwG,EAAAA,iBA1DmC;AA2DnCC,EAAAA,qBA3DmC;AA4DnCtG,EAAAA,aA5DmC;AA6DnCC,EAAAA,iBA7DmC;AA8DnCG,EAAAA,eA9DmC;AA+DnCC,EAAAA,mBA/DmC;AAgEnCwG,EAAAA,YAhEmC;AAiEnCC,EAAAA,gBAjEmC;AAkEnCpG,EAAAA,QAlEmC;AAmEnC6F,EAAAA,cAnEmC;AAoEnCC,EAAAA,kBApEmC;AAqEnCiB,EAAAA,aArEmC;AAsEnCC,EAAAA,iBAtEmC;AAuEnCtD,EAAAA,gBAvEmC;AAwEnCC,EAAAA,oBAxEmC;AAyEnCT,EAAAA,UAzEmC;AA0EnCC,EAAAA,cA1EmC;AA2EnCxB,EAAAA,kBA3EmC;AA4EnCiC,EAAAA,gBA5EmC;AA6EnCC,EAAAA,oBA7EmC;AA8EnC/E,EAAAA,eA9EmC;AA+EnCC,EAAAA,mBA/EmC;AAgFnC+J,EAAAA,0BAhFmC;AAiFnCC,EAAAA,8BAjFmC;AAkFnC3I,EAAAA,aAlFmC;AAmFnCO,EAAAA,gBAnFmC;AAoFnCyC,EAAAA,OApFmC;AAqFnCC,EAAAA,WArFmC;AAsFnCgD,EAAAA,oBAtFmC;AAuFnCE,EAAAA,uBAvFmC;AAwFnCD,EAAAA,wBAxFmC;AAyFnCE,EAAAA,2BAzFmC;AA0FnCP,EAAAA,cA1FmC;AA2FnCC,EAAAA,kBA3FmC;AA4FnC5C,EAAAA,OA5FmC;AA6FnCC,EAAAA,WA7FmC;AA8FnCrF,EAAAA,WA9FmC;AA+FnCC,EAAAA,eA/FmC;AAgGnC2D,EAAAA,aAhGmC;AAiGnCC,EAAAA,iBAjGmC;AAkGnCC,EAAAA,YAlGmC;AAmGnCC,EAAAA,gBAnGmC;AAoGnCR,EAAAA,UApGmC;AAqGnCwI,EAAAA,aArGmC;AAsGnCC,EAAAA,iBAtGmC;AAuGnC/E,EAAAA,MAvGmC;AAwGnCC,EAAAA,UAxGmC;AAyGnCC,EAAAA,MAzGmC;AA0GnCC,EAAAA,UA1GmC;AA2GnCZ,EAAAA,QA3GmC;AA4GnCQ,EAAAA,gBA5GmC;AA6GnCuD,EAAAA,gBA7GmC;AA8GnCE,EAAAA,oBA9GmC;AA+GnCnB,EAAAA,cA/GmC;AAgHnCC,EAAAA,kBAhHmC;AAiHnCR,EAAAA,iBAjHmC;AAkHnCC,EAAAA,qBAlHmC;AAmHnC9H,EAAAA,eAnHmC;AAoHnCC,EAAAA,mBApHmC;AAqHnC6E,EAAAA,UArHmC;AAsHnCC,EAAAA,cAtHmC;AAuHnC+E,EAAAA,qBAvHmC;AAwHnCC,EAAAA,yBAxHmC;AAyHnCC,EAAAA,0BAzHmC;AA0HnCC,EAAAA,8BA1HmC;AA2HnCC,EAAAA,8BA3HmC;AA4HnCC,EAAAA,kCA5HmC;AA6HnCpC,EAAAA,WA7HmC;AA8HnCC,EAAAA,eA9HmC;AA+HnC4B,EAAAA,iBA/HmC;AAgInCC,EAAAA,qBAhImC;AAiInC1E,EAAAA,qBAjImC;AAkInCC,EAAAA,yBAlImC;AAmInCgF,EAAAA,gBAnImC;AAoInCC,EAAAA,oBApImC;AAqInCC,EAAAA,mBArImC;AAsInCC,EAAAA,uBAtImC;AAuInCG,EAAAA,YAvImC;AAwInC1F,EAAAA,QAxImC;AAyInCC,EAAAA,cAzImC;AA0InCC,EAAAA,eA1ImC;AA2InC2D,EAAAA,kBA3ImC;AA4InCC,EAAAA,sBA5ImC;AA6InCG,EAAAA,kBA7ImC;AA8InCC,EAAAA,sBA9ImC;AA+InCT,EAAAA,aA/ImC;AAgJnCC,EAAAA,iBAhJmC;AAiJnCpJ,EAAAA,YAjJmC;AAkJnC2L,EAAAA,eAlJmC;AAmJnCS,EAAAA,oBAnJmC;AAoJnCW,EAAAA,aApJmC;AAqJnCD,EAAAA,mBArJmC;AAsJnCE,EAAAA,mBAtJmC;AAuJnCE,EAAAA,aAvJmC;AAwJnCX,EAAAA,aAxJmC;AAyJnCY,EAAAA,eAzJmC;AA0JnCR,EAAAA,wBA1JmC;AA2JnCC,EAAAA,6BA3JmC;AA4JnCC,EAAAA,iCA5JmC;AA6JnCO,EAAAA,aA7JmC;AA8JnClC,EAAAA,yBA9JmC;AA+JnCC,EAAAA;AA/JmC,CAArC;AAkKA,eAAeqC,UAAf", "sourcesContent": ["import { useCallback, useEffect, useState } from 'react';\nimport { Dimensions, NativeEventEmitter, NativeModules, Platform } from 'react-native';\nimport { useOnEvent, useOnMount } from './internal/asyncHookWrappers';\nimport devicesWithDynamicIsland from \"./internal/devicesWithDynamicIsland\";\nimport devicesWithNotch from './internal/devicesWithNotch';\nimport RNDeviceInfo from './internal/nativeInterface';\nimport {\n  getSupportedPlatformInfoAsync,\n  getSupportedPlatformInfoFunctions,\n  getSupportedPlatformInfoSync,\n} from './internal/supported-platform-info';\nimport { DeviceInfoModule } from './internal/privateTypes';\nimport type {\n  AsyncHookResult,\n  DeviceType,\n  LocationProviderInfo,\n  PowerState,\n} from './internal/types';\n\nexport const [getUniqueId, getUniqueIdSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'uniqueId',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getUniqueId(),\n  syncGetter: () => RNDeviceInfo.getUniqueIdSync(),\n  defaultValue: 'unknown',\n});\n\nlet uniqueId: string;\nexport async function syncUniqueId() {\n  if (Platform.OS === 'ios') {\n    uniqueId = await RNDeviceInfo.syncUniqueId();\n  } else {\n    uniqueId = await getUniqueId();\n  }\n  return uniqueId;\n}\n\nexport const [getInstanceId, getInstanceIdSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'instanceId',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getInstanceId(),\n  syncGetter: () => RNDeviceInfo.getInstanceIdSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getSerialNumber, getSerialNumberSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'serialNumber',\n  supportedPlatforms: ['android', 'windows'],\n  getter: () => RNDeviceInfo.getSerialNumber(),\n  syncGetter: () => RNDeviceInfo.getSerialNumberSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getAndroidId, getAndroidIdSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'androidId',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getAndroidId(),\n  syncGetter: () => RNDeviceInfo.getAndroidIdSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getIpAddress, getIpAddressSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getIpAddress(),\n  syncGetter: () => RNDeviceInfo.getIpAddressSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [isCameraPresent, isCameraPresentSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'windows', 'web'],\n  getter: () => RNDeviceInfo.isCameraPresent(),\n  syncGetter: () => RNDeviceInfo.isCameraPresentSync(),\n  defaultValue: false,\n});\n\nexport async function getMacAddress() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getMacAddress();\n  } else if (Platform.OS === 'ios') {\n    return '02:00:00:00:00:00';\n  }\n  return 'unknown';\n}\n\nexport function getMacAddressSync() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getMacAddressSync();\n  } else if (Platform.OS === 'ios') {\n    return '02:00:00:00:00:00';\n  }\n  return 'unknown';\n}\n\nexport const getDeviceId = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: 'unknown',\n    memoKey: 'deviceId',\n    getter: () => RNDeviceInfo.deviceId,\n    supportedPlatforms: ['android', 'ios', 'windows'],\n  });\n\nexport const [getManufacturer, getManufacturerSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'manufacturer',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () =>\n    Platform.OS == 'ios' ? Promise.resolve('Apple') : RNDeviceInfo.getSystemManufacturer(),\n  syncGetter: () => (Platform.OS == 'ios' ? 'Apple' : RNDeviceInfo.getSystemManufacturerSync()),\n  defaultValue: 'unknown',\n});\n\nexport const getModel = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'model',\n    defaultValue: 'unknown',\n    supportedPlatforms: ['ios', 'android', 'windows'],\n    getter: () => RNDeviceInfo.model,\n  });\n\nexport const getBrand = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'brand',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.brand,\n  });\n\nexport const getSystemName = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: 'unknown',\n    supportedPlatforms: ['ios', 'android', 'windows'],\n    memoKey: 'systemName',\n    getter: () =>\n      Platform.select({\n        ios: RNDeviceInfo.systemName,\n        android: 'Android',\n        windows: 'Windows',\n        default: 'unknown',\n      }),\n  });\n\nexport const getSystemVersion = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.systemVersion,\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    memoKey: 'systemVersion',\n  });\n\nexport const [getBuildId, getBuildIdSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'buildId',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getBuildId(),\n  syncGetter: () => RNDeviceInfo.getBuildIdSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getApiLevel, getApiLevelSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'apiLevel',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getApiLevel(),\n  syncGetter: () => RNDeviceInfo.getApiLevelSync(),\n  defaultValue: -1,\n});\n\nexport const getBundleId = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'bundleId',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.bundleId,\n  });\n\nexport const [\n  getInstallerPackageName,\n  getInstallerPackageNameSync,\n] = getSupportedPlatformInfoFunctions({\n  memoKey: 'installerPackageName',\n  supportedPlatforms: ['android', 'windows', 'ios'],\n  getter: () => RNDeviceInfo.getInstallerPackageName(),\n  syncGetter: () => RNDeviceInfo.getInstallerPackageNameSync(),\n  defaultValue: 'unknown',\n});\n\nexport const getApplicationName = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'appName',\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.appName,\n    supportedPlatforms: ['android', 'ios', 'windows'],\n  });\n\nexport const getBuildNumber = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'buildNumber',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    getter: () => RNDeviceInfo.buildNumber,\n    defaultValue: 'unknown',\n  });\n\nexport const getVersion = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'version',\n    defaultValue: 'unknown',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    getter: () => RNDeviceInfo.appVersion,\n  });\n\nexport function getReadableVersion() {\n  return getVersion() + '.' + getBuildNumber();\n}\n\nexport const [getDeviceName, getDeviceNameSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getDeviceName(),\n  syncGetter: () => RNDeviceInfo.getDeviceNameSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getUsedMemory, getUsedMemorySync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getUsedMemory(),\n  syncGetter: () => RNDeviceInfo.getUsedMemorySync(),\n  defaultValue: -1,\n});\n\nexport const getUserAgent = () =>\n  getSupportedPlatformInfoAsync({\n    memoKey: 'userAgent',\n    defaultValue: 'unknown',\n    supportedPlatforms: ['android', 'ios', 'web'],\n    getter: () => RNDeviceInfo.getUserAgent(),\n  });\n\nexport const getUserAgentSync = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'userAgentSync',\n    defaultValue: 'unknown',\n    supportedPlatforms: ['android', 'web'],\n    getter: () => RNDeviceInfo.getUserAgentSync(),\n  });\n\nexport const [getFontScale, getFontScaleSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getFontScale(),\n  syncGetter: () => RNDeviceInfo.getFontScaleSync(),\n  defaultValue: -1,\n});\n\nexport const [getBootloader, getBootloaderSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'bootloader',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getBootloader(),\n  syncGetter: () => RNDeviceInfo.getBootloaderSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getDevice, getDeviceSync] = getSupportedPlatformInfoFunctions({\n  getter: () => RNDeviceInfo.getDevice(),\n  syncGetter: () => RNDeviceInfo.getDeviceSync(),\n  defaultValue: 'unknown',\n  memoKey: 'device',\n  supportedPlatforms: ['android'],\n});\n\nexport const [getDisplay, getDisplaySync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'display',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getDisplay(),\n  syncGetter: () => RNDeviceInfo.getDisplaySync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getFingerprint, getFingerprintSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'fingerprint',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getFingerprint(),\n  syncGetter: () => RNDeviceInfo.getFingerprintSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getHardware, getHardwareSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'hardware',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getHardware(),\n  syncGetter: () => RNDeviceInfo.getHardwareSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getHost, getHostSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'host',\n  supportedPlatforms: ['android', 'windows'],\n  getter: () => RNDeviceInfo.getHost(),\n  syncGetter: () => RNDeviceInfo.getHostSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getHostNames, getHostNamesSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'hostNames',\n  supportedPlatforms: ['windows'],\n  getter: () => RNDeviceInfo.getHostNames(),\n  syncGetter: () => RNDeviceInfo.getHostNamesSync(),\n  defaultValue: [] as string[],\n});\n\nexport const [getProduct, getProductSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'product',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getProduct(),\n  syncGetter: () => RNDeviceInfo.getProductSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getTags, getTagsSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'tags',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getTags(),\n  syncGetter: () => RNDeviceInfo.getTagsSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getType, getTypeSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'type',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getType(),\n  syncGetter: () => RNDeviceInfo.getTypeSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getBaseOs, getBaseOsSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'baseOs',\n  supportedPlatforms: ['android', 'web', 'windows'],\n  getter: () => RNDeviceInfo.getBaseOs(),\n  syncGetter: () => RNDeviceInfo.getBaseOsSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getPreviewSdkInt, getPreviewSdkIntSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'previewSdkInt',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getPreviewSdkInt(),\n  syncGetter: () => RNDeviceInfo.getPreviewSdkIntSync(),\n  defaultValue: -1,\n});\n\nexport const [getSecurityPatch, getSecurityPatchSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'securityPatch',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getSecurityPatch(),\n  syncGetter: () => RNDeviceInfo.getSecurityPatchSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getCodename, getCodenameSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'codeName',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getCodename(),\n  syncGetter: () => RNDeviceInfo.getCodenameSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getIncremental, getIncrementalSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'incremental',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getIncremental(),\n  syncGetter: () => RNDeviceInfo.getIncrementalSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [isEmulator, isEmulatorSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'emulator',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.isEmulator(),\n  syncGetter: () => RNDeviceInfo.isEmulatorSync(),\n  defaultValue: false,\n});\n\nexport const isTablet = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: false,\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    memoKey: 'tablet',\n    getter: () => RNDeviceInfo.isTablet,\n  });\n\nexport const isLowRamDevice = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: false,\n    supportedPlatforms: ['android'],\n    memoKey: 'lowRam',\n    getter: () => RNDeviceInfo.isLowRamDevice,\n  });\n\nexport const isDisplayZoomed = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: false,\n    supportedPlatforms: ['ios'],\n    memoKey: 'zoomed',\n    getter: () => RNDeviceInfo.isDisplayZoomed,\n  });\n\nexport const [isPinOrFingerprintSet, isPinOrFingerprintSetSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    getter: () => RNDeviceInfo.isPinOrFingerprintSet(),\n    syncGetter: () => RNDeviceInfo.isPinOrFingerprintSetSync(),\n    defaultValue: false,\n  }\n);\n\nlet notch: boolean;\nexport function hasNotch() {\n  if (notch === undefined) {\n    let _brand = getBrand();\n    let _model = getModel();\n    notch =\n      devicesWithNotch.findIndex(\n        (item) =>\n          item.brand.toLowerCase() === _brand.toLowerCase() &&\n          item.model.toLowerCase() === _model.toLowerCase()\n      ) !== -1;\n  }\n  return notch;\n}\n\nlet dynamicIsland: boolean;\nexport function hasDynamicIsland() {\n  if (dynamicIsland === undefined) {\n    let _brand = getBrand();\n    let _model = getModel();\n    dynamicIsland =\n      devicesWithDynamicIsland.findIndex(\n        (item) =>\n          item.brand.toLowerCase() === _brand.toLowerCase() &&\n          item.model.toLowerCase() === _model.toLowerCase()\n      ) !== -1;\n  }\n  return dynamicIsland;\n}\n\nexport const [hasGms, hasGmsSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.hasGms(),\n  syncGetter: () => RNDeviceInfo.hasGmsSync(),\n  defaultValue: false,\n});\n\nexport const [hasHms, hasHmsSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.hasHms(),\n  syncGetter: () => RNDeviceInfo.hasHmsSync(),\n  defaultValue: false,\n});\n\nexport const [getFirstInstallTime, getFirstInstallTimeSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'firstInstallTime',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getFirstInstallTime(),\n  syncGetter: () => RNDeviceInfo.getFirstInstallTimeSync(),\n  defaultValue: -1,\n});\n\nexport const [getInstallReferrer, getInstallReferrerSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'installReferrer',\n  supportedPlatforms: ['android', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getInstallReferrer(),\n  syncGetter: () => RNDeviceInfo.getInstallReferrerSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getLastUpdateTime, getLastUpdateTimeSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'lastUpdateTime',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getLastUpdateTime(),\n  syncGetter: () => RNDeviceInfo.getLastUpdateTimeSync(),\n  defaultValue: -1,\n});\n\nexport const [getPhoneNumber, getPhoneNumberSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getPhoneNumber(),\n  syncGetter: () => RNDeviceInfo.getPhoneNumberSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getCarrier, getCarrierSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios'],\n  getter: () => RNDeviceInfo.getCarrier(),\n  syncGetter: () => RNDeviceInfo.getCarrierSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getTotalMemory, getTotalMemorySync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'totalMemory',\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getTotalMemory(),\n  syncGetter: () => RNDeviceInfo.getTotalMemorySync(),\n  defaultValue: -1,\n});\n\nexport const [getMaxMemory, getMaxMemorySync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'maxMemory',\n  supportedPlatforms: ['android', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getMaxMemory(),\n  syncGetter: () => RNDeviceInfo.getMaxMemorySync(),\n  defaultValue: -1,\n});\n\nexport const [getTotalDiskCapacity, getTotalDiskCapacitySync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getTotalDiskCapacity(),\n  syncGetter: () => RNDeviceInfo.getTotalDiskCapacitySync(),\n  defaultValue: -1,\n});\n\nexport async function getTotalDiskCapacityOld() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getTotalDiskCapacityOld();\n  }\n  if (Platform.OS === 'ios' || Platform.OS === 'windows' || Platform.OS === 'web') {\n    return getTotalDiskCapacity();\n  }\n\n  return -1;\n}\n\nexport function getTotalDiskCapacityOldSync() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getTotalDiskCapacityOldSync();\n  }\n  if (Platform.OS === 'ios' || Platform.OS === 'windows' || Platform.OS === 'web') {\n    return getTotalDiskCapacitySync();\n  }\n\n  return -1;\n}\n\nexport const [getFreeDiskStorage, getFreeDiskStorageSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getFreeDiskStorage(),\n  syncGetter: () => RNDeviceInfo.getFreeDiskStorageSync(),\n  defaultValue: -1,\n});\n\nexport async function getFreeDiskStorageOld() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getFreeDiskStorageOld();\n  }\n  if (Platform.OS === 'ios' || Platform.OS === 'windows' || Platform.OS === 'web') {\n    return getFreeDiskStorage();\n  }\n\n  return -1;\n}\n\nexport function getFreeDiskStorageOldSync() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getFreeDiskStorageOldSync();\n  }\n  if (Platform.OS === 'ios' || Platform.OS === 'windows' || Platform.OS === 'web') {\n    return getFreeDiskStorageSync();\n  }\n\n  return -1;\n}\n\nexport const [getBatteryLevel, getBatteryLevelSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getBatteryLevel(),\n  syncGetter: () => RNDeviceInfo.getBatteryLevelSync(),\n  defaultValue: -1,\n});\n\nexport const [getPowerState, getPowerStateSync] = getSupportedPlatformInfoFunctions<\n  Partial<PowerState>\n>({\n  supportedPlatforms: ['ios', 'android', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getPowerState(),\n  syncGetter: () => RNDeviceInfo.getPowerStateSync(),\n  defaultValue: {},\n});\n\nexport const [isBatteryCharging, isBatteryChargingSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.isBatteryCharging(),\n  syncGetter: () => RNDeviceInfo.isBatteryChargingSync(),\n  defaultValue: false,\n});\n\nexport async function isLandscape() {\n  return Promise.resolve(isLandscapeSync());\n}\n\nexport function isLandscapeSync() {\n  const { height, width } = Dimensions.get('window');\n  return width >= height;\n}\n\nexport const [isAirplaneMode, isAirplaneModeSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'web'],\n  getter: () => RNDeviceInfo.isAirplaneMode(),\n  syncGetter: () => RNDeviceInfo.isAirplaneModeSync(),\n  defaultValue: false,\n});\n\nexport const getDeviceType = () => {\n  return getSupportedPlatformInfoSync({\n    memoKey: 'deviceType',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.deviceType,\n  });\n};\n\nexport const getDeviceTypeSync = () => {\n  return getSupportedPlatformInfoSync({\n    memoKey: 'deviceType',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.deviceType,\n  });\n};\n\nexport const [supportedAbis, supportedAbisSync] = getSupportedPlatformInfoFunctions({\n  memoKey: '_supportedAbis',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getSupportedAbis(),\n  syncGetter: () => RNDeviceInfo.getSupportedAbisSync(),\n  defaultValue: [] as string[],\n});\n\nexport const [supported32BitAbis, supported32BitAbisSync] = getSupportedPlatformInfoFunctions({\n  memoKey: '_supported32BitAbis',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getSupported32BitAbis(),\n  syncGetter: () => RNDeviceInfo.getSupported32BitAbisSync(),\n  defaultValue: [] as string[],\n});\n\nexport const [supported64BitAbis, supported64BitAbisSync] = getSupportedPlatformInfoFunctions({\n  memoKey: '_supported64BitAbis',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getSupported64BitAbis(),\n  syncGetter: () => RNDeviceInfo.getSupported64BitAbisSync(),\n  defaultValue: [],\n});\n\nexport async function hasSystemFeature(feature: string) {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.hasSystemFeature(feature);\n  }\n  return false;\n}\n\nexport function hasSystemFeatureSync(feature: string) {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.hasSystemFeatureSync(feature);\n  }\n  return false;\n}\n\nexport function isLowBatteryLevel(level: number): boolean {\n  if (Platform.OS === 'android') {\n    return level < 0.15;\n  }\n  return level < 0.2;\n}\n\nexport const [\n  getSystemAvailableFeatures,\n  getSystemAvailableFeaturesSync,\n] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getSystemAvailableFeatures(),\n  syncGetter: () => RNDeviceInfo.getSystemAvailableFeaturesSync(),\n  defaultValue: [] as string[],\n});\n\nexport const [isLocationEnabled, isLocationEnabledSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'web'],\n  getter: () => RNDeviceInfo.isLocationEnabled(),\n  syncGetter: () => RNDeviceInfo.isLocationEnabledSync(),\n  defaultValue: false,\n});\n\nexport const [isHeadphonesConnected, isHeadphonesConnectedSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android', 'ios'],\n    getter: () => RNDeviceInfo.isHeadphonesConnected(),\n    syncGetter: () => RNDeviceInfo.isHeadphonesConnectedSync(),\n    defaultValue: false,\n  }\n);\n\nexport const [isWiredHeadphonesConnected, isWiredHeadphonesConnectedSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android', 'ios'],\n    getter: () => RNDeviceInfo.isWiredHeadphonesConnected(),\n    syncGetter: () => RNDeviceInfo.isWiredHeadphonesConnectedSync(),\n    defaultValue: false,\n  }\n);\n\nexport const [isBluetoothHeadphonesConnected, isBluetoothHeadphonesConnectedSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android', 'ios'],\n    getter: () => RNDeviceInfo.isBluetoothHeadphonesConnected(),\n    syncGetter: () => RNDeviceInfo.isBluetoothHeadphonesConnectedSync(),\n    defaultValue: false,\n  }\n);\n\nexport const [isMouseConnected, isMouseConnectedSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['windows'],\n  getter: () => RNDeviceInfo.isMouseConnected(),\n  syncGetter: () => RNDeviceInfo.isMouseConnectedSync(),\n  defaultValue: false,\n});\n\nexport const [isKeyboardConnected, isKeyboardConnectedSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['windows'],\n  getter: () => RNDeviceInfo.isKeyboardConnected(),\n  syncGetter: () => RNDeviceInfo.isKeyboardConnectedSync(),\n  defaultValue: false,\n});\n\nexport const [getSupportedMediaTypeList, getSupportedMediaTypeListSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android'],\n    getter: () => RNDeviceInfo.getSupportedMediaTypeList(),\n    syncGetter: () => RNDeviceInfo.getSupportedMediaTypeListSync(),\n    defaultValue: []\n  }\n)\n\n\nexport const isTabletMode = () =>\n  getSupportedPlatformInfoAsync({\n    supportedPlatforms: ['windows'],\n    getter: () => RNDeviceInfo.isTabletMode(),\n    defaultValue: false,\n  });\n\nexport const [\n  getAvailableLocationProviders,\n  getAvailableLocationProvidersSync,\n] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios'],\n  getter: () => RNDeviceInfo.getAvailableLocationProviders(),\n  syncGetter: () => RNDeviceInfo.getAvailableLocationProvidersSync(),\n  defaultValue: {},\n});\n\nexport const [getBrightness, getBrightnessSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['ios'],\n  getter: () => RNDeviceInfo.getBrightness(),\n  syncGetter: () => RNDeviceInfo.getBrightnessSync(),\n  defaultValue: -1,\n});\n\nexport async function getDeviceToken() {\n  if (Platform.OS === 'ios') {\n    return RNDeviceInfo.getDeviceToken();\n  }\n  return 'unknown';\n}\n\nconst deviceInfoEmitter = new NativeEventEmitter(NativeModules.RNDeviceInfo);\nexport function useBatteryLevel(): number | null {\n  const [batteryLevel, setBatteryLevel] = useState<number | null>(null);\n\n  useEffect(() => {\n    const setInitialValue = async () => {\n      const initialValue: number = await getBatteryLevel();\n      setBatteryLevel(initialValue);\n    };\n\n    const onChange = (level: number) => {\n      setBatteryLevel(level);\n    };\n\n    setInitialValue();\n\n    const subscription = deviceInfoEmitter.addListener(\n      'RNDeviceInfo_batteryLevelDidChange',\n      onChange\n    );\n\n    return () => subscription.remove();\n  }, []);\n\n  return batteryLevel;\n}\n\nexport function useBatteryLevelIsLow(): number | null {\n  const [batteryLevelIsLow, setBatteryLevelIsLow] = useState<number | null>(null);\n\n  useEffect(() => {\n    const setInitialValue = async () => {\n      const initialValue: number = await getBatteryLevel();\n      isLowBatteryLevel(initialValue) && setBatteryLevelIsLow(initialValue);\n    };\n\n    setInitialValue();\n\n    const onChange = (level: number) => {\n      setBatteryLevelIsLow(level);\n    };\n\n    const subscription = deviceInfoEmitter.addListener('RNDeviceInfo_batteryLevelIsLow', onChange);\n\n    return () => subscription.remove();\n  }, []);\n\n  return batteryLevelIsLow;\n}\n\nexport function usePowerState(): Partial<PowerState> {\n  const [powerState, setPowerState] = useState<Partial<PowerState>>({});\n\n  useEffect(() => {\n    const setInitialValue = async () => {\n      const initialValue: Partial<PowerState> = await getPowerState();\n      setPowerState(initialValue);\n    };\n\n    const onChange = (state: PowerState) => {\n      setPowerState(state);\n    };\n\n    setInitialValue();\n\n    const subscription = deviceInfoEmitter.addListener(\n      'RNDeviceInfo_powerStateDidChange',\n      onChange\n    );\n\n    return () => subscription.remove();\n  }, []);\n\n  return powerState;\n}\n\nexport function useIsHeadphonesConnected(): AsyncHookResult<boolean> {\n  return useOnEvent('RNDeviceInfo_headphoneConnectionDidChange', isHeadphonesConnected, false);\n}\n\nexport function useIsWiredHeadphonesConnected(): AsyncHookResult<boolean> {\n  return useOnEvent('RNDeviceInfo_headphoneWiredConnectionDidChange', isWiredHeadphonesConnected, false);\n}\n\nexport function useIsBluetoothHeadphonesConnected(): AsyncHookResult<boolean> {\n  return useOnEvent('RNDeviceInfo_headphoneBluetoothConnectionDidChange', isBluetoothHeadphonesConnected, false);\n}\n\nexport function useFirstInstallTime(): AsyncHookResult<number> {\n  return useOnMount(getFirstInstallTime, -1);\n}\n\nexport function useDeviceName(): AsyncHookResult<string> {\n  return useOnMount(getDeviceName, 'unknown');\n}\n\nexport function useHasSystemFeature(feature: string): AsyncHookResult<boolean> {\n  const asyncGetter = useCallback(() => hasSystemFeature(feature), [feature]);\n  return useOnMount(asyncGetter, false);\n}\n\nexport function useIsEmulator(): AsyncHookResult<boolean> {\n  return useOnMount(isEmulator, false);\n}\n\nexport function useManufacturer(): AsyncHookResult<string> {\n  return useOnMount(getManufacturer, 'unknown');\n}\n\nexport function useBrightness(): number | null {\n  const [brightness, setBrightness] = useState<number | null>(null);\n\n  useEffect(() => {\n    const setInitialValue = async () => {\n      const initialValue: number = await getBrightness();\n      setBrightness(initialValue);\n    };\n\n    const onChange = (value: number) => {\n      setBrightness(value);\n    };\n\n    setInitialValue();\n\n    const subscription = deviceInfoEmitter.addListener(\n      'RNDeviceInfo_brightnessDidChange',\n      onChange\n    );\n\n    return () => subscription.remove();\n  }, []);\n\n  return brightness;\n}\n\nexport type { AsyncHookResult, DeviceType, LocationProviderInfo, PowerState };\n\nconst DeviceInfo: DeviceInfoModule = {\n  getAndroidId,\n  getAndroidIdSync,\n  getApiLevel,\n  getApiLevelSync,\n  getApplicationName,\n  getAvailableLocationProviders,\n  getAvailableLocationProvidersSync,\n  getBaseOs,\n  getBaseOsSync,\n  getBatteryLevel,\n  getBatteryLevelSync,\n  getBootloader,\n  getBootloaderSync,\n  getBrand,\n  getBuildId,\n  getBuildIdSync,\n  getBuildNumber,\n  getBundleId,\n  getCarrier,\n  getCarrierSync,\n  getCodename,\n  getCodenameSync,\n  getDevice,\n  getDeviceId,\n  getDeviceName,\n  getDeviceNameSync,\n  getDeviceSync,\n  getDeviceToken,\n  getDeviceType,\n  getDisplay,\n  getDisplaySync,\n  getFingerprint,\n  getFingerprintSync,\n  getFirstInstallTime,\n  getFirstInstallTimeSync,\n  getFontScale,\n  getFontScaleSync,\n  getFreeDiskStorage,\n  getFreeDiskStorageOld,\n  getFreeDiskStorageSync,\n  getFreeDiskStorageOldSync,\n  getHardware,\n  getHardwareSync,\n  getHost,\n  getHostSync,\n  getHostNames,\n  getHostNamesSync,\n  getIncremental,\n  getIncrementalSync,\n  getInstallerPackageName,\n  getInstallerPackageNameSync,\n  getInstallReferrer,\n  getInstallReferrerSync,\n  getInstanceId,\n  getInstanceIdSync,\n  getIpAddress,\n  getIpAddressSync,\n  getLastUpdateTime,\n  getLastUpdateTimeSync,\n  getMacAddress,\n  getMacAddressSync,\n  getManufacturer,\n  getManufacturerSync,\n  getMaxMemory,\n  getMaxMemorySync,\n  getModel,\n  getPhoneNumber,\n  getPhoneNumberSync,\n  getPowerState,\n  getPowerStateSync,\n  getPreviewSdkInt,\n  getPreviewSdkIntSync,\n  getProduct,\n  getProductSync,\n  getReadableVersion,\n  getSecurityPatch,\n  getSecurityPatchSync,\n  getSerialNumber,\n  getSerialNumberSync,\n  getSystemAvailableFeatures,\n  getSystemAvailableFeaturesSync,\n  getSystemName,\n  getSystemVersion,\n  getTags,\n  getTagsSync,\n  getTotalDiskCapacity,\n  getTotalDiskCapacityOld,\n  getTotalDiskCapacitySync,\n  getTotalDiskCapacityOldSync,\n  getTotalMemory,\n  getTotalMemorySync,\n  getType,\n  getTypeSync,\n  getUniqueId,\n  getUniqueIdSync,\n  getUsedMemory,\n  getUsedMemorySync,\n  getUserAgent,\n  getUserAgentSync,\n  getVersion,\n  getBrightness,\n  getBrightnessSync,\n  hasGms,\n  hasGmsSync,\n  hasHms,\n  hasHmsSync,\n  hasNotch,\n  hasDynamicIsland,\n  hasSystemFeature,\n  hasSystemFeatureSync,\n  isAirplaneMode,\n  isAirplaneModeSync,\n  isBatteryCharging,\n  isBatteryChargingSync,\n  isCameraPresent,\n  isCameraPresentSync,\n  isEmulator,\n  isEmulatorSync,\n  isHeadphonesConnected,\n  isHeadphonesConnectedSync,\n  isWiredHeadphonesConnected,\n  isWiredHeadphonesConnectedSync,\n  isBluetoothHeadphonesConnected,\n  isBluetoothHeadphonesConnectedSync,\n  isLandscape,\n  isLandscapeSync,\n  isLocationEnabled,\n  isLocationEnabledSync,\n  isPinOrFingerprintSet,\n  isPinOrFingerprintSetSync,\n  isMouseConnected,\n  isMouseConnectedSync,\n  isKeyboardConnected,\n  isKeyboardConnectedSync,\n  isTabletMode,\n  isTablet,\n  isLowRamDevice,\n  isDisplayZoomed,\n  supported32BitAbis,\n  supported32BitAbisSync,\n  supported64BitAbis,\n  supported64BitAbisSync,\n  supportedAbis,\n  supportedAbisSync,\n  syncUniqueId,\n  useBatteryLevel,\n  useBatteryLevelIsLow,\n  useDeviceName,\n  useFirstInstallTime,\n  useHasSystemFeature,\n  useIsEmulator,\n  usePowerState,\n  useManufacturer,\n  useIsHeadphonesConnected,\n  useIsWiredHeadphonesConnected,\n  useIsBluetoothHeadphonesConnected,\n  useBrightness,\n  getSupportedMediaTypeList,\n  getSupportedMediaTypeListSync\n};\n\nexport default DeviceInfo;\n"]}