const e=1e8;var t=Object.freeze({__proto__:null,HeapSnapshotProgressEvent:{Update:"ProgressUpdate",BrokenSnapshot:"BrokenSnapshot"},baseSystemDistance:e,baseUnreachableDistance:2e8,AllocationNodeCallers:class{nodesWithSingleCaller;branchingCallers;constructor(e,t){this.nodesWithSingleCaller=e,this.branchingCallers=t}},SerializedAllocationNode:class{id;name;scriptName;scriptId;line;column;count;size;liveCount;liveSize;hasChildren;constructor(e,t,s,i,d,o,n,a,r,c,l){this.id=e,this.name=t,this.scriptName=s,this.scriptId=i,this.line=d,this.column=o,this.count=n,this.size=a,this.liveCount=r,this.liveSize=c,this.hasChildren=l}},AllocationStackFrame:class{functionName;scriptName;scriptId;line;column;constructor(e,t,s,i,d){this.functionName=e,this.scriptName=t,this.scriptId=s,this.line=i,this.column=d}},Node:class{id;name;distance;nodeIndex;retainedSize;selfSize;type;canBeQueried;detachedDOMTreeNode;isAddedNotRemoved;ignored;constructor(e,t,s,i,d,o,n){this.id=e,this.name=t,this.distance=s,this.nodeIndex=i,this.retainedSize=d,this.selfSize=o,this.type=n,this.canBeQueried=!1,this.detachedDOMTreeNode=!1,this.isAddedNotRemoved=null,this.ignored=!1}},Edge:class{name;node;type;edgeIndex;isAddedNotRemoved;constructor(e,t,s,i){this.name=e,this.node=t,this.type=s,this.edgeIndex=i,this.isAddedNotRemoved=null}},Aggregate:class{count;distance;self;maxRet;type;name;idxs;constructor(){}},AggregateForDiff:class{indexes;ids;selfSizes;constructor(){this.indexes=[],this.ids=[],this.selfSizes=[]}},Diff:class{addedCount;removedCount;addedSize;removedSize;deletedIndexes;addedIndexes;countDelta;sizeDelta;constructor(){this.addedCount=0,this.removedCount=0,this.addedSize=0,this.removedSize=0,this.deletedIndexes=[],this.addedIndexes=[]}},DiffForClass:class{addedCount;removedCount;addedSize;removedSize;deletedIndexes;addedIndexes;countDelta;sizeDelta;constructor(){}},ComparatorConfig:class{fieldName1;ascending1;fieldName2;ascending2;constructor(e,t,s,i){this.fieldName1=e,this.ascending1=t,this.fieldName2=s,this.ascending2=i}},WorkerCommand:class{callId;disposition;objectId;newObjectId;methodName;methodArguments;source;constructor(){}},ItemsRange:class{startPosition;endPosition;totalLength;items;constructor(e,t,s,i){this.startPosition=e,this.endPosition=t,this.totalLength=s,this.items=i}},StaticData:class{nodeCount;rootNodeIndex;totalSize;maxJSObjectId;constructor(e,t,s,i){this.nodeCount=e,this.rootNodeIndex=t,this.totalSize=s,this.maxJSObjectId=i}},Statistics:class{total;v8heap;native;code;jsArrays;strings;system;constructor(){}},NodeFilter:class{minNodeId;maxNodeId;allocationNodeId;filterName;constructor(e,t){this.minNodeId=e,this.maxNodeId=t}equals(e){return this.minNodeId===e.minNodeId&&this.maxNodeId===e.maxNodeId&&this.allocationNodeId===e.allocationNodeId&&this.filterName===e.filterName}},SearchConfig:class{query;caseSensitive;isRegex;shouldJump;jumpBackward;constructor(e,t,s,i,d){this.query=e,this.caseSensitive=t,this.isRegex=s,this.shouldJump=i,this.jumpBackward=d}toSearchRegex(e){throw new Error("Unsupported operation on search config")}},Samples:class{timestamps;lastAssignedIds;sizes;constructor(e,t,s){this.timestamps=e,this.lastAssignedIds=t,this.sizes=s}},Location:class{scriptId;lineNumber;columnNumber;constructor(e,t,s){this.scriptId=e,this.lineNumber=t,this.columnNumber=s}}});export{t as HeapSnapshotModel};
