// @flow
// This file is generated automatically by `scripts/build/typings.js`. Please, don't change it.

export type Interval = {
  start: Date | number,
  end: Date | number,
}

export type Locale = {
  code?: string,
  formatDistance?: (...args: Array<any>) => any,
  formatRelative?: (...args: Array<any>) => any,
  localize?: {
    ordinalNumber: (...args: Array<any>) => any,
    era: (...args: Array<any>) => any,
    quarter: (...args: Array<any>) => any,
    month: (...args: Array<any>) => any,
    day: (...args: Array<any>) => any,
    dayPeriod: (...args: Array<any>) => any,
  },
  formatLong?: {
    date: (...args: Array<any>) => any,
    time: (...args: Array<any>) => any,
    dateTime: (...args: Array<any>) => any,
  },
  match?: {
    ordinalNumber: (...args: Array<any>) => any,
    era: (...args: Array<any>) => any,
    quarter: (...args: Array<any>) => any,
    month: (...args: Array<any>) => any,
    day: (...args: Array<any>) => any,
    dayPeriod: (...args: Array<any>) => any,
  },
  options?: {
    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
  },
}

export type Duration = {
  years?: number,
  months?: number,
  weeks?: number,
  days?: number,
  hours?: number,
  minutes?: number,
  seconds?: number,
}

export type Day = 0 | 1 | 2 | 3 | 4 | 5 | 6

declare module.exports: {
  add: (date: Date | number, duration: Duration) => Date,

  addBusinessDays: (date: Date | number, amount: number) => Date,

  addDays: (date: Date | number, amount: number) => Date,

  addHours: (date: Date | number, amount: number) => Date,

  addISOWeekYears: (date: Date | number, amount: number) => Date,

  addMilliseconds: (date: Date | number, amount: number) => Date,

  addMinutes: (date: Date | number, amount: number) => Date,

  addMonths: (date: Date | number, amount: number) => Date,

  addQuarters: (date: Date | number, amount: number) => Date,

  addSeconds: (date: Date | number, amount: number) => Date,

  addWeeks: (date: Date | number, amount: number) => Date,

  addYears: (date: Date | number, amount: number) => Date,

  areIntervalsOverlapping: (
    intervalLeft: Interval,
    intervalRight: Interval,
    options?: {
      inclusive?: boolean,
    }
  ) => boolean,

  clamp: (date: Date | number, interval: Interval) => Date,

  closestIndexTo: (
    dateToCompare: Date | number,
    datesArray: (Date | number)[]
  ) => ?number,

  closestTo: (
    dateToCompare: Date | number,
    datesArray: (Date | number)[]
  ) => ?Date,

  compareAsc: (dateLeft: Date | number, dateRight: Date | number) => number,

  compareDesc: (dateLeft: Date | number, dateRight: Date | number) => number,

  daysToWeeks: (days: number) => number,

  differenceInBusinessDays: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInCalendarDays: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInCalendarISOWeeks: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInCalendarISOWeekYears: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInCalendarMonths: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInCalendarQuarters: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInCalendarWeeks: (
    dateLeft: Date | number,
    dateRight: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => number,

  differenceInCalendarYears: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInDays: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInHours: (
    dateLeft: Date | number,
    dateRight: Date | number,
    options?: {
      roundingMethod?: string,
    }
  ) => number,

  differenceInISOWeekYears: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInMilliseconds: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInMinutes: (
    dateLeft: Date | number,
    dateRight: Date | number,
    options?: {
      roundingMethod?: string,
    }
  ) => number,

  differenceInMonths: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  differenceInQuarters: (
    dateLeft: Date | number,
    dateRight: Date | number,
    options?: {
      roundingMethod?: string,
    }
  ) => number,

  differenceInSeconds: (
    dateLeft: Date | number,
    dateRight: Date | number,
    options?: {
      roundingMethod?: string,
    }
  ) => number,

  differenceInWeeks: (
    dateLeft: Date | number,
    dateRight: Date | number,
    options?: {
      roundingMethod?: string,
    }
  ) => number,

  differenceInYears: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => number,

  eachDayOfInterval: (
    interval: Interval,
    options?: {
      step?: number,
    }
  ) => Date[],

  eachHourOfInterval: (
    interval: Interval,
    options?: {
      step?: number,
    }
  ) => Date[],

  eachMinuteOfInterval: (
    interval: Interval,
    options?: {
      step?: number,
    }
  ) => Date[],

  eachMonthOfInterval: (interval: Interval) => Date[],

  eachQuarterOfInterval: (interval: Interval) => Date[],

  eachWeekendOfInterval: (interval: Interval) => Date[],

  eachWeekendOfMonth: (date: Date | number) => Date[],

  eachWeekendOfYear: (date: Date | number) => Date[],

  eachWeekOfInterval: (
    interval: Interval,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => Date[],

  eachYearOfInterval: (interval: Interval) => Date[],

  endOfDay: (date: Date | number) => Date,

  endOfDecade: (
    date: Date | number,
    options?: {
      additionalDigits?: 0 | 1 | 2,
    }
  ) => Date,

  endOfHour: (date: Date | number) => Date,

  endOfISOWeek: (date: Date | number) => Date,

  endOfISOWeekYear: (date: Date | number) => Date,

  endOfMinute: (date: Date | number) => Date,

  endOfMonth: (date: Date | number) => Date,

  endOfQuarter: (date: Date | number) => Date,

  endOfSecond: (date: Date | number) => Date,

  endOfToday: () => Date,

  endOfTomorrow: () => Date,

  endOfWeek: (
    date: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => Date,

  endOfYear: (date: Date | number) => Date,

  endOfYesterday: () => Date,

  format: (
    date: Date | number,
    format: string,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
      firstWeekContainsDate?: number,
      useAdditionalWeekYearTokens?: boolean,
      useAdditionalDayOfYearTokens?: boolean,
    }
  ) => string,

  formatDistance: (
    date: Date | number,
    baseDate: Date | number,
    options?: {
      includeSeconds?: boolean,
      addSuffix?: boolean,
      locale?: Locale,
    }
  ) => string,

  formatDistanceStrict: (
    date: Date | number,
    baseDate: Date | number,
    options?: {
      addSuffix?: boolean,
      unit?: 'second' | 'minute' | 'hour' | 'day' | 'month' | 'year',
      roundingMethod?: 'floor' | 'ceil' | 'round',
      locale?: Locale,
    }
  ) => string,

  formatDistanceToNow: (
    date: Date | number,
    options?: {
      includeSeconds?: boolean,
      addSuffix?: boolean,
      locale?: Locale,
    }
  ) => string,

  formatDistanceToNowStrict: (
    date: Date | number,
    options?: {
      addSuffix?: boolean,
      unit?: 'second' | 'minute' | 'hour' | 'day' | 'month' | 'year',
      roundingMethod?: 'floor' | 'ceil' | 'round',
      locale?: Locale,
    }
  ) => string,

  formatDuration: (
    duration: Duration,
    options?: {
      format?: string[],
      zero?: boolean,
      delimiter?: string,
      locale?: Locale,
    }
  ) => string,

  formatISO: (
    date: Date | number,
    options?: {
      format?: 'extended' | 'basic',
      representation?: 'complete' | 'date' | 'time',
    }
  ) => string,

  formatISO9075: (
    date: Date | number,
    options?: {
      format?: 'extended' | 'basic',
      representation?: 'complete' | 'date' | 'time',
    }
  ) => string,

  formatISODuration: (duration: Duration) => string,

  formatRelative: (
    date: Date | number,
    baseDate: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => string,

  formatRFC3339: (
    date: Date | number,
    options?: {
      fractionDigits?: 0 | 1 | 2 | 3,
    }
  ) => string,

  formatRFC7231: (date: Date | number) => string,

  fromUnixTime: (unixTime: number) => Date,

  getDate: (date: Date | number) => number,

  getDay: (date: Date | number) => 0 | 1 | 2 | 3 | 4 | 5 | 6,

  getDayOfYear: (date: Date | number) => number,

  getDaysInMonth: (date: Date | number) => number,

  getDaysInYear: (date: Date | number) => number,

  getDecade: (date: Date | number) => number,

  getDefaultOptions: () => Object,

  getHours: (date: Date | number) => number,

  getISODay: (date: Date | number) => number,

  getISOWeek: (date: Date | number) => number,

  getISOWeeksInYear: (date: Date | number) => number,

  getISOWeekYear: (date: Date | number) => number,

  getMilliseconds: (date: Date | number) => number,

  getMinutes: (date: Date | number) => number,

  getMonth: (date: Date | number) => number,

  getOverlappingDaysInIntervals: (
    intervalLeft: Interval,
    intervalRight: Interval
  ) => number,

  getQuarter: (date: Date | number) => number,

  getSeconds: (date: Date | number) => number,

  getTime: (date: Date | number) => number,

  getUnixTime: (date: Date | number) => number,

  getWeek: (
    date: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
      firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
    }
  ) => number,

  getWeekOfMonth: (
    date: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => number,

  getWeeksInMonth: (
    date: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => number,

  getWeekYear: (
    date: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
      firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
    }
  ) => number,

  getYear: (date: Date | number) => number,

  hoursToMilliseconds: (hours: number) => number,

  hoursToMinutes: (hours: number) => number,

  hoursToSeconds: (hours: number) => number,

  intervalToDuration: (interval: Interval) => Duration,

  intlFormat: (
    argument: Date | number,
    formatOptions?: {
      localeMatcher?: 'lookup' | 'best fit',
      weekday?: 'narrow' | 'short' | 'long',
      era?: 'narrow' | 'short' | 'long',
      year?: 'numeric' | '2-digit',
      month?: 'numeric' | '2-digit' | 'narrow' | 'short' | 'long',
      day?: 'numeric' | '2-digit',
      hour?: 'numeric' | '2-digit',
      minute?: 'numeric' | '2-digit',
      second?: 'numeric' | '2-digit',
      timeZoneName?: 'short' | 'long',
      formatMatcher?: 'basic' | 'best fit',
      hour12?: boolean,
      timeZone?: string,
    },
    localeOptions?: {
      locale?: string | string[],
    }
  ) => string,

  intlFormatDistance: (
    date: Date | number,
    baseDate: Date | number,
    options?: {
      unit?: string,
      locale?: string | string[],
      localeMatcher?: string,
      numeric?: string,
      style?: string,
    }
  ) => string,

  isAfter: (date: Date | number, dateToCompare: Date | number) => boolean,

  isBefore: (date: Date | number, dateToCompare: Date | number) => boolean,

  isDate: (value: any) => boolean,

  isEqual: (dateLeft: Date | number, dateRight: Date | number) => boolean,

  isExists: (year: number, month: number, day: number) => boolean,

  isFirstDayOfMonth: (date: Date | number) => boolean,

  isFriday: (date: Date | number) => boolean,

  isFuture: (date: Date | number) => boolean,

  isLastDayOfMonth: (date: Date | number) => boolean,

  isLeapYear: (date: Date | number) => boolean,

  isMatch: (
    dateString: string,
    formatString: string,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
      firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
      useAdditionalWeekYearTokens?: boolean,
      useAdditionalDayOfYearTokens?: boolean,
    }
  ) => boolean,

  isMonday: (date: Date | number) => boolean,

  isPast: (date: Date | number) => boolean,

  isSameDay: (dateLeft: Date | number, dateRight: Date | number) => boolean,

  isSameHour: (dateLeft: Date | number, dateRight: Date | number) => boolean,

  isSameISOWeek: (dateLeft: Date | number, dateRight: Date | number) => boolean,

  isSameISOWeekYear: (
    dateLeft: Date | number,
    dateRight: Date | number
  ) => boolean,

  isSameMinute: (dateLeft: Date | number, dateRight: Date | number) => boolean,

  isSameMonth: (dateLeft: Date | number, dateRight: Date | number) => boolean,

  isSameQuarter: (dateLeft: Date | number, dateRight: Date | number) => boolean,

  isSameSecond: (dateLeft: Date | number, dateRight: Date | number) => boolean,

  isSameWeek: (
    dateLeft: Date | number,
    dateRight: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => boolean,

  isSameYear: (dateLeft: Date | number, dateRight: Date | number) => boolean,

  isSaturday: (date: Date | number) => boolean,

  isSunday: (date: Date | number) => boolean,

  isThisHour: (date: Date | number) => boolean,

  isThisISOWeek: (date: Date | number) => boolean,

  isThisMinute: (date: Date | number) => boolean,

  isThisMonth: (date: Date | number) => boolean,

  isThisQuarter: (date: Date | number) => boolean,

  isThisSecond: (date: Date | number) => boolean,

  isThisWeek: (
    date: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => boolean,

  isThisYear: (date: Date | number) => boolean,

  isThursday: (date: Date | number) => boolean,

  isToday: (date: Date | number) => boolean,

  isTomorrow: (date: Date | number) => boolean,

  isTuesday: (date: Date | number) => boolean,

  isValid: (date: any) => boolean,

  isWednesday: (date: Date | number) => boolean,

  isWeekend: (date: Date | number) => boolean,

  isWithinInterval: (date: Date | number, interval: Interval) => boolean,

  isYesterday: (date: Date | number) => boolean,

  lastDayOfDecade: (date: Date | number) => Date,

  lastDayOfISOWeek: (date: Date | number) => Date,

  lastDayOfISOWeekYear: (date: Date | number) => Date,

  lastDayOfMonth: (date: Date | number) => Date,

  lastDayOfQuarter: (
    date: Date | number,
    options?: {
      additionalDigits?: 0 | 1 | 2,
    }
  ) => Date,

  lastDayOfWeek: (
    date: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => Date,

  lastDayOfYear: (date: Date | number) => Date,

  lightFormat: (date: Date | number, format: string) => string,

  max: (datesArray: (Date | number)[]) => Date,

  milliseconds: (duration: Duration) => number,

  millisecondsToHours: (milliseconds: number) => number,

  millisecondsToMinutes: (milliseconds: number) => number,

  millisecondsToSeconds: (milliseconds: number) => number,

  min: (datesArray: (Date | number)[]) => Date,

  minutesToHours: (minutes: number) => number,

  minutesToMilliseconds: (minutes: number) => number,

  minutesToSeconds: (minutes: number) => number,

  monthsToQuarters: (months: number) => number,

  monthsToYears: (months: number) => number,

  nextDay: (date: Date | number, day: Day) => Date,

  nextFriday: (date: Date | number) => Date,

  nextMonday: (date: Date | number) => Date,

  nextSaturday: (date: Date | number) => Date,

  nextSunday: (date: Date | number) => Date,

  nextThursday: (date: Date | number) => Date,

  nextTuesday: (date: Date | number) => Date,

  nextWednesday: (date: Date | number) => Date,

  parse: (
    dateString: string,
    formatString: string,
    referenceDate: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
      firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
      useAdditionalWeekYearTokens?: boolean,
      useAdditionalDayOfYearTokens?: boolean,
    }
  ) => Date,

  parseISO: (
    argument: string,
    options?: {
      additionalDigits?: 0 | 1 | 2,
    }
  ) => Date,

  parseJSON: (argument: string | number | Date) => Date,

  previousDay: (date: Date | number, day: number) => Date,

  previousFriday: (date: Date | number) => Date,

  previousMonday: (date: Date | number) => Date,

  previousSaturday: (date: Date | number) => Date,

  previousSunday: (date: Date | number) => Date,

  previousThursday: (date: Date | number) => Date,

  previousTuesday: (date: Date | number) => Date,

  previousWednesday: (date: Date | number) => Date,

  quartersToMonths: (quarters: number) => number,

  quartersToYears: (quarters: number) => number,

  roundToNearestMinutes: (
    date: Date | number,
    options?: {
      nearestTo?: number,
      roundingMethod?: string,
    }
  ) => Date,

  secondsToHours: (seconds: number) => number,

  secondsToMilliseconds: (seconds: number) => number,

  secondsToMinutes: (seconds: number) => number,

  set: (
    date: Date | number,
    values: {
      year?: number,
      month?: number,
      date?: number,
      hours?: number,
      minutes?: number,
      seconds?: number,
      milliseconds?: number,
    }
  ) => Date,

  setDate: (date: Date | number, dayOfMonth: number) => Date,

  setDay: (
    date: Date | number,
    day: number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => Date,

  setDayOfYear: (date: Date | number, dayOfYear: number) => Date,

  setDefaultOptions: (newOptions: {
    locale?: Locale,
    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
  }) => void,

  setHours: (date: Date | number, hours: number) => Date,

  setISODay: (date: Date | number, day: number) => Date,

  setISOWeek: (date: Date | number, isoWeek: number) => Date,

  setISOWeekYear: (date: Date | number, isoWeekYear: number) => Date,

  setMilliseconds: (date: Date | number, milliseconds: number) => Date,

  setMinutes: (date: Date | number, minutes: number) => Date,

  setMonth: (date: Date | number, month: number) => Date,

  setQuarter: (date: Date | number, quarter: number) => Date,

  setSeconds: (date: Date | number, seconds: number) => Date,

  setWeek: (
    date: Date | number,
    week: number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
      firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
    }
  ) => Date,

  setWeekYear: (
    date: Date | number,
    weekYear: number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
      firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
    }
  ) => Date,

  setYear: (date: Date | number, year: number) => Date,

  startOfDay: (date: Date | number) => Date,

  startOfDecade: (date: Date | number) => Date,

  startOfHour: (date: Date | number) => Date,

  startOfISOWeek: (date: Date | number) => Date,

  startOfISOWeekYear: (date: Date | number) => Date,

  startOfMinute: (date: Date | number) => Date,

  startOfMonth: (date: Date | number) => Date,

  startOfQuarter: (date: Date | number) => Date,

  startOfSecond: (date: Date | number) => Date,

  startOfToday: () => Date,

  startOfTomorrow: () => Date,

  startOfWeek: (
    date: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
    }
  ) => Date,

  startOfWeekYear: (
    date: Date | number,
    options?: {
      locale?: Locale,
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,
      firstWeekContainsDate?: 1 | 2 | 3 | 4 | 5 | 6 | 7,
    }
  ) => Date,

  startOfYear: (date: Date | number) => Date,

  startOfYesterday: () => Date,

  sub: (date: Date | number, duration: Duration) => Date,

  subBusinessDays: (date: Date | number, amount: number) => Date,

  subDays: (date: Date | number, amount: number) => Date,

  subHours: (date: Date | number, amount: number) => Date,

  subISOWeekYears: (date: Date | number, amount: number) => Date,

  subMilliseconds: (date: Date | number, amount: number) => Date,

  subMinutes: (date: Date | number, amount: number) => Date,

  subMonths: (date: Date | number, amount: number) => Date,

  subQuarters: (date: Date | number, amount: number) => Date,

  subSeconds: (date: Date | number, amount: number) => Date,

  subWeeks: (date: Date | number, amount: number) => Date,

  subYears: (date: Date | number, amount: number) => Date,

  toDate: (argument: Date | number) => Date,

  weeksToDays: (weeks: number) => number,

  yearsToMonths: (years: number) => number,

  yearsToQuarters: (years: number) => number,

  daysInWeek: number,

  daysInYear: number,

  maxTime: number,

  millisecondsInMinute: number,

  millisecondsInHour: number,

  millisecondsInSecond: number,

  minTime: number,

  minutesInHour: number,

  monthsInQuarter: number,

  monthsInYear: number,

  quartersInYear: number,

  secondsInHour: number,

  secondsInMinute: number,

  secondsInDay: number,

  secondsInWeek: number,

  secondsInYear: number,

  secondsInMonth: number,

  secondsInQuarter: number,
}
