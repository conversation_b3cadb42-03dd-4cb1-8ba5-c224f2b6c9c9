{"version": 3, "file": "byteCounter.js", "sourceRoot": "", "sources": ["../src/byteCounter.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,iBAAiB,GAAG,IAAI,CAAC;AAEtC,kDAAkD;AAClD,mDAAmD;AACnD,MAAM,UAAU,kBAAkB,CAAC,KAAa,EAAE,KAAa;IAC7D,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAEtC,gDAAgD;QAChD,IAAI,SAAS,IAAI,MAAM,IAAI,SAAS,GAAG,MAAM,EAAE,CAAC;YAC9C,IAAI,SAAS,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC/C,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAErC,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,MAAM,EAAE,CAAC;oBACpC,KAAK,IAAI,CAAC,CAAC;oBACX,IAAI,KAAK,GAAG,KAAK,EAAE,CAAC;wBAClB,OAAO,IAAI,CAAC;oBACd,CAAC;oBACD,CAAC,EAAE,CAAC;oBACJ,SAAS;gBACX,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,IAAI,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,KAAK,GAAG,KAAK,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,GAAG,KAAK,CAAC;AACvB,CAAC", "sourcesContent": ["export const VALUE_BYTES_LIMIT = 2048;\n\n// note this probably could be JS-engine dependent\n// inspired by https://stackoverflow.com/a/39488643\nexport function byteCountOverLimit(value: string, limit: number): boolean {\n  let bytes = 0;\n\n  for (let i = 0; i < value.length; i++) {\n    const codePoint = value.charCodeAt(i);\n\n    // Lone surrogates cannot be passed to encodeURI\n    if (codePoint >= 0xd800 && codePoint < 0xe000) {\n      if (codePoint < 0xdc00 && i + 1 < value.length) {\n        const next = value.charCodeAt(i + 1);\n\n        if (next >= 0xdc00 && next < 0xe000) {\n          bytes += 4;\n          if (bytes > limit) {\n            return true;\n          }\n          i++;\n          continue;\n        }\n      }\n    }\n\n    bytes += codePoint < 0x80 ? 1 : codePoint < 0x800 ? 2 : 3;\n    if (bytes > limit) {\n      return true;\n    }\n  }\n\n  return bytes > limit;\n}\n"]}