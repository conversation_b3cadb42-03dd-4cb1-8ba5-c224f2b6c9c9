{"version": 3, "sources": ["../../../src/utils/profile.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { env } from './env';\nimport * as Log from '../log';\n\n/**\n * Wrap a method and profile the time it takes to execute the method using `EXPO_PROFILE`.\n * Works best with named functions (i.e. not arrow functions).\n *\n * @param fn function to profile.\n * @param functionName optional name of the function to display in the profile output.\n */\nexport function profile<IArgs extends any[], T extends (...args: IArgs) => any>(\n  fn: T,\n  functionName: string = fn.name\n): T {\n  if (!env.EXPO_PROFILE) {\n    return fn;\n  }\n\n  const name = chalk.dim(`⏱  [profile] ${functionName ?? 'unknown'}`);\n\n  return ((...args: IArgs) => {\n    // Start the timer.\n    Log.time(name);\n\n    // Invoke the method.\n    const results = fn(...args);\n\n    // If non-promise then return as-is.\n    if (!(results instanceof Promise)) {\n      Log.timeEnd(name);\n      return results;\n    }\n\n    // Otherwise await to profile after the promise resolves.\n    return new Promise<Awaited<ReturnType<T>>>((resolve, reject) => {\n      results.then(\n        (results) => {\n          resolve(results);\n          Log.timeEnd(name);\n        },\n        (reason) => {\n          reject(reason);\n          Log.timeEnd(name);\n        }\n      );\n    });\n  }) as T;\n}\n"], "names": ["profile", "fn", "functionName", "name", "env", "EXPO_PROFILE", "chalk", "dim", "args", "Log", "time", "results", "Promise", "timeEnd", "resolve", "reject", "then", "reason"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;;gEAZE;;;;;;qBAEE;6DACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASd,SAASA,QACdC,EAAK,EACLC,eAAuBD,GAAGE,IAAI;IAE9B,IAAI,CAACC,QAAG,CAACC,YAAY,EAAE;QACrB,OAAOJ;IACT;IAEA,MAAME,OAAOG,gBAAK,CAACC,GAAG,CAAC,CAAC,aAAa,EAAEL,gBAAgB,WAAW;IAElE,OAAQ,CAAC,GAAGM;QACV,mBAAmB;QACnBC,KAAIC,IAAI,CAACP;QAET,qBAAqB;QACrB,MAAMQ,UAAUV,MAAMO;QAEtB,oCAAoC;QACpC,IAAI,CAAEG,CAAAA,mBAAmBC,OAAM,GAAI;YACjCH,KAAII,OAAO,CAACV;YACZ,OAAOQ;QACT;QAEA,yDAAyD;QACzD,OAAO,IAAIC,QAAgC,CAACE,SAASC;YACnDJ,QAAQK,IAAI,CACV,CAACL;gBACCG,QAAQH;gBACRF,KAAII,OAAO,CAACV;YACd,GACA,CAACc;gBACCF,OAAOE;gBACPR,KAAII,OAAO,CAACV;YACd;QAEJ;IACF;AACF"}