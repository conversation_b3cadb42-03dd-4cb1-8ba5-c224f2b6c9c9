{"version": 3, "sources": ["../../../src/utils/multipartMixed.ts"], "sourcesContent": ["import { randomBytes } from 'node:crypto';\n\nexport interface FormDataField {\n  name: string;\n  value: string | File | Blob;\n  contentType?: string | null;\n  partHeaders?: Record<string, string> | null;\n}\n\nexport interface EncodedFormData {\n  boundary: string;\n  body: string;\n}\n\nconst CRLF = '\\r\\n';\nconst BOUNDARY_HYPHEN_CHARACTERS = '-'.repeat(2);\n\nconst getFormHeader = (boundary: string, field: FormDataField): string => {\n  let header = `${BOUNDARY_HYPHEN_CHARACTERS}${boundary}${CRLF}`;\n  header += `Content-Disposition: form-data; name=\"${field.name}\"`;\n  if (typeof field.value !== 'string') {\n    header += `; filename=\"${(field.value as File).name ?? 'blob'}\"${CRLF}`;\n    header += `Content-Type: ${field.value.type || 'application/octet-stream'}`;\n  } else if (field.contentType) {\n    header += `${CRLF}Content-Type: ${field.contentType}`;\n  }\n  if (field.partHeaders) {\n    for (const headerName in field.partHeaders) {\n      header += `${CRLF}${headerName}: ${field.partHeaders[headerName]}`;\n    }\n  }\n  return `${header}${CRLF}${CRLF}`;\n};\n\nconst getFormFooter = (boundary: string) =>\n  `${BOUNDARY_HYPHEN_CHARACTERS}${boundary}${BOUNDARY_HYPHEN_CHARACTERS}${CRLF}${CRLF}`;\n\nexport async function encodeMultipartMixed(fields: FormDataField[]): Promise<EncodedFormData> {\n  const boundary = `formdata-${randomBytes(8).toString('hex')}`;\n  let body = '';\n  for (const field of fields) {\n    if (typeof field.value !== 'string') {\n      body += getFormHeader(boundary, field);\n      body += await field.value.text();\n      body += CRLF;\n    } else {\n      body += getFormHeader(boundary, field) + field.value + CRLF;\n    }\n  }\n  body += getFormFooter(boundary);\n  return { boundary, body };\n}\n"], "names": ["encodeMultipartMixed", "CRLF", "BOUNDARY_<PERSON><PERSON><PERSON><PERSON>_CHARACTERS", "repeat", "getFormHeader", "boundary", "field", "header", "name", "value", "type", "contentType", "partHeaders", "headerName", "get<PERSON>orm<PERSON><PERSON><PERSON>", "fields", "randomBytes", "toString", "body", "text"], "mappings": ";;;;+BAqCsBA;;;eAAAA;;;;yBArCM;;;;;;AAc5B,MAAMC,OAAO;AACb,MAAMC,6BAA6B,IAAIC,MAAM,CAAC;AAE9C,MAAMC,gBAAgB,CAACC,UAAkBC;IACvC,IAAIC,SAAS,GAAGL,6BAA6BG,WAAWJ,MAAM;IAC9DM,UAAU,CAAC,sCAAsC,EAAED,MAAME,IAAI,CAAC,CAAC,CAAC;IAChE,IAAI,OAAOF,MAAMG,KAAK,KAAK,UAAU;QACnCF,UAAU,CAAC,YAAY,EAAE,AAACD,MAAMG,KAAK,CAAUD,IAAI,IAAI,OAAO,CAAC,EAAEP,MAAM;QACvEM,UAAU,CAAC,cAAc,EAAED,MAAMG,KAAK,CAACC,IAAI,IAAI,4BAA4B;IAC7E,OAAO,IAAIJ,MAAMK,WAAW,EAAE;QAC5BJ,UAAU,GAAGN,KAAK,cAAc,EAAEK,MAAMK,WAAW,EAAE;IACvD;IACA,IAAIL,MAAMM,WAAW,EAAE;QACrB,IAAK,MAAMC,cAAcP,MAAMM,WAAW,CAAE;YAC1CL,UAAU,GAAGN,OAAOY,WAAW,EAAE,EAAEP,MAAMM,WAAW,CAACC,WAAW,EAAE;QACpE;IACF;IACA,OAAO,GAAGN,SAASN,OAAOA,MAAM;AAClC;AAEA,MAAMa,gBAAgB,CAACT,WACrB,GAAGH,6BAA6BG,WAAWH,6BAA6BD,OAAOA,MAAM;AAEhF,eAAeD,qBAAqBe,MAAuB;IAChE,MAAMV,WAAW,CAAC,SAAS,EAAEW,IAAAA,yBAAW,EAAC,GAAGC,QAAQ,CAAC,QAAQ;IAC7D,IAAIC,OAAO;IACX,KAAK,MAAMZ,SAASS,OAAQ;QAC1B,IAAI,OAAOT,MAAMG,KAAK,KAAK,UAAU;YACnCS,QAAQd,cAAcC,UAAUC;YAChCY,QAAQ,MAAMZ,MAAMG,KAAK,CAACU,IAAI;YAC9BD,QAAQjB;QACV,OAAO;YACLiB,QAAQd,cAAcC,UAAUC,SAASA,MAAMG,KAAK,GAAGR;QACzD;IACF;IACAiB,QAAQJ,cAAcT;IACtB,OAAO;QAAEA;QAAUa;IAAK;AAC1B"}