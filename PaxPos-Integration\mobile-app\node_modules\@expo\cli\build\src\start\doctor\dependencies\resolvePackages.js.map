{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/resolvePackages.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nimport { CommandError } from '../../../utils/errors';\n\nexport async function resolvePackageVersionAsync(\n  projectRoot: string,\n  packageName: string\n): Promise<string> {\n  let packageJsonPath: string | undefined;\n  try {\n    packageJsonPath = resolveFrom(projectRoot, `${packageName}/package.json`);\n  } catch (error: any) {\n    // This is a workaround for packages using `exports`. If this doesn't\n    // include `package.json`, we have to use the error message to get the location.\n    if (error.code === 'ERR_PACKAGE_PATH_NOT_EXPORTED') {\n      packageJsonPath = error.message.match(/(\"exports\"|defined) in (.*)$/i)?.[2];\n    }\n  }\n  if (!packageJsonPath) {\n    throw new CommandError(\n      'PACKAGE_NOT_FOUND',\n      `\"${packageName}\" is added as a dependency in your project's package.json but it doesn't seem to be installed. Run \"npm install\", or the equivalent for your package manager, and try again.`\n    );\n  }\n  const packageJson = await JsonFile.readAsync<{ version: string }>(packageJsonPath);\n  return packageJson.version;\n}\n\nexport async function resolveAllPackageVersionsAsync(projectRoot: string, packages: string[]) {\n  const resolvedPackages = await Promise.all(\n    packages.map(async (packageName) => [\n      packageName,\n      await resolvePackageVersionAsync(projectRoot, packageName),\n    ])\n  );\n\n  return Object.fromEntries(resolvedPackages);\n}\n\n/**\n * Determine if the project has a `expo@canary` version installed.\n * This means that an unsable version is used, without the API knowing the exact packages.\n * Since this may be called during, or before, packages are installed, we also need to test on `package.json`.\n * Note, this returns `false` for beta releases.\n */\nexport async function hasExpoCanaryAsync(projectRoot: string) {\n  let expoVersion = '';\n\n  try {\n    // Resolve installed `expo` version first\n    expoVersion = await resolvePackageVersionAsync(projectRoot, 'expo');\n  } catch (error: any) {\n    if (error.code !== 'PACKAGE_NOT_FOUND') {\n      throw error;\n    }\n\n    // Resolve through project `package.json`\n    const packageJson = await JsonFile.readAsync<{ dependencies?: Record<string, string> }>(\n      resolveFrom(projectRoot, './package.json')\n    );\n    expoVersion = packageJson.dependencies?.expo ?? '';\n  }\n\n  if (expoVersion === 'canary') {\n    return true;\n  }\n\n  const prerelease = semver.prerelease(expoVersion) || [];\n  return !!prerelease.some((segment) => typeof segment === 'string' && segment.includes('canary'));\n}\n"], "names": ["hasExpoCanaryAsync", "resolveAllPackageVersionsAsync", "resolvePackageVersionAsync", "projectRoot", "packageName", "packageJsonPath", "resolveFrom", "error", "code", "message", "match", "CommandError", "packageJson", "JsonFile", "readAsync", "version", "packages", "resolvedPackages", "Promise", "all", "map", "Object", "fromEntries", "expoVersion", "dependencies", "expo", "prerelease", "semver", "some", "segment", "includes"], "mappings": ";;;;;;;;;;;IA+CsBA,kBAAkB;eAAlBA;;IAjBAC,8BAA8B;eAA9BA;;IAxBAC,0BAA0B;eAA1BA;;;;gEAND;;;;;;;gEACG;;;;;;;gEACL;;;;;;wBAEU;;;;;;AAEtB,eAAeA,2BACpBC,WAAmB,EACnBC,WAAmB;IAEnB,IAAIC;IACJ,IAAI;QACFA,kBAAkBC,IAAAA,sBAAW,EAACH,aAAa,GAAGC,YAAY,aAAa,CAAC;IAC1E,EAAE,OAAOG,OAAY;QACnB,qEAAqE;QACrE,gFAAgF;QAChF,IAAIA,MAAMC,IAAI,KAAK,iCAAiC;gBAChCD;YAAlBF,mBAAkBE,uBAAAA,MAAME,OAAO,CAACC,KAAK,CAAC,qDAApBH,oBAAsD,CAAC,EAAE;QAC7E;IACF;IACA,IAAI,CAACF,iBAAiB;QACpB,MAAM,IAAIM,oBAAY,CACpB,qBACA,CAAC,CAAC,EAAEP,YAAY,4KAA4K,CAAC;IAEjM;IACA,MAAMQ,cAAc,MAAMC,mBAAQ,CAACC,SAAS,CAAsBT;IAClE,OAAOO,YAAYG,OAAO;AAC5B;AAEO,eAAed,+BAA+BE,WAAmB,EAAEa,QAAkB;IAC1F,MAAMC,mBAAmB,MAAMC,QAAQC,GAAG,CACxCH,SAASI,GAAG,CAAC,OAAOhB,cAAgB;YAClCA;YACA,MAAMF,2BAA2BC,aAAaC;SAC/C;IAGH,OAAOiB,OAAOC,WAAW,CAACL;AAC5B;AAQO,eAAejB,mBAAmBG,WAAmB;IAC1D,IAAIoB,cAAc;IAElB,IAAI;QACF,yCAAyC;QACzCA,cAAc,MAAMrB,2BAA2BC,aAAa;IAC9D,EAAE,OAAOI,OAAY;YASLK;QARd,IAAIL,MAAMC,IAAI,KAAK,qBAAqB;YACtC,MAAMD;QACR;QAEA,yCAAyC;QACzC,MAAMK,cAAc,MAAMC,mBAAQ,CAACC,SAAS,CAC1CR,IAAAA,sBAAW,EAACH,aAAa;QAE3BoB,cAAcX,EAAAA,4BAAAA,YAAYY,YAAY,qBAAxBZ,0BAA0Ba,IAAI,KAAI;IAClD;IAEA,IAAIF,gBAAgB,UAAU;QAC5B,OAAO;IACT;IAEA,MAAMG,aAAaC,iBAAM,CAACD,UAAU,CAACH,gBAAgB,EAAE;IACvD,OAAO,CAAC,CAACG,WAAWE,IAAI,CAAC,CAACC,UAAY,OAAOA,YAAY,YAAYA,QAAQC,QAAQ,CAAC;AACxF"}