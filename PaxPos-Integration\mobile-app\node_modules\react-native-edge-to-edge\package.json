{"name": "react-native-edge-to-edge", "version": "1.6.0", "license": "MIT", "description": "Effortlessly enable edge-to-edge display in React Native", "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/zoontek/react-native-edge-to-edge", "main": "dist/commonjs/index.js", "module": "dist/module/index.js", "types": "dist/typescript/index.d.ts", "files": ["dist", "src", "app.plugin.js", "android", "!android/build", "!android/.cxx", "!android/.gradle"], "repository": {"type": "git", "url": "https://github.com/zoontek/react-native-edge-to-edge.git"}, "keywords": ["react", "react-native", "edge-to-edge", "status-bar", "navigation-bar", "system-bar", "system-bars"], "scripts": {"clean": "rm -rf dist", "format": "prettier '**/*' -u -w", "typecheck": "tsc --noEmit", "build": "yarn clean && bob build && rm -rf dist/*/package.json", "prepack": "prettier '**/*' -u -c && yarn typecheck && yarn build"}, "react-native-builder-bob": {"source": "src", "output": "dist", "targets": ["commonjs", "module", "typescript"]}, "prettier": {"plugins": ["prettier-plugin-organize-imports"]}, "peerDependencies": {"react": "*", "react-native": "*"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@expo/config-plugins": "^7.0.0 || ^8.0.0 || ^9.0.0", "@types/react": "^19.0.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "react": "19.0.0", "react-native": "0.79.0", "react-native-builder-bob": "^0.40.6", "typescript": "^5.8.3"}, "codegenConfig": {"name": "RNEdgeToEdge", "type": "modules", "jsSrcsDir": "./src/specs", "android": {"javaPackageName": "com.zoontek.rnedgetoedge"}}}