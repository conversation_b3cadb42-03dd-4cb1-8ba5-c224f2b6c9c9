{"version": 3, "file": "FetchResponse.d.ts", "sourceRoot": "", "sources": ["../../../src/winter/fetch/FetchResponse.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAEtD,QAAA,MAAM,sBAAsB,EAAqC,OAAO,cAAc,CAAC;AACvF,MAAM,MAAM,gCAAgC,GAAG,MAAM,IAAI,CAAC;AAI1D,KAAK,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACvE,KAAK,iBAAiB,GAAG,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC;AAE1D;;GAEG;AACH,qBAAa,aAAc,SAAQ,sBAAuB,YAAW,QAAQ;IAI/D,OAAO,CAAC,QAAQ,CAAC,oBAAoB;IAHjD,OAAO,CAAC,cAAc,CAA4C;IAClE,OAAO,CAAC,UAAU,CAA2C;gBAEhC,oBAAoB,EAAE,gCAAgC;IAKnF,IAAI,IAAI,IAAI,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAuD5C;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,IAAI,EAAE,IAAI,OAAO,CAEhB;IAED,SAAgB,IAAI,aAAa;IAEjC;;OAEG;IACG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAKrB,QAAQ,IAAI,OAAO,CAAC,iBAAiB,CAAC;IAYtC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC;IAKpB,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC;IAIlC,QAAQ,IAAI,MAAM;IAIlB,MAAM,IAAI,MAAM;IAShB,KAAK,IAAI,QAAQ;IAIjB,OAAO,CAAC,QAAQ,CAQd;CACH"}