{"version": 3, "sources": ["../../../src/customize/customizeAsync.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\n\nimport { queryAndGenerateAsync, selectAndGenerateAsync } from './generate';\nimport { Options } from './resolveOptions';\nimport { DestinationResolutionProps } from './templates';\nimport { getRouterDirectoryModuleIdWithManifest } from '../start/server/metro/router';\nimport { getPlatformBundlers } from '../start/server/platformBundlers';\nimport { findUpProjectRootOrAssert } from '../utils/findUp';\nimport { setNodeEnv } from '../utils/nodeEnv';\n\nexport async function customizeAsync(files: string[], options: Options, extras: any[]) {\n  setNodeEnv('development');\n  // Locate the project root based on the process current working directory.\n  // This enables users to run `npx expo customize` from a subdirectory of the project.\n  const projectRoot = findUpProjectRootOrAssert(process.cwd());\n\n  require('@expo/env').load(projectRoot);\n\n  // Get the static path (defaults to 'web/')\n  // Doesn't matter if expo is installed or which mode is used.\n  const { exp } = getConfig(projectRoot, {\n    skipSDKVersionRequirement: true,\n  });\n\n  const routerRoot = getRouterDirectoryModuleIdWithManifest(projectRoot, exp);\n\n  // Create the destination resolution props which are used in both\n  // the query and select functions.\n  const props: DestinationResolutionProps = {\n    webStaticPath:\n      (exp.web?.staticPath ?? getPlatformBundlers(projectRoot, exp).web === 'webpack')\n        ? 'web'\n        : 'public',\n    appDirPath: routerRoot,\n  };\n\n  // If the user provided files, we'll generate them without prompting.\n  if (files.length) {\n    return queryAndGenerateAsync(projectRoot, {\n      files,\n      props,\n      extras,\n    });\n  }\n\n  // Otherwise, we'll prompt the user to select which files to generate.\n  await selectAndGenerateAsync(projectRoot, {\n    props,\n    extras,\n  });\n}\n"], "names": ["customizeAsync", "files", "options", "extras", "exp", "setNodeEnv", "projectRoot", "findUpProjectRootOrAssert", "process", "cwd", "require", "load", "getConfig", "skipSDKVersionRequirement", "routerRoot", "getRouterDirectoryModuleIdWithManifest", "props", "webStaticPath", "web", "staticPath", "getPlatformBundlers", "appDirPath", "length", "queryAndGenerateAsync", "selectAndGenerateAsync"], "mappings": ";;;;+BAUsBA;;;eAAAA;;;;yBAVI;;;;;;0BAEoC;wBAGP;kCACnB;wBACM;yBACf;AAEpB,eAAeA,eAAeC,KAAe,EAAEC,OAAgB,EAAEC,MAAa;QAoB9EC;IAnBLC,IAAAA,mBAAU,EAAC;IACX,0EAA0E;IAC1E,qFAAqF;IACrF,MAAMC,cAAcC,IAAAA,iCAAyB,EAACC,QAAQC,GAAG;IAEzDC,QAAQ,aAAaC,IAAI,CAACL;IAE1B,2CAA2C;IAC3C,6DAA6D;IAC7D,MAAM,EAAEF,GAAG,EAAE,GAAGQ,IAAAA,mBAAS,EAACN,aAAa;QACrCO,2BAA2B;IAC7B;IAEA,MAAMC,aAAaC,IAAAA,8CAAsC,EAACT,aAAaF;IAEvE,iEAAiE;IACjE,kCAAkC;IAClC,MAAMY,QAAoC;QACxCC,eACE,AAACb,EAAAA,WAAAA,IAAIc,GAAG,qBAAPd,SAASe,UAAU,KAAIC,IAAAA,qCAAmB,EAACd,aAAaF,KAAKc,GAAG,KAAK,YAClE,QACA;QACNG,YAAYP;IACd;IAEA,qEAAqE;IACrE,IAAIb,MAAMqB,MAAM,EAAE;QAChB,OAAOC,IAAAA,+BAAqB,EAACjB,aAAa;YACxCL;YACAe;YACAb;QACF;IACF;IAEA,sEAAsE;IACtE,MAAMqB,IAAAA,gCAAsB,EAAClB,aAAa;QACxCU;QACAb;IACF;AACF"}