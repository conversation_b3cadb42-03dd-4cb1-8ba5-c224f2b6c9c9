{"version": 3, "sources": ["../../../../../src/start/platforms/android/adbReverse.ts"], "sourcesContent": ["import { assertSdkRoot } from './AndroidSdk';\nimport { adbArgs, Device, getAttachedDevicesAsync, getServer, logUnauthorized } from './adb';\nimport * as Log from '../../../log';\nimport { installExitHooks } from '../../../utils/exit';\n\nconst debug = require('debug')('expo:start:platforms:android:adbReverse') as typeof console.log;\n\nlet removeExitHook: (() => void) | null = null;\n\nexport function hasAdbReverseAsync(): boolean {\n  try {\n    return !!assertSdkRoot();\n  } catch (error: any) {\n    debug('Failed to resolve the Android SDK path, skipping ADB: %s', error.message);\n    return false;\n  }\n}\n\nexport async function startAdbReverseAsync(ports: number[]): Promise<boolean> {\n  // Install cleanup automatically...\n  removeExitHook = installExitHooks(() => {\n    stopAdbReverseAsync(ports);\n  });\n\n  const devices = await getAttachedDevicesAsync();\n  for (const device of devices) {\n    for (const port of ports) {\n      if (!(await adbReverseAsync(device, port))) {\n        debug(`Failed to start reverse port ${port} on device \"${device.name}\"`);\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nexport async function stopAdbReverseAsync(ports: number[]): Promise<void> {\n  removeExitHook?.();\n\n  const devices = await getAttachedDevicesAsync();\n  for (const device of devices) {\n    for (const port of ports) {\n      await adbReverseRemoveAsync(device, port);\n    }\n  }\n}\n\nasync function adbReverseAsync(device: Device, port: number): Promise<boolean> {\n  if (!device.isAuthorized) {\n    logUnauthorized(device);\n    return false;\n  }\n\n  try {\n    await getServer().runAsync(adbArgs(device.pid, 'reverse', `tcp:${port}`, `tcp:${port}`));\n    return true;\n  } catch (error: any) {\n    Log.warn(`[ADB] Couldn't reverse port ${port}: ${error.message}`);\n    return false;\n  }\n}\n\nasync function adbReverseRemoveAsync(device: Device, port: number): Promise<boolean> {\n  if (!device.isAuthorized) {\n    return false;\n  }\n\n  try {\n    await getServer().runAsync(adbArgs(device.pid, 'reverse', '--remove', `tcp:${port}`));\n    return true;\n  } catch (error: any) {\n    // Don't send this to warn because we call this preemptively sometimes\n    debug(`Could not unforward port ${port}: ${error.message}`);\n    return false;\n  }\n}\n"], "names": ["hasAdbReverseAsync", "startAdbReverseAsync", "stopAdbReverseAsync", "debug", "require", "removeExitHook", "assertSdkRoot", "error", "message", "ports", "installExitHooks", "devices", "getAttachedDevicesAsync", "device", "port", "adbReverseAsync", "name", "adbReverseRemoveAsync", "isAuthorized", "logUnauthorized", "getServer", "runAsync", "adbArgs", "pid", "Log", "warn"], "mappings": ";;;;;;;;;;;IASgBA,kBAAkB;eAAlBA;;IASMC,oBAAoB;eAApBA;;IAkBAC,mBAAmB;eAAnBA;;;4BApCQ;qBACuD;6DAChE;sBACY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,IAAIC,iBAAsC;AAEnC,SAASL;IACd,IAAI;QACF,OAAO,CAAC,CAACM,IAAAA,yBAAa;IACxB,EAAE,OAAOC,OAAY;QACnBJ,MAAM,4DAA4DI,MAAMC,OAAO;QAC/E,OAAO;IACT;AACF;AAEO,eAAeP,qBAAqBQ,KAAe;IACxD,mCAAmC;IACnCJ,iBAAiBK,IAAAA,sBAAgB,EAAC;QAChCR,oBAAoBO;IACtB;IAEA,MAAME,UAAU,MAAMC,IAAAA,4BAAuB;IAC7C,KAAK,MAAMC,UAAUF,QAAS;QAC5B,KAAK,MAAMG,QAAQL,MAAO;YACxB,IAAI,CAAE,MAAMM,gBAAgBF,QAAQC,OAAQ;gBAC1CX,MAAM,CAAC,6BAA6B,EAAEW,KAAK,YAAY,EAAED,OAAOG,IAAI,CAAC,CAAC,CAAC;gBACvE,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AAEO,eAAed,oBAAoBO,KAAe;IACvDJ,kCAAAA;IAEA,MAAMM,UAAU,MAAMC,IAAAA,4BAAuB;IAC7C,KAAK,MAAMC,UAAUF,QAAS;QAC5B,KAAK,MAAMG,QAAQL,MAAO;YACxB,MAAMQ,sBAAsBJ,QAAQC;QACtC;IACF;AACF;AAEA,eAAeC,gBAAgBF,MAAc,EAAEC,IAAY;IACzD,IAAI,CAACD,OAAOK,YAAY,EAAE;QACxBC,IAAAA,oBAAe,EAACN;QAChB,OAAO;IACT;IAEA,IAAI;QACF,MAAMO,IAAAA,cAAS,IAAGC,QAAQ,CAACC,IAAAA,YAAO,EAACT,OAAOU,GAAG,EAAE,WAAW,CAAC,IAAI,EAAET,MAAM,EAAE,CAAC,IAAI,EAAEA,MAAM;QACtF,OAAO;IACT,EAAE,OAAOP,OAAY;QACnBiB,KAAIC,IAAI,CAAC,CAAC,4BAA4B,EAAEX,KAAK,EAAE,EAAEP,MAAMC,OAAO,EAAE;QAChE,OAAO;IACT;AACF;AAEA,eAAeS,sBAAsBJ,MAAc,EAAEC,IAAY;IAC/D,IAAI,CAACD,OAAOK,YAAY,EAAE;QACxB,OAAO;IACT;IAEA,IAAI;QACF,MAAME,IAAAA,cAAS,IAAGC,QAAQ,CAACC,IAAAA,YAAO,EAACT,OAAOU,GAAG,EAAE,WAAW,YAAY,CAAC,IAAI,EAAET,MAAM;QACnF,OAAO;IACT,EAAE,OAAOP,OAAY;QACnB,sEAAsE;QACtEJ,MAAM,CAAC,yBAAyB,EAAEW,KAAK,EAAE,EAAEP,MAAMC,OAAO,EAAE;QAC1D,OAAO;IACT;AACF"}