{"version": 3, "file": "PlatformUtils.web.js", "sourceRoot": "", "sources": ["../src/PlatformUtils.web.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,wBAAwB,GAAG,KAAK,CAAC;AAE9C,MAAM,UAAU,cAAc;IAC5B,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,UAAU,YAAY;IAC1B,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,yCAAyC;AACzC,MAAM,CAAC,MAAM,eAAe,GAAG,IAAI,CAAC", "sourcesContent": ["export const IS_ENV_WITH_LOCAL_ASSETS = false;\n\nexport function getLocalAssets(): Record<string, string> {\n  return {};\n}\n\nexport function getManifest2() {\n  return {};\n}\n\n// Compute manifest base URL if available\nexport const manifestBaseUrl = null;\n"]}