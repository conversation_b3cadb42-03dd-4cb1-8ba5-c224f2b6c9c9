{"version": 3, "file": "devtools.js", "sourceRoot": "", "sources": ["../../src/platforms/devtools.ts"], "names": [], "mappings": ";;;;;AAIA,gDAcC;AAED,gFAIC;AAxBD,gDAAwB;AAIjB,KAAK,UAAU,kBAAkB,CACtC,WAAmB,EACnB,QAAyB;IAEzB,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC;IAC1D,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO;QACL,WAAW;QACX,WAAW,EAAE,QAAQ,CAAC,IAAI;QAC1B,WAAW,EAAE,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAc,CAAC,WAAW,CAAC;KAClE,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,kCAAkC,CACtD,kBAA0B;IAE1B,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import path from 'path';\n\nimport type { ExtraDependencies, ModuleDescriptorDevTools, PackageRevision } from '../types';\n\nexport async function resolveModuleAsync(\n  packageName: string,\n  revision: PackageRevision\n): Promise<ModuleDescriptorDevTools | null> {\n  const devtoolsConfig = revision.config?.toJSON().devtools;\n  if (devtoolsConfig == null) {\n    return null;\n  }\n\n  return {\n    packageName,\n    packageRoot: revision.path,\n    webpageRoot: path.join(revision.path, devtoolsConfig.webpageRoot),\n  };\n}\n\nexport async function resolveExtraBuildDependenciesAsync(\n  _projectNativeRoot: string\n): Promise<ExtraDependencies | null> {\n  return null;\n}\n"]}