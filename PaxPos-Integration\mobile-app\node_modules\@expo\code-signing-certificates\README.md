# @expo/code-signing-certificates

A library for working with expo-updates code signing certificates.

[![tests](https://github.com/expo/code-signing-certificates/workflows/tests/badge.svg)](https://github.com/expo/code-signing-certificates/actions?query=workflow%3Atests)
[![codecov](https://codecov.io/gh/expo/code-signing-certificates/branch/main/graph/badge.svg?token=tZvsh5UDLO)](https://codecov.io/gh/expo/code-signing-certificates)
[![npm](https://img.shields.io/npm/v/@expo/code-signing-certificates)](https://www.npmjs.com/package/@expo/code-signing-certificates)
[![NPM](https://img.shields.io/npm/l/@expo/code-signing-certificates)](https://www.npmjs.com/package/@expo/code-signing-certificates)

# Running Scripts

Choose script you want to run from scripts directory and look up the corresponding yarn script in package.json.

1. `yarn`
1. `yarn <yarn-script-name>`