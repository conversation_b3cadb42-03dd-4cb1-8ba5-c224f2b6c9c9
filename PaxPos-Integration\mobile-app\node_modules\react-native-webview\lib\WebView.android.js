var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _defineProperty2=_interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireWildcard(require("react"));var _reactNative=require("react-native");var _BatchedBridge=_interopRequireDefault(require("react-native/Libraries/BatchedBridge/BatchedBridge"));var _EventEmitter=_interopRequireDefault(require("react-native/Libraries/vendor/emitter/EventEmitter"));var _invariant=_interopRequireDefault(require("invariant"));var _RNCWebViewNativeComponent=_interopRequireWildcard(require("./RNCWebViewNativeComponent"));var _NativeRNCWebViewModule=_interopRequireDefault(require("./NativeRNCWebViewModule"));var _WebViewShared=require("./WebViewShared");var _WebView=_interopRequireDefault(require("./WebView.styles"));var _jsxRuntime=require("react/jsx-runtime");var _excluded=["overScrollMode","javaScriptEnabled","thirdPartyCookiesEnabled","scalesPageToFit","allowsFullscreenVideo","allowFileAccess","saveFormDataDisabled","cacheEnabled","androidLayerType","originWhitelist","setSupportMultipleWindows","setBuiltInZoomControls","setDisplayZoomControls","nestedScrollEnabled","startInLoadingState","onNavigationStateChange","onLoadStart","onError","onLoad","onLoadEnd","onLoadProgress","onHttpError","onRenderProcessGone","onMessage","onOpenWindow","renderLoading","renderError","style","containerStyle","source","nativeConfig","onShouldStartLoadWithRequest","injectedJavaScriptObject"],_excluded2=["messagingModuleName"],_excluded3=["messagingModuleName"];var _require$registerCall,_this=this,_jsxFileName="/home/<USER>/code/src/WebView.android.tsx";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&Object.prototype.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}var resolveAssetSource=_reactNative.Image.resolveAssetSource;var directEventEmitter=new _EventEmitter.default();var registerCallableModule=(_require$registerCall=require('react-native').registerCallableModule)!=null?_require$registerCall:_BatchedBridge.default.registerCallableModule.bind(_BatchedBridge.default);registerCallableModule('RNCWebViewMessagingModule',{onShouldStartLoadWithRequest:function onShouldStartLoadWithRequest(event){directEventEmitter.emit('onShouldStartLoadWithRequest',event);},onMessage:function onMessage(event){directEventEmitter.emit('onMessage',event);}});var uniqueRef=0;var WebViewComponent=(0,_react.forwardRef)(function(_ref,ref){var _ref$overScrollMode=_ref.overScrollMode,overScrollMode=_ref$overScrollMode===void 0?'always':_ref$overScrollMode,_ref$javaScriptEnable=_ref.javaScriptEnabled,javaScriptEnabled=_ref$javaScriptEnable===void 0?true:_ref$javaScriptEnable,_ref$thirdPartyCookie=_ref.thirdPartyCookiesEnabled,thirdPartyCookiesEnabled=_ref$thirdPartyCookie===void 0?true:_ref$thirdPartyCookie,_ref$scalesPageToFit=_ref.scalesPageToFit,scalesPageToFit=_ref$scalesPageToFit===void 0?true:_ref$scalesPageToFit,_ref$allowsFullscreen=_ref.allowsFullscreenVideo,allowsFullscreenVideo=_ref$allowsFullscreen===void 0?false:_ref$allowsFullscreen,_ref$allowFileAccess=_ref.allowFileAccess,allowFileAccess=_ref$allowFileAccess===void 0?false:_ref$allowFileAccess,_ref$saveFormDataDisa=_ref.saveFormDataDisabled,saveFormDataDisabled=_ref$saveFormDataDisa===void 0?false:_ref$saveFormDataDisa,_ref$cacheEnabled=_ref.cacheEnabled,cacheEnabled=_ref$cacheEnabled===void 0?true:_ref$cacheEnabled,_ref$androidLayerType=_ref.androidLayerType,androidLayerType=_ref$androidLayerType===void 0?'none':_ref$androidLayerType,_ref$originWhitelist=_ref.originWhitelist,originWhitelist=_ref$originWhitelist===void 0?_WebViewShared.defaultOriginWhitelist:_ref$originWhitelist,_ref$setSupportMultip=_ref.setSupportMultipleWindows,setSupportMultipleWindows=_ref$setSupportMultip===void 0?true:_ref$setSupportMultip,_ref$setBuiltInZoomCo=_ref.setBuiltInZoomControls,setBuiltInZoomControls=_ref$setBuiltInZoomCo===void 0?true:_ref$setBuiltInZoomCo,_ref$setDisplayZoomCo=_ref.setDisplayZoomControls,setDisplayZoomControls=_ref$setDisplayZoomCo===void 0?false:_ref$setDisplayZoomCo,_ref$nestedScrollEnab=_ref.nestedScrollEnabled,nestedScrollEnabled=_ref$nestedScrollEnab===void 0?false:_ref$nestedScrollEnab,startInLoadingState=_ref.startInLoadingState,onNavigationStateChange=_ref.onNavigationStateChange,onLoadStart=_ref.onLoadStart,onError=_ref.onError,onLoad=_ref.onLoad,onLoadEnd=_ref.onLoadEnd,onLoadProgress=_ref.onLoadProgress,onHttpErrorProp=_ref.onHttpError,onRenderProcessGoneProp=_ref.onRenderProcessGone,onMessageProp=_ref.onMessage,onOpenWindowProp=_ref.onOpenWindow,renderLoading=_ref.renderLoading,renderError=_ref.renderError,style=_ref.style,containerStyle=_ref.containerStyle,source=_ref.source,nativeConfig=_ref.nativeConfig,onShouldStartLoadWithRequestProp=_ref.onShouldStartLoadWithRequest,injectedJavaScriptObject=_ref.injectedJavaScriptObject,otherProps=(0,_objectWithoutProperties2.default)(_ref,_excluded);var messagingModuleName=(0,_react.useRef)(`WebViewMessageHandler${uniqueRef+=1}`).current;var webViewRef=(0,_react.useRef)(null);var onShouldStartLoadWithRequestCallback=(0,_react.useCallback)(function(shouldStart,url,lockIdentifier){if(lockIdentifier){_NativeRNCWebViewModule.default.shouldStartLoadWithLockIdentifier(shouldStart,lockIdentifier);}else if(shouldStart&&webViewRef.current){_RNCWebViewNativeComponent.Commands.loadUrl(webViewRef.current,url);}},[]);var _useWebViewLogic=(0,_WebViewShared.useWebViewLogic)({onNavigationStateChange:onNavigationStateChange,onLoad:onLoad,onError:onError,onHttpErrorProp:onHttpErrorProp,onLoadEnd:onLoadEnd,onLoadProgress:onLoadProgress,onLoadStart:onLoadStart,onRenderProcessGoneProp:onRenderProcessGoneProp,onMessageProp:onMessageProp,onOpenWindowProp:onOpenWindowProp,startInLoadingState:startInLoadingState,originWhitelist:originWhitelist,onShouldStartLoadWithRequestProp:onShouldStartLoadWithRequestProp,onShouldStartLoadWithRequestCallback:onShouldStartLoadWithRequestCallback}),onLoadingStart=_useWebViewLogic.onLoadingStart,onShouldStartLoadWithRequest=_useWebViewLogic.onShouldStartLoadWithRequest,onMessage=_useWebViewLogic.onMessage,viewState=_useWebViewLogic.viewState,setViewState=_useWebViewLogic.setViewState,lastErrorEvent=_useWebViewLogic.lastErrorEvent,onHttpError=_useWebViewLogic.onHttpError,onLoadingError=_useWebViewLogic.onLoadingError,onLoadingFinish=_useWebViewLogic.onLoadingFinish,onLoadingProgress=_useWebViewLogic.onLoadingProgress,onOpenWindow=_useWebViewLogic.onOpenWindow,onRenderProcessGone=_useWebViewLogic.onRenderProcessGone;(0,_react.useImperativeHandle)(ref,function(){return{goForward:function goForward(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.goForward(webViewRef.current);},goBack:function goBack(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.goBack(webViewRef.current);},reload:function reload(){setViewState('LOADING');if(webViewRef.current){_RNCWebViewNativeComponent.Commands.reload(webViewRef.current);}},stopLoading:function stopLoading(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.stopLoading(webViewRef.current);},postMessage:function postMessage(data){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.postMessage(webViewRef.current,data);},injectJavaScript:function injectJavaScript(data){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.injectJavaScript(webViewRef.current,data);},requestFocus:function requestFocus(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.requestFocus(webViewRef.current);},clearFormData:function clearFormData(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.clearFormData(webViewRef.current);},clearCache:function clearCache(includeDiskFiles){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.clearCache(webViewRef.current,includeDiskFiles);},clearHistory:function clearHistory(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.clearHistory(webViewRef.current);}};},[setViewState,webViewRef]);(0,_react.useEffect)(function(){var onShouldStartLoadWithRequestSubscription=directEventEmitter.addListener('onShouldStartLoadWithRequest',function(event){if(event.messagingModuleName===messagingModuleName){var _=event.messagingModuleName,rest=(0,_objectWithoutProperties2.default)(event,_excluded2);onShouldStartLoadWithRequest(rest);}});var onMessageSubscription=directEventEmitter.addListener('onMessage',function(event){if(event.messagingModuleName===messagingModuleName){var _=event.messagingModuleName,rest=(0,_objectWithoutProperties2.default)(event,_excluded3);onMessage(rest);}});return function(){onShouldStartLoadWithRequestSubscription.remove();onMessageSubscription.remove();};},[messagingModuleName,onMessage,onShouldStartLoadWithRequest]);var otherView;if(viewState==='LOADING'){otherView=(renderLoading||_WebViewShared.defaultRenderLoading)();}else if(viewState==='ERROR'){(0,_invariant.default)(lastErrorEvent!=null,'lastErrorEvent expected to be non-null');if(lastErrorEvent){otherView=(renderError||_WebViewShared.defaultRenderError)(lastErrorEvent.domain,lastErrorEvent.code,lastErrorEvent.description);}}else if(viewState!=='IDLE'){console.error(`RNCWebView invalid state encountered: ${viewState}`);}var webViewStyles=[_WebView.default.container,_WebView.default.webView,style];var webViewContainerStyle=[_WebView.default.container,containerStyle];if(typeof source!=='number'&&source&&'method'in source){if(source.method==='POST'&&source.headers){console.warn('WebView: `source.headers` is not supported when using POST.');}else if(source.method==='GET'&&source.body){console.warn('WebView: `source.body` is not supported when using GET.');}}var NativeWebView=(nativeConfig==null?void 0:nativeConfig.component)||_RNCWebViewNativeComponent.default;var sourceResolved=resolveAssetSource(source);var newSource=typeof sourceResolved==='object'?Object.entries(sourceResolved).reduce(function(prev,_ref2){var _ref3=(0,_slicedToArray2.default)(_ref2,2),currKey=_ref3[0],currValue=_ref3[1];return Object.assign({},prev,(0,_defineProperty2.default)({},currKey,currKey==='headers'&&currValue&&typeof currValue==='object'?Object.entries(currValue).map(function(_ref4){var _ref5=(0,_slicedToArray2.default)(_ref4,2),key=_ref5[0],value=_ref5[1];return{name:key,value:value};}):currValue));},{}):sourceResolved;var webView=(0,_jsxRuntime.jsx)(NativeWebView,Object.assign({},otherProps,{messagingEnabled:typeof onMessageProp==='function',messagingModuleName:messagingModuleName,hasOnScroll:!!otherProps.onScroll,onLoadingError:onLoadingError,onLoadingFinish:onLoadingFinish,onLoadingProgress:onLoadingProgress,onLoadingStart:onLoadingStart,onHttpError:onHttpError,onRenderProcessGone:onRenderProcessGone,onMessage:onMessage,onOpenWindow:onOpenWindow,hasOnOpenWindowEvent:onOpenWindowProp!==undefined,onShouldStartLoadWithRequest:onShouldStartLoadWithRequest,ref:webViewRef,source:sourceResolved,newSource:newSource,style:webViewStyles,overScrollMode:overScrollMode,javaScriptEnabled:javaScriptEnabled,thirdPartyCookiesEnabled:thirdPartyCookiesEnabled,scalesPageToFit:scalesPageToFit,allowsFullscreenVideo:allowsFullscreenVideo,allowFileAccess:allowFileAccess,saveFormDataDisabled:saveFormDataDisabled,cacheEnabled:cacheEnabled,androidLayerType:androidLayerType,setSupportMultipleWindows:setSupportMultipleWindows,setBuiltInZoomControls:setBuiltInZoomControls,setDisplayZoomControls:setDisplayZoomControls,nestedScrollEnabled:nestedScrollEnabled,injectedJavaScriptObject:JSON.stringify(injectedJavaScriptObject)},nativeConfig==null?void 0:nativeConfig.props),"webViewKey");return(0,_jsxRuntime.jsxs)(_reactNative.View,{style:webViewContainerStyle,children:[webView,otherView]});});var isFileUploadSupported=_NativeRNCWebViewModule.default.isFileUploadSupported;var WebView=Object.assign(WebViewComponent,{isFileUploadSupported:isFileUploadSupported});var _default=exports.default=WebView;