import { DeviceInfoModule } from './internal/privateTypes';
import type { AsyncHookR<PERSON>ult, DeviceType, LocationProviderInfo, PowerState } from './internal/types';
export declare const getUniqueId: import("./internal/privateTypes").Getter<Promise<string>>, getUniqueIdSync: import("./internal/privateTypes").Getter<string>;
export declare function syncUniqueId(): Promise<string>;
export declare const getInstanceId: import("./internal/privateTypes").Getter<Promise<string>>, getInstanceIdSync: import("./internal/privateTypes").Getter<string>;
export declare const getSerialNumber: import("./internal/privateTypes").Getter<Promise<string>>, getSerialNumberSync: import("./internal/privateTypes").Getter<string>;
export declare const getAndroidId: import("./internal/privateTypes").Getter<Promise<string>>, getAndroidIdSync: import("./internal/privateTypes").Getter<string>;
export declare const getIpAddress: import("./internal/privateTypes").Getter<Promise<string>>, getIpAddressSync: import("./internal/privateTypes").Getter<string>;
export declare const isCameraPresent: import("./internal/privateTypes").Getter<Promise<boolean>>, isCameraPresentSync: import("./internal/privateTypes").Getter<boolean>;
export declare function getMacAddress(): Promise<string>;
export declare function getMacAddressSync(): string;
export declare const getDeviceId: () => string;
export declare const getManufacturer: import("./internal/privateTypes").Getter<Promise<string>>, getManufacturerSync: import("./internal/privateTypes").Getter<string>;
export declare const getModel: () => string;
export declare const getBrand: () => string;
export declare const getSystemName: () => string;
export declare const getSystemVersion: () => string;
export declare const getBuildId: import("./internal/privateTypes").Getter<Promise<string>>, getBuildIdSync: import("./internal/privateTypes").Getter<string>;
export declare const getApiLevel: import("./internal/privateTypes").Getter<Promise<number>>, getApiLevelSync: import("./internal/privateTypes").Getter<number>;
export declare const getBundleId: () => string;
export declare const getInstallerPackageName: import("./internal/privateTypes").Getter<Promise<string>>, getInstallerPackageNameSync: import("./internal/privateTypes").Getter<string>;
export declare const getApplicationName: () => string;
export declare const getBuildNumber: () => string;
export declare const getVersion: () => string;
export declare function getReadableVersion(): string;
export declare const getDeviceName: import("./internal/privateTypes").Getter<Promise<string>>, getDeviceNameSync: import("./internal/privateTypes").Getter<string>;
export declare const getUsedMemory: import("./internal/privateTypes").Getter<Promise<number>>, getUsedMemorySync: import("./internal/privateTypes").Getter<number>;
export declare const getUserAgent: () => Promise<string>;
export declare const getUserAgentSync: () => string;
export declare const getFontScale: import("./internal/privateTypes").Getter<Promise<number>>, getFontScaleSync: import("./internal/privateTypes").Getter<number>;
export declare const getBootloader: import("./internal/privateTypes").Getter<Promise<string>>, getBootloaderSync: import("./internal/privateTypes").Getter<string>;
export declare const getDevice: import("./internal/privateTypes").Getter<Promise<string>>, getDeviceSync: import("./internal/privateTypes").Getter<string>;
export declare const getDisplay: import("./internal/privateTypes").Getter<Promise<string>>, getDisplaySync: import("./internal/privateTypes").Getter<string>;
export declare const getFingerprint: import("./internal/privateTypes").Getter<Promise<string>>, getFingerprintSync: import("./internal/privateTypes").Getter<string>;
export declare const getHardware: import("./internal/privateTypes").Getter<Promise<string>>, getHardwareSync: import("./internal/privateTypes").Getter<string>;
export declare const getHost: import("./internal/privateTypes").Getter<Promise<string>>, getHostSync: import("./internal/privateTypes").Getter<string>;
export declare const getHostNames: import("./internal/privateTypes").Getter<Promise<string[]>>, getHostNamesSync: import("./internal/privateTypes").Getter<string[]>;
export declare const getProduct: import("./internal/privateTypes").Getter<Promise<string>>, getProductSync: import("./internal/privateTypes").Getter<string>;
export declare const getTags: import("./internal/privateTypes").Getter<Promise<string>>, getTagsSync: import("./internal/privateTypes").Getter<string>;
export declare const getType: import("./internal/privateTypes").Getter<Promise<string>>, getTypeSync: import("./internal/privateTypes").Getter<string>;
export declare const getBaseOs: import("./internal/privateTypes").Getter<Promise<string>>, getBaseOsSync: import("./internal/privateTypes").Getter<string>;
export declare const getPreviewSdkInt: import("./internal/privateTypes").Getter<Promise<number>>, getPreviewSdkIntSync: import("./internal/privateTypes").Getter<number>;
export declare const getSecurityPatch: import("./internal/privateTypes").Getter<Promise<string>>, getSecurityPatchSync: import("./internal/privateTypes").Getter<string>;
export declare const getCodename: import("./internal/privateTypes").Getter<Promise<string>>, getCodenameSync: import("./internal/privateTypes").Getter<string>;
export declare const getIncremental: import("./internal/privateTypes").Getter<Promise<string>>, getIncrementalSync: import("./internal/privateTypes").Getter<string>;
export declare const isEmulator: import("./internal/privateTypes").Getter<Promise<boolean>>, isEmulatorSync: import("./internal/privateTypes").Getter<boolean>;
export declare const isTablet: () => boolean;
export declare const isLowRamDevice: () => boolean;
export declare const isDisplayZoomed: () => boolean;
export declare const isPinOrFingerprintSet: import("./internal/privateTypes").Getter<Promise<boolean>>, isPinOrFingerprintSetSync: import("./internal/privateTypes").Getter<boolean>;
export declare function hasNotch(): boolean;
export declare function hasDynamicIsland(): boolean;
export declare const hasGms: import("./internal/privateTypes").Getter<Promise<boolean>>, hasGmsSync: import("./internal/privateTypes").Getter<boolean>;
export declare const hasHms: import("./internal/privateTypes").Getter<Promise<boolean>>, hasHmsSync: import("./internal/privateTypes").Getter<boolean>;
export declare const getFirstInstallTime: import("./internal/privateTypes").Getter<Promise<number>>, getFirstInstallTimeSync: import("./internal/privateTypes").Getter<number>;
export declare const getInstallReferrer: import("./internal/privateTypes").Getter<Promise<string>>, getInstallReferrerSync: import("./internal/privateTypes").Getter<string>;
export declare const getLastUpdateTime: import("./internal/privateTypes").Getter<Promise<number>>, getLastUpdateTimeSync: import("./internal/privateTypes").Getter<number>;
export declare const getPhoneNumber: import("./internal/privateTypes").Getter<Promise<string>>, getPhoneNumberSync: import("./internal/privateTypes").Getter<string>;
export declare const getCarrier: import("./internal/privateTypes").Getter<Promise<string>>, getCarrierSync: import("./internal/privateTypes").Getter<string>;
export declare const getTotalMemory: import("./internal/privateTypes").Getter<Promise<number>>, getTotalMemorySync: import("./internal/privateTypes").Getter<number>;
export declare const getMaxMemory: import("./internal/privateTypes").Getter<Promise<number>>, getMaxMemorySync: import("./internal/privateTypes").Getter<number>;
export declare const getTotalDiskCapacity: import("./internal/privateTypes").Getter<Promise<number>>, getTotalDiskCapacitySync: import("./internal/privateTypes").Getter<number>;
export declare function getTotalDiskCapacityOld(): Promise<number>;
export declare function getTotalDiskCapacityOldSync(): number;
export declare const getFreeDiskStorage: import("./internal/privateTypes").Getter<Promise<number>>, getFreeDiskStorageSync: import("./internal/privateTypes").Getter<number>;
export declare function getFreeDiskStorageOld(): Promise<number>;
export declare function getFreeDiskStorageOldSync(): number;
export declare const getBatteryLevel: import("./internal/privateTypes").Getter<Promise<number>>, getBatteryLevelSync: import("./internal/privateTypes").Getter<number>;
export declare const getPowerState: import("./internal/privateTypes").Getter<Promise<Partial<PowerState>>>, getPowerStateSync: import("./internal/privateTypes").Getter<Partial<PowerState>>;
export declare const isBatteryCharging: import("./internal/privateTypes").Getter<Promise<boolean>>, isBatteryChargingSync: import("./internal/privateTypes").Getter<boolean>;
export declare function isLandscape(): Promise<boolean>;
export declare function isLandscapeSync(): boolean;
export declare const isAirplaneMode: import("./internal/privateTypes").Getter<Promise<boolean>>, isAirplaneModeSync: import("./internal/privateTypes").Getter<boolean>;
export declare const getDeviceType: () => string;
export declare const getDeviceTypeSync: () => string;
export declare const supportedAbis: import("./internal/privateTypes").Getter<Promise<string[]>>, supportedAbisSync: import("./internal/privateTypes").Getter<string[]>;
export declare const supported32BitAbis: import("./internal/privateTypes").Getter<Promise<string[]>>, supported32BitAbisSync: import("./internal/privateTypes").Getter<string[]>;
export declare const supported64BitAbis: import("./internal/privateTypes").Getter<Promise<string[]>>, supported64BitAbisSync: import("./internal/privateTypes").Getter<string[]>;
export declare function hasSystemFeature(feature: string): Promise<boolean>;
export declare function hasSystemFeatureSync(feature: string): boolean;
export declare function isLowBatteryLevel(level: number): boolean;
export declare const getSystemAvailableFeatures: import("./internal/privateTypes").Getter<Promise<string[]>>, getSystemAvailableFeaturesSync: import("./internal/privateTypes").Getter<string[]>;
export declare const isLocationEnabled: import("./internal/privateTypes").Getter<Promise<boolean>>, isLocationEnabledSync: import("./internal/privateTypes").Getter<boolean>;
export declare const isHeadphonesConnected: import("./internal/privateTypes").Getter<Promise<boolean>>, isHeadphonesConnectedSync: import("./internal/privateTypes").Getter<boolean>;
export declare const isWiredHeadphonesConnected: import("./internal/privateTypes").Getter<Promise<boolean>>, isWiredHeadphonesConnectedSync: import("./internal/privateTypes").Getter<boolean>;
export declare const isBluetoothHeadphonesConnected: import("./internal/privateTypes").Getter<Promise<boolean>>, isBluetoothHeadphonesConnectedSync: import("./internal/privateTypes").Getter<boolean>;
export declare const isMouseConnected: import("./internal/privateTypes").Getter<Promise<boolean>>, isMouseConnectedSync: import("./internal/privateTypes").Getter<boolean>;
export declare const isKeyboardConnected: import("./internal/privateTypes").Getter<Promise<boolean>>, isKeyboardConnectedSync: import("./internal/privateTypes").Getter<boolean>;
export declare const getSupportedMediaTypeList: import("./internal/privateTypes").Getter<Promise<string[]>>, getSupportedMediaTypeListSync: import("./internal/privateTypes").Getter<string[]>;
export declare const isTabletMode: () => Promise<boolean>;
export declare const getAvailableLocationProviders: import("./internal/privateTypes").Getter<Promise<LocationProviderInfo>>, getAvailableLocationProvidersSync: import("./internal/privateTypes").Getter<LocationProviderInfo>;
export declare const getBrightness: import("./internal/privateTypes").Getter<Promise<number>>, getBrightnessSync: import("./internal/privateTypes").Getter<number>;
export declare function getDeviceToken(): Promise<string>;
export declare function useBatteryLevel(): number | null;
export declare function useBatteryLevelIsLow(): number | null;
export declare function usePowerState(): Partial<PowerState>;
export declare function useIsHeadphonesConnected(): AsyncHookResult<boolean>;
export declare function useIsWiredHeadphonesConnected(): AsyncHookResult<boolean>;
export declare function useIsBluetoothHeadphonesConnected(): AsyncHookResult<boolean>;
export declare function useFirstInstallTime(): AsyncHookResult<number>;
export declare function useDeviceName(): AsyncHookResult<string>;
export declare function useHasSystemFeature(feature: string): AsyncHookResult<boolean>;
export declare function useIsEmulator(): AsyncHookResult<boolean>;
export declare function useManufacturer(): AsyncHookResult<string>;
export declare function useBrightness(): number | null;
export type { AsyncHookResult, DeviceType, LocationProviderInfo, PowerState };
declare const DeviceInfo: DeviceInfoModule;
export default DeviceInfo;
