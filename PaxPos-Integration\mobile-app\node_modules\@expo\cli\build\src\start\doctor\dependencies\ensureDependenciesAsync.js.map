{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/ensureDependenciesAsync.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport wrapAnsi from 'wrap-ansi';\n\nimport { getMissingPackagesAsync, ResolvedPackage } from './getMissingPackages';\nimport { installAsync } from '../../../install/installAsync';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { isInteractive } from '../../../utils/interactive';\nimport { logNewSection } from '../../../utils/ora';\nimport { confirmAsync } from '../../../utils/prompts';\n\nexport type EnsureDependenciesOptions = {\n  /** The packages and/or version ranges that should be enforced in the project */\n  requiredPackages: ResolvedPackage[];\n  /** The user-facing message when the required packages are missing or incorrect */\n  installMessage: string;\n  /** The user-facing message when users aborted the installation */\n  warningMessage: string;\n  /** A previously loaded Expo configuration (loads when omitted) */\n  exp?: ExpoConfig;\n  /** If the prompts asking users to install should be skipped (defaults to false, in CI defaults to true) */\n  skipPrompt?: boolean;\n  /** Project can be mutated in the current environment (defaults to true, in CI defaults to false) */\n  isProjectMutable?: boolean;\n};\n\nexport async function ensureDependenciesAsync(\n  projectRoot: string,\n  {\n    exp = getConfig(projectRoot).exp,\n    requiredPackages,\n    warningMessage,\n    installMessage,\n    // Don't prompt in CI\n    skipPrompt = !isInteractive(),\n    isProjectMutable = isInteractive(),\n  }: EnsureDependenciesOptions\n): Promise<boolean> {\n  const { missing } = await getMissingPackagesAsync(projectRoot, {\n    sdkVersion: exp.sdkVersion,\n    requiredPackages,\n  });\n  if (!missing.length) {\n    return true;\n  }\n\n  // Prompt to install or bail out...\n  const readableMissingPackages = missing\n    .map(({ pkg, version }) => (version ? [pkg, version].join('@') : pkg))\n    .join(', ');\n\n  let title = installMessage;\n\n  if (skipPrompt && !isProjectMutable) {\n    title += '\\n\\n';\n  } else {\n    let confirm = skipPrompt;\n    if (skipPrompt) {\n      // Automatically install packages without prompting.\n      Log.log(wrapForTerminal(title + ` Installing ${chalk.cyan(readableMissingPackages)}`));\n    } else {\n      confirm = await confirmAsync({\n        message: wrapForTerminal(\n          title + ` Would you like to install ${chalk.cyan(readableMissingPackages)}?`\n        ),\n        initial: true,\n      });\n    }\n\n    if (confirm) {\n      // Format with version if available.\n      const [packages, devPackages] = missing.reduce(\n        ([deps, devDeps], p) => {\n          const pkg = p.version ? [p.pkg, p.version].join('@') : p.pkg;\n          if (p.dev) {\n            return [deps, devDeps.concat(pkg)];\n          }\n          return [deps.concat(pkg), devDeps];\n        },\n        [[], []] as [string[], string[]]\n      );\n\n      if (packages.length) {\n        await installPackagesAsync(projectRoot, {\n          packages,\n        });\n      }\n\n      if (devPackages.length) {\n        await installPackagesAsync(projectRoot, {\n          packages: devPackages,\n          dev: true,\n        });\n      }\n\n      // Try again but skip prompting twice, simply fail if the packages didn't install correctly.\n      return await ensureDependenciesAsync(projectRoot, {\n        skipPrompt: true,\n        installMessage,\n        warningMessage,\n        requiredPackages,\n      });\n    }\n\n    // Reset the title so it doesn't print twice in interactive mode.\n    title = '';\n  }\n\n  const installCommand = 'npx expo install ' + missing.map(({ pkg }) => pkg).join(' ');\n\n  const disableMessage = warningMessage;\n\n  const solution = `Install ${chalk.bold(\n    readableMissingPackages\n  )} by running:\\n\\n  ${chalk.reset.bold(installCommand)}\\n\\n`;\n\n  // This prevents users from starting a misconfigured JS or TS project by default.\n  throw new CommandError(wrapForTerminal(title + solution + disableMessage + '\\n'));\n}\n\n/**  Wrap long messages to fit smaller terminals. */\nfunction wrapForTerminal(message: string): string {\n  return wrapAnsi(message, process.stdout.columns || 80);\n}\n\n/** Create the bash install command from a given set of packages and settings. */\nexport function createInstallCommand({\n  packages,\n}: {\n  packages: {\n    file: string;\n    pkg: string;\n    version?: string | undefined;\n  }[];\n}) {\n  return 'npx expo install ' + packages.map(({ pkg }) => pkg).join(' ');\n}\n\n/** Install packages in the project. */\nasync function installPackagesAsync(\n  projectRoot: string,\n  { packages, dev }: { packages: string[]; dev?: boolean }\n) {\n  const packagesStr = chalk.bold(packages.join(', '));\n  Log.log();\n  const installingPackageStep = logNewSection(`Installing ${packagesStr}`);\n  try {\n    await installAsync(packages, { projectRoot, dev });\n  } catch (e: any) {\n    installingPackageStep.fail(`Failed to install ${packagesStr} with error: ${e.message}`);\n    throw e;\n  }\n  installingPackageStep.succeed(`Installed ${packagesStr}`);\n}\n"], "names": ["createInstallCommand", "ensureDependenciesAsync", "projectRoot", "exp", "getConfig", "requiredPackages", "warningMessage", "installMessage", "skip<PERSON>rompt", "isInteractive", "isProjectMutable", "missing", "getMissingPackagesAsync", "sdkVersion", "length", "readableMissingPackages", "map", "pkg", "version", "join", "title", "confirm", "Log", "log", "wrapForTerminal", "chalk", "cyan", "<PERSON><PERSON><PERSON>", "message", "initial", "packages", "devPackages", "reduce", "deps", "devDeps", "p", "dev", "concat", "installPackagesAsync", "installCommand", "disableMessage", "solution", "bold", "reset", "CommandError", "wrapAnsi", "process", "stdout", "columns", "packagesStr", "installingPackageStep", "logNewSection", "installAsync", "e", "fail", "succeed"], "mappings": ";;;;;;;;;;;IA+HgBA,oBAAoB;eAApBA;;IApGMC,uBAAuB;eAAvBA;;;;yBA3BgB;;;;;;;gEACpB;;;;;;;gEACG;;;;;;oCAEoC;8BAC5B;6DACR;wBACQ;6BACC;qBACA;yBACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBtB,eAAeA,wBACpBC,WAAmB,EACnB,EACEC,MAAMC,IAAAA,mBAAS,EAACF,aAAaC,GAAG,EAChCE,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACd,qBAAqB;AACrBC,aAAa,CAACC,IAAAA,0BAAa,GAAE,EAC7BC,mBAAmBD,IAAAA,0BAAa,GAAE,EACR;IAE5B,MAAM,EAAEE,OAAO,EAAE,GAAG,MAAMC,IAAAA,2CAAuB,EAACV,aAAa;QAC7DW,YAAYV,IAAIU,UAAU;QAC1BR;IACF;IACA,IAAI,CAACM,QAAQG,MAAM,EAAE;QACnB,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAMC,0BAA0BJ,QAC7BK,GAAG,CAAC,CAAC,EAAEC,GAAG,EAAEC,OAAO,EAAE,GAAMA,UAAU;YAACD;YAAKC;SAAQ,CAACC,IAAI,CAAC,OAAOF,KAChEE,IAAI,CAAC;IAER,IAAIC,QAAQb;IAEZ,IAAIC,cAAc,CAACE,kBAAkB;QACnCU,SAAS;IACX,OAAO;QACL,IAAIC,UAAUb;QACd,IAAIA,YAAY;YACd,oDAAoD;YACpDc,KAAIC,GAAG,CAACC,gBAAgBJ,QAAQ,CAAC,YAAY,EAAEK,gBAAK,CAACC,IAAI,CAACX,0BAA0B;QACtF,OAAO;YACLM,UAAU,MAAMM,IAAAA,qBAAY,EAAC;gBAC3BC,SAASJ,gBACPJ,QAAQ,CAAC,2BAA2B,EAAEK,gBAAK,CAACC,IAAI,CAACX,yBAAyB,CAAC,CAAC;gBAE9Ec,SAAS;YACX;QACF;QAEA,IAAIR,SAAS;YACX,oCAAoC;YACpC,MAAM,CAACS,UAAUC,YAAY,GAAGpB,QAAQqB,MAAM,CAC5C,CAAC,CAACC,MAAMC,QAAQ,EAAEC;gBAChB,MAAMlB,MAAMkB,EAAEjB,OAAO,GAAG;oBAACiB,EAAElB,GAAG;oBAAEkB,EAAEjB,OAAO;iBAAC,CAACC,IAAI,CAAC,OAAOgB,EAAElB,GAAG;gBAC5D,IAAIkB,EAAEC,GAAG,EAAE;oBACT,OAAO;wBAACH;wBAAMC,QAAQG,MAAM,CAACpB;qBAAK;gBACpC;gBACA,OAAO;oBAACgB,KAAKI,MAAM,CAACpB;oBAAMiB;iBAAQ;YACpC,GACA;gBAAC,EAAE;gBAAE,EAAE;aAAC;YAGV,IAAIJ,SAAShB,MAAM,EAAE;gBACnB,MAAMwB,qBAAqBpC,aAAa;oBACtC4B;gBACF;YACF;YAEA,IAAIC,YAAYjB,MAAM,EAAE;gBACtB,MAAMwB,qBAAqBpC,aAAa;oBACtC4B,UAAUC;oBACVK,KAAK;gBACP;YACF;YAEA,4FAA4F;YAC5F,OAAO,MAAMnC,wBAAwBC,aAAa;gBAChDM,YAAY;gBACZD;gBACAD;gBACAD;YACF;QACF;QAEA,iEAAiE;QACjEe,QAAQ;IACV;IAEA,MAAMmB,iBAAiB,sBAAsB5B,QAAQK,GAAG,CAAC,CAAC,EAAEC,GAAG,EAAE,GAAKA,KAAKE,IAAI,CAAC;IAEhF,MAAMqB,iBAAiBlC;IAEvB,MAAMmC,WAAW,CAAC,QAAQ,EAAEhB,gBAAK,CAACiB,IAAI,CACpC3B,yBACA,kBAAkB,EAAEU,gBAAK,CAACkB,KAAK,CAACD,IAAI,CAACH,gBAAgB,IAAI,CAAC;IAE5D,iFAAiF;IACjF,MAAM,IAAIK,oBAAY,CAACpB,gBAAgBJ,QAAQqB,WAAWD,iBAAiB;AAC7E;AAEA,kDAAkD,GAClD,SAAShB,gBAAgBI,OAAe;IACtC,OAAOiB,IAAAA,mBAAQ,EAACjB,SAASkB,QAAQC,MAAM,CAACC,OAAO,IAAI;AACrD;AAGO,SAAShD,qBAAqB,EACnC8B,QAAQ,EAOT;IACC,OAAO,sBAAsBA,SAASd,GAAG,CAAC,CAAC,EAAEC,GAAG,EAAE,GAAKA,KAAKE,IAAI,CAAC;AACnE;AAEA,qCAAqC,GACrC,eAAemB,qBACbpC,WAAmB,EACnB,EAAE4B,QAAQ,EAAEM,GAAG,EAAyC;IAExD,MAAMa,cAAcxB,gBAAK,CAACiB,IAAI,CAACZ,SAASX,IAAI,CAAC;IAC7CG,KAAIC,GAAG;IACP,MAAM2B,wBAAwBC,IAAAA,kBAAa,EAAC,CAAC,WAAW,EAAEF,aAAa;IACvE,IAAI;QACF,MAAMG,IAAAA,0BAAY,EAACtB,UAAU;YAAE5B;YAAakC;QAAI;IAClD,EAAE,OAAOiB,GAAQ;QACfH,sBAAsBI,IAAI,CAAC,CAAC,kBAAkB,EAAEL,YAAY,aAAa,EAAEI,EAAEzB,OAAO,EAAE;QACtF,MAAMyB;IACR;IACAH,sBAAsBK,OAAO,CAAC,CAAC,UAAU,EAAEN,aAAa;AAC1D"}