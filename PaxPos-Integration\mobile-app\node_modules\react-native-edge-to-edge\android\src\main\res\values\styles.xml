<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.EdgeToEdge.DayNight.Common" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">false</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">@bool/windowLightSystemBars</item>
    </style>

    <style name="Theme.EdgeToEdge.Material2.DayNight.Common" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">false</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">@bool/windowLightSystemBars</item>
    </style>

    <style name="Theme.EdgeToEdge.Material3.DayNight.Common" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">false</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">@bool/windowLightSystemBars</item>
    </style>

    <style name="Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common" parent="Theme.Material3.DynamicColors.DayNight.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">false</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">@bool/windowLightSystemBars</item>
    </style>

    <style name="Theme.EdgeToEdge.Light.Common" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">true</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <style name="Theme.EdgeToEdge.Material2.Light.Common" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">true</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <style name="Theme.EdgeToEdge.Material3.Light.Common" parent="Theme.Material3.Light.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">true</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <style name="Theme.EdgeToEdge.Material3.Dynamic.Light.Common" parent="Theme.Material3.DynamicColors.Light.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">true</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <style name="Theme.EdgeToEdge" parent="Theme.EdgeToEdge.DayNight.Common" />
    <style name="Theme.EdgeToEdge.Material2" parent="Theme.EdgeToEdge.Material2.DayNight.Common" />
    <style name="Theme.EdgeToEdge.Material3" parent="Theme.EdgeToEdge.Material3.DayNight.Common" />
    <style name="Theme.EdgeToEdge.Material3.Dynamic" parent="Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common" />

    <style name="Theme.EdgeToEdge.Light" parent="Theme.EdgeToEdge.Light.Common" />
    <style name="Theme.EdgeToEdge.Material2.Light" parent="Theme.EdgeToEdge.Material2.Light.Common" />
    <style name="Theme.EdgeToEdge.Material3.Light" parent="Theme.EdgeToEdge.Material3.Light.Common" />
    <style name="Theme.EdgeToEdge.Material3.Dynamic.Light" parent="Theme.EdgeToEdge.Material3.Dynamic.Light.Common" />
</resources>
