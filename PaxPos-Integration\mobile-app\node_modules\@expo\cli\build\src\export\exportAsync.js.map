{"version": 3, "sources": ["../../../src/export/exportAsync.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport path from 'path';\n\nimport { exportAppAsync } from './exportApp';\nimport { Options } from './resolveOptions';\nimport * as Log from '../log';\nimport { waitUntilAtlasExportIsReadyAsync } from '../start/server/metro/debugging/attachAtlas';\nimport { FileNotifier } from '../utils/FileNotifier';\nimport { ensureDirectoryAsync, removeAsync } from '../utils/dir';\nimport { CommandError } from '../utils/errors';\nimport { ensureProcessExitsAfterDelay } from '../utils/exit';\n\nexport async function exportAsync(projectRoot: string, options: Options) {\n  // Ensure the output directory is created\n  const outputPath = path.resolve(projectRoot, options.outputDir);\n\n  if (outputPath === projectRoot) {\n    throw new CommandError('--output-dir cannot be the same as the project directory.');\n  } else if (path.relative(projectRoot, outputPath).startsWith('..')) {\n    throw new CommandError(\n      '--output-dir must be a subdirectory of the project directory. Generating outside of the project directory is not supported.'\n    );\n  }\n\n  // Delete the output directory if it exists\n  await removeAsync(outputPath);\n  // Create the output directory\n  await ensureDirectoryAsync(outputPath);\n\n  // Export the app\n  await exportAppAsync(projectRoot, options);\n\n  // Stop any file watchers to prevent the CLI from hanging.\n  FileNotifier.stopAll();\n  // Wait until Atlas is ready, when enabled\n  // NOTE(cedric): this is a workaround, remove when `process.exit` is removed\n  await waitUntilAtlasExportIsReadyAsync(projectRoot);\n\n  // Final notes\n  Log.log(chalk.greenBright`Exported: ${options.outputDir}`);\n\n  // Exit the process to stop any hanging processes from reading the app.config.js or server rendering.\n  ensureProcessExitsAfterDelay();\n}\n"], "names": ["exportAsync", "projectRoot", "options", "outputPath", "path", "resolve", "outputDir", "CommandError", "relative", "startsWith", "removeAsync", "ensureDirectoryAsync", "exportAppAsync", "FileNotifier", "stopAll", "waitUntilAtlasExportIsReadyAsync", "Log", "log", "chalk", "<PERSON><PERSON><PERSON>", "ensureProcessExitsAfterDelay"], "mappings": ";;;;+BAYsBA;;;eAAAA;;;;gEAZJ;;;;;;;gEACD;;;;;;2BAEc;6DAEV;6BAC4B;8BACpB;qBACqB;wBACrB;sBACgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtC,eAAeA,YAAYC,WAAmB,EAAEC,OAAgB;IACrE,yCAAyC;IACzC,MAAMC,aAAaC,eAAI,CAACC,OAAO,CAACJ,aAAaC,QAAQI,SAAS;IAE9D,IAAIH,eAAeF,aAAa;QAC9B,MAAM,IAAIM,oBAAY,CAAC;IACzB,OAAO,IAAIH,eAAI,CAACI,QAAQ,CAACP,aAAaE,YAAYM,UAAU,CAAC,OAAO;QAClE,MAAM,IAAIF,oBAAY,CACpB;IAEJ;IAEA,2CAA2C;IAC3C,MAAMG,IAAAA,gBAAW,EAACP;IAClB,8BAA8B;IAC9B,MAAMQ,IAAAA,yBAAoB,EAACR;IAE3B,iBAAiB;IACjB,MAAMS,IAAAA,yBAAc,EAACX,aAAaC;IAElC,0DAA0D;IAC1DW,0BAAY,CAACC,OAAO;IACpB,0CAA0C;IAC1C,4EAA4E;IAC5E,MAAMC,IAAAA,6CAAgC,EAACd;IAEvC,cAAc;IACde,KAAIC,GAAG,CAACC,gBAAK,CAACC,WAAW,CAAC,UAAU,EAAEjB,QAAQI,SAAS,CAAC,CAAC;IAEzD,qGAAqG;IACrGc,IAAAA,kCAA4B;AAC9B"}