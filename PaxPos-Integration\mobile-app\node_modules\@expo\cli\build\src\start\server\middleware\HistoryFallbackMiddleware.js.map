{"version": 3, "sources": ["../../../../../src/start/server/middleware/HistoryFallbackMiddleware.ts"], "sourcesContent": ["import { parsePlatformHeader } from './resolvePlatform';\nimport { ServerNext, ServerRequest, ServerResponse } from './server.types';\n\n/**\n * Create a web-only middleware which redirects to the index middleware without losing the path component.\n * This is useful for things like React Navigation which need to render the index.html and then direct the user in-memory.\n */\nexport class HistoryFallbackMiddleware {\n  constructor(\n    private indexMiddleware: (\n      req: ServerRequest,\n      res: ServerResponse,\n      next: ServerNext\n    ) => Promise<void>\n  ) {}\n  getHandler() {\n    return (req: ServerRequest, res: ServerResponse, next: any) => {\n      const platform = parsePlatformHeader(req);\n\n      if (!platform || platform === 'web') {\n        // Redirect unknown to the manifest handler while preserving the path.\n        // This implements the HTML5 history fallback API.\n        return this.indexMiddleware(req, res, next);\n      }\n\n      return next();\n    };\n  }\n}\n"], "names": ["HistoryFallbackMiddleware", "constructor", "indexMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "req", "res", "next", "platform", "parsePlatformHeader"], "mappings": ";;;;+BAOaA;;;eAAAA;;;iCAPuB;AAO7B,MAAMA;IACXC,YACE,AAAQC,eAIU,CAClB;aALQA,kBAAAA;IAKP;IACHC,aAAa;QACX,OAAO,CAACC,KAAoBC,KAAqBC;YAC/C,MAAMC,WAAWC,IAAAA,oCAAmB,EAACJ;YAErC,IAAI,CAACG,YAAYA,aAAa,OAAO;gBACnC,sEAAsE;gBACtE,kDAAkD;gBAClD,OAAO,IAAI,CAACL,eAAe,CAACE,KAAKC,KAAKC;YACxC;YAEA,OAAOA;QACT;IACF;AACF"}