import*as e from"../../../core/i18n/i18n.js";import*as t from"../../../models/emulation/emulation.js";import*as i from"../../../ui/legacy/legacy.js";import*as s from"../../../ui/visual_logging/visual_logging.js";import*as a from"./components/components.js";const d=new CSSStyleSheet;d.replaceSync(".devices-settings-tab .settings-container-wrapper{padding-top:5px}.devices-settings-tab .settings-tab.settings-content{display:flex;flex-direction:column;align-items:flex-start;height:100%;margin:0}.devices-settings-tab .devices-button-row{flex:none;display:flex;devtools-button{margin:4px 0 0 5px}}.devices-settings-tab .devices-list{width:min(350px,100%);margin-top:10px}.devices-list-item{padding:3px 5px;height:30px;display:flex;align-items:center;flex:auto 1 1;overflow:hidden;color:var(--sys-color-on-surface);user-select:none;white-space:nowrap;text-overflow:ellipsis}.devices-list-checkbox{height:12px;width:12px;margin:2px 5px 2px 2px;flex:none;pointer-events:none}.devices-list-checkbox:focus{outline:auto 5px -webkit-focus-ring-color}.device-name{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.devices-edit-fields{flex:auto;display:flex;flex-direction:column;align-items:stretch;padding-left:4px;margin-bottom:5px}.devices-edit-fields b{margin-top:8px;margin-bottom:0}.devices-edit-client-hints-heading{display:flex;flex-direction:row;align-items:center;margin-bottom:5px}li .devices-edit-client-hints-heading{margin-bottom:0}.devices-edit-client-hints-heading b{margin-inline-end:2px}.devices-edit-client-hints-heading .help-icon{margin-left:2px;margin-right:2px;vertical-align:middle}.devices-edit-client-hints-heading a:focus{box-shadow:var(--sys-color-state-focus-ring)}.devices-edit-fields input{flex:auto;margin:8px 5px 0}li.devices-edit-client-hints-field{left:-12px}.devices-edit-client-hints-field input{flex:auto;margin:8px 5px 0}.devices-edit-fields .device-edit-fixed{flex:0 0 140px}.devices-edit-fields select{margin:8px 5px 0}\n/*# sourceURL=devicesSettingsTab.css */\n");const n={emulatedDevices:"Emulated Devices",addCustomDevice:"Add custom device...",device:"Device",deviceName:"Device Name",width:"Width",height:"Height",devicePixelRatio:"Device pixel ratio",userAgentString:"User agent string",userAgentType:"User agent type",deviceNameMustBeLessThanS:"Device name must be less than {PH1} characters.",deviceNameCannotBeEmpty:"Device name cannot be empty.",deviceAddedOrUpdated:"Device {PH1} successfully added/updated."},c=e.i18n.registerUIStrings("panels/settings/emulation/DevicesSettingsTab.ts",n),l=e.i18n.getLocalizedString.bind(void 0,c);class o extends i.Widget.VBox{containerElement;addCustomButton;ariaSuccessMessageElement;list;muteUpdate;emulatedDevicesList;editor;constructor(){super(),this.element.setAttribute("jslog",`${s.pane("devices")}`),this.element.classList.add("settings-tab-container"),this.element.classList.add("devices-settings-tab");const e=this.element.createChild("header");i.UIUtils.createTextChild(e.createChild("h1"),l(n.emulatedDevices)),this.containerElement=this.element.createChild("div","settings-container-wrapper").createChild("div","settings-tab settings-content settings-container");const a=this.containerElement.createChild("div","devices-button-row");this.addCustomButton=i.UIUtils.createTextButton(l(n.addCustomDevice),this.addCustomDevice.bind(this),{jslogContext:"add-custom-device"}),this.addCustomButton.id="custom-device-add-button",a.appendChild(this.addCustomButton),this.ariaSuccessMessageElement=this.containerElement.createChild("div","device-success-message"),i.ARIAUtils.markAsPoliteLiveRegion(this.ariaSuccessMessageElement,!1),this.list=new i.ListWidget.ListWidget(this,!1),this.list.element.classList.add("devices-list"),this.list.show(this.containerElement),this.muteUpdate=!1,this.emulatedDevicesList=t.EmulatedDevices.EmulatedDevicesList.instance(),this.emulatedDevicesList.addEventListener("CustomDevicesUpdated",this.devicesUpdated,this),this.emulatedDevicesList.addEventListener("StandardDevicesUpdated",this.devicesUpdated,this),this.setDefaultFocusedElement(this.addCustomButton)}wasShown(){super.wasShown(),this.devicesUpdated(),this.registerCSSFiles([d]),this.list.registerCSSFiles([d])}devicesUpdated(){if(this.muteUpdate)return;this.list.clear();let e=this.emulatedDevicesList.custom().slice();for(let t=0;t<e.length;++t)this.list.appendItem(e[t],!0);this.list.appendSeparator(),e=this.emulatedDevicesList.standard().slice(),e.sort(t.EmulatedDevices.EmulatedDevice.deviceComparator);for(let t=0;t<e.length;++t)this.list.appendItem(e[t],!1)}muteAndSaveDeviceList(e){this.muteUpdate=!0,e?this.emulatedDevicesList.saveCustomDevices():this.emulatedDevicesList.saveStandardDevices(),this.muteUpdate=!1}addCustomDevice(){const e=new t.EmulatedDevices.EmulatedDevice;e.deviceScaleFactor=0,e.horizontal.width=700,e.horizontal.height=400,e.vertical.width=400,e.vertical.height=700,this.list.addNewItem(this.emulatedDevicesList.custom().length,e)}toNumericInputValue(e){return e?String(e):""}renderItem(e,t){const i=document.createElement("label");i.classList.add("devices-list-item");const a=i.createChild("input","devices-list-checkbox");a.type="checkbox",a.checked=e.show(),a.addEventListener("click",function(i){const s=a.checked;e.setShow(s),this.muteAndSaveDeviceList(t),i.consume()}.bind(this),!1),a.setAttribute("jslog",`${s.toggle().track({click:!0})}`);const d=document.createElement("span");return d.classList.add("device-name"),d.appendChild(document.createTextNode(e.title)),i.appendChild(d),i}removeItemRequested(e){this.emulatedDevicesList.removeCustomDevice(e)}commitEdit(e,i,s){e.title=i.control("title").value.trim(),e.vertical.width=i.control("width").value?parseInt(i.control("width").value,10):0,e.vertical.height=i.control("height").value?parseInt(i.control("height").value,10):0,e.horizontal.width=e.vertical.height,e.horizontal.height=e.vertical.width,e.deviceScaleFactor=i.control("scale").value?parseFloat(i.control("scale").value):0,e.userAgent=i.control("user-agent").value,e.modes=[],e.modes.push({title:"",orientation:t.EmulatedDevices.Vertical,insets:new t.DeviceModeModel.Insets(0,0,0,0),image:null}),e.modes.push({title:"",orientation:t.EmulatedDevices.Horizontal,insets:new t.DeviceModeModel.Insets(0,0,0,0),image:null}),e.capabilities=[];const a=i.control("ua-type").value;"Mobile"!==a&&"Mobile (no touch)"!==a||e.capabilities.push("mobile"),"Mobile"!==a&&"Desktop (touch)"!==a||e.capabilities.push("touch");const d=i.control("ua-metadata").value.metaData;d&&(e.userAgentMetadata={...d,mobile:"Mobile"===a||"Mobile (no touch)"===a}),s?this.emulatedDevicesList.addCustomDevice(e):this.emulatedDevicesList.saveCustomDevices(),this.addCustomButton.scrollIntoViewIfNeeded(),this.addCustomButton.focus(),this.ariaSuccessMessageElement.setAttribute("aria-label",l(n.deviceAddedOrUpdated,{PH1:e.title}))}beginEdit(e){const t=this.createEditor();let i;return t.control("title").value=e.title,t.control("width").value=this.toNumericInputValue(e.vertical.width),t.control("height").value=this.toNumericInputValue(e.vertical.height),t.control("scale").value=this.toNumericInputValue(e.deviceScaleFactor),t.control("user-agent").value=e.userAgent,i=e.mobile()?e.touch()?"Mobile":"Mobile (no touch)":e.touch()?"Desktop (touch)":"Desktop",t.control("ua-type").value=i,t.control("ua-metadata").value={metaData:e.userAgentMetadata||void 0},t}createEditor(){if(this.editor)return this.editor;const e=new i.ListWidget.Editor;this.editor=e;const s=e.contentElement(),d=s.createChild("div","devices-edit-fields");i.UIUtils.createTextChild(d.createChild("b"),l(n.device));const c=e.createInput("title","text",l(n.deviceName),(function(e,i,s){let a,d=!1;const c=s.value.trim();c.length>=t.DeviceModeModel.MaxDeviceNameLength?a=l(n.deviceNameMustBeLessThanS,{PH1:t.DeviceModeModel.MaxDeviceNameLength}):0===c.length?a=l(n.deviceNameCannotBeEmpty):d=!0;return{valid:d,errorMessage:a}}));d.createChild("div","hbox").appendChild(c),c.id="custom-device-name-field";const o=d.createChild("div","hbox");o.appendChild(e.createInput("width","text",l(n.width),(function(e,i,s){return t.DeviceModeModel.DeviceModeModel.widthValidator(s.value)}))),o.appendChild(e.createInput("height","text",l(n.height),(function(e,i,s){return t.DeviceModeModel.DeviceModeModel.heightValidator(s.value)})));const r=e.createInput("scale","text",l(n.devicePixelRatio),(function(e,i,s){return t.DeviceModeModel.DeviceModeModel.scaleValidator(s.value)}));r.classList.add("device-edit-fixed"),o.appendChild(r);const u=s.createChild("div","devices-edit-fields");i.UIUtils.createTextChild(u.createChild("b"),l(n.userAgentString));const h=u.createChild("div","hbox");h.appendChild(e.createInput("user-agent","text",l(n.userAgentString),(()=>({valid:!0,errorMessage:void 0}))));const v=e.createSelect("ua-type",["Mobile","Mobile (no touch)","Desktop","Desktop (touch)"],(()=>({valid:!0,errorMessage:void 0})),l(n.userAgentType));v.classList.add("device-edit-fixed"),h.appendChild(v);const m=e.createCustomControl("ua-metadata",a.UserAgentClientHintsForm.UserAgentClientHintsForm,(function(){return m.validate()}));return m.value={},m.addEventListener("clienthintschange",(()=>e.requestValidation()),!1),s.appendChild(m),e}}var r=Object.freeze({__proto__:null,DevicesSettingsTab:o});export{r as DevicesSettingsTab};
