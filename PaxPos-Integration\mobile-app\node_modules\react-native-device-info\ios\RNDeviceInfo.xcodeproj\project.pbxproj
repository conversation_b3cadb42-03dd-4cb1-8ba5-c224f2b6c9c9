// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		60FD13C42BB15A9D00518094 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 60FD13C22BB156C000518094 /* PrivacyInfo.xcprivacy */; };
		663F51B9262200CA0023385E /* EnvironmentUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 663F51B8262200CA0023385E /* EnvironmentUtil.m */; };
		76E65CA41D4CA143009B7AF1 /* DeviceUID.m in Sources */ = {isa = PBXBuildFile; fileRef = 76E65CA31D4CA143009B7AF1 /* DeviceUID.m */; };
		BF770A3D1F6A3EEE007E5F09 /* DeviceUID.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 76E65CA21D4CA143009B7AF1 /* DeviceUID.h */; };
		DA5891DC1BA9A9FC002B4DB2 /* RNDeviceInfo.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = DA5891DB1BA9A9FC002B4DB2 /* RNDeviceInfo.h */; };
		DA5891DE1BA9A9FC002B4DB2 /* RNDeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = DA5891DD1BA9A9FC002B4DB2 /* RNDeviceInfo.m */; };
		E72EC1491F7ABC0C0001BC90 /* DeviceUID.m in Sources */ = {isa = PBXBuildFile; fileRef = 76E65CA31D4CA143009B7AF1 /* DeviceUID.m */; };
		E72EC14A1F7ABC0E0001BC90 /* RNDeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = DA5891DD1BA9A9FC002B4DB2 /* RNDeviceInfo.m */; };
		E72EC14B1F7ABC1A0001BC90 /* DeviceUID.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 76E65CA21D4CA143009B7AF1 /* DeviceUID.h */; };
		E72EC14C1F7ABC1D0001BC90 /* RNDeviceInfo.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = DA5891DB1BA9A9FC002B4DB2 /* RNDeviceInfo.h */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		DA5891D61BA9A9FC002B4DB2 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
				BF770A3D1F6A3EEE007E5F09 /* DeviceUID.h in CopyFiles */,
				DA5891DC1BA9A9FC002B4DB2 /* RNDeviceInfo.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E72EC13E1F7ABB5A0001BC90 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
				E72EC14B1F7ABC1A0001BC90 /* DeviceUID.h in CopyFiles */,
				E72EC14C1F7ABC1D0001BC90 /* RNDeviceInfo.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		60FD13C22BB156C000518094 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		663F51B7262200520023385E /* EnvironmentUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EnvironmentUtil.h; sourceTree = "<group>"; };
		663F51B8262200CA0023385E /* EnvironmentUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EnvironmentUtil.m; sourceTree = "<group>"; };
		76E65CA21D4CA143009B7AF1 /* DeviceUID.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceUID.h; sourceTree = "<group>"; };
		76E65CA31D4CA143009B7AF1 /* DeviceUID.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceUID.m; sourceTree = "<group>"; };
		DA5891D81BA9A9FC002B4DB2 /* libRNDeviceInfo.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNDeviceInfo.a; sourceTree = BUILT_PRODUCTS_DIR; };
		DA5891DB1BA9A9FC002B4DB2 /* RNDeviceInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNDeviceInfo.h; sourceTree = "<group>"; };
		DA5891DD1BA9A9FC002B4DB2 /* RNDeviceInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNDeviceInfo.m; sourceTree = "<group>"; };
		E72EC1401F7ABB5A0001BC90 /* libRNDeviceInfo-tvOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libRNDeviceInfo-tvOS.a"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DA5891D51BA9A9FC002B4DB2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E72EC13D1F7ABB5A0001BC90 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		DA5891CF1BA9A9FC002B4DB2 = {
			isa = PBXGroup;
			children = (
				60FD13C22BB156C000518094 /* PrivacyInfo.xcprivacy */,
				DA5891DA1BA9A9FC002B4DB2 /* RNDeviceInfo */,
				DA5891D91BA9A9FC002B4DB2 /* Products */,
			);
			sourceTree = "<group>";
		};
		DA5891D91BA9A9FC002B4DB2 /* Products */ = {
			isa = PBXGroup;
			children = (
				DA5891D81BA9A9FC002B4DB2 /* libRNDeviceInfo.a */,
				E72EC1401F7ABB5A0001BC90 /* libRNDeviceInfo-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DA5891DA1BA9A9FC002B4DB2 /* RNDeviceInfo */ = {
			isa = PBXGroup;
			children = (
				76E65CA21D4CA143009B7AF1 /* DeviceUID.h */,
				76E65CA31D4CA143009B7AF1 /* DeviceUID.m */,
				DA5891DB1BA9A9FC002B4DB2 /* RNDeviceInfo.h */,
				DA5891DD1BA9A9FC002B4DB2 /* RNDeviceInfo.m */,
				663F51B7262200520023385E /* EnvironmentUtil.h */,
				663F51B8262200CA0023385E /* EnvironmentUtil.m */,
			);
			path = RNDeviceInfo;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DA5891D71BA9A9FC002B4DB2 /* RNDeviceInfo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DA5891E11BA9A9FC002B4DB2 /* Build configuration list for PBXNativeTarget "RNDeviceInfo" */;
			buildPhases = (
				DA5891D41BA9A9FC002B4DB2 /* Sources */,
				DA5891D51BA9A9FC002B4DB2 /* Frameworks */,
				DA5891D61BA9A9FC002B4DB2 /* CopyFiles */,
				60FD13C32BB15A9800518094 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNDeviceInfo;
			productName = RNDeviceInfo;
			productReference = DA5891D81BA9A9FC002B4DB2 /* libRNDeviceInfo.a */;
			productType = "com.apple.product-type.library.static";
		};
		E72EC13F1F7ABB5A0001BC90 /* RNDeviceInfo-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E72EC1481F7ABB5A0001BC90 /* Build configuration list for PBXNativeTarget "RNDeviceInfo-tvOS" */;
			buildPhases = (
				E72EC13C1F7ABB5A0001BC90 /* Sources */,
				E72EC13D1F7ABB5A0001BC90 /* Frameworks */,
				E72EC13E1F7ABB5A0001BC90 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "RNDeviceInfo-tvOS";
			productName = "RNDeviceInfo-tvOS";
			productReference = E72EC1401F7ABB5A0001BC90 /* libRNDeviceInfo-tvOS.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DA5891D01BA9A9FC002B4DB2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0700;
				ORGANIZATIONNAME = Learnium;
				TargetAttributes = {
					DA5891D71BA9A9FC002B4DB2 = {
						CreatedOnToolsVersion = 7.0;
					};
					E72EC13F1F7ABB5A0001BC90 = {
						CreatedOnToolsVersion = 9.0;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = DA5891D31BA9A9FC002B4DB2 /* Build configuration list for PBXProject "RNDeviceInfo" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = DA5891CF1BA9A9FC002B4DB2;
			productRefGroup = DA5891D91BA9A9FC002B4DB2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DA5891D71BA9A9FC002B4DB2 /* RNDeviceInfo */,
				E72EC13F1F7ABB5A0001BC90 /* RNDeviceInfo-tvOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		60FD13C32BB15A9800518094 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				60FD13C42BB15A9D00518094 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DA5891D41BA9A9FC002B4DB2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				76E65CA41D4CA143009B7AF1 /* DeviceUID.m in Sources */,
				663F51B9262200CA0023385E /* EnvironmentUtil.m in Sources */,
				DA5891DE1BA9A9FC002B4DB2 /* RNDeviceInfo.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E72EC13C1F7ABB5A0001BC90 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E72EC1491F7ABC0C0001BC90 /* DeviceUID.m in Sources */,
				E72EC14A1F7ABC0E0001BC90 /* RNDeviceInfo.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		DA5891DF1BA9A9FC002B4DB2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(BUILT_PRODUCTS_DIR)/usr/local/include",
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/../../../node_modules/react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		DA5891E01BA9A9FC002B4DB2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(BUILT_PRODUCTS_DIR)/usr/local/include",
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/../../../node_modules/react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DA5891E21BA9A9FC002B4DB2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(BUILT_PRODUCTS_DIR)/usr/local/include",
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/../../../node_modules/react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		DA5891E31BA9A9FC002B4DB2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(BUILT_PRODUCTS_DIR)/usr/local/include",
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/../../../node_modules/react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		E72EC1461F7ABB5A0001BC90 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(BUILT_PRODUCTS_DIR)/usr/local/include",
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/../../../node_modules/react-native/React/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		E72EC1471F7ABB5A0001BC90 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(BUILT_PRODUCTS_DIR)/usr/local/include",
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/../../../node_modules/react-native/React/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DA5891D31BA9A9FC002B4DB2 /* Build configuration list for PBXProject "RNDeviceInfo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DA5891DF1BA9A9FC002B4DB2 /* Debug */,
				DA5891E01BA9A9FC002B4DB2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DA5891E11BA9A9FC002B4DB2 /* Build configuration list for PBXNativeTarget "RNDeviceInfo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DA5891E21BA9A9FC002B4DB2 /* Debug */,
				DA5891E31BA9A9FC002B4DB2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E72EC1481F7ABB5A0001BC90 /* Build configuration list for PBXNativeTarget "RNDeviceInfo-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E72EC1461F7ABB5A0001BC90 /* Debug */,
				E72EC1471F7ABB5A0001BC90 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DA5891D01BA9A9FC002B4DB2 /* Project object */;
}
