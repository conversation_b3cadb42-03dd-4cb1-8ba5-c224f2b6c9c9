"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _reactNative = require("react-native");
/* eslint-disable no-dupe-class-members */

const {
  RNEncryptedStorage
} = _reactNative.NativeModules;
if (!RNEncryptedStorage) {
  throw new Error('RNEncryptedStorage is undefined');
}
class EncryptedStorage {
  /**
   * Writes data to the disk, using SharedPreferences or KeyChain, depending on the platform.
   * @param {string} key - A string that will be associated to the value for later retrieval.
   * @param {string} value - The data to store.
   */

  /**
   * Writes data to the disk, using SharedPreferences or KeyChain, depending on the platform.
   * @param {string} key - A string that will be associated to the value for later retrieval.
   * @param {string} value - The data to store.
   * @param {Function} cb - The function to call when the operation completes.
   */

  static setItem(key, value, cb) {
    if (cb) {
      RNEncryptedStorage.setItem(key, value).then(cb).catch(cb);
      return;
    }
    return RNEncryptedStorage.setItem(key, value);
  }

  /**
   * Retrieves data from the disk, using SharedPreferences or KeyChain, depending on the platform and returns it as the specified type.
   * @param {string} key - A string that is associated to a value.
   */

  static getItem(key, cb) {
    if (cb) {
      RNEncryptedStorage.getItem(key).then(cb).catch(cb);
      return;
    }
    return RNEncryptedStorage.getItem(key);
  }

  /**
   * Deletes data from the disk, using SharedPreferences or KeyChain, depending on the platform.
   * @param {string} key - A string that is associated to a value.
   */

  static removeItem(key, cb) {
    if (cb) {
      RNEncryptedStorage.removeItem(key).then(cb).catch(cb);
      return;
    }
    return RNEncryptedStorage.removeItem(key);
  }

  /**
   * Clears all data from disk, using SharedPreferences or KeyChain, depending on the platform.
   */

  static clear(cb) {
    if (cb) {
      RNEncryptedStorage.clear().then(cb).catch(cb);
      return;
    }
    return RNEncryptedStorage.clear();
  }
}
exports.default = EncryptedStorage;
//# sourceMappingURL=EncryptedStorage.js.map