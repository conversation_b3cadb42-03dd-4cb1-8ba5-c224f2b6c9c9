{"version": 3, "file": "paths.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_resolveFrom", "_resolveWorkspaceRoot", "_env", "_extensions", "_Config", "_Errors", "e", "__esModule", "default", "ensureSlash", "inputPath", "needsSlash", "hasSlash", "endsWith", "substring", "length", "getPossibleProjectRoot", "fs", "realpathSync", "process", "cwd", "nativePlatforms", "resolveEntryPoint", "projectRoot", "platform", "pkg", "getPackageJson", "platforms", "includes", "extensions", "getBareExtensions", "main", "entry", "getFileWithExtensions", "resolveFromSilentWithExtensions", "ConfigError", "resolveFrom", "fromDirectory", "moduleId", "extension", "modulePath", "silent", "path", "join", "existsSync", "getMetroServerRoot", "env", "EXPO_NO_METRO_WORKSPACE_ROOT", "resolveWorkspaceRoot", "getMetroWorkspaceGlobs", "monorepoRoot", "getWorkspaceGlobs", "convertEntryPointToRelative", "absolutePath", "relative", "resolveRelativeEntryPoint", "options", "exports"], "sources": ["../../src/paths/paths.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport { getWorkspaceGlobs, resolveWorkspaceRoot } from 'resolve-workspace-root';\n\nimport { env } from './env';\nimport { getBareExtensions } from './extensions';\nimport { getPackageJson } from '../Config';\nimport { PackageJSONConfig } from '../Config.types';\nimport { ConfigError } from '../Errors';\n\n// https://github.com/facebook/create-react-app/blob/9750738cce89a967cc71f28390daf5d4311b193c/packages/react-scripts/config/paths.js#L22\nexport function ensureSlash(inputPath: string, needsSlash: boolean): string {\n  const hasSlash = inputPath.endsWith('/');\n  if (hasSlash && !needsSlash) {\n    return inputPath.substring(0, inputPath.length - 1);\n  } else if (!hasSlash && needsSlash) {\n    return `${inputPath}/`;\n  } else {\n    return inputPath;\n  }\n}\n\nexport function getPossibleProjectRoot(): string {\n  return fs.realpathSync(process.cwd());\n}\n\nconst nativePlatforms = ['ios', 'android'];\n\n/** @returns the absolute entry file for an Expo project. */\nexport function resolveEntryPoint(\n  projectRoot: string,\n  {\n    platform,\n    pkg = getPackageJson(projectRoot),\n  }: {\n    platform?: string;\n    pkg?: PackageJSONConfig;\n  } = {}\n): string {\n  const platforms = !platform\n    ? []\n    : nativePlatforms.includes(platform)\n      ? [platform, 'native']\n      : [platform];\n  const extensions = getBareExtensions(platforms);\n\n  // If the config doesn't define a custom entry then we want to look at the `package.json`s `main` field, and try again.\n  const { main } = pkg;\n  if (main && typeof main === 'string') {\n    // Testing the main field against all of the provided extensions - for legacy reasons we can't use node module resolution as the package.json allows you to pass in a file without a relative path and expect it as a relative path.\n    let entry = getFileWithExtensions(projectRoot, main, extensions);\n    if (!entry) {\n      // Allow for paths like: `{ \"main\": \"expo/AppEntry\" }`\n      entry = resolveFromSilentWithExtensions(projectRoot, main, extensions);\n      if (!entry)\n        throw new ConfigError(\n          `Cannot resolve entry file: The \\`main\\` field defined in your \\`package.json\\` points to an unresolvable or non-existent path.`,\n          'ENTRY_NOT_FOUND'\n        );\n    }\n    return entry;\n  }\n\n  // Check for a root index.* file in the project root.\n  const entry = resolveFromSilentWithExtensions(projectRoot, './index', extensions);\n  if (entry) {\n    return entry;\n  }\n\n  try {\n    // If none of the default files exist then we will attempt to use the main Expo entry point.\n    // This requires `expo` to be installed in the project to work as it will use `node_module/expo/AppEntry.js`\n    // Doing this enables us to create a bare minimum Expo project.\n\n    // TODO(Bacon): We may want to do a check against `./App` and `expo` in the `package.json` `dependencies` as we can more accurately ensure that the project is expo-min without needing the modules installed.\n    return resolveFrom(projectRoot, 'expo/AppEntry');\n  } catch {\n    throw new ConfigError(\n      `The project entry file could not be resolved. Define it in the \\`main\\` field of the \\`package.json\\`, create an \\`index.js\\`, or install the \\`expo\\` package.`,\n      'ENTRY_NOT_FOUND'\n    );\n  }\n}\n\n// Resolve from but with the ability to resolve like a bundler\nfunction resolveFromSilentWithExtensions(\n  fromDirectory: string,\n  moduleId: string,\n  extensions: string[]\n): string | null {\n  for (const extension of extensions) {\n    const modulePath = resolveFrom.silent(fromDirectory, `${moduleId}.${extension}`);\n    if (modulePath?.endsWith(extension)) {\n      return modulePath;\n    }\n  }\n  return resolveFrom.silent(fromDirectory, moduleId) || null;\n}\n\n// Statically attempt to resolve a module but with the ability to resolve like a bundler.\n// This won't use node module resolution.\nexport function getFileWithExtensions(\n  fromDirectory: string,\n  moduleId: string,\n  extensions: string[]\n): string | null {\n  const modulePath = path.join(fromDirectory, moduleId);\n  if (fs.existsSync(modulePath)) {\n    return modulePath;\n  }\n  for (const extension of extensions) {\n    const modulePath = path.join(fromDirectory, `${moduleId}.${extension}`);\n    if (fs.existsSync(modulePath)) {\n      return modulePath;\n    }\n  }\n  return null;\n}\n\n/** Get the Metro server root, when working in monorepos */\nexport function getMetroServerRoot(projectRoot: string): string {\n  if (env.EXPO_NO_METRO_WORKSPACE_ROOT) {\n    return projectRoot;\n  }\n\n  return resolveWorkspaceRoot(projectRoot) ?? projectRoot;\n}\n\n/**\n * Get the workspace globs for Metro's watchFolders.\n * @note This does not traverse the monorepo, and should be used with `getMetroServerRoot`\n */\nexport function getMetroWorkspaceGlobs(monorepoRoot: string): string[] | null {\n  return getWorkspaceGlobs(monorepoRoot);\n}\n\n/**\n * Convert an absolute entry point to a server or project root relative filepath.\n * This is useful on Android where the entry point is an absolute path.\n */\nexport function convertEntryPointToRelative(projectRoot: string, absolutePath: string) {\n  // The project root could be using a different root on MacOS (`/var` vs `/private/var`)\n  // We need to make sure to get the non-symlinked path to the server or project root.\n  return path.relative(\n    fs.realpathSync(getMetroServerRoot(projectRoot)),\n    fs.realpathSync(absolutePath)\n  );\n}\n\n/**\n * Resolve the entry point relative to either the server or project root.\n * This relative entry path should be used to pass non-absolute paths to Metro,\n * accounting for possible monorepos and keeping the cache sharable (no absolute paths).\n */\nexport const resolveRelativeEntryPoint: typeof resolveEntryPoint = (projectRoot, options) => {\n  return convertEntryPointToRelative(projectRoot, resolveEntryPoint(projectRoot, options));\n};\n"], "mappings": ";;;;;;;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,aAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,YAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,sBAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,qBAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,KAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,IAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,YAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,WAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,QAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAS,QAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,OAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAwC,SAAAC,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAExC;AACO,SAASG,WAAWA,CAACC,SAAiB,EAAEC,UAAmB,EAAU;EAC1E,MAAMC,QAAQ,GAAGF,SAAS,CAACG,QAAQ,CAAC,GAAG,CAAC;EACxC,IAAID,QAAQ,IAAI,CAACD,UAAU,EAAE;IAC3B,OAAOD,SAAS,CAACI,SAAS,CAAC,CAAC,EAAEJ,SAAS,CAACK,MAAM,GAAG,CAAC,CAAC;EACrD,CAAC,MAAM,IAAI,CAACH,QAAQ,IAAID,UAAU,EAAE;IAClC,OAAO,GAAGD,SAAS,GAAG;EACxB,CAAC,MAAM;IACL,OAAOA,SAAS;EAClB;AACF;AAEO,SAASM,sBAAsBA,CAAA,EAAW;EAC/C,OAAOC,aAAE,CAACC,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,CAAC;AACvC;AAEA,MAAMC,eAAe,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC;;AAE1C;AACO,SAASC,iBAAiBA,CAC/BC,WAAmB,EACnB;EACEC,QAAQ;EACRC,GAAG,GAAG,IAAAC,wBAAc,EAACH,WAAW;AAIlC,CAAC,GAAG,CAAC,CAAC,EACE;EACR,MAAMI,SAAS,GAAG,CAACH,QAAQ,GACvB,EAAE,GACFH,eAAe,CAACO,QAAQ,CAACJ,QAAQ,CAAC,GAChC,CAACA,QAAQ,EAAE,QAAQ,CAAC,GACpB,CAACA,QAAQ,CAAC;EAChB,MAAMK,UAAU,GAAG,IAAAC,+BAAiB,EAACH,SAAS,CAAC;;EAE/C;EACA,MAAM;IAAEI;EAAK,CAAC,GAAGN,GAAG;EACpB,IAAIM,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACpC;IACA,IAAIC,KAAK,GAAGC,qBAAqB,CAACV,WAAW,EAAEQ,IAAI,EAAEF,UAAU,CAAC;IAChE,IAAI,CAACG,KAAK,EAAE;MACV;MACAA,KAAK,GAAGE,+BAA+B,CAACX,WAAW,EAAEQ,IAAI,EAAEF,UAAU,CAAC;MACtE,IAAI,CAACG,KAAK,EACR,MAAM,KAAIG,qBAAW,EACnB,gIAAgI,EAChI,iBACF,CAAC;IACL;IACA,OAAOH,KAAK;EACd;;EAEA;EACA,MAAMA,KAAK,GAAGE,+BAA+B,CAACX,WAAW,EAAE,SAAS,EAAEM,UAAU,CAAC;EACjF,IAAIG,KAAK,EAAE;IACT,OAAOA,KAAK;EACd;EAEA,IAAI;IACF;IACA;IACA;;IAEA;IACA,OAAO,IAAAI,sBAAW,EAACb,WAAW,EAAE,eAAe,CAAC;EAClD,CAAC,CAAC,MAAM;IACN,MAAM,KAAIY,qBAAW,EACnB,iKAAiK,EACjK,iBACF,CAAC;EACH;AACF;;AAEA;AACA,SAASD,+BAA+BA,CACtCG,aAAqB,EACrBC,QAAgB,EAChBT,UAAoB,EACL;EACf,KAAK,MAAMU,SAAS,IAAIV,UAAU,EAAE;IAClC,MAAMW,UAAU,GAAGJ,sBAAW,CAACK,MAAM,CAACJ,aAAa,EAAE,GAAGC,QAAQ,IAAIC,SAAS,EAAE,CAAC;IAChF,IAAIC,UAAU,EAAE3B,QAAQ,CAAC0B,SAAS,CAAC,EAAE;MACnC,OAAOC,UAAU;IACnB;EACF;EACA,OAAOJ,sBAAW,CAACK,MAAM,CAACJ,aAAa,EAAEC,QAAQ,CAAC,IAAI,IAAI;AAC5D;;AAEA;AACA;AACO,SAASL,qBAAqBA,CACnCI,aAAqB,EACrBC,QAAgB,EAChBT,UAAoB,EACL;EACf,MAAMW,UAAU,GAAGE,eAAI,CAACC,IAAI,CAACN,aAAa,EAAEC,QAAQ,CAAC;EACrD,IAAIrB,aAAE,CAAC2B,UAAU,CAACJ,UAAU,CAAC,EAAE;IAC7B,OAAOA,UAAU;EACnB;EACA,KAAK,MAAMD,SAAS,IAAIV,UAAU,EAAE;IAClC,MAAMW,UAAU,GAAGE,eAAI,CAACC,IAAI,CAACN,aAAa,EAAE,GAAGC,QAAQ,IAAIC,SAAS,EAAE,CAAC;IACvE,IAAItB,aAAE,CAAC2B,UAAU,CAACJ,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU;IACnB;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACO,SAASK,kBAAkBA,CAACtB,WAAmB,EAAU;EAC9D,IAAIuB,UAAG,CAACC,4BAA4B,EAAE;IACpC,OAAOxB,WAAW;EACpB;EAEA,OAAO,IAAAyB,4CAAoB,EAACzB,WAAW,CAAC,IAAIA,WAAW;AACzD;;AAEA;AACA;AACA;AACA;AACO,SAAS0B,sBAAsBA,CAACC,YAAoB,EAAmB;EAC5E,OAAO,IAAAC,yCAAiB,EAACD,YAAY,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACO,SAASE,2BAA2BA,CAAC7B,WAAmB,EAAE8B,YAAoB,EAAE;EACrF;EACA;EACA,OAAOX,eAAI,CAACY,QAAQ,CAClBrC,aAAE,CAACC,YAAY,CAAC2B,kBAAkB,CAACtB,WAAW,CAAC,CAAC,EAChDN,aAAE,CAACC,YAAY,CAACmC,YAAY,CAC9B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACO,MAAME,yBAAmD,GAAGA,CAAChC,WAAW,EAAEiC,OAAO,KAAK;EAC3F,OAAOJ,2BAA2B,CAAC7B,WAAW,EAAED,iBAAiB,CAACC,WAAW,EAAEiC,OAAO,CAAC,CAAC;AAC1F,CAAC;AAACC,OAAA,CAAAF,yBAAA,GAAAA,yBAAA", "ignoreList": []}