{"author": "<PERSON> <g.tavon<PERSON>@gmail.com> (http://jaz-lounge.com)", "name": "stacktrace-parser", "description": "Parses every stack trace into a nicely formatted array of hashes.", "main": "dist/stack-trace-parser.cjs.js", "module": "dist/stack-trace-parser.esm.js", "types": "dist/stack-trace-parser.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "dev": "rollup -c -w", "dist": "rollup -c && cpy --rename stack-trace-parser.d.ts src/index.d.ts dist/ && cpy --rename stack-trace-parser.test-d.ts src/index.test-d.ts dist/", "prepublish": "npm run dist", "pretest": "npm run dist", "test": "tsd && mocha --require ./mocha-babel-hook 'test/**/*.spec.js'", "lint": "eslint --fix '{src,test}/**/*.js'"}, "keywords": ["errors", "stacktrace", "parser", "exceptions"], "engines": {"node": ">=6"}, "version": "0.1.11", "files": ["dist/stack-trace-parser.cjs.js", "dist/stack-trace-parser.esm.js", "dist/stack-trace-parser.d.ts", "LICENSE", "README.md"], "dependencies": {"type-fest": "^0.7.1"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/register": "^7.9.0", "cpy-cli": "^2.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.3", "expect.js": "^0.3.1", "mocha": "^10.2.0", "prettier": "^1.19.1", "rimraf": "^3.0.2", "rollup": "^1.32.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.2.0", "tsd": "^0.25.0"}, "homepage": "https://github.com/errwischt/stacktrace-parser", "repository": {"type": "git", "url": "git+https://github.com/errwischt/stacktrace-parser.git"}, "bugs": {"url": "http://github.com/errwischt/stacktrace-parser/issues"}, "license": "MIT"}