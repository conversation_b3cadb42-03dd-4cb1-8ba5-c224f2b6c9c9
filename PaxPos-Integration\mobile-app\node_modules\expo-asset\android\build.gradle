plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'expo.modules.asset'
version = '11.1.7'

android {
  namespace "expo.modules.asset"
  defaultConfig {
    versionCode 1
    versionName "11.1.7"
  }
}

dependencies {
  testImplementation 'androidx.test:core:1.6.1'
  testImplementation 'io.mockk:mockk:1.13.11'
  testImplementation 'junit:junit:4.13.2'
  testImplementation 'org.robolectric:robolectric:4.14.1'
}
