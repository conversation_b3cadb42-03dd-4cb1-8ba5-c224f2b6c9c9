import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Simple HTTP-like server implementation for React Native
export class SimpleHTTPServer {
  private port: number = 8080;
  private isRunning: boolean = false;
  private routes: Map<string, Function> = new Map();
  private frontendAssets: Map<string, string> = new Map();

  constructor() {
    this.setupRoutes();
  }

  async start(): Promise<number> {
    try {
      // Load frontend assets
      await this.loadFrontendAssets();
      
      // Mark as running
      this.isRunning = true;
      
      console.log(`Simple HTTP server started on port ${this.port}`);
      return this.port;
    } catch (error) {
      console.error('Failed to start server:', error);
      throw error;
    }
  }

  private async loadFrontendAssets(): Promise<void> {
    try {
      // Load default HTML
      const defaultHTML = this.getDefaultHTML();
      this.frontendAssets.set('/', defaultHTML);
      this.frontendAssets.set('/index.html', defaultHTML);
      
      // Load default CSS
      const defaultCSS = this.getDefaultCSS();
      this.frontendAssets.set('/bundle.css', defaultCSS);
      
      // Load default JS
      const defaultJS = this.getDefaultJS();
      this.frontendAssets.set('/bundle.js', defaultJS);
      
      console.log('Frontend assets loaded successfully');
    } catch (error) {
      console.error('Failed to load frontend assets:', error);
    }
  }

  private setupRoutes(): void {
    // Static file routes
    this.routes.set('GET /', this.serveAsset.bind(this, '/'));
    this.routes.set('GET /index.html', this.serveAsset.bind(this, '/'));
    this.routes.set('GET /bundle.js', this.serveAsset.bind(this, '/bundle.js'));
    this.routes.set('GET /bundle.css', this.serveAsset.bind(this, '/bundle.css'));

    // API Routes - PAX Terminal
    this.routes.set('POST /api/pax/payment', this.handlePayment.bind(this));
    this.routes.set('GET /api/pax/status', this.handleStatus.bind(this));
    this.routes.set('POST /api/pax/test', this.handleTest.bind(this));

    // API Routes - Transactions
    this.routes.set('GET /api/transactions', this.getTransactions.bind(this));
    this.routes.set('POST /api/transactions', this.createTransaction.bind(this));

    // API Routes - System
    this.routes.set('GET /api/system/health', this.getHealth.bind(this));
  }

  // Public method to handle requests (called from WebView)
  async handleRequest(method: string, url: string, body?: any): Promise<any> {
    try {
      const routeKey = `${method} ${url.split('?')[0]}`;
      const handler = this.routes.get(routeKey);
      
      if (handler) {
        return await handler({ method, url, body });
      } else {
        return {
          statusCode: 404,
          headers: { 'Content-Type': 'application/json' },
          body: { error: 'Not Found' }
        };
      }
    } catch (error) {
      console.error('Request handling error:', error);
      return {
        statusCode: 500,
        headers: { 'Content-Type': 'application/json' },
        body: { error: error.message }
      };
    }
  }

  // Static file handlers
  private async serveAsset(assetPath: string): Promise<any> {
    const content = this.frontendAssets.get(assetPath) || 'Asset not found';
    const contentType = this.getContentType(assetPath);
    
    return {
      statusCode: 200,
      headers: { 'Content-Type': contentType },
      body: content
    };
  }

  private getContentType(path: string): string {
    if (path.endsWith('.html')) return 'text/html';
    if (path.endsWith('.js')) return 'application/javascript';
    if (path.endsWith('.css')) return 'text/css';
    if (path.endsWith('.json')) return 'application/json';
    return 'text/plain';
  }

  // API Handlers
  private async handlePayment(request: any): Promise<any> {
    try {
      const { amount, transType = 'SALE', tenderType = 'CREDIT' } = request.body || {};
      
      // Simulate payment processing
      const transaction = {
        id: `txn_${Date.now()}`,
        amount: amount || 0,
        transType,
        tenderType,
        status: 'success',
        authCode: `AUTH${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
        referenceNumber: `REF${Date.now()}`,
        resultCode: '00',
        receiptData: this.generateReceipt(amount, transType),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Store transaction
      await this.storeTransaction(transaction);

      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: { success: true, data: transaction }
      };
    } catch (error) {
      return {
        statusCode: 500,
        headers: { 'Content-Type': 'application/json' },
        body: { success: false, error: error.message }
      };
    }
  }

  private async handleStatus(): Promise<any> {
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: {
        success: true,
        data: {
          connected: true,
          ip: '*************',
          port: 8080,
          model: 'PAX A920',
          capabilities: {
            contactless: true,
            emv: true,
            magneticStripe: true,
            printer: true,
            camera: true,
            wifi: true,
            cellular: true
          }
        }
      }
    };
  }

  private async handleTest(): Promise<any> {
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: {
        success: true,
        message: 'PAX terminal connection test successful'
      }
    };
  }

  private async getTransactions(): Promise<any> {
    try {
      const transactions = await this.loadTransactions();
      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: { success: true, data: transactions }
      };
    } catch (error) {
      return {
        statusCode: 500,
        headers: { 'Content-Type': 'application/json' },
        body: { success: false, error: error.message }
      };
    }
  }

  private async createTransaction(request: any): Promise<any> {
    try {
      const transaction = {
        id: `txn_${Date.now()}`,
        ...request.body,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await this.storeTransaction(transaction);

      return {
        statusCode: 201,
        headers: { 'Content-Type': 'application/json' },
        body: { success: true, data: transaction }
      };
    } catch (error) {
      return {
        statusCode: 500,
        headers: { 'Content-Type': 'application/json' },
        body: { success: false, error: error.message }
      };
    }
  }

  private async getHealth(): Promise<any> {
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: {
        success: true,
        data: {
          status: 'healthy',
          uptime: Date.now(),
          memory: 'N/A',
          version: '1.0.0'
        }
      }
    };
  }

  // Data storage helpers
  private async storeTransaction(transaction: any): Promise<void> {
    try {
      const transactions = await this.loadTransactions();
      transactions.unshift(transaction); // Add to beginning
      
      // Keep only last 100 transactions
      const limitedTransactions = transactions.slice(0, 100);
      
      await AsyncStorage.setItem('transactions', JSON.stringify(limitedTransactions));
    } catch (error) {
      console.error('Failed to store transaction:', error);
    }
  }

  private async loadTransactions(): Promise<any[]> {
    try {
      const transactionsData = await AsyncStorage.getItem('transactions');
      return transactionsData ? JSON.parse(transactionsData) : [];
    } catch (error) {
      console.error('Failed to load transactions:', error);
      return [];
    }
  }

  private generateReceipt(amount: number, transType: string): string {
    return `
PAX POS RECEIPT
===============
Transaction: ${transType}
Amount: $${amount.toFixed(2)}
Date: ${new Date().toLocaleString()}
Terminal: PAX A920
===============
Thank you!
    `.trim();
  }

  // Default frontend assets
  private getDefaultHTML(): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PAX POS Enterprise</title>
    <link rel="stylesheet" href="/bundle.css">
</head>
<body>
    <div id="app">
        <div class="container">
            <header class="header">
                <h1>🏭 PAX POS Enterprise</h1>
                <p>Production-Grade Point of Sale System</p>
            </header>
            
            <main class="main">
                <div class="card">
                    <h2>💳 Payment Processing</h2>
                    <div class="form-group">
                        <label for="amount">Amount ($)</label>
                        <input type="number" id="amount" value="10.00" step="0.01" min="0.01">
                    </div>
                    <div class="form-group">
                        <label for="transType">Transaction Type</label>
                        <select id="transType">
                            <option value="SALE">Sale</option>
                            <option value="REFUND">Refund</option>
                            <option value="VOID">Void</option>
                        </select>
                    </div>
                    <button onclick="processPayment()" class="btn-primary">Process Payment</button>
                </div>
                
                <div class="card">
                    <h2>🔧 Terminal Status</h2>
                    <div id="status">Checking...</div>
                    <button onclick="checkStatus()" class="btn-secondary">Check Status</button>
                    <button onclick="testConnection()" class="btn-secondary">Test Connection</button>
                </div>
                
                <div class="card">
                    <h2>📊 Recent Transactions</h2>
                    <div id="transactions">Loading...</div>
                    <button onclick="loadTransactions()" class="btn-secondary">Refresh</button>
                </div>
            </main>
        </div>
    </div>
    
    <script src="/bundle.js"></script>
</body>
</html>`;
  }

  private getDefaultCSS(): string {
    return `
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    color: white;
    margin-bottom: 30px;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.main {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.card h2 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.5rem;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    margin: 5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e1e5e9;
}

.btn-secondary:hover {
    background: #e9ecef;
}

#status,
#transactions {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    min-height: 60px;
}

.transaction-item {
    background: white;
    padding: 10px;
    margin: 5px 0;
    border-radius: 5px;
    border-left: 4px solid #667eea;
}

.success {
    color: #28a745;
    font-weight: 600;
}

.error {
    color: #dc3545;
    font-weight: 600;
}

@media (max-width: 768px) {
    .main {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 2rem;
    }
}`;
  }

  private getDefaultJS(): string {
    return `
// PAX POS Enterprise JavaScript

let serverAPI = null;

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    console.log('PAX POS Enterprise loaded');
    checkStatus();
    loadTransactions();
});

// Set up API communication bridge
if (window.ReactNativeWebView) {
    // Running in React Native WebView
    serverAPI = {
        request: async (method, url, body) => {
            return new Promise((resolve) => {
                const requestId = Date.now().toString();
                
                window.addEventListener('message', function handler(event) {
                    const data = JSON.parse(event.data);
                    if (data.requestId === requestId) {
                        window.removeEventListener('message', handler);
                        resolve(data.response);
                    }
                });
                
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'API_REQUEST',
                    requestId,
                    method,
                    url,
                    body
                }));
            });
        }
    };
} else {
    // Fallback for testing
    serverAPI = {
        request: async (method, url, body) => {
            console.log('Mock API request:', method, url, body);
            return {
                statusCode: 200,
                body: { success: true, message: 'Mock response' }
            };
        }
    };
}

async function processPayment() {
    const amount = parseFloat(document.getElementById('amount').value);
    const transType = document.getElementById('transType').value;
    
    if (!amount || amount <= 0) {
        alert('Please enter a valid amount');
        return;
    }
    
    updateStatus('Processing payment...', 'info');
    
    try {
        const response = await serverAPI.request('POST', '/api/pax/payment', {
            amount,
            transType,
            tenderType: 'CREDIT'
        });
        
        if (response.body.success) {
            updateStatus('Payment processed successfully!', 'success');
            loadTransactions();
        } else {
            updateStatus('Payment failed: ' + response.body.error, 'error');
        }
    } catch (error) {
        updateStatus('Payment error: ' + error.message, 'error');
    }
}

async function checkStatus() {
    updateStatus('Checking terminal status...', 'info');
    
    try {
        const response = await serverAPI.request('GET', '/api/pax/status');
        
        if (response.body.success) {
            const status = response.body.data;
            updateStatus(\`Terminal connected: \${status.model} at \${status.ip}:\${status.port}\`, 'success');
        } else {
            updateStatus('Status check failed', 'error');
        }
    } catch (error) {
        updateStatus('Status error: ' + error.message, 'error');
    }
}

async function testConnection() {
    updateStatus('Testing connection...', 'info');
    
    try {
        const response = await serverAPI.request('POST', '/api/pax/test');
        
        if (response.body.success) {
            updateStatus('Connection test successful!', 'success');
        } else {
            updateStatus('Connection test failed', 'error');
        }
    } catch (error) {
        updateStatus('Test error: ' + error.message, 'error');
    }
}

async function loadTransactions() {
    const transactionsDiv = document.getElementById('transactions');
    transactionsDiv.innerHTML = 'Loading transactions...';
    
    try {
        const response = await serverAPI.request('GET', '/api/transactions');
        
        if (response.body.success) {
            const transactions = response.body.data;
            
            if (transactions.length === 0) {
                transactionsDiv.innerHTML = 'No transactions found';
                return;
            }
            
            const html = transactions.slice(0, 5).map(txn => \`
                <div class="transaction-item">
                    <strong>\${txn.id}</strong> - $\${txn.amount.toFixed(2)} 
                    <span class="\${txn.status}">\${txn.status.toUpperCase()}</span>
                    <br><small>\${new Date(txn.createdAt).toLocaleString()}</small>
                </div>
            \`).join('');
            
            transactionsDiv.innerHTML = html;
        } else {
            transactionsDiv.innerHTML = 'Failed to load transactions';
        }
    } catch (error) {
        transactionsDiv.innerHTML = 'Error loading transactions: ' + error.message;
    }
}

function updateStatus(message, type = 'info') {
    const statusDiv = document.getElementById('status');
    statusDiv.innerHTML = message;
    statusDiv.className = type;
}

console.log('PAX POS Enterprise JavaScript loaded successfully');`;
  }

  stop(): void {
    this.isRunning = false;
    console.log('Simple HTTP server stopped');
  }

  isServerRunning(): boolean {
    return this.isRunning;
  }
}
