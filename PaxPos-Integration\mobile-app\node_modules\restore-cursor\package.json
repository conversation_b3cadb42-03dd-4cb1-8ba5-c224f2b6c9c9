{"name": "restore-cursor", "version": "2.0.0", "description": "Gracefully restore the CLI cursor on exit", "license": "MIT", "repository": "sindresorhus/restore-cursor", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "files": ["index.js"], "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}}