{"version": 3, "sources": ["../../../src/lint/ESlintPrerequisite.ts"], "sourcesContent": ["import JsonFile, { JSONObject } from '@expo/json-file';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nimport { Log } from '../log';\nimport { PrerequisiteCommandError, ProjectPrerequisite } from '../start/doctor/Prerequisite';\nimport { ensureDependenciesAsync } from '../start/doctor/dependencies/ensureDependenciesAsync';\nimport { findFileInParents } from '../utils/findUp';\nimport { isInteractive } from '../utils/interactive';\nimport { confirmAsync } from '../utils/prompts';\n\nconst debug = require('debug')('expo:lint') as typeof console.log;\n\n/** Ensure the project has the required ESLint config. */\nexport class ESLintProjectPrerequisite extends ProjectPrerequisite<boolean> {\n  async assertImplementation(): Promise<boolean> {\n    const hasEslintConfig = await isEslintConfigured(this.projectRoot);\n    const hasLegacyConfig = await isLegacyEslintConfigured(this.projectRoot);\n    const hasLintScript = await lintScriptIsConfigured(this.projectRoot);\n\n    if (hasLegacyConfig) {\n      Log.warn(`Using legacy ESLint config. Consider upgrading to flat config.`);\n    }\n\n    return (hasEslintConfig || hasLegacyConfig) && hasLintScript;\n  }\n\n  async bootstrapAsync(): Promise<boolean> {\n    debug('Setting up ESLint');\n\n    const hasEslintConfig = await isEslintConfigured(this.projectRoot);\n    if (!hasEslintConfig) {\n      if (!isInteractive()) {\n        Log.warn(`No ESLint config found. Configuring automatically.`);\n      } else {\n        const shouldSetupLint = await confirmAsync({\n          message: 'No ESLint config found. Install and configure ESLint in this project?',\n        });\n        if (!shouldSetupLint) {\n          throw new PrerequisiteCommandError('ESLint is not configured for this project.');\n        }\n      }\n\n      await this._ensureDependenciesInstalledAsync({ skipPrompt: true, isProjectMutable: true });\n\n      await fs.writeFile(\n        path.join(this.projectRoot, 'eslint.config.js'),\n        await fs.readFile(require.resolve(`@expo/cli/static/template/eslint.config.js`), 'utf8'),\n        'utf8'\n      );\n    }\n\n    const hasLintScript = await lintScriptIsConfigured(this.projectRoot);\n    if (!hasLintScript) {\n      const scripts = JsonFile.read(path.join(this.projectRoot, 'package.json')).scripts;\n      await JsonFile.setAsync(\n        path.join(this.projectRoot, 'package.json'),\n        'scripts',\n        typeof scripts === 'object' ? { ...scripts, lint: 'expo lint' } : { lint: 'expo lint' },\n        { json5: false }\n      );\n    }\n\n    Log.log();\n    Log.log('ESLint has been configured 🎉');\n    Log.log();\n\n    return true;\n  }\n\n  async _ensureDependenciesInstalledAsync({\n    skipPrompt,\n    isProjectMutable,\n  }: {\n    skipPrompt?: boolean;\n    isProjectMutable?: boolean;\n  }): Promise<boolean> {\n    try {\n      return await ensureDependenciesAsync(this.projectRoot, {\n        skipPrompt,\n        isProjectMutable,\n        installMessage: 'ESLint is required to lint your project.',\n        warningMessage: 'ESLint not installed, unable to set up linting for your project.',\n        requiredPackages: [\n          { version: '^9.0.0', pkg: 'eslint', file: 'eslint/package.json', dev: true },\n          {\n            pkg: 'eslint-config-expo',\n            file: 'eslint-config-expo/package.json',\n            dev: true,\n          },\n        ],\n      });\n    } catch (error) {\n      this.resetAssertion();\n      throw error;\n    }\n  }\n}\n\nasync function isLegacyEslintConfigured(projectRoot: string) {\n  debug('Checking for legacy ESLint configuration', projectRoot);\n\n  const packageFile = await JsonFile.readAsync(path.join(projectRoot, 'package.json'));\n  if (\n    typeof packageFile.eslintConfig === 'object' &&\n    Object.keys(packageFile.eslintConfig as JSONObject).length > 0\n  ) {\n    debug('Found legacy ESLint config in package.json');\n    return true;\n  }\n\n  const eslintConfigFiles = [\n    '.eslintrc.js',\n    '.eslintrc.cjs',\n    '.eslintrc.yaml',\n    '.eslintrc.yml',\n    '.eslintrc.json',\n  ];\n  for (const configFile of eslintConfigFiles) {\n    const configPath = findFileInParents(projectRoot, configFile);\n\n    if (configPath) {\n      debug('Found ESLint config file:', configPath);\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/** Check for flat config. */\nasync function isEslintConfigured(projectRoot: string) {\n  debug('Ensuring ESLint is configured in', projectRoot);\n\n  const eslintConfigFiles = ['eslint.config.js', 'eslint.config.mjs', 'eslint.config.cjs'];\n  for (const configFile of eslintConfigFiles) {\n    const configPath = findFileInParents(projectRoot, configFile);\n\n    if (configPath) {\n      debug('Found ESLint config file:', configPath);\n      return true;\n    }\n  }\n\n  return false;\n}\n\nasync function lintScriptIsConfigured(projectRoot: string) {\n  const packageFile = await JsonFile.readAsync(path.join(projectRoot, 'package.json'));\n  return typeof (packageFile.scripts as JSONObject | undefined)?.lint === 'string';\n}\n"], "names": ["ESLintProjectPrerequisite", "debug", "require", "ProjectPrerequisite", "assertImplementation", "hasEslintConfig", "isEslintConfigured", "projectRoot", "hasLegacyConfig", "isLegacyEslintConfigured", "hasLintScript", "lintScriptIsConfigured", "Log", "warn", "bootstrapAsync", "isInteractive", "shouldSetupLint", "<PERSON><PERSON><PERSON>", "message", "PrerequisiteCommandError", "_ensureDependenciesInstalledAsync", "skip<PERSON>rompt", "isProjectMutable", "fs", "writeFile", "path", "join", "readFile", "resolve", "scripts", "JsonFile", "read", "setAsync", "lint", "json5", "log", "ensureDependenciesAsync", "installMessage", "warningMessage", "requiredPackages", "version", "pkg", "file", "dev", "error", "resetAssertion", "packageFile", "readAsync", "eslintConfig", "Object", "keys", "length", "eslintConfigFiles", "configFile", "config<PERSON><PERSON>", "findFileInParents"], "mappings": ";;;;+BAcaA;;;eAAAA;;;;gEAdwB;;;;;;;gEACtB;;;;;;;gEACE;;;;;;qBAEG;8BAC0C;yCACtB;wBACN;6BACJ;yBACD;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,MAAMF,kCAAkCG,iCAAmB;IAChE,MAAMC,uBAAyC;QAC7C,MAAMC,kBAAkB,MAAMC,mBAAmB,IAAI,CAACC,WAAW;QACjE,MAAMC,kBAAkB,MAAMC,yBAAyB,IAAI,CAACF,WAAW;QACvE,MAAMG,gBAAgB,MAAMC,uBAAuB,IAAI,CAACJ,WAAW;QAEnE,IAAIC,iBAAiB;YACnBI,QAAG,CAACC,IAAI,CAAC,CAAC,8DAA8D,CAAC;QAC3E;QAEA,OAAO,AAACR,CAAAA,mBAAmBG,eAAc,KAAME;IACjD;IAEA,MAAMI,iBAAmC;QACvCb,MAAM;QAEN,MAAMI,kBAAkB,MAAMC,mBAAmB,IAAI,CAACC,WAAW;QACjE,IAAI,CAACF,iBAAiB;YACpB,IAAI,CAACU,IAAAA,0BAAa,KAAI;gBACpBH,QAAG,CAACC,IAAI,CAAC,CAAC,kDAAkD,CAAC;YAC/D,OAAO;gBACL,MAAMG,kBAAkB,MAAMC,IAAAA,qBAAY,EAAC;oBACzCC,SAAS;gBACX;gBACA,IAAI,CAACF,iBAAiB;oBACpB,MAAM,IAAIG,sCAAwB,CAAC;gBACrC;YACF;YAEA,MAAM,IAAI,CAACC,iCAAiC,CAAC;gBAAEC,YAAY;gBAAMC,kBAAkB;YAAK;YAExF,MAAMC,mBAAE,CAACC,SAAS,CAChBC,eAAI,CAACC,IAAI,CAAC,IAAI,CAACnB,WAAW,EAAE,qBAC5B,MAAMgB,mBAAE,CAACI,QAAQ,CAACzB,QAAQ0B,OAAO,CAAC,CAAC,0CAA0C,CAAC,GAAG,SACjF;QAEJ;QAEA,MAAMlB,gBAAgB,MAAMC,uBAAuB,IAAI,CAACJ,WAAW;QACnE,IAAI,CAACG,eAAe;YAClB,MAAMmB,UAAUC,mBAAQ,CAACC,IAAI,CAACN,eAAI,CAACC,IAAI,CAAC,IAAI,CAACnB,WAAW,EAAE,iBAAiBsB,OAAO;YAClF,MAAMC,mBAAQ,CAACE,QAAQ,CACrBP,eAAI,CAACC,IAAI,CAAC,IAAI,CAACnB,WAAW,EAAE,iBAC5B,WACA,OAAOsB,YAAY,WAAW;gBAAE,GAAGA,OAAO;gBAAEI,MAAM;YAAY,IAAI;gBAAEA,MAAM;YAAY,GACtF;gBAAEC,OAAO;YAAM;QAEnB;QAEAtB,QAAG,CAACuB,GAAG;QACPvB,QAAG,CAACuB,GAAG,CAAC;QACRvB,QAAG,CAACuB,GAAG;QAEP,OAAO;IACT;IAEA,MAAMf,kCAAkC,EACtCC,UAAU,EACVC,gBAAgB,EAIjB,EAAoB;QACnB,IAAI;YACF,OAAO,MAAMc,IAAAA,gDAAuB,EAAC,IAAI,CAAC7B,WAAW,EAAE;gBACrDc;gBACAC;gBACAe,gBAAgB;gBAChBC,gBAAgB;gBAChBC,kBAAkB;oBAChB;wBAAEC,SAAS;wBAAUC,KAAK;wBAAUC,MAAM;wBAAuBC,KAAK;oBAAK;oBAC3E;wBACEF,KAAK;wBACLC,MAAM;wBACNC,KAAK;oBACP;iBACD;YACH;QACF,EAAE,OAAOC,OAAO;YACd,IAAI,CAACC,cAAc;YACnB,MAAMD;QACR;IACF;AACF;AAEA,eAAenC,yBAAyBF,WAAmB;IACzDN,MAAM,4CAA4CM;IAElD,MAAMuC,cAAc,MAAMhB,mBAAQ,CAACiB,SAAS,CAACtB,eAAI,CAACC,IAAI,CAACnB,aAAa;IACpE,IACE,OAAOuC,YAAYE,YAAY,KAAK,YACpCC,OAAOC,IAAI,CAACJ,YAAYE,YAAY,EAAgBG,MAAM,GAAG,GAC7D;QACAlD,MAAM;QACN,OAAO;IACT;IAEA,MAAMmD,oBAAoB;QACxB;QACA;QACA;QACA;QACA;KACD;IACD,KAAK,MAAMC,cAAcD,kBAAmB;QAC1C,MAAME,aAAaC,IAAAA,yBAAiB,EAAChD,aAAa8C;QAElD,IAAIC,YAAY;YACdrD,MAAM,6BAA6BqD;YACnC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,2BAA2B,GAC3B,eAAehD,mBAAmBC,WAAmB;IACnDN,MAAM,oCAAoCM;IAE1C,MAAM6C,oBAAoB;QAAC;QAAoB;QAAqB;KAAoB;IACxF,KAAK,MAAMC,cAAcD,kBAAmB;QAC1C,MAAME,aAAaC,IAAAA,yBAAiB,EAAChD,aAAa8C;QAElD,IAAIC,YAAY;YACdrD,MAAM,6BAA6BqD;YACnC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,eAAe3C,uBAAuBJ,WAAmB;QAExCuC;IADf,MAAMA,cAAc,MAAMhB,mBAAQ,CAACiB,SAAS,CAACtB,eAAI,CAACC,IAAI,CAACnB,aAAa;IACpE,OAAO,SAAQuC,uBAAAA,YAAYjB,OAAO,qBAApB,AAACiB,qBAAgDb,IAAI,MAAK;AAC1E"}