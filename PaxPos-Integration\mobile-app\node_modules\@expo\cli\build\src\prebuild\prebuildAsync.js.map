{"version": 3, "sources": ["../../../src/prebuild/prebuildAsync.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\nimport chalk from 'chalk';\n\nimport { clearNativeFolder, promptToClearMalformedNativeProjectsAsync } from './clearNativeFolder';\nimport { configureProjectAsync } from './configureProjectAsync';\nimport { ensureConfigAsync } from './ensureConfigAsync';\nimport { assertPlatforms, ensureValidPlatforms, resolveTemplateOption } from './resolveOptions';\nimport { updateFromTemplateAsync } from './updateFromTemplate';\nimport { installAsync } from '../install/installAsync';\nimport { Log } from '../log';\nimport { env } from '../utils/env';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { clearNodeModulesAsync } from '../utils/nodeModules';\nimport { logNewSection } from '../utils/ora';\nimport { profile } from '../utils/profile';\nimport { confirmAsync } from '../utils/prompts';\n\nconst debug = require('debug')('expo:prebuild') as typeof console.log;\n\nexport type PrebuildResults = {\n  /** Expo config. */\n  exp: ExpoConfig;\n  /** Indicates if the process created new files. */\n  hasNewProjectFiles: boolean;\n  /** The platforms that were prebuilt. */\n  platforms: ModPlatform[];\n  /** Indicates if pod install was run. */\n  podInstall: boolean;\n  /** Indicates if node modules were installed. */\n  nodeInstall: boolean;\n};\n\n/**\n * Entry point into the prebuild process, delegates to other helpers to perform various steps.\n *\n * 0. Attempt to clean the project folders.\n * 1. Create native projects (ios, android).\n * 2. Install node modules.\n * 3. Apply config to native projects.\n * 4. Install CocoaPods.\n */\nexport async function prebuildAsync(\n  projectRoot: string,\n  options: {\n    /** Should install node modules and cocoapods. */\n    install?: boolean;\n    /** List of platforms to prebuild. */\n    platforms: ModPlatform[];\n    /** Should delete the native folders before attempting to prebuild. */\n    clean?: boolean;\n    /** URL or file path to the prebuild template. */\n    template?: string;\n    /** Name of the node package manager to install with. */\n    packageManager?: {\n      npm?: boolean;\n      yarn?: boolean;\n      pnpm?: boolean;\n      bun?: boolean;\n    };\n    /** List of node modules to skip updating. */\n    skipDependencyUpdate?: string[];\n  }\n): Promise<PrebuildResults | null> {\n  setNodeEnv('development');\n  require('@expo/env').load(projectRoot);\n\n  if (options.clean) {\n    const { maybeBailOnGitStatusAsync } = await import('../utils/git.js');\n    // Clean the project folders...\n    if (await maybeBailOnGitStatusAsync()) {\n      return null;\n    }\n    // Clear the native folders before syncing\n    await clearNativeFolder(projectRoot, options.platforms);\n  } else {\n    // Check if the existing project folders are malformed.\n    await promptToClearMalformedNativeProjectsAsync(projectRoot, options.platforms);\n  }\n\n  // Warn if the project is attempting to prebuild an unsupported platform (iOS on Windows).\n  options.platforms = ensureValidPlatforms(options.platforms);\n  // Assert if no platforms are left over after filtering.\n  assertPlatforms(options.platforms);\n\n  // Get the Expo config, create it if missing.\n  const { exp, pkg } = await ensureConfigAsync(projectRoot, { platforms: options.platforms });\n\n  // Create native projects from template.\n  const { hasNewProjectFiles, needsPodInstall, templateChecksum, changedDependencies } =\n    await updateFromTemplateAsync(projectRoot, {\n      exp,\n      pkg,\n      template: options.template != null ? resolveTemplateOption(options.template) : undefined,\n      platforms: options.platforms,\n      skipDependencyUpdate: options.skipDependencyUpdate,\n    });\n\n  // Install node modules\n  if (options.install) {\n    if (changedDependencies.length) {\n      if (options.packageManager?.npm) {\n        await clearNodeModulesAsync(projectRoot);\n      }\n\n      Log.log(chalk.gray(chalk`Dependencies in the {bold package.json} changed:`));\n      Log.log(chalk.gray('  ' + changedDependencies.join(', ')));\n\n      // Installing dependencies is a legacy feature from the unversioned\n      // command. We know opt to not change dependencies unless a template\n      // indicates a new dependency is required, or if the core dependencies are wrong.\n      if (\n        await confirmAsync({\n          message: `Install the updated dependencies?`,\n          initial: true,\n        })\n      ) {\n        await installAsync([], {\n          npm: !!options.packageManager?.npm,\n          yarn: !!options.packageManager?.yarn,\n          pnpm: !!options.packageManager?.pnpm,\n          bun: !!options.packageManager?.bun,\n          silent: !(env.EXPO_DEBUG || env.CI),\n        });\n      }\n    }\n  }\n\n  // Apply Expo config to native projects. Prevent log-spew from ora when running in debug mode.\n  const configSyncingStep: { succeed(text?: string): unknown; fail(text?: string): unknown } =\n    env.EXPO_DEBUG\n      ? {\n          succeed(text) {\n            Log.log(text!);\n          },\n          fail(text) {\n            Log.error(text!);\n          },\n        }\n      : logNewSection('Running prebuild');\n  try {\n    await profile(configureProjectAsync)(projectRoot, {\n      platforms: options.platforms,\n      exp,\n      templateChecksum,\n    });\n    configSyncingStep.succeed('Finished prebuild');\n  } catch (error) {\n    configSyncingStep.fail('Prebuild failed');\n    throw error;\n  }\n\n  // Install CocoaPods\n  let podsInstalled: boolean = false;\n  // err towards running pod install less because it's slow and users can easily run npx pod-install afterwards.\n  if (options.platforms.includes('ios') && options.install && needsPodInstall) {\n    const { installCocoaPodsAsync } = await import('../utils/cocoapods.js');\n\n    podsInstalled = await installCocoaPodsAsync(projectRoot);\n  } else {\n    debug('Skipped pod install');\n  }\n\n  return {\n    nodeInstall: !!options.install,\n    podInstall: !podsInstalled,\n    platforms: options.platforms,\n    hasNewProjectFiles,\n    exp,\n  };\n}\n"], "names": ["prebuildAsync", "debug", "require", "projectRoot", "options", "setNodeEnv", "load", "clean", "maybeBailOnGitStatusAsync", "clearNativeFolder", "platforms", "promptToClearMalformedNativeProjectsAsync", "ensureValidPlatforms", "assertPlatforms", "exp", "pkg", "ensureConfigAsync", "hasNewProjectFiles", "needsPodInstall", "templateChecksum", "changedDependencies", "updateFromTemplateAsync", "template", "resolveTemplateOption", "undefined", "skipDependencyUpdate", "install", "length", "packageManager", "npm", "clearNodeModulesAsync", "Log", "log", "chalk", "gray", "join", "<PERSON><PERSON><PERSON>", "message", "initial", "installAsync", "yarn", "pnpm", "bun", "silent", "env", "EXPO_DEBUG", "CI", "configSyncingStep", "succeed", "text", "fail", "error", "logNewSection", "profile", "configureProjectAsync", "podsInstalled", "includes", "installCocoaPodsAsync", "nodeInstall", "podInstall"], "mappings": ";;;;+BA0CsBA;;;eAAAA;;;;gEAxCJ;;;;;;mCAE2D;uCACvC;mCACJ;gCAC2C;oCACrC;8BACX;qBACT;qBACA;yBACO;6BACW;qBACR;yBACN;yBACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAwBxB,eAAeF,cACpBG,WAAmB,EACnBC,OAkBC;IAEDC,IAAAA,mBAAU,EAAC;IACXH,QAAQ,aAAaI,IAAI,CAACH;IAE1B,IAAIC,QAAQG,KAAK,EAAE;QACjB,MAAM,EAAEC,yBAAyB,EAAE,GAAG,MAAM,mEAAA,QAAO;QACnD,+BAA+B;QAC/B,IAAI,MAAMA,6BAA6B;YACrC,OAAO;QACT;QACA,0CAA0C;QAC1C,MAAMC,IAAAA,oCAAiB,EAACN,aAAaC,QAAQM,SAAS;IACxD,OAAO;QACL,uDAAuD;QACvD,MAAMC,IAAAA,4DAAyC,EAACR,aAAaC,QAAQM,SAAS;IAChF;IAEA,0FAA0F;IAC1FN,QAAQM,SAAS,GAAGE,IAAAA,oCAAoB,EAACR,QAAQM,SAAS;IAC1D,wDAAwD;IACxDG,IAAAA,+BAAe,EAACT,QAAQM,SAAS;IAEjC,6CAA6C;IAC7C,MAAM,EAAEI,GAAG,EAAEC,GAAG,EAAE,GAAG,MAAMC,IAAAA,oCAAiB,EAACb,aAAa;QAAEO,WAAWN,QAAQM,SAAS;IAAC;IAEzF,wCAAwC;IACxC,MAAM,EAAEO,kBAAkB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAE,GAClF,MAAMC,IAAAA,2CAAuB,EAAClB,aAAa;QACzCW;QACAC;QACAO,UAAUlB,QAAQkB,QAAQ,IAAI,OAAOC,IAAAA,qCAAqB,EAACnB,QAAQkB,QAAQ,IAAIE;QAC/Ed,WAAWN,QAAQM,SAAS;QAC5Be,sBAAsBrB,QAAQqB,oBAAoB;IACpD;IAEF,uBAAuB;IACvB,IAAIrB,QAAQsB,OAAO,EAAE;QACnB,IAAIN,oBAAoBO,MAAM,EAAE;gBAC1BvB;YAAJ,KAAIA,0BAAAA,QAAQwB,cAAc,qBAAtBxB,wBAAwByB,GAAG,EAAE;gBAC/B,MAAMC,IAAAA,kCAAqB,EAAC3B;YAC9B;YAEA4B,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,IAAI,CAACD,IAAAA,gBAAK,CAAA,CAAC,gDAAgD,CAAC;YAC1EF,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAC,OAAOd,oBAAoBe,IAAI,CAAC;YAEnD,mEAAmE;YACnE,oEAAoE;YACpE,iFAAiF;YACjF,IACE,MAAMC,IAAAA,qBAAY,EAAC;gBACjBC,SAAS,CAAC,iCAAiC,CAAC;gBAC5CC,SAAS;YACX,IACA;oBAESlC,0BACCA,0BACAA,0BACDA;gBAJT,MAAMmC,IAAAA,0BAAY,EAAC,EAAE,EAAE;oBACrBV,KAAK,CAAC,GAACzB,2BAAAA,QAAQwB,cAAc,qBAAtBxB,yBAAwByB,GAAG;oBAClCW,MAAM,CAAC,GAACpC,2BAAAA,QAAQwB,cAAc,qBAAtBxB,yBAAwBoC,IAAI;oBACpCC,MAAM,CAAC,GAACrC,2BAAAA,QAAQwB,cAAc,qBAAtBxB,yBAAwBqC,IAAI;oBACpCC,KAAK,CAAC,GAACtC,2BAAAA,QAAQwB,cAAc,qBAAtBxB,yBAAwBsC,GAAG;oBAClCC,QAAQ,CAAEC,CAAAA,QAAG,CAACC,UAAU,IAAID,QAAG,CAACE,EAAE,AAAD;gBACnC;YACF;QACF;IACF;IAEA,8FAA8F;IAC9F,MAAMC,oBACJH,QAAG,CAACC,UAAU,GACV;QACEG,SAAQC,IAAI;YACVlB,QAAG,CAACC,GAAG,CAACiB;QACV;QACAC,MAAKD,IAAI;YACPlB,QAAG,CAACoB,KAAK,CAACF;QACZ;IACF,IACAG,IAAAA,kBAAa,EAAC;IACpB,IAAI;QACF,MAAMC,IAAAA,gBAAO,EAACC,4CAAqB,EAAEnD,aAAa;YAChDO,WAAWN,QAAQM,SAAS;YAC5BI;YACAK;QACF;QACA4B,kBAAkBC,OAAO,CAAC;IAC5B,EAAE,OAAOG,OAAO;QACdJ,kBAAkBG,IAAI,CAAC;QACvB,MAAMC;IACR;IAEA,oBAAoB;IACpB,IAAII,gBAAyB;IAC7B,8GAA8G;IAC9G,IAAInD,QAAQM,SAAS,CAAC8C,QAAQ,CAAC,UAAUpD,QAAQsB,OAAO,IAAIR,iBAAiB;QAC3E,MAAM,EAAEuC,qBAAqB,EAAE,GAAG,MAAM,mEAAA,QAAO;QAE/CF,gBAAgB,MAAME,sBAAsBtD;IAC9C,OAAO;QACLF,MAAM;IACR;IAEA,OAAO;QACLyD,aAAa,CAAC,CAACtD,QAAQsB,OAAO;QAC9BiC,YAAY,CAACJ;QACb7C,WAAWN,QAAQM,SAAS;QAC5BO;QACAH;IACF;AACF"}