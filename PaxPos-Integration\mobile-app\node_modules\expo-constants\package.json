{"name": "expo-constants", "version": "17.1.7", "description": "Provides system information that remains constant throughout the lifetime of your app.", "main": "build/Constants.js", "types": "build/Constants.d.ts", "sideEffects": false, "exports": {"./package.json": "./package.json", ".": {"types": "./build/Constants.d.ts", "react-server": "./build/Constants.server.js", "default": "./build/Constants.js"}}, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "test:rsc": "jest --config jest-rsc.config.js", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "constants"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-constants"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/constants/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"@expo/config": "~11.0.12", "@expo/env": "~1.0.7"}, "devDependencies": {"expo-module-scripts": "^4.1.9"}, "peerDependencies": {"expo": "*", "react-native": "*"}, "gitHead": "03d3724918c94f6a46df0b48ba8ec43b995a8e96"}