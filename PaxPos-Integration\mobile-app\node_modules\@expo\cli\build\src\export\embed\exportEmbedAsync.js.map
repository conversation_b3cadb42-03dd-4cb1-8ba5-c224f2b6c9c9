{"version": 3, "sources": ["../../../../src/export/embed/exportEmbedAsync.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getConfig } from '@expo/config';\nimport getMetroAssets from '@expo/metro-config/build/transform-worker/getAssets';\nimport assert from 'assert';\nimport fs from 'fs';\nimport { sync as globSync } from 'glob';\nimport Server from 'metro/src/Server';\nimport splitBundleOptions from 'metro/src/lib/splitBundleOptions';\nimport output from 'metro/src/shared/output/bundle';\nimport type { BundleOptions } from 'metro/src/shared/types';\nimport path from 'path';\n\nimport { deserializeEagerKey, getExportEmbedOptionsKey, Options } from './resolveOptions';\nimport { isExecutingFromXcodebuild, logMetroErrorInXcode } from './xcodeCompilerLogger';\nimport { Log } from '../../log';\nimport { DevServerManager } from '../../start/server/DevServerManager';\nimport { MetroBundlerDevServer } from '../../start/server/metro/MetroBundlerDevServer';\nimport { loadMetroConfigAsync } from '../../start/server/metro/instantiateMetro';\nimport { assertMetroPrivateServer } from '../../start/server/metro/metroPrivateServer';\nimport { DOM_COMPONENTS_BUNDLE_DIR } from '../../start/server/middleware/DomComponentsMiddleware';\nimport { getMetroDirectBundleOptionsForExpoConfig } from '../../start/server/middleware/metroOptions';\nimport { stripAnsi } from '../../utils/ansi';\nimport { copyAsync, removeAsync } from '../../utils/dir';\nimport { env } from '../../utils/env';\nimport { setNodeEnv } from '../../utils/nodeEnv';\nimport { exportDomComponentAsync } from '../exportDomComponents';\nimport { isEnableHermesManaged } from '../exportHermes';\nimport { persistMetroAssetsAsync } from '../persistMetroAssets';\nimport { copyPublicFolderAsync } from '../publicFolder';\nimport { BundleAssetWithFileHashes, ExportAssetMap, persistMetroFilesAsync } from '../saveAssets';\nimport { exportStandaloneServerAsync } from './exportServer';\nimport { ensureProcessExitsAfterDelay } from '../../utils/exit';\nimport { resolveRealEntryFilePath } from '../../utils/filePath';\n\nconst debug = require('debug')('expo:export:embed');\n\n/**\n * Extended type for the Metro server build result to support the `code` property as a `Buffer`.\n */\ntype ExtendedMetroServerBuildResult =\n  | Awaited<ReturnType<Server['build']>>\n  | {\n      code: string | Buffer;\n    };\n\nfunction guessCopiedAppleBundlePath(bundleOutput: string) {\n  // Ensure the path is familiar before guessing.\n  if (\n    !bundleOutput.match(/\\/Xcode\\/DerivedData\\/.*\\/Build\\/Products\\//) &&\n    !bundleOutput.match(/\\/CoreSimulator\\/Devices\\/.*\\/data\\/Containers\\/Bundle\\/Application\\//)\n  ) {\n    debug('Bundling to non-standard location:', bundleOutput);\n    return false;\n  }\n  const bundleName = path.basename(bundleOutput);\n  const bundleParent = path.dirname(bundleOutput);\n  const possiblePath = globSync(`*.app/${bundleName}`, {\n    cwd: bundleParent,\n    absolute: true,\n    // bundle identifiers can start with dots.\n    dot: true,\n  })[0];\n  debug('Possible path for previous bundle:', possiblePath);\n  return possiblePath;\n}\n\nexport async function exportEmbedAsync(projectRoot: string, options: Options) {\n  // The React Native build scripts always enable the cache reset but we shouldn't need this in CI environments.\n  // By disabling it, we can eagerly bundle code before the build and reuse the cached artifacts in subsequent builds.\n  if (env.CI && options.resetCache) {\n    debug('CI environment detected, disabling automatic cache reset');\n    options.resetCache = false;\n  }\n\n  setNodeEnv(options.dev ? 'development' : 'production');\n  require('@expo/env').load(projectRoot);\n\n  // This is an optimized codepath that can occur during `npx expo run` and does not occur during builds from Xcode or Android Studio.\n  // Here we reconcile a bundle pass that was run before the native build process. This order can fail faster and is show better errors since the logs won't be obscured by Xcode and Android Studio.\n  // This path is also used for automatically deploying server bundles to a remote host.\n  const eagerBundleOptions = env.__EXPO_EAGER_BUNDLE_OPTIONS\n    ? deserializeEagerKey(env.__EXPO_EAGER_BUNDLE_OPTIONS)\n    : null;\n  if (eagerBundleOptions) {\n    // Get the cache key for the current process to compare against the eager key.\n    const inputKey = getExportEmbedOptionsKey(options);\n\n    // If the app was bundled previously in the same process, then we should reuse the Metro cache.\n    options.resetCache = false;\n\n    if (eagerBundleOptions.key === inputKey) {\n      // Copy the eager bundleOutput and assets to the new locations.\n      await removeAsync(options.bundleOutput);\n\n      copyAsync(eagerBundleOptions.options.bundleOutput, options.bundleOutput);\n\n      if (eagerBundleOptions.options.assetsDest && options.assetsDest) {\n        copyAsync(eagerBundleOptions.options.assetsDest, options.assetsDest);\n      }\n\n      console.log('info: Copied output to binary:', options.bundleOutput);\n      return;\n    }\n    // TODO: sourcemapOutput is set on Android but not during eager. This is tolerable since it doesn't invalidate the Metro cache.\n    console.log('  Eager key:', eagerBundleOptions.key);\n    console.log('Request key:', inputKey);\n\n    // TODO: We may want an analytic event here in the future to understand when this happens.\n    console.warn('warning: Eager bundle does not match new options, bundling again.');\n  }\n\n  await exportEmbedInternalAsync(projectRoot, options);\n\n  // Ensure the process closes after bundling\n  ensureProcessExitsAfterDelay();\n}\n\nexport async function exportEmbedInternalAsync(projectRoot: string, options: Options) {\n  // Ensure we delete the old bundle to trigger a failure if the bundle cannot be created.\n  await removeAsync(options.bundleOutput);\n\n  // The iOS bundle is copied in to the Xcode project, so we need to remove the old one\n  // to prevent Xcode from loading the old one after a build failure.\n  if (options.platform === 'ios') {\n    const previousPath = guessCopiedAppleBundlePath(options.bundleOutput);\n    if (previousPath && fs.existsSync(previousPath)) {\n      debug('Removing previous iOS bundle:', previousPath);\n      await removeAsync(previousPath);\n    }\n  }\n\n  const { bundle, assets, files } = await exportEmbedBundleAndAssetsAsync(projectRoot, options);\n\n  fs.mkdirSync(path.dirname(options.bundleOutput), { recursive: true, mode: 0o755 });\n\n  // On Android, dom components proxy files should write to the assets directory instead of the res directory.\n  // We use the bundleOutput directory to get the assets directory.\n  const domComponentProxyOutputDir =\n    options.platform === 'android' ? path.dirname(options.bundleOutput) : options.assetsDest;\n  const hasDomComponents = domComponentProxyOutputDir && files.size > 0;\n\n  // Persist bundle and source maps.\n  await Promise.all([\n    // @ts-expect-error: The `save()` method from metro is typed to support `code: string` only but it also supports `Buffer` actually.\n    output.save(bundle, options, Log.log),\n\n    // Write dom components proxy files.\n    hasDomComponents ? persistMetroFilesAsync(files, domComponentProxyOutputDir) : null,\n    // Copy public folder for dom components only if\n    hasDomComponents\n      ? copyPublicFolderAsync(\n          path.resolve(projectRoot, env.EXPO_PUBLIC_FOLDER),\n          path.join(domComponentProxyOutputDir, DOM_COMPONENTS_BUNDLE_DIR)\n        )\n      : null,\n\n    // NOTE(EvanBacon): This may need to be adjusted in the future if want to support baseUrl on native\n    // platforms when doing production embeds (unlikely).\n    options.assetsDest\n      ? persistMetroAssetsAsync(projectRoot, assets, {\n          platform: options.platform,\n          outputDirectory: options.assetsDest,\n          iosAssetCatalogDirectory: options.assetCatalogDest,\n        })\n      : null,\n  ]);\n}\n\nexport async function exportEmbedBundleAndAssetsAsync(\n  projectRoot: string,\n  options: Options\n): Promise<{\n  bundle: ExtendedMetroServerBuildResult;\n  assets: readonly BundleAssetWithFileHashes[];\n  files: ExportAssetMap;\n}> {\n  const devServerManager = await DevServerManager.startMetroAsync(projectRoot, {\n    minify: options.minify,\n    mode: options.dev ? 'development' : 'production',\n    port: 8081,\n    isExporting: true,\n    location: {},\n    resetDevServer: options.resetCache,\n    maxWorkers: options.maxWorkers,\n  });\n\n  const devServer = devServerManager.getDefaultDevServer();\n  assert(devServer instanceof MetroBundlerDevServer);\n\n  const { exp, pkg } = getConfig(projectRoot, { skipSDKVersionRequirement: true });\n  const isHermes = isEnableHermesManaged(exp, options.platform);\n\n  let sourceMapUrl = options.sourcemapOutput;\n  if (sourceMapUrl && !options.sourcemapUseAbsolutePath) {\n    sourceMapUrl = path.basename(sourceMapUrl);\n  }\n\n  const files: ExportAssetMap = new Map();\n\n  try {\n    const bundles = await devServer.nativeExportBundleAsync(\n      exp,\n      {\n        // TODO: Re-enable when we get bytecode chunk splitting working again.\n        splitChunks: false, //devServer.isReactServerComponentsEnabled,\n        mainModuleName: resolveRealEntryFilePath(projectRoot, options.entryFile),\n        platform: options.platform,\n        minify: options.minify,\n        mode: options.dev ? 'development' : 'production',\n        engine: isHermes ? 'hermes' : undefined,\n        serializerIncludeMaps: !!sourceMapUrl,\n        bytecode: options.bytecode ?? false,\n        // source map inline\n        reactCompiler: !!exp.experiments?.reactCompiler,\n      },\n      files,\n      {\n        sourceMapUrl,\n        unstable_transformProfile: (options.unstableTransformProfile ||\n          (isHermes ? 'hermes-stable' : 'default')) as BundleOptions['unstable_transformProfile'],\n      }\n    );\n\n    const apiRoutesEnabled =\n      devServer.isReactServerComponentsEnabled || exp.web?.output === 'server';\n\n    if (apiRoutesEnabled) {\n      await exportStandaloneServerAsync(projectRoot, devServer, {\n        exp,\n        pkg,\n        files,\n        options,\n      });\n    }\n\n    // TODO: Remove duplicates...\n    const expoDomComponentReferences = bundles.artifacts\n      .map((artifact) =>\n        Array.isArray(artifact.metadata.expoDomComponentReferences)\n          ? artifact.metadata.expoDomComponentReferences\n          : []\n      )\n      .flat();\n    if (expoDomComponentReferences.length > 0) {\n      await Promise.all(\n        // TODO: Make a version of this which uses `this.metro.getBundler().buildGraphForEntries([])` to bundle all the DOM components at once.\n        expoDomComponentReferences.map(async (filePath) => {\n          const { bundle } = await exportDomComponentAsync({\n            filePath,\n            projectRoot,\n            dev: options.dev,\n            devServer,\n            isHermes,\n            includeSourceMaps: !!sourceMapUrl,\n            exp,\n            files,\n          });\n\n          if (options.assetsDest) {\n            // Save assets like a typical bundler, preserving the file paths on web.\n            // This is saving web-style inside of a native app's binary.\n            await persistMetroAssetsAsync(\n              projectRoot,\n              bundle.assets.map((asset) => ({\n                ...asset,\n                httpServerLocation: path.join(DOM_COMPONENTS_BUNDLE_DIR, asset.httpServerLocation),\n              })),\n              {\n                files,\n                platform: 'web',\n                outputDirectory: options.assetsDest,\n              }\n            );\n          }\n        })\n      );\n    }\n\n    return {\n      files,\n      bundle: {\n        code: bundles.artifacts.filter((a: any) => a.type === 'js')[0].source,\n        // Can be optional when source maps aren't enabled.\n        map: bundles.artifacts.filter((a: any) => a.type === 'map')[0]?.source.toString(),\n      },\n      assets: bundles.assets,\n    };\n  } catch (error: any) {\n    if (isError(error)) {\n      // Log using Xcode error format so the errors are picked up by xcodebuild.\n      // https://developer.apple.com/documentation/xcode/running-custom-scripts-during-a-build#Log-errors-and-warnings-from-your-script\n      if (options.platform === 'ios') {\n        // If the error is about to be presented in Xcode, strip the ansi characters from the message.\n        if ('message' in error && isExecutingFromXcodebuild()) {\n          error.message = stripAnsi(error.message) as string;\n        }\n        logMetroErrorInXcode(projectRoot, error);\n      }\n    }\n    throw error;\n  } finally {\n    devServerManager.stopAsync();\n  }\n}\n\n// Exports for expo-updates\nexport async function createMetroServerAndBundleRequestAsync(\n  projectRoot: string,\n  options: Pick<\n    Options,\n    | 'maxWorkers'\n    | 'config'\n    | 'platform'\n    | 'sourcemapOutput'\n    | 'sourcemapUseAbsolutePath'\n    | 'entryFile'\n    | 'minify'\n    | 'dev'\n    | 'resetCache'\n    | 'unstableTransformProfile'\n  >\n): Promise<{ server: Server; bundleRequest: BundleOptions }> {\n  const exp = getConfig(projectRoot, { skipSDKVersionRequirement: true }).exp;\n\n  // TODO: This is slow ~40ms\n  const { config } = await loadMetroConfigAsync(\n    projectRoot,\n    {\n      // TODO: This is always enabled in the native script and there's no way to disable it.\n      resetCache: options.resetCache,\n\n      maxWorkers: options.maxWorkers,\n      config: options.config,\n    },\n    {\n      exp,\n      isExporting: true,\n      getMetroBundler() {\n        return server.getBundler().getBundler();\n      },\n    }\n  );\n\n  const isHermes = isEnableHermesManaged(exp, options.platform);\n\n  let sourceMapUrl = options.sourcemapOutput;\n  if (sourceMapUrl && !options.sourcemapUseAbsolutePath) {\n    sourceMapUrl = path.basename(sourceMapUrl);\n  }\n\n  // TODO(cedric): check if we can use the proper `bundleType=bundle` and `entryPoint=mainModuleName` properties\n  // @ts-expect-error: see above\n  const bundleRequest: BundleOptions = {\n    ...Server.DEFAULT_BUNDLE_OPTIONS,\n    ...getMetroDirectBundleOptionsForExpoConfig(projectRoot, exp, {\n      splitChunks: false,\n      mainModuleName: resolveRealEntryFilePath(projectRoot, options.entryFile),\n      platform: options.platform,\n      minify: options.minify,\n      mode: options.dev ? 'development' : 'production',\n      engine: isHermes ? 'hermes' : undefined,\n      isExporting: true,\n      // Never output bytecode in the exported bundle since that is hardcoded in the native run script.\n      bytecode: false,\n    }),\n    sourceMapUrl,\n    unstable_transformProfile: (options.unstableTransformProfile ||\n      (isHermes ? 'hermes-stable' : 'default')) as BundleOptions['unstable_transformProfile'],\n  };\n\n  const server = new Server(config, {\n    watch: false,\n  });\n\n  return { server, bundleRequest };\n}\n\nexport async function exportEmbedAssetsAsync(\n  server: Server,\n  bundleRequest: BundleOptions,\n  projectRoot: string,\n  options: Pick<Options, 'platform'>\n) {\n  try {\n    const { entryFile, onProgress, resolverOptions, transformOptions } = splitBundleOptions({\n      ...bundleRequest,\n      bundleType: 'todo',\n    });\n\n    assertMetroPrivateServer(server);\n\n    const dependencies = await server._bundler.getDependencies(\n      [entryFile],\n      transformOptions,\n      resolverOptions,\n      { onProgress, shallow: false, lazy: false }\n    );\n\n    const config = server._config;\n\n    return getMetroAssets(dependencies, {\n      processModuleFilter: config.serializer.processModuleFilter,\n      assetPlugins: config.transformer.assetPlugins,\n      platform: transformOptions.platform!,\n      // Forked out of Metro because the `this._getServerRootDir()` doesn't match the development\n      // behavior.\n      projectRoot: config.projectRoot, // this._getServerRootDir(),\n      publicPath: config.transformer.publicPath,\n    });\n  } catch (error: any) {\n    if (isError(error)) {\n      // Log using Xcode error format so the errors are picked up by xcodebuild.\n      // https://developer.apple.com/documentation/xcode/running-custom-scripts-during-a-build#Log-errors-and-warnings-from-your-script\n      if (options.platform === 'ios') {\n        // If the error is about to be presented in Xcode, strip the ansi characters from the message.\n        if ('message' in error && isExecutingFromXcodebuild()) {\n          error.message = stripAnsi(error.message) as string;\n        }\n        logMetroErrorInXcode(projectRoot, error);\n      }\n    }\n    throw error;\n  }\n}\n\nfunction isError(error: any): error is Error {\n  return error instanceof Error;\n}\n"], "names": ["createMetroServerAndBundleRequestAsync", "exportEmbedAssetsAsync", "exportEmbedAsync", "exportEmbedBundleAndAssetsAsync", "exportEmbedInternalAsync", "debug", "require", "guessCopiedAppleBundlePath", "bundleOutput", "match", "bundleName", "path", "basename", "bundleParent", "dirname", "<PERSON><PERSON><PERSON>", "globSync", "cwd", "absolute", "dot", "projectRoot", "options", "env", "CI", "resetCache", "setNodeEnv", "dev", "load", "eagerBundleOptions", "__EXPO_EAGER_BUNDLE_OPTIONS", "deserializeEagerKey", "inputKey", "getExportEmbedOptionsKey", "key", "removeAsync", "copyAsync", "assetsDest", "console", "log", "warn", "ensureProcessExitsAfterDelay", "platform", "previousPath", "fs", "existsSync", "bundle", "assets", "files", "mkdirSync", "recursive", "mode", "domComponentProxyOutputDir", "hasDomComponents", "size", "Promise", "all", "output", "save", "Log", "persistMetroFilesAsync", "copyPublicFolderAsync", "resolve", "EXPO_PUBLIC_FOLDER", "join", "DOM_COMPONENTS_BUNDLE_DIR", "persistMetroAssetsAsync", "outputDirectory", "iosAssetCatalogDirectory", "assetCatalogDest", "devServerManager", "DevServerManager", "startMetroAsync", "minify", "port", "isExporting", "location", "resetDevServer", "maxWorkers", "devServer", "getDefaultDevServer", "assert", "MetroBundlerDevServer", "exp", "pkg", "getConfig", "skipSDKVersionRequirement", "isHermes", "isEnableHermesManaged", "sourceMapUrl", "sourcemapOutput", "sourcemapUseAbsolutePath", "Map", "bundles", "nativeExportBundleAsync", "splitChunks", "mainModuleName", "resolveRealEntryFilePath", "entryFile", "engine", "undefined", "serializerIncludeMaps", "bytecode", "reactCompiler", "experiments", "unstable_transformProfile", "unstableTransformProfile", "apiRoutesEnabled", "isReactServerComponentsEnabled", "web", "exportStandaloneServerAsync", "expoDomComponentReferences", "artifacts", "map", "artifact", "Array", "isArray", "metadata", "flat", "length", "filePath", "exportDomComponentAsync", "includeSourceMaps", "asset", "httpServerLocation", "code", "filter", "a", "type", "source", "toString", "error", "isError", "isExecutingFromXcodebuild", "message", "stripAnsi", "logMetroErrorInXcode", "stopAsync", "config", "loadMetroConfigAsync", "getMetroBundler", "server", "getBundler", "bundleRequest", "Server", "DEFAULT_BUNDLE_OPTIONS", "getMetroDirectBundleOptionsForExpoConfig", "watch", "onProgress", "resolverOptions", "transformOptions", "splitBundleOptions", "bundleType", "assertMetroPrivateServer", "dependencies", "_bundler", "getDependencies", "shallow", "lazy", "_config", "getMetroAssets", "processModuleFilter", "serializer", "assetPlugins", "transformer", "publicPath", "Error"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAkTqBA,sCAAsC;eAAtCA;;IAuEAC,sBAAsB;eAAtBA;;IAvTAC,gBAAgB;eAAhBA;;IAsGAC,+BAA+B;eAA/BA;;IAnDAC,wBAAwB;eAAxBA;;;;yBApHI;;;;;;;gEACC;;;;;;;gEACR;;;;;;;gEACJ;;;;;;;yBACkB;;;;;;;gEACd;;;;;;;gEACY;;;;;;;gEACZ;;;;;;;gEAEF;;;;;;gCAEsD;qCACP;qBAC5C;kCACa;uCACK;kCACD;oCACI;yCACC;8BACe;sBAC/B;qBACa;qBACnB;yBACO;qCACa;8BACF;oCACE;8BACF;4BAC4C;8BACtC;sBACC;0BACJ;;;;;;AAEzC,MAAMC,QAAQC,QAAQ,SAAS;AAW/B,SAASC,2BAA2BC,YAAoB;IACtD,+CAA+C;IAC/C,IACE,CAACA,aAAaC,KAAK,CAAC,kDACpB,CAACD,aAAaC,KAAK,CAAC,0EACpB;QACAJ,MAAM,sCAAsCG;QAC5C,OAAO;IACT;IACA,MAAME,aAAaC,eAAI,CAACC,QAAQ,CAACJ;IACjC,MAAMK,eAAeF,eAAI,CAACG,OAAO,CAACN;IAClC,MAAMO,eAAeC,IAAAA,YAAQ,EAAC,CAAC,MAAM,EAAEN,YAAY,EAAE;QACnDO,KAAKJ;QACLK,UAAU;QACV,0CAA0C;QAC1CC,KAAK;IACP,EAAE,CAAC,EAAE;IACLd,MAAM,sCAAsCU;IAC5C,OAAOA;AACT;AAEO,eAAeb,iBAAiBkB,WAAmB,EAAEC,OAAgB;IAC1E,8GAA8G;IAC9G,oHAAoH;IACpH,IAAIC,QAAG,CAACC,EAAE,IAAIF,QAAQG,UAAU,EAAE;QAChCnB,MAAM;QACNgB,QAAQG,UAAU,GAAG;IACvB;IAEAC,IAAAA,mBAAU,EAACJ,QAAQK,GAAG,GAAG,gBAAgB;IACzCpB,QAAQ,aAAaqB,IAAI,CAACP;IAE1B,oIAAoI;IACpI,mMAAmM;IACnM,sFAAsF;IACtF,MAAMQ,qBAAqBN,QAAG,CAACO,2BAA2B,GACtDC,IAAAA,mCAAmB,EAACR,QAAG,CAACO,2BAA2B,IACnD;IACJ,IAAID,oBAAoB;QACtB,8EAA8E;QAC9E,MAAMG,WAAWC,IAAAA,wCAAwB,EAACX;QAE1C,+FAA+F;QAC/FA,QAAQG,UAAU,GAAG;QAErB,IAAII,mBAAmBK,GAAG,KAAKF,UAAU;YACvC,+DAA+D;YAC/D,MAAMG,IAAAA,gBAAW,EAACb,QAAQb,YAAY;YAEtC2B,IAAAA,cAAS,EAACP,mBAAmBP,OAAO,CAACb,YAAY,EAAEa,QAAQb,YAAY;YAEvE,IAAIoB,mBAAmBP,OAAO,CAACe,UAAU,IAAIf,QAAQe,UAAU,EAAE;gBAC/DD,IAAAA,cAAS,EAACP,mBAAmBP,OAAO,CAACe,UAAU,EAAEf,QAAQe,UAAU;YACrE;YAEAC,QAAQC,GAAG,CAAC,kCAAkCjB,QAAQb,YAAY;YAClE;QACF;QACA,+HAA+H;QAC/H6B,QAAQC,GAAG,CAAC,gBAAgBV,mBAAmBK,GAAG;QAClDI,QAAQC,GAAG,CAAC,gBAAgBP;QAE5B,0FAA0F;QAC1FM,QAAQE,IAAI,CAAC;IACf;IAEA,MAAMnC,yBAAyBgB,aAAaC;IAE5C,2CAA2C;IAC3CmB,IAAAA,kCAA4B;AAC9B;AAEO,eAAepC,yBAAyBgB,WAAmB,EAAEC,OAAgB;IAClF,wFAAwF;IACxF,MAAMa,IAAAA,gBAAW,EAACb,QAAQb,YAAY;IAEtC,qFAAqF;IACrF,mEAAmE;IACnE,IAAIa,QAAQoB,QAAQ,KAAK,OAAO;QAC9B,MAAMC,eAAenC,2BAA2Bc,QAAQb,YAAY;QACpE,IAAIkC,gBAAgBC,aAAE,CAACC,UAAU,CAACF,eAAe;YAC/CrC,MAAM,iCAAiCqC;YACvC,MAAMR,IAAAA,gBAAW,EAACQ;QACpB;IACF;IAEA,MAAM,EAAEG,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE,GAAG,MAAM5C,gCAAgCiB,aAAaC;IAErFsB,aAAE,CAACK,SAAS,CAACrC,eAAI,CAACG,OAAO,CAACO,QAAQb,YAAY,GAAG;QAAEyC,WAAW;QAAMC,MAAM;IAAM;IAEhF,4GAA4G;IAC5G,iEAAiE;IACjE,MAAMC,6BACJ9B,QAAQoB,QAAQ,KAAK,YAAY9B,eAAI,CAACG,OAAO,CAACO,QAAQb,YAAY,IAAIa,QAAQe,UAAU;IAC1F,MAAMgB,mBAAmBD,8BAA8BJ,MAAMM,IAAI,GAAG;IAEpE,kCAAkC;IAClC,MAAMC,QAAQC,GAAG,CAAC;QAChB,mIAAmI;QACnIC,iBAAM,CAACC,IAAI,CAACZ,QAAQxB,SAASqC,QAAG,CAACpB,GAAG;QAEpC,oCAAoC;QACpCc,mBAAmBO,IAAAA,kCAAsB,EAACZ,OAAOI,8BAA8B;QAC/E,gDAAgD;QAChDC,mBACIQ,IAAAA,mCAAqB,EACnBjD,eAAI,CAACkD,OAAO,CAACzC,aAAaE,QAAG,CAACwC,kBAAkB,GAChDnD,eAAI,CAACoD,IAAI,CAACZ,4BAA4Ba,kDAAyB,KAEjE;QAEJ,mGAAmG;QACnG,qDAAqD;QACrD3C,QAAQe,UAAU,GACd6B,IAAAA,2CAAuB,EAAC7C,aAAa0B,QAAQ;YAC3CL,UAAUpB,QAAQoB,QAAQ;YAC1ByB,iBAAiB7C,QAAQe,UAAU;YACnC+B,0BAA0B9C,QAAQ+C,gBAAgB;QACpD,KACA;KACL;AACH;AAEO,eAAejE,gCACpBiB,WAAmB,EACnBC,OAAgB;IAMhB,MAAMgD,mBAAmB,MAAMC,kCAAgB,CAACC,eAAe,CAACnD,aAAa;QAC3EoD,QAAQnD,QAAQmD,MAAM;QACtBtB,MAAM7B,QAAQK,GAAG,GAAG,gBAAgB;QACpC+C,MAAM;QACNC,aAAa;QACbC,UAAU,CAAC;QACXC,gBAAgBvD,QAAQG,UAAU;QAClCqD,YAAYxD,QAAQwD,UAAU;IAChC;IAEA,MAAMC,YAAYT,iBAAiBU,mBAAmB;IACtDC,IAAAA,iBAAM,EAACF,qBAAqBG,4CAAqB;IAEjD,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAChE,aAAa;QAAEiE,2BAA2B;IAAK;IAC9E,MAAMC,WAAWC,IAAAA,mCAAqB,EAACL,KAAK7D,QAAQoB,QAAQ;IAE5D,IAAI+C,eAAenE,QAAQoE,eAAe;IAC1C,IAAID,gBAAgB,CAACnE,QAAQqE,wBAAwB,EAAE;QACrDF,eAAe7E,eAAI,CAACC,QAAQ,CAAC4E;IAC/B;IAEA,MAAMzC,QAAwB,IAAI4C;IAElC,IAAI;YAcmBT,kBAWyBA,UA2DrCU;QAnFT,MAAMA,UAAU,MAAMd,UAAUe,uBAAuB,CACrDX,KACA;YACE,sEAAsE;YACtEY,aAAa;YACbC,gBAAgBC,IAAAA,kCAAwB,EAAC5E,aAAaC,QAAQ4E,SAAS;YACvExD,UAAUpB,QAAQoB,QAAQ;YAC1B+B,QAAQnD,QAAQmD,MAAM;YACtBtB,MAAM7B,QAAQK,GAAG,GAAG,gBAAgB;YACpCwE,QAAQZ,WAAW,WAAWa;YAC9BC,uBAAuB,CAAC,CAACZ;YACzBa,UAAUhF,QAAQgF,QAAQ,IAAI;YAC9B,oBAAoB;YACpBC,eAAe,CAAC,GAACpB,mBAAAA,IAAIqB,WAAW,qBAAfrB,iBAAiBoB,aAAa;QACjD,GACAvD,OACA;YACEyC;YACAgB,2BAA4BnF,QAAQoF,wBAAwB,IACzDnB,CAAAA,WAAW,kBAAkB,SAAQ;QAC1C;QAGF,MAAMoB,mBACJ5B,UAAU6B,8BAA8B,IAAIzB,EAAAA,WAAAA,IAAI0B,GAAG,qBAAP1B,SAAS1B,MAAM,MAAK;QAElE,IAAIkD,kBAAkB;YACpB,MAAMG,IAAAA,yCAA2B,EAACzF,aAAa0D,WAAW;gBACxDI;gBACAC;gBACApC;gBACA1B;YACF;QACF;QAEA,6BAA6B;QAC7B,MAAMyF,6BAA6BlB,QAAQmB,SAAS,CACjDC,GAAG,CAAC,CAACC,WACJC,MAAMC,OAAO,CAACF,SAASG,QAAQ,CAACN,0BAA0B,IACtDG,SAASG,QAAQ,CAACN,0BAA0B,GAC5C,EAAE,EAEPO,IAAI;QACP,IAAIP,2BAA2BQ,MAAM,GAAG,GAAG;YACzC,MAAMhE,QAAQC,GAAG,CACf,uIAAuI;YACvIuD,2BAA2BE,GAAG,CAAC,OAAOO;gBACpC,MAAM,EAAE1E,MAAM,EAAE,GAAG,MAAM2E,IAAAA,4CAAuB,EAAC;oBAC/CD;oBACAnG;oBACAM,KAAKL,QAAQK,GAAG;oBAChBoD;oBACAQ;oBACAmC,mBAAmB,CAAC,CAACjC;oBACrBN;oBACAnC;gBACF;gBAEA,IAAI1B,QAAQe,UAAU,EAAE;oBACtB,wEAAwE;oBACxE,4DAA4D;oBAC5D,MAAM6B,IAAAA,2CAAuB,EAC3B7C,aACAyB,OAAOC,MAAM,CAACkE,GAAG,CAAC,CAACU,QAAW,CAAA;4BAC5B,GAAGA,KAAK;4BACRC,oBAAoBhH,eAAI,CAACoD,IAAI,CAACC,kDAAyB,EAAE0D,MAAMC,kBAAkB;wBACnF,CAAA,IACA;wBACE5E;wBACAN,UAAU;wBACVyB,iBAAiB7C,QAAQe,UAAU;oBACrC;gBAEJ;YACF;QAEJ;QAEA,OAAO;YACLW;YACAF,QAAQ;gBACN+E,MAAMhC,QAAQmB,SAAS,CAACc,MAAM,CAAC,CAACC,IAAWA,EAAEC,IAAI,KAAK,KAAK,CAAC,EAAE,CAACC,MAAM;gBACrE,mDAAmD;gBACnDhB,GAAG,GAAEpB,6BAAAA,QAAQmB,SAAS,CAACc,MAAM,CAAC,CAACC,IAAWA,EAAEC,IAAI,KAAK,MAAM,CAAC,EAAE,qBAAzDnC,2BAA2DoC,MAAM,CAACC,QAAQ;YACjF;YACAnF,QAAQ8C,QAAQ9C,MAAM;QACxB;IACF,EAAE,OAAOoF,OAAY;QACnB,IAAIC,QAAQD,QAAQ;YAClB,0EAA0E;YAC1E,iIAAiI;YACjI,IAAI7G,QAAQoB,QAAQ,KAAK,OAAO;gBAC9B,8FAA8F;gBAC9F,IAAI,aAAayF,SAASE,IAAAA,8CAAyB,KAAI;oBACrDF,MAAMG,OAAO,GAAGC,IAAAA,eAAS,EAACJ,MAAMG,OAAO;gBACzC;gBACAE,IAAAA,yCAAoB,EAACnH,aAAa8G;YACpC;QACF;QACA,MAAMA;IACR,SAAU;QACR7D,iBAAiBmE,SAAS;IAC5B;AACF;AAGO,eAAexI,uCACpBoB,WAAmB,EACnBC,OAYC;IAED,MAAM6D,MAAME,IAAAA,mBAAS,EAAChE,aAAa;QAAEiE,2BAA2B;IAAK,GAAGH,GAAG;IAE3E,2BAA2B;IAC3B,MAAM,EAAEuD,MAAM,EAAE,GAAG,MAAMC,IAAAA,sCAAoB,EAC3CtH,aACA;QACE,sFAAsF;QACtFI,YAAYH,QAAQG,UAAU;QAE9BqD,YAAYxD,QAAQwD,UAAU;QAC9B4D,QAAQpH,QAAQoH,MAAM;IACxB,GACA;QACEvD;QACAR,aAAa;QACbiE;YACE,OAAOC,OAAOC,UAAU,GAAGA,UAAU;QACvC;IACF;IAGF,MAAMvD,WAAWC,IAAAA,mCAAqB,EAACL,KAAK7D,QAAQoB,QAAQ;IAE5D,IAAI+C,eAAenE,QAAQoE,eAAe;IAC1C,IAAID,gBAAgB,CAACnE,QAAQqE,wBAAwB,EAAE;QACrDF,eAAe7E,eAAI,CAACC,QAAQ,CAAC4E;IAC/B;IAEA,8GAA8G;IAC9G,8BAA8B;IAC9B,MAAMsD,gBAA+B;QACnC,GAAGC,iBAAM,CAACC,sBAAsB;QAChC,GAAGC,IAAAA,sDAAwC,EAAC7H,aAAa8D,KAAK;YAC5DY,aAAa;YACbC,gBAAgBC,IAAAA,kCAAwB,EAAC5E,aAAaC,QAAQ4E,SAAS;YACvExD,UAAUpB,QAAQoB,QAAQ;YAC1B+B,QAAQnD,QAAQmD,MAAM;YACtBtB,MAAM7B,QAAQK,GAAG,GAAG,gBAAgB;YACpCwE,QAAQZ,WAAW,WAAWa;YAC9BzB,aAAa;YACb,iGAAiG;YACjG2B,UAAU;QACZ,EAAE;QACFb;QACAgB,2BAA4BnF,QAAQoF,wBAAwB,IACzDnB,CAAAA,WAAW,kBAAkB,SAAQ;IAC1C;IAEA,MAAMsD,SAAS,IAAIG,CAAAA,SAAK,SAAC,CAACN,QAAQ;QAChCS,OAAO;IACT;IAEA,OAAO;QAAEN;QAAQE;IAAc;AACjC;AAEO,eAAe7I,uBACpB2I,MAAc,EACdE,aAA4B,EAC5B1H,WAAmB,EACnBC,OAAkC;IAElC,IAAI;QACF,MAAM,EAAE4E,SAAS,EAAEkD,UAAU,EAAEC,eAAe,EAAEC,gBAAgB,EAAE,GAAGC,IAAAA,6BAAkB,EAAC;YACtF,GAAGR,aAAa;YAChBS,YAAY;QACd;QAEAC,IAAAA,4CAAwB,EAACZ;QAEzB,MAAMa,eAAe,MAAMb,OAAOc,QAAQ,CAACC,eAAe,CACxD;YAAC1D;SAAU,EACXoD,kBACAD,iBACA;YAAED;YAAYS,SAAS;YAAOC,MAAM;QAAM;QAG5C,MAAMpB,SAASG,OAAOkB,OAAO;QAE7B,OAAOC,IAAAA,oBAAc,EAACN,cAAc;YAClCO,qBAAqBvB,OAAOwB,UAAU,CAACD,mBAAmB;YAC1DE,cAAczB,OAAO0B,WAAW,CAACD,YAAY;YAC7CzH,UAAU4G,iBAAiB5G,QAAQ;YACnC,2FAA2F;YAC3F,YAAY;YACZrB,aAAaqH,OAAOrH,WAAW;YAC/BgJ,YAAY3B,OAAO0B,WAAW,CAACC,UAAU;QAC3C;IACF,EAAE,OAAOlC,OAAY;QACnB,IAAIC,QAAQD,QAAQ;YAClB,0EAA0E;YAC1E,iIAAiI;YACjI,IAAI7G,QAAQoB,QAAQ,KAAK,OAAO;gBAC9B,8FAA8F;gBAC9F,IAAI,aAAayF,SAASE,IAAAA,8CAAyB,KAAI;oBACrDF,MAAMG,OAAO,GAAGC,IAAAA,eAAS,EAACJ,MAAMG,OAAO;gBACzC;gBACAE,IAAAA,yCAAoB,EAACnH,aAAa8G;YACpC;QACF;QACA,MAAMA;IACR;AACF;AAEA,SAASC,QAAQD,KAAU;IACzB,OAAOA,iBAAiBmC;AAC1B"}