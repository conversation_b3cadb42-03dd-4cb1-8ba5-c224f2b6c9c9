{"name": "merge-options", "version": "3.0.4", "description": "Merge Option Objects", "license": "MIT", "repository": "schnittstabil/merge-options", "author": {"name": "<PERSON>", "email": "micha<PERSON>@schnittstabil.de"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && tsd && nyc ava", "lint": "xo", "unit": "ava", "typecheck": "tsd", "clean": "rimraf .nyc_output/ coverage/", "coverage-html": "nyc ava && nyc report --reporter=html"}, "main": "./index.js", "exports": {"require": "./index.js", "import": "./index.mjs"}, "files": ["index.d.ts", "index.js", "index.mjs"], "keywords": ["merge", "options", "deep", "plain", "object", "extend", "clone"], "devDependencies": {"ava": "^3.11.1", "coveralls": "^3.1.0", "nyc": "^15.1.0", "rimraf": "^3.0.2", "tsd": "^0.13.1", "xo": "^0.33.0"}, "dependencies": {"is-plain-obj": "^2.1.0"}, "xo": {"rules": {"import/extensions": "off", "import/no-useless-path-segments": "off", "unicorn/import-index": "off"}}}