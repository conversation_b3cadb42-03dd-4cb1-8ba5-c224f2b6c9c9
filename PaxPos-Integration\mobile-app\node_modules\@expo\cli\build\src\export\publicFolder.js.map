{"version": 3, "sources": ["../../../src/export/publicFolder.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport { copyAsync } from '../utils/dir';\nimport { env } from '../utils/env';\n\nconst debug = require('debug')('expo:public-folder') as typeof console.log;\n\n/** @returns the file system path for a user-defined file in the public folder. */\nexport function getUserDefinedFile(projectRoot: string, possiblePaths: string[]): string | null {\n  const publicPath = path.join(projectRoot, env.EXPO_PUBLIC_FOLDER);\n\n  for (const possiblePath of possiblePaths) {\n    const fullPath = path.join(publicPath, possiblePath);\n    if (fs.existsSync(fullPath)) {\n      debug(`Found user-defined public file: ` + possiblePath);\n      return fullPath;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Copy the contents of the public folder into the output folder.\n * This enables users to add static files like `favicon.ico`.\n *\n * The contents of this folder are completely universal since they refer to\n * static network requests which fall outside the scope of React Native's magic\n * platform resolution patterns.\n */\nexport async function copyPublicFolderAsync(publicFolder: string, outputFolder: string) {\n  if (fs.existsSync(publicFolder)) {\n    await fs.promises.mkdir(outputFolder, { recursive: true });\n    await copyAsync(publicFolder, outputFolder);\n  }\n}\n"], "names": ["copyPublicFolderAsync", "getUserDefinedFile", "debug", "require", "projectRoot", "possiblePaths", "publicPath", "path", "join", "env", "EXPO_PUBLIC_FOLDER", "<PERSON><PERSON><PERSON>", "fullPath", "fs", "existsSync", "publicFolder", "outputFolder", "promises", "mkdir", "recursive", "copyAsync"], "mappings": ";;;;;;;;;;;IA+BsBA,qBAAqB;eAArBA;;IAtBNC,kBAAkB;eAAlBA;;;;gEATD;;;;;;;gEACE;;;;;;qBAES;qBACN;;;;;;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,SAASF,mBAAmBG,WAAmB,EAAEC,aAAuB;IAC7E,MAAMC,aAAaC,eAAI,CAACC,IAAI,CAACJ,aAAaK,QAAG,CAACC,kBAAkB;IAEhE,KAAK,MAAMC,gBAAgBN,cAAe;QACxC,MAAMO,WAAWL,eAAI,CAACC,IAAI,CAACF,YAAYK;QACvC,IAAIE,aAAE,CAACC,UAAU,CAACF,WAAW;YAC3BV,MAAM,CAAC,gCAAgC,CAAC,GAAGS;YAC3C,OAAOC;QACT;IACF;IAEA,OAAO;AACT;AAUO,eAAeZ,sBAAsBe,YAAoB,EAAEC,YAAoB;IACpF,IAAIH,aAAE,CAACC,UAAU,CAACC,eAAe;QAC/B,MAAMF,aAAE,CAACI,QAAQ,CAACC,KAAK,CAACF,cAAc;YAAEG,WAAW;QAAK;QACxD,MAAMC,IAAAA,cAAS,EAACL,cAAcC;IAChC;AACF"}