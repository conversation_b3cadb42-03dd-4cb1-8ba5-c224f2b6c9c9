{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/AtlasPrerequisite.ts"], "sourcesContent": ["import { ProjectPrerequisite } from '../../../doctor/Prerequisite';\nimport {\n  type EnsureDependenciesOptions,\n  ensureDependenciesAsync,\n} from '../../../doctor/dependencies/ensureDependenciesAsync';\n\nexport class AtlasPrerequisite extends ProjectPrerequisite<\n  boolean,\n  Pick<EnsureDependenciesOptions, 'exp'>\n> {\n  async assertImplementation({ exp }: Pick<EnsureDependenciesOptions, 'exp'> = {}) {\n    await this.ensureAtlasInstalled({ exp });\n    return true;\n  }\n\n  async bootstrapAsync({ exp }: Pick<EnsureDependenciesOptions, 'exp'> = {}) {\n    await this.ensureAtlasInstalled({ exp, skipPrompt: true, isProjectMutable: true });\n  }\n\n  private async ensureAtlasInstalled(options: Partial<EnsureDependenciesOptions> = {}) {\n    try {\n      return await ensureDependenciesAsync(this.projectRoot, {\n        ...options,\n        installMessage:\n          'Expo Atlas is required to gather bundle information, but it is not installed in this project.',\n        warningMessage:\n          'Expo Atlas is not installed in this project, unable to gather bundle information.',\n        requiredPackages: [\n          { version: '^0.4.0', pkg: 'expo-atlas', file: 'expo-atlas/package.json', dev: true },\n        ],\n      });\n    } catch (error) {\n      this.resetAssertion({});\n      throw error;\n    }\n  }\n}\n"], "names": ["AtlasPrerequisite", "ProjectPrerequisite", "assertImplementation", "exp", "ensureAtlasInstalled", "bootstrapAsync", "skip<PERSON>rompt", "isProjectMutable", "options", "ensureDependenciesAsync", "projectRoot", "installMessage", "warningMessage", "requiredPackages", "version", "pkg", "file", "dev", "error", "resetAssertion"], "mappings": ";;;;+BAMaA;;;eAAAA;;;8BANuB;yCAI7B;AAEA,MAAMA,0BAA0BC,iCAAmB;IAIxD,MAAMC,qBAAqB,EAAEC,GAAG,EAA0C,GAAG,CAAC,CAAC,EAAE;QAC/E,MAAM,IAAI,CAACC,oBAAoB,CAAC;YAAED;QAAI;QACtC,OAAO;IACT;IAEA,MAAME,eAAe,EAAEF,GAAG,EAA0C,GAAG,CAAC,CAAC,EAAE;QACzE,MAAM,IAAI,CAACC,oBAAoB,CAAC;YAAED;YAAKG,YAAY;YAAMC,kBAAkB;QAAK;IAClF;IAEA,MAAcH,qBAAqBI,UAA8C,CAAC,CAAC,EAAE;QACnF,IAAI;YACF,OAAO,MAAMC,IAAAA,gDAAuB,EAAC,IAAI,CAACC,WAAW,EAAE;gBACrD,GAAGF,OAAO;gBACVG,gBACE;gBACFC,gBACE;gBACFC,kBAAkB;oBAChB;wBAAEC,SAAS;wBAAUC,KAAK;wBAAcC,MAAM;wBAA2BC,KAAK;oBAAK;iBACpF;YACH;QACF,EAAE,OAAOC,OAAO;YACd,IAAI,CAACC,cAAc,CAAC,CAAC;YACrB,MAAMD;QACR;IACF;AACF"}