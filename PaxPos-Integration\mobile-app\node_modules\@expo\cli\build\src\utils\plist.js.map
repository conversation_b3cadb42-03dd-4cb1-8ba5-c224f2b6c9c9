{"version": 3, "sources": ["../../../src/utils/plist.ts"], "sourcesContent": ["import plist from '@expo/plist';\nimport binaryPlist from 'bplist-parser';\nimport fs from 'fs/promises';\n\nimport { CommandError } from './errors';\nimport * as Log from '../log';\n\nconst CHAR_CHEVRON_OPEN = 60;\nconst CHAR_B_LOWER = 98;\n// .mobileprovision\n// const CHAR_ZERO = 30;\n\nexport async function parsePlistAsync(plistPath: string) {\n  Log.debug(`Parse plist: ${plistPath}`);\n\n  return parsePlistBuffer(await fs.readFile(plistPath));\n}\n\nexport function parsePlistBuffer(contents: Buffer) {\n  if (contents[0] === CHAR_CHEVRON_OPEN) {\n    const info = plist.parse(contents.toString());\n    if (Array.isArray(info)) return info[0];\n    return info;\n  } else if (contents[0] === CHAR_B_LOWER) {\n    // @ts-expect-error\n    const info = binaryPlist.parseBuffer(contents);\n    if (Array.isArray(info)) return info[0];\n    return info;\n  } else {\n    throw new CommandError(\n      'PLIST',\n      `Cannot parse plist of type byte (0x${contents[0].toString(16)})`\n    );\n  }\n}\n"], "names": ["parsePlistAsync", "parsePlist<PERSON><PERSON><PERSON>", "CHAR_CHEVRON_OPEN", "CHAR_B_LOWER", "plist<PERSON><PERSON>", "Log", "debug", "fs", "readFile", "contents", "info", "plist", "parse", "toString", "Array", "isArray", "binaryPlist", "parse<PERSON><PERSON>er", "CommandError"], "mappings": ";;;;;;;;;;;IAYsBA,eAAe;eAAfA;;IAMNC,gBAAgB;eAAhBA;;;;gEAlBE;;;;;;;gEACM;;;;;;;gEACT;;;;;;wBAEc;6DACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,oBAAoB;AAC1B,MAAMC,eAAe;AAId,eAAeH,gBAAgBI,SAAiB;IACrDC,KAAIC,KAAK,CAAC,CAAC,aAAa,EAAEF,WAAW;IAErC,OAAOH,iBAAiB,MAAMM,mBAAE,CAACC,QAAQ,CAACJ;AAC5C;AAEO,SAASH,iBAAiBQ,QAAgB;IAC/C,IAAIA,QAAQ,CAAC,EAAE,KAAKP,mBAAmB;QACrC,MAAMQ,OAAOC,gBAAK,CAACC,KAAK,CAACH,SAASI,QAAQ;QAC1C,IAAIC,MAAMC,OAAO,CAACL,OAAO,OAAOA,IAAI,CAAC,EAAE;QACvC,OAAOA;IACT,OAAO,IAAID,QAAQ,CAAC,EAAE,KAAKN,cAAc;QACvC,mBAAmB;QACnB,MAAMO,OAAOM,uBAAW,CAACC,WAAW,CAACR;QACrC,IAAIK,MAAMC,OAAO,CAACL,OAAO,OAAOA,IAAI,CAAC,EAAE;QACvC,OAAOA;IACT,OAAO;QACL,MAAM,IAAIQ,oBAAY,CACpB,SACA,CAAC,mCAAmC,EAAET,QAAQ,CAAC,EAAE,CAACI,QAAQ,CAAC,IAAI,CAAC,CAAC;IAErE;AACF"}