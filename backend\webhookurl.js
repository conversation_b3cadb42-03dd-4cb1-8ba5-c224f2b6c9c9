#!/usr/bin/env node

/**
 * Viva Wallet Webhook Verification Key Generator
 * 
 * This script generates a webhook verification key from Viva Wallet
 * and sets up the webhook URL for receiving payment notifications.
 * 
 * Usage: node webhookurl.js
 * 
 * The script will:
 * 1. Generate a verification key from Viva API
 * 2. Display the key for webhook verification
 * 3. Show instructions for setting up the webhook in Viva Self Care
 */

const https = require('https');
const http = require('http');

// Configuration
const CONFIG = {
  // Viva API Configuration
  MERCHANT_ID: process.env.VIVA_MERCHANT_ID || 'your_merchant_id_here',
  API_KEY: process.env.VIVA_API_KEY || 'your_api_key_here',
  ENVIRONMENT: process.env.VIVA_ENVIRONMENT || 'demo', // 'demo' or 'production'
  
  // Webhook URL - Update this to your actual webhook endpoint
  WEBHOOK_URL: 'https://multiposbackend.onrender.com/api/v1/viva/webhook',
  
  // API Endpoints
  ENDPOINTS: {
    demo: 'https://demo.vivapayments.com/api/messages/config/token',
    production: 'https://www.vivapayments.com/api/messages/config/token'
  }
};

/**
 * Make HTTP request with promise
 */
function makeRequest(url, options) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.request(url, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.end();
  });
}

/**
 * Generate webhook verification key from Viva API
 */
async function generateVerificationKey() {
  try {
    console.log('🔑 Generating Viva Wallet Webhook Verification Key...\n');
    
    // Validate configuration
    if (CONFIG.MERCHANT_ID === 'your_merchant_id_here' || CONFIG.API_KEY === 'your_api_key_here') {
      console.error('❌ Error: Please set your Viva Merchant ID and API Key');
      console.log('\nYou can set them as environment variables:');
      console.log('export VIVA_MERCHANT_ID="your_merchant_id"');
      console.log('export VIVA_API_KEY="your_api_key"');
      console.log('\nOr edit the CONFIG object in this script.');
      process.exit(1);
    }
    
    // Prepare authentication
    const credentials = Buffer.from(`${CONFIG.MERCHANT_ID}:${CONFIG.API_KEY}`).toString('base64');
    const apiUrl = CONFIG.ENDPOINTS[CONFIG.ENVIRONMENT];
    
    console.log(`📡 Environment: ${CONFIG.ENVIRONMENT.toUpperCase()}`);
    console.log(`🌐 API URL: ${apiUrl}`);
    console.log(`🔗 Webhook URL: ${CONFIG.WEBHOOK_URL}\n`);
    
    // Make API request
    const options = {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Viva-Webhook-Generator/1.0'
      }
    };
    
    console.log('⏳ Making API request to Viva...');
    const response = await makeRequest(apiUrl, options);
    
    if (response.statusCode === 200 && response.data.Key) {
      console.log('✅ Success! Verification key generated:\n');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log(`🔑 VERIFICATION KEY: ${response.data.Key}`);
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
      
      // Display setup instructions
      displaySetupInstructions(response.data.Key);
      
    } else {
      console.error('❌ Failed to generate verification key');
      console.error(`Status Code: ${response.statusCode}`);
      console.error('Response:', response.data);
      
      if (response.statusCode === 401) {
        console.error('\n💡 This usually means invalid credentials. Please check:');
        console.error('   - Merchant ID is correct');
        console.error('   - API Key is correct');
        console.error('   - Environment (demo/production) matches your credentials');
      }
    }
    
  } catch (error) {
    console.error('❌ Error generating verification key:', error.message);
    process.exit(1);
  }
}

/**
 * Display webhook setup instructions
 */
function displaySetupInstructions(verificationKey) {
  console.log('📋 WEBHOOK SETUP INSTRUCTIONS:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
  
  console.log('1️⃣  Log in to Viva Self Care:');
  if (CONFIG.ENVIRONMENT === 'demo') {
    console.log('   🌐 https://demo.vivapayments.com/');
  } else {
    console.log('   🌐 https://www.vivapayments.com/');
  }
  
  console.log('\n2️⃣  Navigate to Settings > API Access > Webhooks');
  
  console.log('\n3️⃣  Click "Create Webhook"');
  
  console.log('\n4️⃣  Enter your webhook URL:');
  console.log(`   📡 ${CONFIG.WEBHOOK_URL}`);
  
  console.log('\n5️⃣  Click "Verify" - Your endpoint should return:');
  console.log(`   📄 {"Key":"${verificationKey}"}`);
  
  console.log('\n6️⃣  Select Event Types (recommended):');
  console.log('   ✅ Transaction Payment Created (1796)');
  console.log('   ✅ Transaction Failed (1798)');
  console.log('   ✅ Transaction Reversal Created (1797)');
  
  console.log('\n7️⃣  Check "Active" and click "Save"');
  
  console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('🎉 Your webhook is ready to receive Viva payment notifications!');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
  
  console.log('💡 TESTING YOUR WEBHOOK:');
  console.log(`   You can test your webhook endpoint by visiting:`);
  console.log(`   🌐 ${CONFIG.WEBHOOK_URL}`);
  console.log(`   It should return the verification key in JSON format.\n`);
  
  console.log('📚 DOCUMENTATION:');
  console.log('   🔗 https://developer.vivawallet.com/webhooks/');
  console.log('   🔗 https://developer.vivawallet.com/webhooks/webhook-events/\n');
}

/**
 * Test webhook endpoint
 */
async function testWebhookEndpoint() {
  try {
    console.log('🧪 Testing webhook endpoint...\n');
    
    const options = {
      method: 'GET',
      headers: {
        'User-Agent': 'Viva-Webhook-Tester/1.0'
      }
    };
    
    const response = await makeRequest(CONFIG.WEBHOOK_URL, options);
    
    if (response.statusCode === 200) {
      console.log('✅ Webhook endpoint is accessible');
      console.log('📄 Response:', response.data);
    } else {
      console.log(`⚠️  Webhook endpoint returned status: ${response.statusCode}`);
      console.log('📄 Response:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Error testing webhook endpoint:', error.message);
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🚀 Viva Wallet Webhook Setup Tool\n');
  
  // Check if we should test the webhook endpoint
  if (process.argv.includes('--test')) {
    await testWebhookEndpoint();
    return;
  }
  
  // Generate verification key
  await generateVerificationKey();
  
  // Optionally test the webhook endpoint
  console.log('🔍 Would you like to test your webhook endpoint? Run:');
  console.log('   node webhookurl.js --test\n');
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = {
  generateVerificationKey,
  testWebhookEndpoint,
  CONFIG
};
