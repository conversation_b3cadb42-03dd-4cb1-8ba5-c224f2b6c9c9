{"version": 3, "file": "getFirstExternalSourceForPod.js", "sourceRoot": "", "sources": ["../../src/utils/getFirstExternalSourceForPod.ts"], "names": [], "mappings": ";;AAEA,4CA2BC;AAUD,oEAmCC;AAxED,SAAgB,gBAAgB,CAC9B,WAAwB,EACxB,EAAE,IAAI,EAAE,OAAO,EAAsC;IAErD,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QACtB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,gBAAgB,GAAG,CAAC,IAAqB,EAAE,EAAE;QACjD,KAAK,MAAM,aAAa,IAAI,IAAI,EAAE,CAAC;YACjC,IAAI,aAAa,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBAChC,OAAO,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,KAAK,OAAO,CAAC;YACvD,CAAC;YACD,IAAI,aAAa,CAAC,YAAY,IAAI,gBAAgB,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC/E,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM,CAAW,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;QACtD,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,IAAI,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YACnF,OAAO,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,4BAA4B,CAC1C,WAAwB,EACxB,EAAE,IAAI,EAAE,OAAO,EAAwC;IAEvD,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QACtC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;IAClE,CAAC;SAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC9B,iCAAiC;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9C,OAAO,EAAE,GAAG,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC;QAClF,CAAC;IACH,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,GAAG,EAAE,CAAC;IACf,CAAC;IACD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEnB,MAAM,UAAU,GAAG,gBAAgB,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IAE3D,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,qEAAqE;QACrE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,4BAA4B,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;YACxF,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { ExternalSource, PodDependency, PodfileLock } from './parsePodfileLock';\n\nexport function getDependentPods(\n  podfileLock: PodfileLock,\n  { name, version }: { name: string; version?: string }\n): string[] {\n  if (!podfileLock.pods) {\n    return [];\n  }\n\n  const hasPodDependency = (pods: PodDependency[]) => {\n    for (const podDependency of pods) {\n      if (podDependency.name === name) {\n        return !version || podDependency.version === version;\n      }\n      if (podDependency.dependencies && hasPodDependency(podDependency.dependencies)) {\n        return true;\n      }\n    }\n    return false;\n  };\n\n  return podfileLock.pods.reduce<string[]>((prev, curr) => {\n    if (curr.name !== name && curr.dependencies && hasPodDependency(curr.dependencies)) {\n      return [...prev, curr.name];\n    }\n\n    return prev;\n  }, []);\n}\n\n/**\n * Find the first \"external source\" (local file path reference) for a given pod.\n *\n * @param podfileLock\n * @param props.name The pod name to search for.\n * @param props.checked A recursive parameter to prevent infinite recursion, not for public use.\n * @returns\n */\nexport function getFirstExternalSourceForPod(\n  podfileLock: PodfileLock,\n  { name, checked }: { name: string; checked?: string[] }\n): { pod: string; source: ExternalSource } | null {\n  if (!podfileLock.externalSources) {\n    return null;\n  }\n\n  if (podfileLock.externalSources[name]) {\n    return { pod: name, source: podfileLock.externalSources[name] };\n  } else if (name.includes('/')) {\n    // Short cut for pods with a path\n    const possibleName = name.split('/')[0];\n    if (podfileLock.externalSources[possibleName]) {\n      return { pod: possibleName, source: podfileLock.externalSources[possibleName] };\n    }\n  }\n\n  if (!checked) {\n    checked = [];\n  }\n  checked.push(name);\n\n  const dependents = getDependentPods(podfileLock, { name });\n\n  for (const dependent of dependents) {\n    // Prevent pods with cyclic dependencies from causing infinite loops.\n    if (!checked.includes(dependent)) {\n      const results = getFirstExternalSourceForPod(podfileLock, { name: dependent, checked });\n      if (results) {\n        return results;\n      }\n    }\n  }\n  return null;\n}\n"]}