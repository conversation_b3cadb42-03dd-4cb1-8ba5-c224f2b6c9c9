import { Asset } from 'expo-asset';
import * as FileSystem from 'expo-file-system';
import { PaxService } from './PaxService';

export class EmbeddedServer {
  private port: number = 3001;
  private isRunning: boolean = false;
  private routes: Map<string, Function> = new Map();

  constructor() {
    this.setupRoutes();
  }

  async start(paxService: PaxService): Promise<number> {
    try {
      // Copy frontend assets to local directory
      await this.copyFrontendAssets();
      
      // Start the embedded server
      await this.startServer(paxService);
      
      this.isRunning = true;
      console.log(`Embedded server started on port ${this.port}`);
      
      return this.port;
    } catch (error) {
      console.error('Failed to start embedded server:', error);
      throw error;
    }
  }

  private async copyFrontendAssets(): Promise<void> {
    try {
      // Load bundled frontend assets
      const frontendAssets = [
        require('../../assets/frontend/index.html'),
        require('../../assets/frontend/bundle.js'),
        require('../../assets/frontend/bundle.css'),
      ];

      const documentsDir = FileSystem.documentDirectory + 'frontend/';
      
      // Ensure directory exists
      await FileSystem.makeDirectoryAsync(documentsDir, { intermediates: true });

      // Copy each asset
      for (const asset of frontendAssets) {
        const assetInfo = Asset.fromModule(asset);
        await assetInfo.downloadAsync();
        
        if (assetInfo.localUri) {
          const filename = assetInfo.name || 'unknown';
          const destPath = documentsDir + filename;
          await FileSystem.copyAsync({
            from: assetInfo.localUri,
            to: destPath
          });
        }
      }

      console.log('Frontend assets copied successfully');
    } catch (error) {
      console.error('Error copying frontend assets:', error);
      throw error;
    }
  }

  private async startServer(paxService: PaxService): Promise<void> {
    // Simulate HTTP server functionality
    // In a real implementation, you'd use a React Native HTTP server library
    
    this.setupAPIRoutes(paxService);
    
    // For this example, we'll serve static files directly
    // In production, you'd use react-native-http-bridge or similar
  }

  private setupRoutes(): void {
    // Static file serving
    this.routes.set('GET /', this.serveIndex.bind(this));
    this.routes.set('GET /bundle.js', this.serveJS.bind(this));
    this.routes.set('GET /bundle.css', this.serveCSS.bind(this));
  }

  private setupAPIRoutes(paxService: PaxService): void {
    // API routes for PAX integration
    this.routes.set('POST /api/pax/payment', async (req: any) => {
      try {
        const result = await paxService.processPayment(req.body);
        return { success: true, data: result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    this.routes.set('GET /api/pax/status', async () => {
      try {
        const status = await paxService.getStatus();
        return { success: true, data: status };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    this.routes.set('POST /api/pax/test', async (req: any) => {
      try {
        const result = await paxService.testConnection();
        return { success: true, data: result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    this.routes.set('GET /api/pax/config', async () => {
      try {
        const config = await paxService.getConfiguration();
        return { success: true, data: config };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });
  }

  private async serveIndex(): Promise<string> {
    const documentsDir = FileSystem.documentDirectory + 'frontend/';
    const indexPath = documentsDir + 'index.html';
    
    try {
      const content = await FileSystem.readAsStringAsync(indexPath);
      return content;
    } catch (error) {
      return this.getDefaultHTML();
    }
  }

  private async serveJS(): Promise<string> {
    const documentsDir = FileSystem.documentDirectory + 'frontend/';
    const jsPath = documentsDir + 'bundle.js';
    
    try {
      return await FileSystem.readAsStringAsync(jsPath);
    } catch (error) {
      return '// Frontend bundle not found';
    }
  }

  private async serveCSS(): Promise<string> {
    const documentsDir = FileSystem.documentDirectory + 'frontend/';
    const cssPath = documentsDir + 'bundle.css';
    
    try {
      return await FileSystem.readAsStringAsync(cssPath);
    } catch (error) {
      return '/* Frontend styles not found */';
    }
  }

  private getDefaultHTML(): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PAX POS System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .primary { background-color: #007bff; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>PAX A920 POS System</h1>
        <div id="status" class="status">Initializing...</div>
        
        <div>
            <h3>Payment Processing</h3>
            <input type="number" id="amount" placeholder="Amount" value="10.00" step="0.01">
            <button class="primary" onclick="processPayment()">Process Payment</button>
        </div>
        
        <div>
            <h3>Terminal Operations</h3>
            <button onclick="checkStatus()">Check Status</button>
            <button onclick="testConnection()">Test Connection</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function updateStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }

        function processPayment() {
            const amount = document.getElementById('amount').value;
            updateStatus('Processing payment...', 'info');
            
            if (window.PaxBridge) {
                window.PaxBridge.processPayment({ amount: parseFloat(amount) });
            } else {
                updateStatus('PAX Bridge not available', 'error');
            }
        }

        function checkStatus() {
            updateStatus('Checking terminal status...', 'info');
            
            if (window.PaxBridge) {
                window.PaxBridge.getStatus();
            } else {
                updateStatus('PAX Bridge not available', 'error');
            }
        }

        function testConnection() {
            updateStatus('Testing connection...', 'info');
            // Implementation would call PAX test endpoint
        }

        // Listen for PAX responses
        window.addEventListener('paxPaymentResponse', (event) => {
            const result = event.detail;
            updateStatus('Payment result: ' + JSON.stringify(result), 
                        result.success ? 'success' : 'error');
        });

        window.addEventListener('paxStatusResponse', (event) => {
            const status = event.detail;
            updateStatus('Terminal status: ' + JSON.stringify(status), 'success');
        });

        updateStatus('PAX POS System Ready', 'success');
    </script>
</body>
</html>`;
  }

  stop(): void {
    this.isRunning = false;
    console.log('Embedded server stopped');
  }

  isServerRunning(): boolean {
    return this.isRunning;
  }
}
