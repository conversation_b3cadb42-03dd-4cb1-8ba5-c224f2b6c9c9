{"version": 3, "file": "Version.js", "names": ["_androidPlugins", "data", "require", "_warnings", "withVersion", "config", "withAppBuildGradle", "modResults", "language", "contents", "setVersionCode", "setVersionName", "addWarningAndroid", "exports", "withBuildScriptExtMinimumVersion", "props", "withProjectBuildGradle", "setMinBuildScriptExtVersion", "buildGradle", "name", "minVersion", "regex", "RegExp", "currentVersion", "match", "currentVersionNum", "Number", "replace", "Math", "max", "getVersionName", "android", "version", "versionName", "pattern", "getVersionCode", "versionCode"], "sources": ["../../src/android/Version.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAppBuildGradle, withProjectBuildGradle } from '../plugins/android-plugins';\nimport { addWarningAndroid } from '../utils/warnings';\n\nexport const withVersion: ConfigPlugin = (config) => {\n  return withAppBuildGradle(config, (config) => {\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = setVersionCode(config, config.modResults.contents);\n      config.modResults.contents = setVersionName(config, config.modResults.contents);\n    } else {\n      addWarningAndroid(\n        'android.versionCode',\n        `Cannot automatically configure app build.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\n/** Sets a numeric version for a value in the project.gradle buildscript.ext object to be at least the provided props.minVersion, if the existing value is greater then no change will be made. */\nexport const withBuildScriptExtMinimumVersion: ConfigPlugin<{\n  name: string;\n  minVersion: number;\n}> = (config, props) => {\n  return withProjectBuildGradle(config, (config) => {\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = setMinBuildScriptExtVersion(config.modResults.contents, props);\n    } else {\n      addWarningAndroid(\n        'withBuildScriptExtVersion',\n        `Cannot automatically configure project build.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\nexport function setMinBuildScriptExtVersion(\n  buildGradle: string,\n  { name, minVersion }: { name: string; minVersion: number }\n) {\n  const regex = new RegExp(`(${name}\\\\s?=\\\\s?)(\\\\d+(?:\\\\.\\\\d+)?)`);\n  const currentVersion = buildGradle.match(regex)?.[2];\n  if (!currentVersion) {\n    addWarningAndroid(\n      'withBuildScriptExtVersion',\n      `Cannot set minimum buildscript.ext.${name} version because the property \"${name}\" cannot be found or does not have a numeric value.`\n    );\n    // TODO: Maybe just add the property...\n    return buildGradle;\n  }\n\n  const currentVersionNum = Number(currentVersion);\n  return buildGradle.replace(regex, `$1${Math.max(minVersion, currentVersionNum)}`);\n}\n\nexport function getVersionName(config: Pick<ExpoConfig, 'version' | 'android'>) {\n  return config.android?.version ?? config.version ?? null;\n}\n\nexport function setVersionName(\n  config: Pick<ExpoConfig, 'version' | 'android'>,\n  buildGradle: string\n) {\n  const versionName = getVersionName(config);\n  if (versionName === null) {\n    return buildGradle;\n  }\n\n  const pattern = new RegExp(`versionName \".*\"`);\n  return buildGradle.replace(pattern, `versionName \"${versionName}\"`);\n}\n\nexport function getVersionCode(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.versionCode ?? 1;\n}\n\nexport function setVersionCode(config: Pick<ExpoConfig, 'android'>, buildGradle: string) {\n  const versionCode = getVersionCode(config);\n  if (versionCode === null) {\n    return buildGradle;\n  }\n\n  const pattern = new RegExp(`versionCode.*`);\n  return buildGradle.replace(pattern, `versionCode ${versionCode}`);\n}\n"], "mappings": ";;;;;;;;;;;AAGA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,oCAAkB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC5C,IAAIA,MAAM,CAACE,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CH,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGC,cAAc,CAACL,MAAM,EAAEA,MAAM,CAACE,UAAU,CAACE,QAAQ,CAAC;MAC/EJ,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGE,cAAc,CAACN,MAAM,EAAEA,MAAM,CAACE,UAAU,CAACE,QAAQ,CAAC;IACjF,CAAC,MAAM;MACL,IAAAG,6BAAiB,EACf,qBAAqB,EACrB,oEACF,CAAC;IACH;IACA,OAAOP,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAQ,OAAA,CAAAT,WAAA,GAAAA,WAAA;AACO,MAAMU,gCAGX,GAAGA,CAACT,MAAM,EAAEU,KAAK,KAAK;EACtB,OAAO,IAAAC,wCAAsB,EAACX,MAAM,EAAGA,MAAM,IAAK;IAChD,IAAIA,MAAM,CAACE,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CH,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGQ,2BAA2B,CAACZ,MAAM,CAACE,UAAU,CAACE,QAAQ,EAAEM,KAAK,CAAC;IAC7F,CAAC,MAAM;MACL,IAAAH,6BAAiB,EACf,2BAA2B,EAC3B,wEACF,CAAC;IACH;IACA,OAAOP,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACQ,OAAA,CAAAC,gCAAA,GAAAA,gCAAA;AAEK,SAASG,2BAA2BA,CACzCC,WAAmB,EACnB;EAAEC,IAAI;EAAEC;AAAiD,CAAC,EAC1D;EACA,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIH,IAAI,8BAA8B,CAAC;EAChE,MAAMI,cAAc,GAAGL,WAAW,CAACM,KAAK,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC;EACpD,IAAI,CAACE,cAAc,EAAE;IACnB,IAAAX,6BAAiB,EACf,2BAA2B,EAC3B,sCAAsCO,IAAI,kCAAkCA,IAAI,qDAClF,CAAC;IACD;IACA,OAAOD,WAAW;EACpB;EAEA,MAAMO,iBAAiB,GAAGC,MAAM,CAACH,cAAc,CAAC;EAChD,OAAOL,WAAW,CAACS,OAAO,CAACN,KAAK,EAAE,KAAKO,IAAI,CAACC,GAAG,CAACT,UAAU,EAAEK,iBAAiB,CAAC,EAAE,CAAC;AACnF;AAEO,SAASK,cAAcA,CAACzB,MAA+C,EAAE;EAC9E,OAAOA,MAAM,CAAC0B,OAAO,EAAEC,OAAO,IAAI3B,MAAM,CAAC2B,OAAO,IAAI,IAAI;AAC1D;AAEO,SAASrB,cAAcA,CAC5BN,MAA+C,EAC/Ca,WAAmB,EACnB;EACA,MAAMe,WAAW,GAAGH,cAAc,CAACzB,MAAM,CAAC;EAC1C,IAAI4B,WAAW,KAAK,IAAI,EAAE;IACxB,OAAOf,WAAW;EACpB;EAEA,MAAMgB,OAAO,GAAG,IAAIZ,MAAM,CAAC,kBAAkB,CAAC;EAC9C,OAAOJ,WAAW,CAACS,OAAO,CAACO,OAAO,EAAE,gBAAgBD,WAAW,GAAG,CAAC;AACrE;AAEO,SAASE,cAAcA,CAAC9B,MAAmC,EAAE;EAClE,OAAOA,MAAM,CAAC0B,OAAO,EAAEK,WAAW,IAAI,CAAC;AACzC;AAEO,SAAS1B,cAAcA,CAACL,MAAmC,EAAEa,WAAmB,EAAE;EACvF,MAAMkB,WAAW,GAAGD,cAAc,CAAC9B,MAAM,CAAC;EAC1C,IAAI+B,WAAW,KAAK,IAAI,EAAE;IACxB,OAAOlB,WAAW;EACpB;EAEA,MAAMgB,OAAO,GAAG,IAAIZ,MAAM,CAAC,eAAe,CAAC;EAC3C,OAAOJ,WAAW,CAACS,OAAO,CAACO,OAAO,EAAE,eAAeE,WAAW,EAAE,CAAC;AACnE", "ignoreList": []}