{"version": 3, "file": "Styles.js", "names": ["_Paths", "data", "require", "_Resources", "fallbackResourceString", "readStylesXMLAsync", "path", "fallback", "readResourcesXMLAsync", "getProjectStylesXMLPathAsync", "projectRoot", "kind", "getResourceXMLPathAsync", "name", "ensureDefaultStyleResourceXML", "xml", "ensureDefaultResourceXML", "Array", "isArray", "resources", "style", "getStyleParent", "group", "findResourceGroup", "getStylesItem", "parent", "appTheme", "item", "existingItem", "filter", "$", "head", "setStylesItem", "buildResourceGroup", "push", "_", "removeStylesItem", "index", "findIndex", "splice", "getAppThemeLightNoActionBarGroup", "getAppThemeGroup", "assignStylesValue", "add", "value", "targetApi", "buildResourceItem", "getStylesGroupAsObject", "xmlGroup", "getResourceItemsAsObject"], "sources": ["../../src/android/Styles.ts"], "sourcesContent": ["import { getResourceXMLPathAsync } from './Paths';\nimport {\n  buildResourceGroup,\n  buildResourceItem,\n  ensureDefaultResourceXML,\n  findResourceGroup,\n  getResourceItemsAsObject,\n  readResourcesXMLAsync,\n  ResourceGroupXML,\n  ResourceItemXML,\n  ResourceKind,\n  ResourceXML,\n} from './Resources';\n\n// Adds support for `tools:x`\nconst fallbackResourceString = `<?xml version=\"1.0\" encoding=\"utf-8\"?><resources xmlns:tools=\"http://schemas.android.com/tools\"></resources>`;\n\nexport async function readStylesXMLAsync({\n  path,\n  fallback = fallbackResourceString,\n}: {\n  path: string;\n  fallback?: string | null;\n}): Promise<ResourceXML> {\n  return readResourcesXMLAsync({ path, fallback });\n}\n\nexport async function getProjectStylesXMLPathAsync(\n  projectRoot: string,\n  { kind }: { kind?: ResourceKind } = {}\n): Promise<string> {\n  return getResourceXMLPathAsync(projectRoot, { kind, name: 'styles' });\n}\n\nfunction ensureDefaultStyleResourceXML(xml: ResourceXML): ResourceXML {\n  xml = ensureDefaultResourceXML(xml);\n  if (!Array.isArray(xml?.resources?.style)) {\n    xml.resources.style = [];\n  }\n  return xml;\n}\n\nexport function getStyleParent(\n  xml: ResourceXML,\n  group: { name: string; parent?: string }\n): ResourceGroupXML | null {\n  return findResourceGroup(xml.resources.style, group);\n}\n\nexport function getStylesItem({\n  name,\n  xml,\n  parent,\n}: {\n  name: string;\n  xml: ResourceXML;\n  parent: { name: string; parent?: string };\n}): ResourceItemXML | null {\n  xml = ensureDefaultStyleResourceXML(xml);\n\n  const appTheme = getStyleParent(xml, parent);\n\n  if (!appTheme) {\n    return null;\n  }\n\n  if (appTheme.item) {\n    const existingItem = appTheme.item.filter(({ $: head }) => head.name === name)[0];\n\n    // Don't want to 2 of the same item, so if one exists, we overwrite it\n    if (existingItem) {\n      return existingItem;\n    }\n  }\n  return null;\n}\n\nexport function setStylesItem({\n  item,\n  xml,\n  parent,\n}: {\n  item: ResourceItemXML;\n  xml: ResourceXML;\n  parent: { name: string; parent?: string };\n}): ResourceXML {\n  xml = ensureDefaultStyleResourceXML(xml);\n\n  let appTheme = getStyleParent(xml, parent);\n\n  if (!appTheme) {\n    appTheme = buildResourceGroup({\n      parent: 'Theme.AppCompat.Light.NoActionBar', // Default AppTheme parent\n      ...parent,\n    });\n\n    xml.resources!.style!.push(appTheme);\n  }\n\n  if (appTheme.item) {\n    const existingItem = appTheme.item.filter(({ $: head }) => head.name === item.$.name)[0];\n\n    // Don't want to 2 of the same item, so if one exists, we overwrite it\n    if (existingItem) {\n      existingItem._ = item._;\n      existingItem.$ = item.$;\n    } else {\n      appTheme.item.push(item);\n    }\n  } else {\n    appTheme.item = [item];\n  }\n  return xml;\n}\n\nexport function removeStylesItem({\n  name,\n  xml,\n  parent,\n}: {\n  name: string;\n  xml: ResourceXML;\n  parent: { name: string; parent?: string };\n}): ResourceXML {\n  xml = ensureDefaultStyleResourceXML(xml);\n  const appTheme = getStyleParent(xml, parent);\n  if (appTheme?.item) {\n    const index = appTheme.item.findIndex(({ $: head }: ResourceItemXML) => head.name === name);\n    if (index > -1) {\n      appTheme.item.splice(index, 1);\n    }\n  }\n  return xml;\n}\n\n/**\n * @deprecated Use `getAppThemeGroup` instead.\n * Matching on both style name and parent leads to prebuild issues, as `AppTheme`\n * style parent might be changed (when edge-to-edge is enabled, for example).\n */\nexport function getAppThemeLightNoActionBarGroup() {\n  return { name: 'AppTheme', parent: 'Theme.AppCompat.Light.NoActionBar' };\n}\n\n// This is a very common theme so make it reusable.\nexport function getAppThemeGroup() {\n  return { name: 'AppTheme' };\n}\n\nexport function assignStylesValue(\n  xml: ResourceXML,\n  {\n    add,\n    value,\n    targetApi,\n    name,\n    parent,\n  }: {\n    add: boolean;\n    value: string;\n    targetApi?: string;\n    name: string;\n    parent: { name: string; parent?: string };\n  }\n): ResourceXML {\n  if (add) {\n    return setStylesItem({\n      xml,\n      parent,\n      item: buildResourceItem({\n        name,\n        targetApi,\n        value,\n      }),\n    });\n  }\n  return removeStylesItem({\n    xml,\n    parent,\n    name,\n  });\n}\n\n/**\n * Helper to convert a styles.xml parent's children into a simple k/v pair.\n * Added for testing purposes.\n *\n * @param xml\n * @returns\n */\nexport function getStylesGroupAsObject(\n  xml: ResourceXML,\n  group: { name: string; parent?: string }\n): Record<string, string> | null {\n  const xmlGroup = getStyleParent(xml, group);\n  return xmlGroup?.item ? getResourceItemsAsObject(xmlGroup.item) : null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAaA;AACA,MAAMG,sBAAsB,GAAG,8GAA8G;AAEtI,eAAeC,kBAAkBA,CAAC;EACvCC,IAAI;EACJC,QAAQ,GAAGH;AAIb,CAAC,EAAwB;EACvB,OAAO,IAAAI,kCAAqB,EAAC;IAAEF,IAAI;IAAEC;EAAS,CAAC,CAAC;AAClD;AAEO,eAAeE,4BAA4BA,CAChDC,WAAmB,EACnB;EAAEC;AAA8B,CAAC,GAAG,CAAC,CAAC,EACrB;EACjB,OAAO,IAAAC,gCAAuB,EAACF,WAAW,EAAE;IAAEC,IAAI;IAAEE,IAAI,EAAE;EAAS,CAAC,CAAC;AACvE;AAEA,SAASC,6BAA6BA,CAACC,GAAgB,EAAe;EACpEA,GAAG,GAAG,IAAAC,qCAAwB,EAACD,GAAG,CAAC;EACnC,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,GAAG,EAAEI,SAAS,EAAEC,KAAK,CAAC,EAAE;IACzCL,GAAG,CAACI,SAAS,CAACC,KAAK,GAAG,EAAE;EAC1B;EACA,OAAOL,GAAG;AACZ;AAEO,SAASM,cAAcA,CAC5BN,GAAgB,EAChBO,KAAwC,EACf;EACzB,OAAO,IAAAC,8BAAiB,EAACR,GAAG,CAACI,SAAS,CAACC,KAAK,EAAEE,KAAK,CAAC;AACtD;AAEO,SAASE,aAAaA,CAAC;EAC5BX,IAAI;EACJE,GAAG;EACHU;AAKF,CAAC,EAA0B;EACzBV,GAAG,GAAGD,6BAA6B,CAACC,GAAG,CAAC;EAExC,MAAMW,QAAQ,GAAGL,cAAc,CAACN,GAAG,EAAEU,MAAM,CAAC;EAE5C,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,IAAIA,QAAQ,CAACC,IAAI,EAAE;IACjB,MAAMC,YAAY,GAAGF,QAAQ,CAACC,IAAI,CAACE,MAAM,CAAC,CAAC;MAAEC,CAAC,EAAEC;IAAK,CAAC,KAAKA,IAAI,CAAClB,IAAI,KAAKA,IAAI,CAAC,CAAC,CAAC,CAAC;;IAEjF;IACA,IAAIe,YAAY,EAAE;MAChB,OAAOA,YAAY;IACrB;EACF;EACA,OAAO,IAAI;AACb;AAEO,SAASI,aAAaA,CAAC;EAC5BL,IAAI;EACJZ,GAAG;EACHU;AAKF,CAAC,EAAe;EACdV,GAAG,GAAGD,6BAA6B,CAACC,GAAG,CAAC;EAExC,IAAIW,QAAQ,GAAGL,cAAc,CAACN,GAAG,EAAEU,MAAM,CAAC;EAE1C,IAAI,CAACC,QAAQ,EAAE;IACbA,QAAQ,GAAG,IAAAO,+BAAkB,EAAC;MAC5BR,MAAM,EAAE,mCAAmC;MAAE;MAC7C,GAAGA;IACL,CAAC,CAAC;IAEFV,GAAG,CAACI,SAAS,CAAEC,KAAK,CAAEc,IAAI,CAACR,QAAQ,CAAC;EACtC;EAEA,IAAIA,QAAQ,CAACC,IAAI,EAAE;IACjB,MAAMC,YAAY,GAAGF,QAAQ,CAACC,IAAI,CAACE,MAAM,CAAC,CAAC;MAAEC,CAAC,EAAEC;IAAK,CAAC,KAAKA,IAAI,CAAClB,IAAI,KAAKc,IAAI,CAACG,CAAC,CAACjB,IAAI,CAAC,CAAC,CAAC,CAAC;;IAExF;IACA,IAAIe,YAAY,EAAE;MAChBA,YAAY,CAACO,CAAC,GAAGR,IAAI,CAACQ,CAAC;MACvBP,YAAY,CAACE,CAAC,GAAGH,IAAI,CAACG,CAAC;IACzB,CAAC,MAAM;MACLJ,QAAQ,CAACC,IAAI,CAACO,IAAI,CAACP,IAAI,CAAC;IAC1B;EACF,CAAC,MAAM;IACLD,QAAQ,CAACC,IAAI,GAAG,CAACA,IAAI,CAAC;EACxB;EACA,OAAOZ,GAAG;AACZ;AAEO,SAASqB,gBAAgBA,CAAC;EAC/BvB,IAAI;EACJE,GAAG;EACHU;AAKF,CAAC,EAAe;EACdV,GAAG,GAAGD,6BAA6B,CAACC,GAAG,CAAC;EACxC,MAAMW,QAAQ,GAAGL,cAAc,CAACN,GAAG,EAAEU,MAAM,CAAC;EAC5C,IAAIC,QAAQ,EAAEC,IAAI,EAAE;IAClB,MAAMU,KAAK,GAAGX,QAAQ,CAACC,IAAI,CAACW,SAAS,CAAC,CAAC;MAAER,CAAC,EAAEC;IAAsB,CAAC,KAAKA,IAAI,CAAClB,IAAI,KAAKA,IAAI,CAAC;IAC3F,IAAIwB,KAAK,GAAG,CAAC,CAAC,EAAE;MACdX,QAAQ,CAACC,IAAI,CAACY,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAChC;EACF;EACA,OAAOtB,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASyB,gCAAgCA,CAAA,EAAG;EACjD,OAAO;IAAE3B,IAAI,EAAE,UAAU;IAAEY,MAAM,EAAE;EAAoC,CAAC;AAC1E;;AAEA;AACO,SAASgB,gBAAgBA,CAAA,EAAG;EACjC,OAAO;IAAE5B,IAAI,EAAE;EAAW,CAAC;AAC7B;AAEO,SAAS6B,iBAAiBA,CAC/B3B,GAAgB,EAChB;EACE4B,GAAG;EACHC,KAAK;EACLC,SAAS;EACThC,IAAI;EACJY;AAOF,CAAC,EACY;EACb,IAAIkB,GAAG,EAAE;IACP,OAAOX,aAAa,CAAC;MACnBjB,GAAG;MACHU,MAAM;MACNE,IAAI,EAAE,IAAAmB,8BAAiB,EAAC;QACtBjC,IAAI;QACJgC,SAAS;QACTD;MACF,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAOR,gBAAgB,CAAC;IACtBrB,GAAG;IACHU,MAAM;IACNZ;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASkC,sBAAsBA,CACpChC,GAAgB,EAChBO,KAAwC,EACT;EAC/B,MAAM0B,QAAQ,GAAG3B,cAAc,CAACN,GAAG,EAAEO,KAAK,CAAC;EAC3C,OAAO0B,QAAQ,EAAErB,IAAI,GAAG,IAAAsB,qCAAwB,EAACD,QAAQ,CAACrB,IAAI,CAAC,GAAG,IAAI;AACxE", "ignoreList": []}