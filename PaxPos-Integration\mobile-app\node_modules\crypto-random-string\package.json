{"name": "crypto-random-string", "version": "2.0.0", "description": "Generate a cryptographically strong random string", "license": "MIT", "repository": "sindresorhus/crypto-random-string", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["random", "string", "text", "id", "identifier", "slug", "salt", "crypto", "strong", "secure", "hex"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}