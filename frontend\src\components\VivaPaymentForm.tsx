import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { ExternalLink } from 'lucide-react';
import { useViva, useVivaConfig } from '../hooks/useViva';
import { generateOrderId } from '../utils/paymentUtils';
import { AndroidNumericKeypad } from './AndroidNumericKeypad';

interface VivaPaymentFormProps {
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  className?: string;
}

const VivaPaymentForm: React.FC<VivaPaymentFormProps> = ({
  onSuccess,
  onError,
  className = '',
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [amountInput, setAmountInput] = useState(''); // Start empty like Move form
  const [payerName, setPayerName] = useState('');
  const [payerEmail, setPayerEmail] = useState('');

  const { createPayment, isLoading } = useViva();
  const { data: vivaConfig } = useVivaConfig();

  // Get currency from backend configuration (EUR for Viva)
  const selectedCurrency = vivaConfig?.currency || 'EUR';

  const getSuccessUrl = () => `${window.location.origin}/payment/success`;
  const getCancelUrl = () => `${window.location.origin}/payment/cancel`;

  // Helper function to handle amount changes from keyboard
  const handleAmountChange = (value: string) => {
    setAmountInput(value);
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue) && numericValue >= 0) {
      setAmount(Math.round(numericValue * 100)); // Convert to cents
    }
  };

  const handleGetPaymentURL = async () => {
    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    try {
      const orderId = generateOrderId();
      console.log('=== VIVA PAYMENT REQUEST ===');
      console.log('Order ID:', orderId);
      console.log('Amount:', amount);
      console.log('Currency:', selectedCurrency);
      console.log('Payer Name:', payerName.trim());
      console.log('=== END VIVA PAYMENT REQUEST ===');

      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      console.log('=== VIVA PAYMENT RESPONSE ===');
      console.log('Full result:', result);
      console.log('Success:', result.success);
      console.log('Data:', result.data);
      console.log('=== END VIVA PAYMENT RESPONSE ===');

      if (result.success && result.data) {
        console.log('=== VIVA REDIRECT ===');
        console.log('Redirect URL:', result.data.redirect_url);
        console.log('About to redirect...');

        // Use robust redirect function
        performRedirect(result.data.redirect_url);

        toast.success('Redirecting to payment page...');
      } else {
        console.log('=== VIVA PAYMENT FAILED ===');
        console.log('Error:', result.error);
        console.log('Message:', result.message);
        toast.error(result.message || 'Failed to generate payment URL');
      }
    } catch (error) {
      console.error('Payment URL generation error:', error);
      toast.error('Failed to generate payment URL');
    }
  };

  // Direct payment URL generation (like Square)
  const handleOpenPaymentPage = async () => {
    console.log('=== VIVA BUTTON CLICKED ===');
    console.log('isLoading:', isLoading);
    console.log('Amount:', amount);
    console.log('Payer Name:', payerName);
    console.log('=== END VIVA BUTTON CLICKED ===');

    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (isLoading) {
      console.log('Request already in progress, ignoring duplicate call');
      return;
    }

    try {
      const orderId = generateOrderId();

      console.log('=== VIVA PAYMENT REQUEST ===');
      console.log('Order ID:', orderId);
      console.log('Amount:', amount);
      console.log('Currency:', selectedCurrency);
      console.log('Payer Name:', payerName.trim());
      console.log('=== END VIVA PAYMENT REQUEST ===');

      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      console.log('=== VIVA PAYMENT URL RESPONSE ===');
      console.log('Full result:', result);
      console.log('Result type:', typeof result);
      console.log('Has success field:', 'success' in result);
      console.log('Has data field:', 'data' in result);
      console.log('Has redirect_url field:', 'redirect_url' in result);
      console.log('=== END VIVA PAYMENT URL RESPONSE ===');

      // Handle both wrapped and unwrapped response formats
      let paymentData: any = null;
      let isSuccess = false;

      // Check for wrapped response (expected format like Square)
      if (result.success && result.data && result.data.redirect_url) {
        paymentData = result.data;
        isSuccess = true;
        console.log('=== VIVA WRAPPED RESPONSE DETECTED ===');
      }
      // Check for unwrapped response (current actual format)
      else if ((result as any).redirect_url) {
        paymentData = result as any;
        isSuccess = true;
        console.log('=== VIVA UNWRAPPED RESPONSE DETECTED ===');
      }

      if (isSuccess && paymentData && paymentData.redirect_url) {
        console.log('=== VIVA PAYMENT URL GENERATED ===');
        console.log('Payment URL:', paymentData.redirect_url);
        console.log('Transaction ID:', paymentData.transaction_id);
        console.log('Order ID:', paymentData.order_id);
        console.log('Order Code:', paymentData.orderCode);
        console.log('=== END VIVA PAYMENT URL ===');

        // Show success message before redirect
        toast.success('Redirecting to payment page...');

        // Use robust redirect function (like Square) - redirect in same tab
        performRedirect(paymentData.redirect_url);
      } else {
        console.log('=== VIVA PAYMENT URL FAILED ===');
        console.log('Error:', result.error);
        console.log('Message:', result.message);
        console.log('Missing redirect_url in both formats');
        console.log('Full result for debugging:', JSON.stringify(result, null, 2));
        toast.error(result.message || 'Failed to generate payment URL');
      }
    } catch (error) {
      console.error('Payment URL generation error:', error);
      toast.error('Failed to generate payment URL');
    }
  };

  // Robust redirect function that tries multiple methods (same tab like Square)
  const performRedirect = (url: string) => {
    console.log('=== VIVA REDIRECT ATTEMPT ===');
    console.log('Attempting redirect to:', url);
    console.log('URL type:', typeof url);
    console.log('URL length:', url?.length);
    console.log('=== END VIVA REDIRECT ATTEMPT ===');

    if (!url || typeof url !== 'string' || url.trim() === '') {
      console.error('Invalid redirect URL provided:', url);
      toast.error('Invalid payment URL received');
      return;
    }

    // Method 1: Direct assignment (most reliable for same tab)
    try {
      console.log('Trying Method 1: window.location.assign()');
      window.location.assign(url);
      return;
    } catch (e1) {
      console.warn('Method 1 failed:', e1);
    }

    // Method 2: Standard href assignment
    try {
      console.log('Trying Method 2: window.location.href');
      window.location.href = url;
      return;
    } catch (e2) {
      console.warn('Method 2 failed:', e2);
    }

    // Method 3: Replace current page
    try {
      console.log('Trying Method 3: window.location.replace()');
      window.location.replace(url);
      return;
    } catch (e3) {
      console.warn('Method 3 failed:', e3);
    }

    // Method 4: Create and click a link element (same tab)
    try {
      console.log('Trying Method 4: programmatic link click');
      const link = document.createElement('a');
      link.href = url;
      link.target = '_self'; // Same tab like Square
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      return;
    } catch (e4) {
      console.warn('Method 4 failed:', e4);
    }

    // Method 5: Force navigation using form submission
    try {
      console.log('Trying Method 5: form submission');
      const form = document.createElement('form');
      form.method = 'GET';
      form.action = url;
      form.target = '_self'; // Same tab
      form.style.display = 'none';
      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);
      return;
    } catch (e5) {
      console.warn('Method 5 failed:', e5);
    }

    // If all methods fail, show the URL to the user
    console.error('All redirect methods failed. Showing URL to user.');
    toast.error('Automatic redirect failed. Please click the link below to complete payment.');

    // Create a visible link for the user to click (same tab)
    const linkElement = document.createElement('a');
    linkElement.href = url;
    linkElement.target = '_self'; // Same tab like Square
    linkElement.textContent = 'Click here to complete payment';
    linkElement.style.cssText = 'display: block; margin: 10px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center;';

    // Find a container to add the link
    const container = document.querySelector('.space-y-6') || document.body;
    container.appendChild(linkElement);
  };



  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 font-['Poppins'] mb-2">
          Viva Wallet
        </h2>
        <p className="text-gray-600 font-['Poppins']">
          European payment processing ({selectedCurrency})
        </p>
      </div>

      {/* Main Form */}
        <div className="space-y-6">
          {/* Amount Input with Keyboard */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Amount ({selectedCurrency}) *
            </label>
            <AndroidNumericKeypad
              value={amountInput}
              onChange={handleAmountChange}
              placeholder="0.00"
              maxLength={8}
              allowDecimal={true}
              className="w-full"
              currency={selectedCurrency}
            />
          </div>

          {/* Payer Name */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Payer Name *
            </label>
            <input
              type="text"
              value={payerName}
              onChange={(e) => setPayerName(e.target.value)}
              placeholder="Enter payer name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins']"
              required
            />
          </div>

          {/* Payer Email */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Payer Email (Optional)
            </label>
            <input
              type="email"
              value={payerEmail}
              onChange={(e) => setPayerEmail(e.target.value)}
              placeholder="Enter payer email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins']"
            />
          </div>

          <div className="grid grid-cols-1 gap-3">
            <button
              onClick={handleOpenPaymentPage}
              disabled={isLoading}
              className={`flex items-center justify-center gap-3 w-full py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins'] font-medium transition-all duration-200 ${
                isLoading
                  ? 'bg-blue-400 cursor-not-allowed opacity-75'
                  : 'bg-blue-600 hover:bg-blue-700 cursor-pointer'
              }`}
            >
              {isLoading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Opening...
                </>
              ) : (
                <>
                  <ExternalLink className="w-5 h-5" />
                  Open Payment Page
                </>
              )}
            </button>
          </div>
        </div>


    </div>
  );
};

export default VivaPaymentForm;
