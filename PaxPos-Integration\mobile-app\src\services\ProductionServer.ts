import httpBridge from 'react-native-http-bridge';
import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import { DatabaseService } from './DatabaseService';
import { PaxService } from './PaxService';
import { SecurityService } from './SecurityService';
import { LoggingService } from './LoggingService';

export class ProductionServer {
  private port: number = 8080;
  private isRunning: boolean = false;
  private routes: Map<string, Function> = new Map();
  private middleware: Function[] = [];
  
  private dbService: DatabaseService;
  private paxService: PaxService;
  private securityService: SecurityService;
  private logger: LoggingService;

  constructor() {
    this.dbService = new DatabaseService();
    this.paxService = new PaxService();
    this.securityService = new SecurityService();
    this.logger = new LoggingService();
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  async start(): Promise<number> {
    try {
      // Initialize all services
      await this.initializeServices();
      
      // Copy and serve frontend assets
      await this.setupFrontendAssets();
      
      // Start HTTP server
      await this.startHttpServer();
      
      this.isRunning = true;
      this.logger.info('Production server started', { port: this.port });
      
      return this.port;
    } catch (error) {
      this.logger.error('Failed to start production server', error);
      throw error;
    }
  }

  private async initializeServices(): Promise<void> {
    await this.dbService.initialize();
    await this.paxService.initialize();
    await this.securityService.initialize();
    await this.logger.initialize();
  }

  private async setupFrontendAssets(): Promise<void> {
    const frontendDir = FileSystem.documentDirectory + 'frontend/';
    await FileSystem.makeDirectoryAsync(frontendDir, { intermediates: true });

    // Load and copy all frontend assets
    const assets = [
      { module: require('../../assets/frontend/index.html'), name: 'index.html' },
      { module: require('../../assets/frontend/bundle.js'), name: 'bundle.js' },
      { module: require('../../assets/frontend/bundle.css'), name: 'bundle.css' },
      { module: require('../../assets/frontend/manifest.json'), name: 'manifest.json' },
    ];

    for (const asset of assets) {
      try {
        const assetInfo = Asset.fromModule(asset.module);
        await assetInfo.downloadAsync();
        
        if (assetInfo.localUri) {
          const destPath = frontendDir + asset.name;
          await FileSystem.copyAsync({
            from: assetInfo.localUri,
            to: destPath
          });
        }
      } catch (error) {
        this.logger.warn(`Failed to copy asset: ${asset.name}`, error);
      }
    }
  }

  private async startHttpServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        httpBridge.start(this.port, 'http_service', (request: any) => {
          this.handleRequest(request);
        });
        
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  private setupMiddleware(): void {
    // Security middleware
    this.middleware.push(this.securityMiddleware.bind(this));
    
    // Logging middleware
    this.middleware.push(this.loggingMiddleware.bind(this));
    
    // CORS middleware
    this.middleware.push(this.corsMiddleware.bind(this));
    
    // Authentication middleware
    this.middleware.push(this.authMiddleware.bind(this));
  }

  private setupRoutes(): void {
    // Static file routes
    this.routes.set('GET /', this.serveIndex.bind(this));
    this.routes.set('GET /bundle.js', this.serveJS.bind(this));
    this.routes.set('GET /bundle.css', this.serveCSS.bind(this));
    this.routes.set('GET /manifest.json', this.serveManifest.bind(this));

    // API Routes - Authentication
    this.routes.set('POST /api/auth/login', this.handleLogin.bind(this));
    this.routes.set('POST /api/auth/logout', this.handleLogout.bind(this));
    this.routes.set('GET /api/auth/verify', this.verifyToken.bind(this));

    // API Routes - PAX Terminal
    this.routes.set('POST /api/pax/payment', this.processPayment.bind(this));
    this.routes.set('POST /api/pax/refund', this.processRefund.bind(this));
    this.routes.set('POST /api/pax/void', this.processVoid.bind(this));
    this.routes.set('GET /api/pax/status', this.getTerminalStatus.bind(this));
    this.routes.set('POST /api/pax/test', this.testTerminal.bind(this));
    this.routes.set('GET /api/pax/config', this.getTerminalConfig.bind(this));
    this.routes.set('POST /api/pax/config', this.updateTerminalConfig.bind(this));

    // API Routes - Transactions
    this.routes.set('GET /api/transactions', this.getTransactions.bind(this));
    this.routes.set('GET /api/transactions/:id', this.getTransaction.bind(this));
    this.routes.set('POST /api/transactions', this.createTransaction.bind(this));
    this.routes.set('PUT /api/transactions/:id', this.updateTransaction.bind(this));
    this.routes.set('DELETE /api/transactions/:id', this.deleteTransaction.bind(this));

    // API Routes - Receipts
    this.routes.set('GET /api/receipts/:id', this.getReceipt.bind(this));
    this.routes.set('POST /api/receipts/:id/print', this.printReceipt.bind(this));
    this.routes.set('POST /api/receipts/:id/email', this.emailReceipt.bind(this));

    // API Routes - Inventory
    this.routes.set('GET /api/inventory', this.getInventory.bind(this));
    this.routes.set('POST /api/inventory', this.addInventoryItem.bind(this));
    this.routes.set('PUT /api/inventory/:id', this.updateInventoryItem.bind(this));
    this.routes.set('DELETE /api/inventory/:id', this.deleteInventoryItem.bind(this));

    // API Routes - Reports
    this.routes.set('GET /api/reports/sales', this.getSalesReport.bind(this));
    this.routes.set('GET /api/reports/transactions', this.getTransactionReport.bind(this));
    this.routes.set('GET /api/reports/inventory', this.getInventoryReport.bind(this));

    // API Routes - System
    this.routes.set('GET /api/system/health', this.getSystemHealth.bind(this));
    this.routes.set('GET /api/system/logs', this.getSystemLogs.bind(this));
    this.routes.set('POST /api/system/backup', this.createBackup.bind(this));
    this.routes.set('POST /api/system/restore', this.restoreBackup.bind(this));
  }

  private async handleRequest(request: any): Promise<void> {
    try {
      const { method, url, headers, body } = request;
      const routeKey = `${method} ${url.split('?')[0]}`;
      
      // Apply middleware
      for (const middleware of this.middleware) {
        const result = await middleware(request);
        if (result === false) {
          return; // Middleware blocked the request
        }
      }

      // Find and execute route handler
      const handler = this.routes.get(routeKey);
      if (handler) {
        const response = await handler(request);
        this.sendResponse(request, response);
      } else {
        this.sendNotFound(request);
      }

    } catch (error) {
      this.logger.error('Request handling error', error);
      this.sendError(request, error);
    }
  }

  // Middleware implementations
  private async securityMiddleware(request: any): Promise<boolean> {
    // Implement security checks
    return this.securityService.validateRequest(request);
  }

  private async loggingMiddleware(request: any): Promise<boolean> {
    this.logger.info('HTTP Request', {
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent']
    });
    return true;
  }

  private async corsMiddleware(request: any): Promise<boolean> {
    // Add CORS headers
    request.corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    };
    return true;
  }

  private async authMiddleware(request: any): Promise<boolean> {
    // Skip auth for public routes
    const publicRoutes = ['/', '/bundle.js', '/bundle.css', '/manifest.json', '/api/auth/login'];
    if (publicRoutes.includes(request.url)) {
      return true;
    }

    return this.securityService.verifyAuthentication(request);
  }

  // Static file handlers
  private async serveIndex(): Promise<any> {
    const content = await this.readFile('frontend/index.html');
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'text/html' },
      body: content
    };
  }

  private async serveJS(): Promise<any> {
    const content = await this.readFile('frontend/bundle.js');
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/javascript' },
      body: content
    };
  }

  private async serveCSS(): Promise<any> {
    const content = await this.readFile('frontend/bundle.css');
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'text/css' },
      body: content
    };
  }

  private async serveManifest(): Promise<any> {
    const content = await this.readFile('frontend/manifest.json');
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: content
    };
  }

  private async readFile(path: string): Promise<string> {
    try {
      const fullPath = FileSystem.documentDirectory + path;
      return await FileSystem.readAsStringAsync(fullPath);
    } catch (error) {
      this.logger.warn(`Failed to read file: ${path}`, error);
      return '';
    }
  }

  // Response helpers
  private sendResponse(request: any, response: any): void {
    const headers = {
      ...response.headers,
      ...request.corsHeaders
    };

    httpBridge.respond(request.requestId, response.statusCode || 200, 'application/json', JSON.stringify(response.body || response), headers);
  }

  private sendNotFound(request: any): void {
    httpBridge.respond(request.requestId, 404, 'application/json', JSON.stringify({ error: 'Not Found' }), request.corsHeaders);
  }

  private sendError(request: any, error: any): void {
    httpBridge.respond(request.requestId, 500, 'application/json', JSON.stringify({ error: error.message }), request.corsHeaders);
  }

  stop(): void {
    if (this.isRunning) {
      httpBridge.stop();
      this.isRunning = false;
      this.logger.info('Production server stopped');
    }
  }

  isServerRunning(): boolean {
    return this.isRunning;
  }
}
