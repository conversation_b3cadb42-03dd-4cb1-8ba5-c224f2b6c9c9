{"version": 3, "file": "ExpoAsset.js", "sourceRoot": "", "sources": ["../src/ExpoAsset.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,MAAM,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;AAErD;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,GAAW,EACX,OAAsB,EACtB,IAAY;IAEZ,OAAO,WAAW,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nconst AssetModule = requireNativeModule('ExpoAsset');\n\n/**\n * Downloads the asset from the given URL to a local cache and returns the local URL of the cached\n * file.\n *\n * If there is already a locally cached file and its MD5 hash matches the given `md5Hash` parameter,\n * if present, the remote asset is not downloaded. The `hash` property is included in Metro's asset\n * metadata objects when this module's `hashAssetFiles` plugin is used, which is the typical way the\n * `md5Hash` parameter of this function is provided.\n */\nexport async function downloadAsync(\n  url: string,\n  md5Hash: string | null,\n  type: string\n): Promise<string> {\n  return AssetModule.downloadAsync(url, md5Hash, type);\n}\n"]}