/**
 * PAX POS Service Integration Layer
 * Provides web-compatible interface to PAX A920 terminal functionality
 */

import { env } from '../config/env';
import { paxHardwareService } from './paxHardwareService';

export interface PaxPaymentRequest {
  amount: number;
  tenderType?: 'CREDIT' | 'DEBIT' | 'CASH';
  transType?: 'SALE' | 'REFUND' | 'VOID';
  referenceNumber?: string;
  items?: Array<{
    name: string;
    price: number;
    quantity: number;
  }>;
}

export interface PaxPaymentResponse {
  success: boolean;
  transactionId?: string;
  authCode?: string;
  resultCode?: string;
  message?: string;
  receiptData?: {
    customer: string;
    merchant: string;
  };
  cardInfo?: {
    last4?: string;
    brand?: string;
    entryMethod?: string;
  };
}

export interface PaxTerminalStatus {
  connected: boolean;
  ip?: string;
  port?: number;
  model?: string;
  serialNumber?: string;
  capabilities?: {
    contactless: boolean;
    emv: boolean;
    magneticStripe: boolean;
    printer: boolean;
  };
  lastResponse?: any;
}

export interface PaxConfiguration {
  terminal: {
    ip: string;
    port: number;
    timeout: number;
  };
  merchant: {
    id: string;
    name: string;
  };
  features: {
    receiptEnabled: boolean;
    signatureRequired: boolean;
  };
}

class PaxPosService {
  private baseUrl: string;
  private isInitialized: boolean = false;
  private useHardware: boolean = false;

  constructor() {
    // For Vercel deployment, use the local server API URL
    // This should point to your local server running the PAX bridge
    this.baseUrl = env.API_URL || 'http://localhost:3001';
  }

  /**
   * Initialize the PAX POS service
   */
  async initialize(): Promise<void> {
    try {
      // For Vercel deployment, always use API mode to communicate with local server
      // The local server will handle the hardware integration
      const status = await this.getTerminalStatus();
      this.isInitialized = status.connected;
      this.useHardware = false; // Always use API mode for Vercel deployment

      if (this.isInitialized) {
        console.log('PAX POS service initialized - connected to local server');
      } else {
        console.warn('PAX terminal not connected, running in simulation mode');
      }
    } catch (error) {
      console.error('Failed to initialize PAX POS service:', error);
      this.isInitialized = false;
      this.useHardware = false;
    }
  }

  /**
   * Check if PAX service is available
   */
  isAvailable(): boolean {
    return this.isInitialized;
  }

  /**
   * Process a payment transaction
   */
  async processPayment(request: PaxPaymentRequest): Promise<PaxPaymentResponse> {
    try {
      // Validate request
      if (!request.amount || request.amount <= 0) {
        throw new Error('Invalid payment amount');
      }

      // Use hardware service if available
      if (this.useHardware && paxHardwareService.isAvailable()) {
        const hardwareRequest = {
          amount: Math.round(request.amount * 100), // Convert to cents
          tenderType: request.tenderType || 'CREDIT',
          transType: request.transType || 'SALE',
          referenceNumber: request.referenceNumber || this.generateReferenceNumber(),
          items: request.items || []
        };

        const hardwareResult = await paxHardwareService.processPayment(hardwareRequest);

        return {
          success: hardwareResult.success,
          transactionId: hardwareResult.transactionId,
          authCode: hardwareResult.authCode,
          resultCode: hardwareResult.resultCode,
          message: hardwareResult.message || (hardwareResult.success ? 'Payment processed successfully' : 'Payment failed'),
          receiptData: hardwareResult.receiptData ? {
            customer: hardwareResult.receiptData,
            merchant: hardwareResult.receiptData.replace('CUSTOMER COPY', 'MERCHANT COPY')
          } : undefined,
          cardInfo: hardwareResult.cardInfo
        };
      }

      // Fallback to API/simulation mode
      const response = await fetch(`${this.baseUrl}/api/v1/pax/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: Math.round(request.amount * 100), // Convert to cents
          tenderType: request.tenderType || 'CREDIT',
          transType: request.transType || 'SALE',
          referenceNumber: request.referenceNumber || this.generateReferenceNumber(),
          items: request.items || []
        }),
      });

      if (!response.ok) {
        throw new Error(`Payment request failed: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Payment processing failed');
      }

      return {
        success: true,
        transactionId: result.data?.transactionId,
        authCode: result.data?.authCode,
        resultCode: result.data?.resultCode,
        message: result.data?.message || 'Payment processed successfully',
        receiptData: result.data?.receiptData,
        cardInfo: result.data?.cardInfo
      };

    } catch (error) {
      console.error('PAX payment processing error:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Payment processing failed'
      };
    }
  }

  /**
   * Get terminal status and capabilities
   */
  async getTerminalStatus(): Promise<PaxTerminalStatus> {
    try {
      // Use hardware service if available
      if (this.useHardware && paxHardwareService.isAvailable()) {
        const hardwareStatus = await paxHardwareService.getHardwareStatus();

        return {
          connected: hardwareStatus.connected && hardwareStatus.terminalReachable,
          model: 'PAX A920 (Hardware)',
          capabilities: hardwareStatus.capabilities || {
            contactless: true,
            emv: true,
            magneticStripe: true,
            printer: true,
          },
          lastResponse: hardwareStatus
        };
      }

      // Fallback to API mode
      const response = await fetch(`${this.baseUrl}/api/v1/pax/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Status request failed: ${response.statusText}`);
      }

      const result = await response.json();

      return {
        connected: result.success && result.data?.connected,
        ip: result.data?.terminal?.ip,
        port: result.data?.terminal?.port,
        model: result.data?.terminal?.model || 'PAX A920',
        serialNumber: result.data?.terminal?.serialNumber,
        capabilities: {
          contactless: result.data?.capabilities?.contactless ?? true,
          emv: result.data?.capabilities?.emv ?? true,
          magneticStripe: result.data?.capabilities?.magneticStripe ?? true,
          printer: result.data?.capabilities?.printer ?? true,
        },
        lastResponse: result.data
      };

    } catch (error) {
      console.error('PAX status check error:', error);
      return {
        connected: false,
        model: 'PAX A920 (Disconnected)'
      };
    }
  }

  /**
   * Test terminal connection
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/pax/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Connection test failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      return {
        success: result.success,
        message: result.message || (result.success ? 'Connection successful' : 'Connection failed')
      };

    } catch (error) {
      console.error('PAX connection test error:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Connection test failed'
      };
    }
  }

  /**
   * Cancel current transaction
   */
  async cancelTransaction(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/pax/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Cancel request failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      return {
        success: result.success,
        message: result.message || (result.success ? 'Transaction cancelled' : 'Cancel failed')
      };

    } catch (error) {
      console.error('PAX cancel transaction error:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Cancel transaction failed'
      };
    }
  }

  /**
   * Get PAX terminal configuration
   */
  async getConfiguration(): Promise<PaxConfiguration | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/pax/config`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Config request failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get configuration');
      }

      return result.data;

    } catch (error) {
      console.error('PAX get configuration error:', error);
      return null;
    }
  }

  /**
   * Generate a unique reference number for transactions
   */
  private generateReferenceNumber(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `PAX${timestamp}${random}`;
  }

  /**
   * Print receipt to thermal printer (hardware only)
   */
  async printReceipt(receiptData: string, copies: number = 1): Promise<{ success: boolean; message: string }> {
    try {
      if (this.useHardware && paxHardwareService.isAvailable()) {
        return await paxHardwareService.printReceipt(receiptData, copies);
      } else {
        // Fallback to browser print for non-hardware environments
        return {
          success: false,
          message: 'Thermal printer not available - use browser print instead'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Print failed'
      };
    }
  }

  /**
   * Check if running in simulation mode (for development/cloud deployment)
   */
  isSimulationMode(): boolean {
    return !this.useHardware || !this.isInitialized || env.NODE_ENV === 'development';
  }

  /**
   * Check if hardware mode is available
   */
  isHardwareMode(): boolean {
    return this.useHardware && paxHardwareService.isAvailable();
  }

  /**
   * Simulate payment for development/cloud environments
   */
  async simulatePayment(request: PaxPaymentRequest): Promise<PaxPaymentResponse> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate random success/failure for testing
    const success = Math.random() > 0.1; // 90% success rate

    if (success) {
      return {
        success: true,
        transactionId: `SIM${Date.now()}`,
        authCode: Math.floor(Math.random() * 999999).toString().padStart(6, '0'),
        resultCode: '00',
        message: 'Simulated payment successful',
        receiptData: {
          customer: this.generateSimulatedReceipt(request, true),
          merchant: this.generateSimulatedReceipt(request, false)
        },
        cardInfo: {
          last4: '1234',
          brand: 'VISA',
          entryMethod: 'CHIP'
        }
      };
    } else {
      return {
        success: false,
        message: 'Simulated payment declined'
      };
    }
  }

  /**
   * Generate simulated receipt for development
   */
  private generateSimulatedReceipt(request: PaxPaymentRequest, customerCopy: boolean): string {
    const timestamp = new Date().toLocaleString();
    const copy = customerCopy ? 'CUSTOMER COPY' : 'MERCHANT COPY';

    return `
=============================
        PAXSOFT POS
      ${copy}
=============================
Date: ${timestamp}
Amount: $${request.amount.toFixed(2)}
Type: ${request.transType || 'SALE'}
Tender: ${request.tenderType || 'CREDIT'}
Ref: ${request.referenceNumber || 'N/A'}
=============================
    SIMULATED TRANSACTION
=============================
    `.trim();
  }
}

// Export singleton instance
export const paxPosService = new PaxPosService();
export default paxPosService;
