{"version": 3, "file": "jimp.js", "sourceRoot": "", "sources": ["../src/jimp.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,8CAWC;AAED,sCAaC;AAED,8BAmCC;AAED,sCAMC;AAED,kCA8BC;AAKD,8CAaC;AAED,8CAKC;AAED,wBAmCC;AAvLD,4CAAoB;AACpB,aAAa;AACb,gEAAgC;AAChC,2CAA6B;AAetB,KAAK,UAAU,iBAAiB,CAAC,MAAc,EAAE,KAAe;IACrE,OAAO,OAAO,CAAC,GAAG,CAChB,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QACvB,wDAAwD;QACxD,wDAAwD;QACxD,MAAM,SAAS,GAAG,MAAM,sBAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;QAEjC,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC,CAAC,CACH,CAAC;AACJ,CAAC;AAED,SAAgB,aAAa,CAAC,MAAe;IAC3C,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,OAAO,MAAM,CAAC;IAEjD,MAAM,KAAK,GAAG,MAAM,EAAE,WAAW,EAAE,CAAC;IACpC,QAAQ,KAAK,EAAE,CAAC;QACd,KAAK,KAAK,CAAC;QACX,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,SAAS,KAAK,EAAE,CAAC;QAC1B,KAAK,KAAK;YACR,OAAO,YAAY,CAAC;IACxB,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAEM,KAAK,UAAU,SAAS,CAC7B,OAA0B,EAC1B,WAAkC,EAAE;IAEpC,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;QACpB,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,KAAW,CAAC;YAChB,IAAI,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;gBACnC,KAAK,GAAG,MAAM,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACzC,CAAC;iBAAM,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC3C,KAAK,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,CAAC,SAAS,8BAA8B,CAAC,CAAC;YACtF,CAAC;YACD,aAAa;YACb,OAAO,SAAS,CAAC,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,MAAM,KAAK,GAAG,MAAM,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACrD,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACnF,MAAM,SAAS,GAAW,MAAM,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAE3D,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;QACvC,IAAI,MAAM,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACxC,MAAM,YAAE,CAAC,QAAQ,CAAC,SAAS,CACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAC/D,SAAS,CACV,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,YAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAEM,KAAK,UAAU,aAAa,CAAC,IAAY;IAC9C,IAAI,CAAC;QACH,OAAO,CAAC,MAAM,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,SAAgB,WAAW,CAAC,IAAU;IACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAEjE,MAAM,MAAM,GAAG;QACb,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;KAC1B,CAAC;IAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,IAAI;aACD,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;aAC1B,IAAI,CACH,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,EAClC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,EACnC,QAAQ,EACR,QAAQ,CACT;aACA,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAS,EAAE,CAAS,EAAE,GAAW,EAAE,EAAE;YACzE,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC;YAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAE9E,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAChC,CAAC;iBAAM,IAAI,MAAM,GAAG,IAAI,GAAG,GAAG,EAAE,CAAC;gBAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;QAEL,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,iBAAiB,CAAC,EACtC,IAAI,EACJ,KAAK,GAAG,SAAS,EACjB,IAAI,GAAG,sBAAI,CAAC,QAAQ,GAKrB;IACC,MAAM,KAAK,GAAG,MAAM,IAAI,sBAAI,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAEhD,iCAAiC;IACjC,OAAO,MAAM,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAC1C,CAAC;AAEM,KAAK,UAAU,iBAAiB,CAAC,KAA6B;IACnE,oCAAoC;IACpC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,YAAY,MAAM;QAAE,OAAO,MAAM,sBAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAExF,OAAO,KAAK,CAAC;AACf,CAAC;AAEM,KAAK,UAAU,MAAM,CAC1B,EAAE,KAAK,EAAE,OAAO,GAAG,GAAG,EAAqB,EAC3C,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAoC;IAE9E,IAAI,YAAY,GAAG,MAAM,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAElD,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;QACrB,MAAM,GAAG,sBAAI,CAAC,IAAI,CAAC;IACrB,CAAC;SAAM,IAAI,CAAC,KAAK,IAAI,MAAM,EAAE,CAAC;QAC5B,KAAK,GAAG,sBAAI,CAAC,IAAI,CAAC;IACpB,CAAC;SAAM,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC;QAClC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;IACtC,CAAC;IAED,MAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;IAC/C,MAAM,WAAW,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;IAChE,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;QACpB,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACjE,CAAC;SAAM,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QAC7B,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACnE,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CACb,oBAAoB,GAAG,6DAA6D,CACrF,CAAC;IACJ,CAAC;IACD,IAAI,UAAU,EAAE,CAAC;QACf,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC,IAAI,sBAAI,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC/E,IAAI,EAAE,sBAAI,CAAC,sBAAsB;YACjC,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,CAAC;SACf,CAAC,CAAC;IACL,CAAC;IAED,OAAO,MAAM,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACjD,CAAC;AAED,KAAK,UAAU,OAAO,CACpB,EAAE,KAAK,EAAE,OAAO,GAAG,GAAG,EAAqB,EAC3C,EAAE,UAAU,EAAqC;IAEjD,MAAM,YAAY,GAAG,MAAM,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,WAAW,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;IAChE,OAAO,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,sBAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;AACtF,CAAC;AAED;;;;GAIG;AACH,SAAS,eAAe,CAAC,QAAmB;IAC1C,IAAI,CAAC,QAAQ;QAAE,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC;IAEhD,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,QAAQ,CAAC;QACd,KAAK,QAAQ;YACX,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,uBAAuB,CAAC;QACnE,KAAK,OAAO,CAAC;QACb,KAAK,KAAK;YACR,OAAO,sBAAI,CAAC,kBAAkB,GAAG,sBAAI,CAAC,uBAAuB,CAAC;QAChE,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO;YACV,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,sBAAsB,CAAC;QAClE,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ;YACX,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,uBAAuB,CAAC;QACnE,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,qBAAqB,CAAC;QACjE,KAAK,WAAW,CAAC;QACjB,KAAK,WAAW;YACd,OAAO,sBAAI,CAAC,kBAAkB,GAAG,sBAAI,CAAC,sBAAsB,CAAC;QAC/D,KAAK,WAAW,CAAC;QACjB,KAAK,cAAc;YACjB,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,sBAAsB,CAAC;QAClE,KAAK,WAAW,CAAC;QACjB,KAAK,aAAa;YAChB,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,qBAAqB,CAAC;QACjE,KAAK,WAAW,CAAC;QACjB,KAAK,UAAU;YACb,OAAO,sBAAI,CAAC,kBAAkB,GAAG,sBAAI,CAAC,qBAAqB,CAAC;QAC9D,KAAK,SAAS,CAAC;QACf,KAAK,WAAW;YACd,MAAM,IAAI,KAAK,CAAC,cAAc,QAAQ,oBAAoB,CAAC,CAAC;QAC9D;YACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,GAAG,CAAC,CAAC;IACvD,CAAC;AACH,CAAC"}