import TcpSocket from 'react-native-tcp-socket';

export interface PaxPaymentRequest {
  amount: number;
  transType?: 'SALE' | 'REFUND' | 'VOID';
  tenderType?: 'CREDIT' | 'DEBIT' | 'CASH';
}

export interface PaxPaymentResponse {
  success: boolean;
  transactionId?: string;
  authCode?: string;
  resultCode?: string;
  message?: string;
  receiptData?: string;
}

export interface PaxTerminalStatus {
  connected: boolean;
  ip: string;
  port: number;
  model: string;
  capabilities: {
    contactless: boolean;
    emv: boolean;
    magneticStripe: boolean;
    printer: boolean;
    camera: boolean;
    wifi: boolean;
    cellular: boolean;
  };
  lastResponse?: any;
}

export class PaxService {
  private terminalIP: string = '*************';
  private terminalPort: number = 8080;
  private socket: any = null;
  private isConnected: boolean = false;

  async initialize(): Promise<void> {
    try {
      // Initialize PAX terminal connection
      await this.connectToTerminal();
      console.log('PAX Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize PAX Service:', error);
      throw error;
    }
  }

  private async connectToTerminal(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = TcpSocket.createConnection({
          port: this.terminalPort,
          host: this.terminalIP,
          timeout: 5000,
        }, () => {
          console.log('Connected to PAX terminal');
          this.isConnected = true;
          resolve();
        });

        this.socket.on('data', (data: Buffer) => {
          console.log('Received from PAX:', data.toString());
          this.handleTerminalResponse(data.toString());
        });

        this.socket.on('error', (error: Error) => {
          console.error('PAX connection error:', error);
          this.isConnected = false;
          reject(error);
        });

        this.socket.on('close', () => {
          console.log('PAX connection closed');
          this.isConnected = false;
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  async processPayment(request: PaxPaymentRequest): Promise<PaxPaymentResponse> {
    try {
      if (!this.isConnected) {
        await this.connectToTerminal();
      }

      // Build PAX command
      const command = this.buildPaxCommand('SALE', request);
      
      // Send command to terminal
      const response = await this.sendCommand(command);
      
      // Parse response
      return this.parsePaymentResponse(response);

    } catch (error) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        message: error.message || 'Payment processing failed'
      };
    }
  }

  async getStatus(): Promise<PaxTerminalStatus> {
    try {
      if (!this.isConnected) {
        await this.connectToTerminal();
      }

      // Send status inquiry command
      const command = this.buildPaxCommand('STATUS', {});
      const response = await this.sendCommand(command);

      return {
        connected: this.isConnected,
        ip: this.terminalIP,
        port: this.terminalPort,
        model: 'PAX A920',
        capabilities: {
          contactless: true,
          emv: true,
          magneticStripe: true,
          printer: true,
          camera: true,
          wifi: true,
          cellular: true,
        },
        lastResponse: response
      };

    } catch (error) {
      console.error('Status check error:', error);
      return {
        connected: false,
        ip: this.terminalIP,
        port: this.terminalPort,
        model: 'PAX A920',
        capabilities: {
          contactless: false,
          emv: false,
          magneticStripe: false,
          printer: false,
          camera: false,
          wifi: false,
          cellular: false,
        }
      };
    }
  }

  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      await this.connectToTerminal();
      
      // Send ping command
      const command = this.buildPaxCommand('PING', {});
      const response = await this.sendCommand(command);
      
      return {
        success: true,
        message: 'Connection test successful'
      };

    } catch (error) {
      return {
        success: false,
        message: `Connection test failed: ${error.message}`
      };
    }
  }

  async getConfiguration(): Promise<any> {
    try {
      if (!this.isConnected) {
        await this.connectToTerminal();
      }

      const command = this.buildPaxCommand('CONFIG', {});
      const response = await this.sendCommand(command);
      
      return this.parseConfigResponse(response);

    } catch (error) {
      console.error('Configuration retrieval error:', error);
      return {
        terminalId: 'Unknown',
        merchantId: 'Unknown',
        version: 'Unknown'
      };
    }
  }

  private buildPaxCommand(commandType: string, data: any): string {
    // Build PAX-specific command format
    // This is a simplified example - actual PAX protocol is more complex
    
    const timestamp = new Date().toISOString();
    const transactionId = `TXN${Date.now()}`;
    
    switch (commandType) {
      case 'SALE':
        return `SALE|${data.amount}|${transactionId}|${timestamp}`;
      case 'STATUS':
        return `STATUS|${transactionId}|${timestamp}`;
      case 'PING':
        return `PING|${transactionId}|${timestamp}`;
      case 'CONFIG':
        return `CONFIG|${transactionId}|${timestamp}`;
      default:
        throw new Error(`Unknown command type: ${commandType}`);
    }
  }

  private async sendCommand(command: string): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.socket || !this.isConnected) {
        reject(new Error('Not connected to terminal'));
        return;
      }

      let responseData = '';
      
      const timeout = setTimeout(() => {
        reject(new Error('Command timeout'));
      }, 10000);

      const onData = (data: Buffer) => {
        responseData += data.toString();
        
        // Check if response is complete (simplified)
        if (responseData.includes('\n') || responseData.includes('\r')) {
          clearTimeout(timeout);
          this.socket.off('data', onData);
          resolve(responseData.trim());
        }
      };

      this.socket.on('data', onData);
      this.socket.write(command + '\n');
    });
  }

  private parsePaymentResponse(response: string): PaxPaymentResponse {
    try {
      // Parse PAX response format
      // This is simplified - actual PAX responses are more complex
      
      const parts = response.split('|');
      
      if (parts[0] === 'APPROVED') {
        return {
          success: true,
          transactionId: parts[1] || '',
          authCode: parts[2] || '',
          resultCode: '00',
          message: 'Transaction approved',
          receiptData: parts[3] || ''
        };
      } else {
        return {
          success: false,
          resultCode: parts[1] || '99',
          message: parts[2] || 'Transaction declined'
        };
      }

    } catch (error) {
      return {
        success: false,
        message: 'Failed to parse terminal response'
      };
    }
  }

  private parseConfigResponse(response: string): any {
    try {
      const parts = response.split('|');
      return {
        terminalId: parts[1] || 'Unknown',
        merchantId: parts[2] || 'Unknown',
        version: parts[3] || 'Unknown',
        capabilities: parts[4] || 'Unknown'
      };
    } catch (error) {
      return {
        terminalId: 'Unknown',
        merchantId: 'Unknown',
        version: 'Unknown'
      };
    }
  }

  private handleTerminalResponse(data: string): void {
    console.log('Terminal response:', data);
    // Handle unsolicited responses from terminal
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.destroy();
      this.socket = null;
    }
    this.isConnected = false;
  }
}
