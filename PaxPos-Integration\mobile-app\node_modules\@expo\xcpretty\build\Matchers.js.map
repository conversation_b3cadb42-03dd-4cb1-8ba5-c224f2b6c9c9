{"version": 3, "file": "Matchers.js", "sourceRoot": "", "sources": ["../src/Matchers.ts"], "names": [], "mappings": ";;;AAAa,QAAA,QAAQ,GAAG;IACtB;;;;OAIG;IACH,eAAe,EAAE,qHAAqH;IAEtI;;;;;OAKG;IACH,oBAAoB,EAAE,4EAA4E;IAElG;;;;;OAKG;IACH,wBAAwB,EAAE,sFAAsF;IAEhH;;;;;OAKG;IACH,sBAAsB,EAAE,8EAA8E;IAEtG,0BAA0B,EAAE,sBAAsB;IAElD;;;;OAIG;IACH,qBAAqB,EAAE,wDAAwD;IAE/E;;OAEG;IACH,oBAAoB,EAAE,gBAAgB;IAEtC;;;;;OAKG;IACH,oBAAoB,EAAE,2EAA2E;IAEjG;;;;;;OAMG;IACH,gBAAgB,EAAE,kHAAkH;IAEpI;;;OAGG;IACH,0BAA0B,EAAE,8HAA8H;IAE1J;;;;;;;;;OASG;IACH,eAAe,EAAE,iJAAiJ;IAElK;;;;OAIG;IACH,uBAAuB,EAAE,6DAA6D;IAEtF;;;;OAIG;IACH,mBAAmB,EAAE,0FAA0F;IAE/G;;;;OAIG;IACH,0BAA0B,EAAE,4GAA4G;IAExI;;;;;;OAMG;IACH,gBAAgB,EAAE,mKAAmK;IAErL;;OAEG;IACH,6BAA6B,EAAE,wGAAwG;IAEvI;;OAEG;IACH,yBAAyB,EAAE,2FAA2F;IAEtH,gBAAgB,EAAE,eAAe;IAEjC;;;;;OAKG;IACH,cAAc,EAAE,iBAAiB;IAEjC;;;;;;OAMG;IACH,oBAAoB,EAAE,6EAA6E;IAEnG;;;;OAIG;IACH,uBAAuB,EAAE,8DAA8D;IAEvF;;OAEG;IACH,wBAAwB,EAAE,mDAAmD;IAE7E;;;OAGG;IACH,qBAAqB,EAAE,kGAAkG;IAEzH;;;OAGG;IACH,eAAe,EAAE,qFAAqF;IAEtG;;;;;OAKG;IACH,eAAe,EAAE,6HAA6H;IAE9I;;;;;OAKG;IACH,wBAAwB,EAAE,sEAAsE;IAEhG;;;;OAIG;IACH,yBAAyB,EAAE,wCAAwC;IAEnE;;;;OAIG;IACH,yBAAyB,EAAE,+CAA+C;IAE1E;;;;;OAKG;IACH,0BAA0B,EAAE,qGAAqG;IAEjI,qBAAqB,EAAE,+CAA+C;IAEtE;;;OAGG;IACH,8BAA8B,EAAE,iGAAiG;IAEjI;;;;;OAKG;IAEH,mBAAmB,EAAE,wKAAwK;IAE7L;;;OAGG;IACH,2BAA2B,EAAE,gDAAgD;IAE7E;;;OAGG;IACH,kBAAkB,EAAE,mDAAmD;IAEvE;;;OAGG;IACH,aAAa,EAAE,0BAA0B;IAEzC;;;OAGG;IACH,0BAA0B,EAAE,kHAAkH;IAE9I;;;;OAIG;IACH,4BAA4B,EAAE,6EAA6E;IAE3G;;;;OAIG;IACH,0BAA0B,EAAE,2DAA2D;IAEvF;;;OAGG;IACH,wBAAwB,EAAE,mCAAmC;IAE7D;;;OAGG;IACH,gBAAgB,EAAE,kBAAkB;IAEpC;;;;OAIG;IACH,aAAa,EAAE,qFAAqF;IAEpG;;;OAGG;IACH,kBAAkB,EAAE,oBAAoB;IAExC,qBAAqB,EAAE,yBAAyB;IAEhD,QAAQ,EAAE;QACR;;;;;WAKG;QACH,uBAAuB,EAAE,qGAAqG;QAE9H,8BAA8B,EAAE,oGAAoG;QAEpI;;;;WAIG;QACH,kBAAkB,EAAE,uBAAuB;QAE3C;;;WAGG;QACH,uBAAuB,EAAE,mBAAmB;QAE5C;;;WAGG;QACH,qCAAqC,EAAE,mCAAmC;QAE1E;;;;WAIG;QACH,gBAAgB,EAAE,kIAAkI;QACpJ;;WAEG;QACH,oBAAoB,EAAE,iJAAiJ;QACvK;;;;;;WAMG;QACH,uBAAuB,EAAE,gHAAgH;QACzI;;;;;;WAMG;QACH,qBAAqB,EAAE,gIAAgI;QAEvJ;;;;;;;WAOG;QACH,oBAAoB,EAAE,4UAA4U;QAElW;;;WAGG;QACH,+BAA+B,EAAE,4CAA4C;QAC7E;;;WAGG;QACH,sBAAsB,EAAE,2IAA2I;QACnK;;;WAGG;QACH,6BAA6B,EAAE,wHAAwH;KACxJ;IAED,MAAM,EAAE;QACN;;;WAGG;QACH,mBAAmB,EAAE,sBAAsB;QAE3C;;;WAGG;QACH,iCAAiC,EAAE,sNAAsN;QAEzP;;;;;;;;WAQG;QACH,+BAA+B,EAAE,yJAAyJ;QAE1L;;;WAGG;QACH,qCAAqC,EAAE,0CAA0C;QAEjF;;;WAGG;QACH,sBAAsB,EAAE,gCAAgC;QAExD;;;;;WAKG;QACH,qBAAqB,EAAE,+GAA+G;QAEtI;;;WAGG;QACH,cAAc,EAAE,+BAA+B;QAE/C;;;;;WAKG;QACH,mBAAmB,EAAE,qBAAqB;QAE1C;;;;WAIG;QACH,mBAAmB,EAAE,qBAAqB;QAE1C;;;;WAIG;QACH,0BAA0B,EAAE,iDAAiD;QAE7E;;;WAGG;QACH,gBAAgB,EAAE,WAAW;QAE7B;;;WAGG;QACH,yCAAyC,EAAE,qBAAqB;QAEhE;;;WAGG;QACH,gCAAgC,EAAE,2DAA2D;QAE7F;;;WAGG;QACH,wCAAwC,EAAE,sBAAsB;QAEhE;;;WAGG;QACH,gCAAgC,EAAE,6CAA6C;QAE/E;;;WAGG;QACH,kBAAkB,EAAE,gBAAgB;QAEpC;;;WAGG;QACH,8BAA8B,EAAE,+BAA+B;QAE/D;;;WAGG;QACH,6BAA6B,EAAE,yDAAyD;KACzF;CACF,CAAC", "sourcesContent": ["export const Matchers = {\n  /**\n   * @regex Captured groups\n   * `$1` filePath\n   * `$2` fileName\n   */\n  ANALYZE_MATCHER: /^Analyze(?:Shallow)?\\s(.*\\/(.*\\.(?:m|mm|cc|cpp|c|cxx)))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` target\n   * `$2` project\n   * `$3` configuration\n   */\n  BUILD_TARGET_MATCHER: /^=== BUILD TARGET\\s(.*)\\sOF PROJECT\\s(.*)\\sWITH.*CONFIGURATION\\s(.*)\\s===/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` target\n   * `$2` project\n   * `$3` configuration\n   */\n  AGGREGATE_TARGET_MATCHER: /^=== BUILD AGGREGATE TARGET\\s(.*)\\sOF PROJECT\\s(.*)\\sWITH.*CONFIGURATION\\s(.*)\\s===/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` target\n   * `$2` project\n   * `$3` configuration\n   */\n  ANALYZE_TARGET_MATCHER: /^=== ANALYZE TARGET\\s(.*)\\sOF PROJECT\\s(.*)\\sWITH.*CONFIGURATION\\s(.*)\\s===/m,\n\n  CHECK_DEPENDENCIES_MATCHER: /^Check dependencies/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` command path\n   * `$2` arguments\n   */\n  SHELL_COMMAND_MATCHER: /^\\s{4}(cd|setenv|(?:[\\w/:\\\\\\s\\-.]+?\\/)?[\\w-]+)\\s(.*)$/m,\n\n  /**\n   * @regex Nothing returned here for now\n   */\n  CLEAN_REMOVE_MATCHER: /^Clean.Remove/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` target\n   * `$2` project\n   * `$3` configuration\n   */\n  CLEAN_TARGET_MATCHER: /^=== CLEAN TARGET\\s(.*)\\sOF PROJECT\\s(.*)\\sWITH CONFIGURATION\\s(.*)\\s===/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = file\n   * `$2` fileName (e.g. Exponent.app)\n   * `$3` target (e.g. ABI39_0_0EXAdsFacebook)\n   * `$4` project (e.g. ABI39_0_0)\n   */\n  CODESIGN_MATCHER: /^CodeSign\\s((?:\\\\.|[^ ])+\\/((?:\\\\.|[^ ])+\\.(?:\\w+)))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = file\n   */\n  CODESIGN_FRAMEWORK_MATCHER: /^CodeSign\\s((?:\\\\.|[^ ])+\\/((?:\\\\.|[^ ])+\\.framework))\\/Versions\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` type\n   * `$2` filePath\n   * `$3` fileName (e.g. KWNull.m)\n   * `$4` target (e.g. ABI39_0_0EXAdsFacebook)\n   * `$5` project (e.g. ABI39_0_0)\n   *\n   * The order of extensions is important in order to make alternation greedier.\n   */\n  COMPILE_MATCHER: /^(Compile[\\w]+)\\s.+?\\s((?:\\\\.|[^ ])+\\/((?:\\\\.|[^ ])+\\.(?:mm|m|cpp|cxx|cc|c|swift)))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` compiler_command\n   * `$2` filePath\n   */\n  COMPILE_COMMAND_MATCHER: /^\\s*(.*clang\\s.*\\s-c\\s(.*\\.(?:m|mm|c|cc|cpp|cxx))\\s.*\\.o)$/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` filePath\n   * `$2` fileName (e.g. MainMenu.xib)\n   */\n  COMPILE_XIB_MATCHER: /^CompileXIB\\s(.*\\/(.*\\.xib))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` filePath\n   * `$2` fileName (e.g. Main.storyboard)\n   */\n  COMPILE_STORYBOARD_MATCHER: /^CompileStoryboard\\s(.*\\/([^/].*\\.storyboard))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * `$1` type of copy\n   * `$2` file path 1\n   * `$3` file path 2\n   * `$4` target\n   * `$5` project\n   */\n  ANY_COPY_MATCHER: /^(CpResource|CopyStringsFile|CopyPlistFile|CpHeader|PBXCp)\\s(\\/?.*\\/(?:.*\\.\\w+))\\s(\\/?.*\\/(?:.*\\.\\w+))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\))?/m,\n\n  /**\n   * `CompileSwiftSources normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'expo-dev-menu-interface' from project 'Pods')`\n   */\n  COMPILE_SWIFT_SOURCES_MATCHER: /^(CompileSwiftSources)\\s([^\\s]+) ([^\\s]+) ([^\\s]+) (?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\))?/m,\n\n  /**\n   * `EmitSwiftModule normal x86_64 (in target 'expo-dev-menu-interface' from project 'Pods')`\n   */\n  EMIT_SWIFT_MODULE_MATCHER: /^(EmitSwiftModule)\\s([^\\s]+) ([^\\s]+) (?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\))?/m,\n\n  EXECUTED_MATCHER: /^\\s*Executed/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = whole message.\n   *\n   * `remark: Incremental compilation has been disabled: it is not compatible with whole module optimization`\n   */\n  REMARK_MATCHER: /^remark: (.*)$/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = file\n   * `$2` = test_suite\n   * `$3` = test_case\n   * `$4` = reason\n   */\n  FAILING_TEST_MATCHER: /^\\s*(.+:\\d+):\\serror:\\s[+-]\\[(.*)\\s(.*)\\]\\s:(?:\\s'.*'\\s\\[FAILED\\],)?\\s(.*)/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = file\n   * `$2` = reason\n   */\n  UI_FAILING_TEST_MATCHER: /^\\s{4}t = \\s+\\d+\\.\\d+s\\s+Assertion Failure: (.*:\\d+): (.*)$/m,\n\n  /**\n   * @regex Captured groups\n   */\n  RESTARTING_TESTS_MATCHER: /^Restarting after unexpected exit or crash in.+$/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = dsym\n   */\n  GENERATE_DSYM_MATCHER: /^GenerateDSYMFile (\\/.*\\/(.*\\.dSYM))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = library\n   */\n  LIBTOOL_MATCHER: /^Libtool\\s(.*\\/(.*\\.a))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = targetName\n   * `$2` = build_variants (normal, profile, debug)\n   * `$3` = architecture\n   */\n  LINKING_MATCHER: /^Ld (\\/?.*\\/(.+?(?:[^\\\\](?=\\s)))) ([^(|\\s]*)(?:\\s([^(|\\s]*)\\s)?\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = suite\n   * `$2` = test_case\n   * `$3` = time\n   */\n  TEST_CASE_PASSED_MATCHER: /^\\s*Test Case\\s'-\\[(.*)\\s(.*)\\]'\\spassed\\s\\((\\d*\\.\\d{3})\\sseconds\\)/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = suite\n   * `$2` = test_case\n   */\n  TEST_CASE_STARTED_MATCHER: /^Test Case '-\\[(.*) (.*)\\]' started.$/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = suite\n   * `$2` = test_case\n   */\n  TEST_CASE_PENDING_MATCHER: /^Test Case\\s'-\\[(.*)\\s(.*)PENDING\\]'\\spassed/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = suite\n   * `$2` = test_case\n   * `$3` = time\n   */\n  TEST_CASE_MEASURED_MATCHER: /^[^:]*:[^:]*:\\sTest Case\\s'-\\[(.*)\\s(.*)\\]'\\smeasured\\s\\[Time,\\sseconds\\]\\saverage:\\s(\\d*\\.\\d{3}),/m,\n\n  PHASE_SUCCESS_MATCHER: /^\\*\\*\\s(.*)\\sSUCCEEDED\\s\\*\\*(?:\\s+\\[(.*)\\])?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = script_name\n   */\n  PHASE_SCRIPT_EXECUTION_MATCHER: /^PhaseScriptExecution\\s((?:\\\\ |\\S)*)\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\))?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = file\n   * `$1` = target\n   * `$1` = project\n   */\n\n  PROCESS_PCH_MATCHER: /^ProcessPCH(?:\\+\\+)? (\\/?.*\\/([^(|\\s|\\n]*(?:.pch.(?:g|p)ch)))(?:\\s(.*.pch))? ([^(|\\s]*)(?:\\s([^(|\\s]*)\\s)?\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` filePath\n   */\n  PROCESS_PCH_COMMAND_MATCHER: /^\\s*.*\\/usr\\/bin\\/clang\\s.*\\s-c\\s(.*)\\s-o\\s.*/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = file\n   */\n  PREPROCESS_MATCHER: /^Preprocess\\s(?:(?:\\\\ |[^ ])*)\\s((?:\\\\ |[^ ])*)$/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = file\n   */\n  PBXCP_MATCHER: /^PBXCp\\s((?:\\\\ |[^ ])*)/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = file\n   */\n  PROCESS_INFO_PLIST_MATCHER: /^ProcessInfoPlistFile\\s.*\\.plist\\s(.*\\/+(.*\\.plist))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = suite\n   * `$2` = time\n   */\n  TESTS_RUN_COMPLETION_MATCHER: /^\\s*Test Suite '(?:.*\\/)?(.*[ox]ctest.*)' (finished|passed|failed) at (.*)/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` = suite\n   * `$2` = time\n   */\n  TEST_SUITE_STARTED_MATCHER: /^\\s*Test Suite '(?:.*\\/)?(.*[ox]ctest.*)' started at(.*)/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` test suite name\n   */\n  TEST_SUITE_START_MATCHER: /^\\s*Test Suite '(.*)' started at/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` fileName\n   */\n  TIFFUTIL_MATCHER: /^TiffUtil\\s(.*)/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` filePath\n   * `$2` fileName\n   */\n  TOUCH_MATCHER: /^Touch\\s(.*\\/([^(|\\n]+))\\s?[^(]+(?:\\(in\\s.*target '([^']*)'.*project '([^']*)'\\))?/m,\n\n  /**\n   * @regex Captured groups\n   * `$1` filePath\n   */\n  WRITE_FILE_MATCHER: /^write-file\\s(.*)/m,\n\n  WRITE_AUXILIARY_FILES: /^Write auxiliary files/m,\n\n  Warnings: {\n    /**\n     * @regex Captured groups\n     * `$1` = filePath\n     * `$2` = fileName\n     * `$3` = reason\n     */\n    COMPILE_WARNING_MATCHER: /^(\\/.+\\/(.*):.*:.*):\\swarning:\\s(.+?(?=\\(in)?)(?:\\(in target '([^']*)' from project '([^']*)'\\))?$/m,\n\n    COMPILE_WARNING_INLINE_MATCHER: /^(\\/.+\\/(.*):.*:.*):\\swarning:\\s(.+?(?=\\(in)?)(?:\\(in target '([^']*)' from project '([^']*)'\\))$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = ld prefix\n     * `$2` = warning message\n     */\n    LD_WARNING_MATCHER: /^(ld: )warning: (.*)/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = whole warning\n     */\n    GENERIC_WARNING_MATCHER: /^warning:\\s(.*)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = filePath\n     */\n    MISSING_FILE_COMPILER_WARNING_MATCHER: /(.*): No such file or directory$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` filePath\n     * `$2` fileName\n     */\n    VERSION_MISMATCH: /^The\\s(\\w*)\\s.*'([^']*)'.*to (\\d+\\.?\\d+\\.?\\d*),.*(\\d+\\.?\\d+\\.?\\d*) to (\\d+\\.?\\d+\\.?\\d*).\\s.*target '([^']*)'.*project '([^']*)'/m,\n    /**\n     * @regex Captured groups\n     */\n    MISSING_ARCHITECTURE: /^\\[CP\\] Vendored binary\\s'([^']*)'.*contains architectures \\(([\\w\\d\\s]+)\\) none of which match the current build architectures \\(([\\w\\d\\s]+)\\)/m,\n    /**\n     * @regex Captured groups\n     * `$1` buildPhase\n     * `$2` filePath\n     * `$3` target\n     * `$4` project\n     */\n    SKIPPING_DUPLICATE_FILE: /^Skipping duplicate build file in ([A-Za-z\\s]+) build phase: (.*) \\(in\\s.*target '([^']*)'.*project '([^']*)'/m,\n    /**\n     * @regex Captured groups\n     * `$1` reservedFileDescription (Info.plist or entitlements)\n     * `$2` filePath\n     * `$3` target\n     * `$4` project\n     */\n    TARGETS_FILE_INCLUDED: /^The Copy Bundle Resources build phase contains this target's (.*) file\\s?'(.*)'. \\(in\\s.*target '([^']*)'.*project '([^']*)'/m,\n\n    /**\n     * Run script build phase '[CP-User] [Hermes] Replace Hermes for the right configuration, if needed' will be run during every build because it does not specify any outputs. To address this warning, either add output dependencies to the script phase, or configure it to run in every build by unchecking \"Based on dependency analysis\" in the script phase. (in target 'hermes-engine' from project 'Pods')\n     *\n     * @regex Captured groups\n     * `$1` script name\n     * `$2` target\n     * `$3` project\n     */\n    AMBIGUOUS_RUN_SCRIPT: /^Run script build phase '(.+)' will be run during every build because it does not specify any outputs\\. To address this warning, either add output dependencies to the script phase, or configure it to run in every build by unchecking \"Based on dependency analysis\" in the script phase\\. \\(in\\s.*target '([^']*)'.*project '([^']*)'/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = whole warning\n     */\n    WILL_NOT_BE_CODE_SIGNED_MATCHER: /^(.* will not be code signed because .*)$/m,\n    /**\n     * @regex Captured groups\n     * `$1` = whole warning\n     */\n    LINKER_METHOD_OVERRIDE: /method '(.*)' in category from (?:(.*\\/(?:.*\\.\\w))(?:\\((.*\\.\\w)\\)))\\soverrides method from class in (?:(.*\\/(?:.*\\.\\w))(?:\\((.*\\.\\w)\\)))/m,\n    /**\n     * @regex Captured groups\n     * `$1` = whole warning\n     */\n    LINKER_METHOD_SINGLE_OVERRIDE: /method '(.*)' in category from (?:(.*\\/(?:.*\\.\\w))(?:\\((.*\\.\\w)\\)))\\sconflicts with same method from another category/m,\n  },\n\n  Errors: {\n    /**\n     * @regex Captured groups\n     * `$1` = whole error\n     */\n    CLANG_ERROR_MATCHER: /^(clang: error:.*)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = whole error\n     */\n    CHECK_DEPENDENCIES_ERRORS_MATCHER: /^(Code\\s?Sign error:.*|Code signing is required for product type .* in SDK .*|No profile matching .* found:.*|Provisioning profile .* doesn't .*|Swift is unavailable on .*|.?Use Legacy Swift Language Version.*)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$0` = whole error\n     * `$1` = profile name\n     * `$2` = entitlement name\n     * `$3` = entitlement type <capability|entitlement>\n     * `$4` = native target\n     * `$5` = native project\n     */\n    UNSUPPORTED_ENTITLEMENT_MATCHER: /^error: Provisioning profile (.*) doesn't (?:support|include) the (.*) (capability|entitlement)\\.(?:\\s\\(in\\s.*target '([^']*)'.*project '([^']*)'\\)$)?/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = whole error\n     */\n    PROVISIONING_PROFILE_REQUIRED_MATCHER: /^(.*requires a provisioning profile.*)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = whole error\n     */\n    NO_CERTIFICATE_MATCHER: /^(No certificate matching.*)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = filePath\n     * `$2` = fileName\n     * `$3` = reason\n     */\n    COMPILE_ERROR_MATCHER: /^(\\/.+\\/(.*):.*:.*):\\s(?:fatal\\s)?error:\\s(.+?(?=\\(in)?)(?:\\(in target '([^']*)' from project '([^']*)'\\))?$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` cursor (with whitespaces and tildes)\n     */\n    CURSOR_MATCHER: /^(?:\\s+\\|)?([\\s~]*\\^[\\s~]*)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = whole error.\n     *\n     * Appears to be related to the installation of files on the connected device\n     */\n    RSYNC_ERROR_MATCHER: /^(rsync error:.*)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = whole error.\n     * it varies a lot, not sure if it makes sense to catch everything separately\n     */\n    FATAL_ERROR_MATCHER: /^(fatal error:.*)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = whole error.\n     * `$2` = file path\n     */\n    FILE_MISSING_ERROR_MATCHER: /^<unknown>:0:\\s(error:\\s.*)\\s'(\\/.+\\/.*\\..*)'$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = whole error\n     */\n    LD_ERROR_MATCHER: /^(ld:.*)/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` file path\n     */\n    LINKER_DUPLICATE_SYMBOLS_LOCATION_MATCHER: /^\\s+(\\/.*\\.o[)]?)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` reason\n     */\n    LINKER_DUPLICATE_SYMBOLS_MATCHER: /^((duplicate symbol|ld: warning: duplicate symbol) .*):$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` symbol location\n     */\n    LINKER_UNDEFINED_SYMBOL_LOCATION_MATCHER: /^(.* in .*\\.o[)]?)$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` reason\n     */\n    LINKER_UNDEFINED_SYMBOLS_MATCHER: /^(Undefined symbols for architecture .*):$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` reason\n     */\n    PODS_ERROR_MATCHER: /^(error:\\s.*)/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = reference\n     */\n    SYMBOL_REFERENCED_FROM_MATCHER: /\\s+\"(.*)\", referenced from:$/m,\n\n    /**\n     * @regex Captured groups\n     * `$1` = error reason\n     */\n    MODULE_INCLUDES_ERROR_MATCHER: /^<module-includes>:.*?:.*?:\\s(?:fatal\\s)?(error:\\s.*)$/m,\n  },\n};\n"]}