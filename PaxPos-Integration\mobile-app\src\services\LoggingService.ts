import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { format } from 'date-fns';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  message: string;
  category: string;
  metadata?: any;
  stackTrace?: string;
}

export interface LogConfig {
  maxLogFiles: number;
  maxLogFileSize: number; // in bytes
  logRetentionDays: number;
  enableConsoleLogging: boolean;
  enableFileLogging: boolean;
  enableRemoteLogging: boolean;
  logLevels: LogLevel[];
}

export class LoggingService {
  private config: LogConfig;
  private logBuffer: LogEntry[] = [];
  private isInitialized: boolean = false;
  private logDirectory: string;

  constructor() {
    this.config = {
      maxLogFiles: 10,
      maxLogFileSize: 5 * 1024 * 1024, // 5MB
      logRetentionDays: 30,
      enableConsoleLogging: true,
      enableFileLogging: true,
      enableRemoteLogging: true,
      logLevels: ['debug', 'info', 'warn', 'error']
    };
    
    this.logDirectory = FileSystem.documentDirectory + 'logs/';
  }

  async initialize(): Promise<void> {
    try {
      // Create logs directory
      await FileSystem.makeDirectoryAsync(this.logDirectory, { intermediates: true });
      
      // Load configuration
      await this.loadConfig();
      
      // Clean up old log files
      await this.cleanupOldLogs();
      
      // Start periodic log flushing
      this.startPeriodicFlush();
      
      this.isInitialized = true;
      
      this.info('Logging service initialized successfully', {
        config: this.config,
        logDirectory: this.logDirectory
      });
    } catch (error) {
      console.error('Failed to initialize logging service:', error);
      throw error;
    }
  }

  private async loadConfig(): Promise<void> {
    try {
      const configData = await AsyncStorage.getItem('loggingConfig');
      if (configData) {
        const savedConfig = JSON.parse(configData);
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      console.warn('Failed to load logging config, using defaults:', error);
    }
  }

  private async saveConfig(): Promise<void> {
    try {
      await AsyncStorage.setItem('loggingConfig', JSON.stringify(this.config));
    } catch (error) {
      console.error('Failed to save logging config:', error);
    }
  }

  private startPeriodicFlush(): void {
    // Flush logs every 30 seconds
    setInterval(() => {
      this.flushLogs();
    }, 30000);
    
    // Flush logs every 5 minutes for remote logging
    setInterval(() => {
      this.flushRemoteLogs();
    }, 300000);
  }

  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private createLogEntry(level: LogLevel, message: string, metadata?: any, category: string = 'general'): LogEntry {
    const entry: LogEntry = {
      id: this.generateLogId(),
      timestamp: new Date(),
      level,
      message,
      category,
      metadata
    };

    // Add stack trace for errors
    if (level === 'error' && metadata instanceof Error) {
      entry.stackTrace = metadata.stack;
      entry.metadata = {
        name: metadata.name,
        message: metadata.message,
        ...metadata
      };
    }

    return entry;
  }

  private shouldLog(level: LogLevel): boolean {
    return this.config.logLevels.includes(level);
  }

  private async writeLog(entry: LogEntry): Promise<void> {
    // Add to buffer
    this.logBuffer.push(entry);

    // Console logging
    if (this.config.enableConsoleLogging) {
      this.writeToConsole(entry);
    }

    // Flush if buffer is getting large
    if (this.logBuffer.length >= 100) {
      await this.flushLogs();
    }
  }

  private writeToConsole(entry: LogEntry): void {
    const timestamp = format(entry.timestamp, 'yyyy-MM-dd HH:mm:ss.SSS');
    const logMessage = `[${timestamp}] [${entry.level.toUpperCase()}] [${entry.category}] ${entry.message}`;

    switch (entry.level) {
      case 'debug':
        console.debug(logMessage, entry.metadata);
        break;
      case 'info':
        console.info(logMessage, entry.metadata);
        break;
      case 'warn':
        console.warn(logMessage, entry.metadata);
        break;
      case 'error':
        console.error(logMessage, entry.metadata);
        if (entry.stackTrace) {
          console.error(entry.stackTrace);
        }
        break;
    }
  }

  private async flushLogs(): Promise<void> {
    if (!this.config.enableFileLogging || this.logBuffer.length === 0) {
      return;
    }

    try {
      const currentDate = format(new Date(), 'yyyy-MM-dd');
      const logFileName = `pax-pos-${currentDate}.log`;
      const logFilePath = this.logDirectory + logFileName;

      // Prepare log entries for writing
      const logLines = this.logBuffer.map(entry => {
        const timestamp = format(entry.timestamp, 'yyyy-MM-dd HH:mm:ss.SSS');
        const metadata = entry.metadata ? ` | ${JSON.stringify(entry.metadata)}` : '';
        const stackTrace = entry.stackTrace ? `\n${entry.stackTrace}` : '';
        
        return `[${timestamp}] [${entry.level.toUpperCase()}] [${entry.category}] ${entry.message}${metadata}${stackTrace}`;
      }).join('\n') + '\n';

      // Check if file exists and its size
      const fileInfo = await FileSystem.getInfoAsync(logFilePath);
      
      if (fileInfo.exists && fileInfo.size && fileInfo.size > this.config.maxLogFileSize) {
        // Rotate log file
        await this.rotateLogFile(logFilePath);
      }

      // Append to log file
      await FileSystem.writeAsStringAsync(logFilePath, logLines, {
        encoding: FileSystem.EncodingType.UTF8
      });

      // Clear buffer
      this.logBuffer = [];

    } catch (error) {
      console.error('Failed to flush logs to file:', error);
    }
  }

  private async rotateLogFile(currentLogPath: string): Promise<void> {
    try {
      const timestamp = format(new Date(), 'yyyy-MM-dd-HH-mm-ss');
      const rotatedPath = currentLogPath.replace('.log', `-${timestamp}.log`);
      
      // Move current log to rotated name
      await FileSystem.moveAsync({
        from: currentLogPath,
        to: rotatedPath
      });

      // Clean up old rotated files
      await this.cleanupRotatedLogs();

    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  private async cleanupRotatedLogs(): Promise<void> {
    try {
      const files = await FileSystem.readDirectoryAsync(this.logDirectory);
      const logFiles = files
        .filter(file => file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: this.logDirectory + file
        }));

      // Sort by modification time (newest first)
      const fileInfos = await Promise.all(
        logFiles.map(async file => {
          const info = await FileSystem.getInfoAsync(file.path);
          return {
            ...file,
            modificationTime: info.modificationTime || 0
          };
        })
      );

      fileInfos.sort((a, b) => b.modificationTime - a.modificationTime);

      // Remove excess files
      if (fileInfos.length > this.config.maxLogFiles) {
        const filesToDelete = fileInfos.slice(this.config.maxLogFiles);
        
        for (const file of filesToDelete) {
          await FileSystem.deleteAsync(file.path);
        }
      }

    } catch (error) {
      console.error('Failed to cleanup rotated logs:', error);
    }
  }

  private async cleanupOldLogs(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.logRetentionDays);

      const files = await FileSystem.readDirectoryAsync(this.logDirectory);
      
      for (const file of files) {
        if (file.endsWith('.log')) {
          const filePath = this.logDirectory + file;
          const fileInfo = await FileSystem.getInfoAsync(filePath);
          
          if (fileInfo.modificationTime && fileInfo.modificationTime < cutoffDate.getTime()) {
            await FileSystem.deleteAsync(filePath);
          }
        }
      }

    } catch (error) {
      console.error('Failed to cleanup old logs:', error);
    }
  }

  private async flushRemoteLogs(): Promise<void> {
    if (!this.config.enableRemoteLogging) {
      return;
    }

    try {
      // Get logs from storage for remote sending
      const pendingLogs = await AsyncStorage.getItem('pendingRemoteLogs');
      if (pendingLogs) {
        const logs = JSON.parse(pendingLogs);
        
        // In a real implementation, you would send these to your remote logging service
        // For now, we'll just clear them
        await AsyncStorage.removeItem('pendingRemoteLogs');
      }

    } catch (error) {
      console.error('Failed to flush remote logs:', error);
    }
  }

  // Public logging methods
  debug(message: string, metadata?: any, category: string = 'general'): void {
    if (!this.shouldLog('debug')) return;
    
    const entry = this.createLogEntry('debug', message, metadata, category);
    this.writeLog(entry);
  }

  info(message: string, metadata?: any, category: string = 'general'): void {
    if (!this.shouldLog('info')) return;
    
    const entry = this.createLogEntry('info', message, metadata, category);
    this.writeLog(entry);
  }

  warn(message: string, metadata?: any, category: string = 'general'): void {
    if (!this.shouldLog('warn')) return;
    
    const entry = this.createLogEntry('warn', message, metadata, category);
    this.writeLog(entry);
  }

  error(message: string, error?: any, category: string = 'general'): void {
    if (!this.shouldLog('error')) return;
    
    const entry = this.createLogEntry('error', message, error, category);
    this.writeLog(entry);
  }

  // Specialized logging methods
  paxLog(message: string, metadata?: any): void {
    this.info(message, metadata, 'pax');
  }

  transactionLog(message: string, metadata?: any): void {
    this.info(message, metadata, 'transaction');
  }

  securityLog(message: string, metadata?: any): void {
    this.warn(message, metadata, 'security');
  }

  performanceLog(message: string, metadata?: any): void {
    this.info(message, metadata, 'performance');
  }

  // Configuration methods
  async updateConfig(newConfig: Partial<LogConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfig();
    this.info('Logging configuration updated', { newConfig });
  }

  getConfig(): LogConfig {
    return { ...this.config };
  }

  // Log retrieval methods
  async getRecentLogs(count: number = 100): Promise<LogEntry[]> {
    try {
      const currentDate = format(new Date(), 'yyyy-MM-dd');
      const logFileName = `pax-pos-${currentDate}.log`;
      const logFilePath = this.logDirectory + logFileName;

      const fileInfo = await FileSystem.getInfoAsync(logFilePath);
      if (!fileInfo.exists) {
        return [];
      }

      const logContent = await FileSystem.readAsStringAsync(logFilePath);
      const lines = logContent.split('\n').filter(line => line.trim());
      
      // Return last 'count' lines
      return lines.slice(-count).map(line => {
        // Parse log line back to LogEntry (simplified)
        const parts = line.match(/\[(.*?)\] \[(.*?)\] \[(.*?)\] (.*)/);
        if (parts) {
          return {
            id: this.generateLogId(),
            timestamp: new Date(parts[1]),
            level: parts[2].toLowerCase() as LogLevel,
            category: parts[3],
            message: parts[4]
          };
        }
        return null;
      }).filter(entry => entry !== null) as LogEntry[];

    } catch (error) {
      console.error('Failed to get recent logs:', error);
      return [];
    }
  }

  async getLogFiles(): Promise<string[]> {
    try {
      const files = await FileSystem.readDirectoryAsync(this.logDirectory);
      return files.filter(file => file.endsWith('.log'));
    } catch (error) {
      console.error('Failed to get log files:', error);
      return [];
    }
  }

  // Cleanup methods
  async clearLogs(): Promise<void> {
    try {
      const files = await FileSystem.readDirectoryAsync(this.logDirectory);
      
      for (const file of files) {
        if (file.endsWith('.log')) {
          await FileSystem.deleteAsync(this.logDirectory + file);
        }
      }

      this.logBuffer = [];
      await AsyncStorage.removeItem('pendingRemoteLogs');
      
      this.info('All logs cleared');
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  }

  // Force flush for immediate writing
  async forceFlush(): Promise<void> {
    await this.flushLogs();
    await this.flushRemoteLogs();
  }

  // Getters
  get isReady(): boolean {
    return this.isInitialized;
  }

  get bufferSize(): number {
    return this.logBuffer.length;
  }
}
