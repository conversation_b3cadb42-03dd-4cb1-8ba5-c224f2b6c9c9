{"name": "unicode-match-property-ecmascript", "version": "2.0.0", "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "homepage": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode properties", "unicode property aliases"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript.git"}, "bugs": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/issues", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "devDependencies": {"ava": "*"}, "scripts": {"test": "ava ./tests/*"}}