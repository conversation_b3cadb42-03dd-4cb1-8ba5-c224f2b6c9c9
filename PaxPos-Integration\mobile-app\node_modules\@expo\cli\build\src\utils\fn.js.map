{"version": 3, "sources": ["../../../src/utils/fn.ts"], "sourcesContent": ["/** `lodash.memoize` */\nexport function memoize<T extends (...args: any[]) => any>(fn: T): T {\n  const cache = new Map<string, any>();\n  return ((...args: any[]) => {\n    const key = JSON.stringify(args);\n    if (cache.has(key)) {\n      return cache.get(key);\n    }\n    const result = fn(...args);\n    cache.set(key, result);\n    return result;\n  }) as T;\n}\n\n/** memoizes an async function to prevent subsequent calls that might be invoked before the function has finished resolving. */\nexport function guardAsync<V, T extends (...args: any[]) => Promise<V>>(fn: T): T {\n  let invoked = false;\n  let returnValue: V;\n\n  const guard: any = async (...args: any[]): Promise<V> => {\n    if (!invoked) {\n      invoked = true;\n      returnValue = await fn(...args);\n    }\n\n    return returnValue;\n  };\n\n  return guard;\n}\n"], "names": ["<PERSON><PERSON><PERSON>", "memoize", "fn", "cache", "Map", "args", "key", "JSON", "stringify", "has", "get", "result", "set", "invoked", "returnValue", "guard"], "mappings": "AAAA,qBAAqB;;;;;;;;;;;IAeLA,UAAU;eAAVA;;IAdAC,OAAO;eAAPA;;;AAAT,SAASA,QAA2CC,EAAK;IAC9D,MAAMC,QAAQ,IAAIC;IAClB,OAAQ,CAAC,GAAGC;QACV,MAAMC,MAAMC,KAAKC,SAAS,CAACH;QAC3B,IAAIF,MAAMM,GAAG,CAACH,MAAM;YAClB,OAAOH,MAAMO,GAAG,CAACJ;QACnB;QACA,MAAMK,SAAST,MAAMG;QACrBF,MAAMS,GAAG,CAACN,KAAKK;QACf,OAAOA;IACT;AACF;AAGO,SAASX,WAAwDE,EAAK;IAC3E,IAAIW,UAAU;IACd,IAAIC;IAEJ,MAAMC,QAAa,OAAO,GAAGV;QAC3B,IAAI,CAACQ,SAAS;YACZA,UAAU;YACVC,cAAc,MAAMZ,MAAMG;QAC5B;QAEA,OAAOS;IACT;IAEA,OAAOC;AACT"}