var e,t=Object.freeze({__proto__:null,NetworkRequestId:class{requestId;manager;constructor(e,t){this.requestId=e,this.manager=t}}});!function(e){e.Domain="domain",e.<PERSON><PERSON>esponseHeader="has-response-header",e.<PERSON>="has-overrides",e.ResponseHeaderValueSetCookie="response-header-set-cookie",e.Is="is",e.<PERSON><PERSON><PERSON>="larger-than",e.Method="method",e.MimeType="mime-type",e.MixedContent="mixed-content",e.Priority="priority",e.Scheme="scheme",e.SetCookieDomain="set-cookie-domain",e.Set<PERSON>ookie<PERSON>ame="set-cookie-name",e.SetCookieValue="set-cookie-value",e.ResourceType="resource-type",e.<PERSON>ie<PERSON>omain="cookie-domain",e.<PERSON>="cookie-name",e.<PERSON>="cookie-path",e.<PERSON>="cookie-value",e.StatusCode="status-code",e.Url="url"}(e||(e={}));class o{filters;constructor(e){this.filters=e}static filters(e){return new o(e)}}var r=Object.freeze({__proto__:null,get FilterType(){return e},UIRequestFilter:o});class s{request;header;searchMatch;isUrlMatch;tab;filterOptions;constructor(e,t,o,r,s,i){this.request=e,this.header=t,this.searchMatch=o,this.isUrlMatch=r,this.tab=s,this.filterOptions=i}static requestHeaderMatch(e,t){return new s(e,{section:"Request",header:t},null,!1,void 0,void 0)}static responseHeaderMatch(e,t){return new s(e,{section:"Response",header:t},null,!1,void 0,void 0)}static bodyMatch(e,t){return new s(e,null,t,!1,void 0,void 0)}static urlMatch(e){return new s(e,null,null,!0,void 0,void 0)}static header(e,t,o){return new s(e,{section:t,header:{name:o,value:""}},null,!1,void 0,void 0)}static tab(e,t,o){return new s(e,null,null,!1,t,o)}}var i=Object.freeze({__proto__:null,UIRequestLocation:s});export{t as NetworkRequestId,r as UIFilter,i as UIRequestLocation};
