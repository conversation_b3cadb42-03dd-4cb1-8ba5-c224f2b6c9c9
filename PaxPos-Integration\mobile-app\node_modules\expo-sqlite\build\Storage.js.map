{"version": 3, "file": "Storage.js", "sourceRoot": "", "sources": ["../src/Storage.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,YAAY,CAAC;AAEnC,OAAO,EAAE,iBAAiB,EAAE,gBAAgB,EAAuB,MAAM,SAAS,CAAC;AASnF,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,aAAa,GAAG,0CAA0C,CAAC;AACjE,MAAM,aAAa,GACjB,uGAAuG,CAAC;AAC1G,MAAM,gBAAgB,GAAG,oCAAoC,CAAC;AAC9D,MAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAC1D,MAAM,eAAe,GAAG,sBAAsB,CAAC;AAE/C,MAAM,qBAAqB,GACzB,iFAAiF,CAAC;AAEpF;;GAEG;AACH,MAAM,OAAO,aAAa;IAIK;IAHrB,EAAE,GAA0B,IAAI,CAAC;IACxB,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;IAE7C,YAA6B,YAAoB;QAApB,iBAAY,GAAZ,YAAY,CAAQ;IAAG,CAAC;IAErD,0BAA0B;IAE1B;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,GAAW;QAC5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,aAAa,CAAoB,aAAa,EAAE,GAAG,CAAC,CAAC;QAC7E,OAAO,MAAM,EAAE,KAAK,IAAI,IAAI,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,YAAY,CAChB,GAAW,EACX,KAAkD;QAElD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjC,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAEnC,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAChC,MAAM,EAAE,CAAC,6BAA6B,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;gBAClD,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,aAAa,CAAoB,aAAa,EAAE,GAAG,CAAC,CAAC;gBACjF,MAAM,SAAS,GAAG,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC;gBAC5C,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;gBACnC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBACrC,MAAM,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,GAAW;QAC/B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;QACxD,OAAO,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,WAAW,CAAkB,sBAAsB,CAAC,CAAC;QAC7E,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QACpC,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;gBACZ,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC;gBAC3B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;YACjB,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,YAAY;IAEZ,yBAAyB;IAEzB;;OAEG;IACH,WAAW,CAAC,GAAW;QACrB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAoB,aAAa,EAAE,GAAG,CAAC,CAAC;QACtE,OAAO,MAAM,EAAE,KAAK,IAAI,IAAI,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,GAAW,EAAE,KAAkD;QACzE,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjC,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE5B,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAChC,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE;gBAC1B,MAAM,UAAU,GAAG,EAAE,CAAC,YAAY,CAAoB,aAAa,EAAE,GAAG,CAAC,CAAC;gBAC1E,MAAM,SAAS,GAAG,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC;gBAC5C,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;gBACnC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBACrC,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAW;QACxB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAG,EAAE,CAAC,UAAU,CAAkB,sBAAsB,CAAC,CAAC;QACtE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC3C,OAAO,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;QACjB,CAAC;IACH,CAAC;IAED,YAAY;IAEZ,mDAAmD;IAEnD;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,GAAW;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,GAAW,EAAE,KAAkD;QAC3E,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,GAAW;QAC1B,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,SAAS,CAAC,GAAW,EAAE,KAAa;QACxC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE,EAAE;YACzC,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;gBACtB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,UAAU,GAAG,aAAa,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,IAAc;QAC3B,OAAO,OAAO,CAAC,GAAG,CAChB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,EAAoC,EAAE;YACvD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC1B,OAAO,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,aAAiC;QAC9C,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,MAAM,EAAE,CAAC,6BAA6B,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;YAClD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,aAAa,EAAE,CAAC;gBACzC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBACjC,MAAM,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,IAAc;QAC9B,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,MAAM,EAAE,CAAC,6BAA6B,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;YAClD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAC1B,MAAM,EAAE,CAAC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,aAAiC;QAChD,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,MAAM,EAAE,CAAC,6BAA6B,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;YAClD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,aAAa,EAAE,CAAC;gBACzC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBACjC,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,aAAa,CAAoB,aAAa,EAAE,GAAG,CAAC,CAAC;gBAChF,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;oBACtB,MAAM,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC7C,SAAS;gBACX,CAAC;gBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAClC,MAAM,UAAU,GAAG,aAAa,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC9D,MAAM,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,CAAC;IAED,YAAY;IAEZ,mBAAmB;IAEX,KAAK,CAAC,UAAU;QACtB,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QACpC,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBACb,MAAM,EAAE,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACtD,MAAM,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;gBACnC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;YACf,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QACD,OAAO,IAAI,CAAC,EAAE,CAAC;IACjB,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,MAAM,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/C,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC,EAAE,CAAC;IACjB,CAAC;IAEO,mBAAmB,CAAC,EAAkB;QAC5C,OAAO,EAAE,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;YACxC,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,aAAa,CAA2B,qBAAqB,CAAC,CAAC;YACvF,IAAI,gBAAgB,GAAG,MAAM,EAAE,YAAY,IAAI,CAAC,CAAC;YACjD,IAAI,gBAAgB,IAAI,gBAAgB,EAAE,CAAC;gBACzC,OAAO;YACT,CAAC;YACD,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,EAAE,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;gBAC1C,gBAAgB,GAAG,CAAC,CAAC;YACvB,CAAC;YACD,MAAM,EAAE,CAAC,SAAS,CAAC,yBAAyB,gBAAgB,EAAE,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,EAAkB;QAC3C,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE;YAC1B,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAA2B,qBAAqB,CAAC,CAAC;YAChF,IAAI,gBAAgB,GAAG,MAAM,EAAE,YAAY,IAAI,CAAC,CAAC;YACjD,IAAI,gBAAgB,IAAI,gBAAgB,EAAE,CAAC;gBACzC,OAAO;YACT,CAAC;YACD,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;gBAC3B,EAAE,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;gBACnC,gBAAgB,GAAG,CAAC,CAAC;YACvB,CAAC;YACD,EAAE,CAAC,QAAQ,CAAC,yBAAyB,gBAAgB,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,SAAS,CAAC,MAAW,EAAE,MAAW;QAC/C,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAE7B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,IAAI,MAAM,CAAC,GAAG,CAAC,YAAY,KAAK,EAAE,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;oBACjB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;gBACnB,CAAC;gBACD,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAChD,CAAC;iBAAM,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC3C,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,eAAe,CAAC,GAAG,KAAgB;QACzC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;QAE3B,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CACb,yBAAyB,OAAO,GAAG,mEAAmE,GAAG,EAAE,CAC5G,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YACjF,MAAM,IAAI,KAAK,CACb,yBAAyB,OAAO,KAAK,qEAAqE,GAAG,oBAAoB,KAAK,EAAE,CACzI,CAAC;QACJ,CAAC;IACH,CAAC;CAGF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,IAAI,aAAa,CAAC,mBAAmB,CAAC,CAAC;AAEnE,eAAe,YAAY,CAAC;AAE5B;;GAEG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,YAAY,CAAC", "sourcesContent": ["import AwaitLock from 'await-lock';\n\nimport { openDatabaseAsync, openDatabaseSync, type SQLiteDatabase } from './index';\n\n/**\n * Update function for the [`setItemAsync()`](#setitemasynckey-value) or [`setItemSync()`](#setitemsynckey-value) method. It computes the new value based on the previous value. The function returns the new value to set for the key.\n * @param prevValue The previous value associated with the key, or `null` if the key was not set.\n * @returns The new value to set for the key.\n */\nexport type SQLiteStorageSetItemUpdateFunction = (prevValue: string | null) => string;\n\nconst DATABASE_VERSION = 1;\nconst STATEMENT_GET = 'SELECT value FROM storage WHERE key = ?;';\nconst STATEMENT_SET =\n  'INSERT INTO storage (key, value) VALUES (?, ?) ON CONFLICT(key) DO UPDATE SET value = excluded.value;';\nconst STATEMENT_REMOVE = 'DELETE FROM storage WHERE key = ?;';\nconst STATEMENT_GET_ALL_KEYS = 'SELECT key FROM storage;';\nconst STATEMENT_CLEAR = 'DELETE FROM storage;';\n\nconst MIGRATION_STATEMENT_0 =\n  'CREATE TABLE IF NOT EXISTS storage (key TEXT PRIMARY KEY NOT NULL, value TEXT);';\n\n/**\n * Key-value store backed by SQLite. This class accepts a `databaseName` parameter in its constructor, which is the name of the database file to use for the storage.\n */\nexport class SQLiteStorage {\n  private db: SQLiteDatabase | null = null;\n  private readonly awaitLock = new AwaitLock();\n\n  constructor(private readonly databaseName: string) {}\n\n  //#region Asynchronous API\n\n  /**\n   * Retrieves the value associated with the given key asynchronously.\n   */\n  async getItemAsync(key: string): Promise<string | null> {\n    this.checkValidInput(key);\n    const db = await this.getDbAsync();\n    const result = await db.getFirstAsync<{ value: string }>(STATEMENT_GET, key);\n    return result?.value ?? null;\n  }\n\n  /**\n   * Sets the value for the given key asynchronously.\n   * If a function is provided, it computes the new value based on the previous value.\n   */\n  async setItemAsync(\n    key: string,\n    value: string | SQLiteStorageSetItemUpdateFunction\n  ): Promise<void> {\n    this.checkValidInput(key, value);\n    const db = await this.getDbAsync();\n\n    if (typeof value === 'function') {\n      await db.withExclusiveTransactionAsync(async (tx) => {\n        const prevResult = await tx.getFirstAsync<{ value: string }>(STATEMENT_GET, key);\n        const prevValue = prevResult?.value ?? null;\n        const nextValue = value(prevValue);\n        this.checkValidInput(key, nextValue);\n        await tx.runAsync(STATEMENT_SET, key, nextValue);\n      });\n      return;\n    }\n\n    await db.runAsync(STATEMENT_SET, key, value);\n  }\n\n  /**\n   * Removes the value associated with the given key asynchronously.\n   */\n  async removeItemAsync(key: string): Promise<boolean> {\n    this.checkValidInput(key);\n    const db = await this.getDbAsync();\n    const result = await db.runAsync(STATEMENT_REMOVE, key);\n    return result.changes > 0;\n  }\n\n  /**\n   * Retrieves all keys stored in the storage asynchronously.\n   */\n  async getAllKeysAsync(): Promise<string[]> {\n    const db = await this.getDbAsync();\n    const result = await db.getAllAsync<{ key: string }>(STATEMENT_GET_ALL_KEYS);\n    return result.map(({ key }) => key);\n  }\n\n  /**\n   * Clears all key-value pairs from the storage asynchronously.\n   */\n  async clearAsync(): Promise<boolean> {\n    const db = await this.getDbAsync();\n    const result = await db.runAsync(STATEMENT_CLEAR);\n    return result.changes > 0;\n  }\n\n  /**\n   * Closes the database connection asynchronously.\n   */\n  async closeAsync(): Promise<void> {\n    await this.awaitLock.acquireAsync();\n    try {\n      if (this.db) {\n        await this.db.closeAsync();\n        this.db = null;\n      }\n    } finally {\n      this.awaitLock.release();\n    }\n  }\n\n  //#endregion\n\n  //#region Synchronous API\n\n  /**\n   * Retrieves the value associated with the given key synchronously.\n   */\n  getItemSync(key: string): string | null {\n    this.checkValidInput(key);\n    const db = this.getDbSync();\n    const result = db.getFirstSync<{ value: string }>(STATEMENT_GET, key);\n    return result?.value ?? null;\n  }\n\n  /**\n   * Sets the value for the given key synchronously.\n   * If a function is provided, it computes the new value based on the previous value.\n   */\n  setItemSync(key: string, value: string | SQLiteStorageSetItemUpdateFunction): void {\n    this.checkValidInput(key, value);\n    const db = this.getDbSync();\n\n    if (typeof value === 'function') {\n      db.withTransactionSync(() => {\n        const prevResult = db.getFirstSync<{ value: string }>(STATEMENT_GET, key);\n        const prevValue = prevResult?.value ?? null;\n        const nextValue = value(prevValue);\n        this.checkValidInput(key, nextValue);\n        db.runSync(STATEMENT_SET, key, nextValue);\n      });\n      return;\n    }\n\n    db.runSync(STATEMENT_SET, key, value);\n  }\n\n  /**\n   * Removes the value associated with the given key synchronously.\n   */\n  removeItemSync(key: string): boolean {\n    this.checkValidInput(key);\n    const db = this.getDbSync();\n    const result = db.runSync(STATEMENT_REMOVE, key);\n    return result.changes > 0;\n  }\n\n  /**\n   * Retrieves all keys stored in the storage synchronously.\n   */\n  getAllKeysSync(): string[] {\n    const db = this.getDbSync();\n    const result = db.getAllSync<{ key: string }>(STATEMENT_GET_ALL_KEYS);\n    return result.map(({ key }) => key);\n  }\n\n  /**\n   * Clears all key-value pairs from the storage synchronously.\n   */\n  clearSync(): boolean {\n    const db = this.getDbSync();\n    const result = db.runSync(STATEMENT_CLEAR);\n    return result.changes > 0;\n  }\n\n  /**\n   * Closes the database connection synchronously.\n   */\n  closeSync(): void {\n    if (this.db) {\n      this.db.closeSync();\n      this.db = null;\n    }\n  }\n\n  //#endregion\n\n  //#region react-native-async-storage compatible API\n\n  /**\n   * Alias for [`getItemAsync()`](#getitemasynckey) method.\n   */\n  async getItem(key: string): Promise<string | null> {\n    return this.getItemAsync(key);\n  }\n\n  /**\n   * Alias for [`setItemAsync()`](#setitemasynckey-value).\n   */\n  async setItem(key: string, value: string | SQLiteStorageSetItemUpdateFunction): Promise<void> {\n    await this.setItemAsync(key, value);\n  }\n\n  /**\n   * Alias for [`removeItemAsync()`](#removeitemasynckey) method.\n   */\n  async removeItem(key: string): Promise<void> {\n    await this.removeItemAsync(key);\n  }\n\n  /**\n   * Alias for [`getAllKeysAsync()`](#getallkeysasync) method.\n   */\n  async getAllKeys(): Promise<string[]> {\n    return this.getAllKeysAsync();\n  }\n\n  /**\n   * Alias for [`clearAsync()`](#clearasync) method.\n   */\n  async clear(): Promise<void> {\n    await this.clearAsync();\n  }\n\n  /**\n   * Merges the given value with the existing value for the given key asynchronously.\n   * If the existing value is a JSON object, performs a deep merge.\n   */\n  async mergeItem(key: string, value: string): Promise<void> {\n    this.checkValidInput(key, value);\n    await this.setItemAsync(key, (prevValue) => {\n      if (prevValue == null) {\n        return value;\n      }\n      const prevJSON = JSON.parse(prevValue);\n      const newJSON = JSON.parse(value);\n      const mergedJSON = SQLiteStorage.mergeDeep(prevJSON, newJSON);\n      return JSON.stringify(mergedJSON);\n    });\n  }\n\n  /**\n   * Retrieves the values associated with the given keys asynchronously.\n   */\n  async multiGet(keys: string[]): Promise<[string, string | null][]> {\n    return Promise.all(\n      keys.map(async (key): Promise<[string, string | null]> => {\n        this.checkValidInput(key);\n        return [key, await this.getItemAsync(key)];\n      })\n    );\n  }\n\n  /**\n   * Sets multiple key-value pairs asynchronously.\n   */\n  async multiSet(keyValuePairs: [string, string][]): Promise<void> {\n    const db = await this.getDbAsync();\n    await db.withExclusiveTransactionAsync(async (tx) => {\n      for (const [key, value] of keyValuePairs) {\n        this.checkValidInput(key, value);\n        await tx.runAsync(STATEMENT_SET, key, value);\n      }\n    });\n  }\n\n  /**\n   * Removes the values associated with the given keys asynchronously.\n   */\n  async multiRemove(keys: string[]): Promise<void> {\n    const db = await this.getDbAsync();\n    await db.withExclusiveTransactionAsync(async (tx) => {\n      for (const key of keys) {\n        this.checkValidInput(key);\n        await tx.runAsync(STATEMENT_REMOVE, key);\n      }\n    });\n  }\n\n  /**\n   * Merges multiple key-value pairs asynchronously.\n   * If existing values are JSON objects, performs a deep merge.\n   */\n  async multiMerge(keyValuePairs: [string, string][]): Promise<void> {\n    const db = await this.getDbAsync();\n    await db.withExclusiveTransactionAsync(async (tx) => {\n      for (const [key, value] of keyValuePairs) {\n        this.checkValidInput(key, value);\n        const prevValue = await tx.getFirstAsync<{ value: string }>(STATEMENT_GET, key);\n        if (prevValue == null) {\n          await tx.runAsync(STATEMENT_SET, key, value);\n          continue;\n        }\n        const prevJSON = JSON.parse(prevValue.value);\n        const newJSON = JSON.parse(value);\n        const mergedJSON = SQLiteStorage.mergeDeep(prevJSON, newJSON);\n        await tx.runAsync(STATEMENT_SET, key, JSON.stringify(mergedJSON));\n      }\n    });\n  }\n\n  /**\n   * Alias for [`closeAsync()`](#closeasync-1) method.\n   */\n  async close(): Promise<void> {\n    await this.closeAsync();\n  }\n\n  //#endregion\n\n  //#region Internals\n\n  private async getDbAsync(): Promise<SQLiteDatabase> {\n    await this.awaitLock.acquireAsync();\n    try {\n      if (!this.db) {\n        const db = await openDatabaseAsync(this.databaseName);\n        await this.maybeMigrateDbAsync(db);\n        this.db = db;\n      }\n    } finally {\n      this.awaitLock.release();\n    }\n    return this.db;\n  }\n\n  private getDbSync(): SQLiteDatabase {\n    if (!this.db) {\n      const db = openDatabaseSync(this.databaseName);\n      this.maybeMigrateDbSync(db);\n      this.db = db;\n    }\n    return this.db;\n  }\n\n  private maybeMigrateDbAsync(db: SQLiteDatabase) {\n    return db.withTransactionAsync(async () => {\n      const result = await db.getFirstAsync<{ user_version: number }>('PRAGMA user_version');\n      let currentDbVersion = result?.user_version ?? 0;\n      if (currentDbVersion >= DATABASE_VERSION) {\n        return;\n      }\n      if (currentDbVersion === 0) {\n        await db.execAsync(MIGRATION_STATEMENT_0);\n        currentDbVersion = 1;\n      }\n      await db.execAsync(`PRAGMA user_version = ${DATABASE_VERSION}`);\n    });\n  }\n\n  private maybeMigrateDbSync(db: SQLiteDatabase) {\n    db.withTransactionSync(() => {\n      const result = db.getFirstSync<{ user_version: number }>('PRAGMA user_version');\n      let currentDbVersion = result?.user_version ?? 0;\n      if (currentDbVersion >= DATABASE_VERSION) {\n        return;\n      }\n      if (currentDbVersion === 0) {\n        db.execSync(MIGRATION_STATEMENT_0);\n        currentDbVersion = 1;\n      }\n      db.execSync(`PRAGMA user_version = ${DATABASE_VERSION}`);\n    });\n  }\n\n  /**\n   * Recursively merge two JSON objects.\n   */\n  private static mergeDeep(target: any, source: any): any {\n    if (typeof target !== 'object' || target === null) {\n      return source;\n    }\n\n    if (typeof source !== 'object' || source === null) {\n      return target;\n    }\n\n    const output = { ...target };\n\n    for (const key of Object.keys(source)) {\n      if (source[key] instanceof Array) {\n        if (!output[key]) {\n          output[key] = [];\n        }\n        output[key] = output[key].concat(source[key]);\n      } else if (typeof source[key] === 'object') {\n        output[key] = this.mergeDeep(target[key], source[key]);\n      } else {\n        output[key] = source[key];\n      }\n    }\n\n    return output;\n  }\n\n  private checkValidInput(...input: unknown[]) {\n    const [key, value] = input;\n\n    if (typeof key !== 'string') {\n      throw new Error(\n        `[SQLiteStorage] Using ${typeof key} type for key is not supported. Use string instead. Key passed: ${key}`\n      );\n    }\n\n    if (input.length > 1 && typeof value !== 'string' && typeof value !== 'function') {\n      throw new Error(\n        `[SQLiteStorage] Using ${typeof value} type for value is not supported. Use string instead. Key passed: ${key}. Value passed : ${value}`\n      );\n    }\n  }\n\n  //#endregion\n}\n\n/**\n * This default instance of the [`SQLiteStorage`](#sqlitestorage-1) class is used as a drop-in replacement for the `AsyncStorage` module from [`@react-native-async-storage/async-storage`](https://github.com/react-native-async-storage/async-storage).\n */\nexport const AsyncStorage = new SQLiteStorage('ExpoSQLiteStorage');\n\nexport default AsyncStorage;\n\n/**\n * Alias for [`AsyncStorage`](#sqliteasyncstorage), given the storage not only offers asynchronous methods.\n */\nexport const Storage = AsyncStorage;\n"]}