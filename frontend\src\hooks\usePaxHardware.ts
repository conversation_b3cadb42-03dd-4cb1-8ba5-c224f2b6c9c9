import { useState, useEffect, useCallback } from 'react';
import { paxService, PaxPaymentRequest, PaxPaymentResponse, PaxReceiptData } from '../services/integratedPaxService';

interface UsePaxHardwareReturn {
  isReady: boolean;
  isProcessing: boolean;
  processPayment: (request: PaxPaymentRequest) => Promise<PaxPaymentResponse>;
  printReceipt: (receiptData: PaxReceiptData) => Promise<boolean>;
  checkStatus: () => Promise<boolean>;
  error: string | null;
}

export const usePaxHardware = (): UsePaxHardwareReturn => {
  const [isReady, setIsReady] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if PAX service is ready
  const checkStatus = useCallback(async (): Promise<boolean> => {
    try {
      const ready = await paxService.isReady();
      setIsReady(ready);
      if (ready) {
        setError(null);
      }
      return ready;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      setIsReady(false);
      return false;
    }
  }, []);

  // Process payment
  const processPayment = useCallback(async (request: PaxPaymentRequest): Promise<PaxPaymentResponse> => {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await paxService.processPayment(request);
      
      if (!response.success) {
        setError(response.message || 'Payment failed');
      }
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Payment processing failed';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsProcessing(false);
    }
  }, []);

  // Print receipt
  const printReceipt = useCallback(async (receiptData: PaxReceiptData): Promise<boolean> => {
    setError(null);

    try {
      const success = await paxService.printReceipt(receiptData);
      
      if (!success) {
        setError('Receipt printing failed');
      }
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Receipt printing failed';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Initialize and check status on mount
  useEffect(() => {
    let mounted = true;
    let statusInterval: NodeJS.Timeout;

    const initialize = async () => {
      // Wait a bit for the service to start up
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (mounted) {
        await checkStatus();
        
        // Set up periodic status checks
        statusInterval = setInterval(async () => {
          if (mounted) {
            await checkStatus();
          }
        }, 10000); // Check every 10 seconds
      }
    };

    initialize();

    return () => {
      mounted = false;
      if (statusInterval) {
        clearInterval(statusInterval);
      }
    };
  }, [checkStatus]);

  return {
    isReady,
    isProcessing,
    processPayment,
    printReceipt,
    checkStatus,
    error
  };
};

export default usePaxHardware;
