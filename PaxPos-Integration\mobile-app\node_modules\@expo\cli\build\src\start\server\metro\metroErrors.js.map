{"version": 3, "sources": ["../../../../../src/start/server/metro/metroErrors.ts"], "sourcesContent": ["// Used to cast a type to metro errors without depending on specific versions of metro.\n\nexport type FileAndDirCandidates = {\n  dir: FileCandidates;\n  file: FileCandidates;\n};\n\n/**\n * This is a way to describe what files we tried to look for when resolving\n * a module name as file. This is mainly used for error reporting, so that\n * we can explain why we cannot resolve a module.\n */\nexport type FileCandidates =\n  // We only tried to resolve a specific asset.\n  | { type: 'asset'; name: string }\n  // We attempted to resolve a name as being a source file (ex. JavaScript,\n  // JSON...), in which case there can be several extensions we tried, for\n  // example `/js/foo.ios.js`, `/js/foo.js`, etc. for a single prefix '/js/foo'.\n  | {\n      type: 'sourceFile';\n      filePathPrefix: string;\n      candidateExts: readonly string[];\n    };\n\ntype FailedToResolveNameError = Error & {\n  dirPaths: string[];\n  extraPaths: string[];\n};\n\ntype FailedToResolvePathError = Error & {\n  candidates: FileAndDirCandidates;\n};\n\nexport function isFailedToResolveNameError(error: any): error is FailedToResolveNameError {\n  return !!error && 'extraPaths' in error && error.constructor.name === 'FailedToResolveNameError';\n}\n\nexport function isFailedToResolvePathError(error: any): error is FailedToResolvePathError {\n  return (\n    !!error &&\n    'candidates' in error &&\n    error.constructor.name === 'FailedToResolvePathError' &&\n    !error.message.includes('Importing native-only module')\n  );\n}\n"], "names": ["isFailedToResolveNameError", "isFailedToResolvePathError", "error", "constructor", "name", "message", "includes"], "mappings": "AAAA,uFAAuF;;;;;;;;;;;;IAiCvEA,0BAA0B;eAA1BA;;IAIAC,0BAA0B;eAA1BA;;;AAJT,SAASD,2BAA2BE,KAAU;IACnD,OAAO,CAAC,CAACA,SAAS,gBAAgBA,SAASA,MAAMC,WAAW,CAACC,IAAI,KAAK;AACxE;AAEO,SAASH,2BAA2BC,KAAU;IACnD,OACE,CAAC,CAACA,SACF,gBAAgBA,SAChBA,MAAMC,WAAW,CAACC,IAAI,KAAK,8BAC3B,CAACF,MAAMG,OAAO,CAACC,QAAQ,CAAC;AAE5B"}