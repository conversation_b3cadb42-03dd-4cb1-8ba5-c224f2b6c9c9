{"version": 3, "sources": ["../../../../src/run/android/resolveDevice.ts"], "sourcesContent": ["import { AndroidDeviceManager } from '../../start/platforms/android/AndroidDeviceManager';\nimport { logDeviceArgument } from '../hints';\n\nconst debug = require('debug')('expo:android:resolveDevice');\n\n/** Given a `device` argument from the CLI, parse and prompt our way to a usable device for building. */\nexport async function resolveDeviceAsync(device?: string | boolean) {\n  if (!device) {\n    const manager = await AndroidDeviceManager.resolveAsync();\n    debug(`Resolved default device (name: ${manager.device.name}, pid: ${manager.device.pid})`);\n    return manager;\n  }\n\n  debug(`Resolving device from argument: ${device}`);\n  const manager =\n    device === true\n      ? // `--device` (no props after)\n        await AndroidDeviceManager.resolveAsync({ shouldPrompt: true })\n      : // `--device <name>`\n        await AndroidDeviceManager.resolveFromNameAsync(device);\n  logDeviceArgument(manager.device.name);\n  return manager;\n}\n"], "names": ["resolveDeviceAsync", "debug", "require", "device", "manager", "AndroidDeviceManager", "resolveAsync", "name", "pid", "should<PERSON>rompt", "resolveFromNameAsync", "logDeviceArgument"], "mappings": ";;;;+BAMs<PERSON>;;;eAAAA;;;sCANe;uBACH;AAElC,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,eAAeF,mBAAmBG,MAAyB;IAChE,IAAI,CAACA,QAAQ;QACX,MAAMC,UAAU,MAAMC,0CAAoB,CAACC,YAAY;QACvDL,MAAM,CAAC,+BAA+B,EAAEG,QAAQD,MAAM,CAACI,IAAI,CAAC,OAAO,EAAEH,QAAQD,MAAM,CAACK,GAAG,CAAC,CAAC,CAAC;QAC1F,OAAOJ;IACT;IAEAH,MAAM,CAAC,gCAAgC,EAAEE,QAAQ;IACjD,MAAMC,UACJD,WAAW,OAEP,MAAME,0CAAoB,CAACC,YAAY,CAAC;QAAEG,cAAc;IAAK,KAE7D,MAAMJ,0CAAoB,CAACK,oBAAoB,CAACP;IACtDQ,IAAAA,wBAAiB,EAACP,QAAQD,MAAM,CAACI,IAAI;IACrC,OAAOH;AACT"}