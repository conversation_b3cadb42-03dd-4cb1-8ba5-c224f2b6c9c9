import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as a from"../../core/sdk/sdk.js";import*as r from"../../ui/legacy/legacy.js";import*as n from"../../ui/visual_logging/visual_logging.js";import*as i from"../../core/root/root.js";import*as s from"../../core/platform/platform.js";import*as o from"../../core/host/host.js";import*as l from"../../ui/components/panel_feedback/panel_feedback.js";const d=new CSSStyleSheet;d.replaceSync('.widget.ax-subpane{overflow-x:hidden;user-select:text}.ax-ignored-info{padding:6px}.ax-ignored-node-pane{flex:none}.invalid{text-decoration:line-through}span.ax-value-undefined{font-style:italic}.ax-value-source-unused{opacity:70%}.ax-value-source-superseded,\n.ax-value-source-invalid{text-decoration:line-through}.tree-outline span[is="dt-icon-label"]{position:relative;left:-11px}.tree-outline li{display:block;overflow-x:hidden;align-items:baseline}.tree-outline li::before{content:"";width:14px;display:inline-block;margin-bottom:-2px;margin-right:3px}.tree-outline li.property{color:var(--sys-color-on-surface)}.tree-outline li.invalid{position:relative;left:-2px}.tree-outline span[is="dt-icon-label"] + .ax-name{margin-left:-11px}.tree-outline li span{flex-shrink:0;text-overflow:ellipsis;white-space:nowrap}@media (forced-colors: active){.ax-value-source-unused{opacity:100%}.tree-outline-disclosure:hover li.parent::before{background-color:ButtonText}}\n/*# sourceURL=accessibilityNode.css */\n');const c={disabled:"Disabled",ifTrueThisElementCurrentlyCannot:"If true, this element currently cannot be interacted with.",invalidUserEntry:"Invalid user entry",ifTrueThisElementsUserentered:"If true, this element's user-entered value does not conform to validation requirement.",editable:"Editable",ifAndHowThisElementCanBeEdited:"If and how this element can be edited.",focusable:"Focusable",ifTrueThisElementCanReceiveFocus:"If true, this element can receive focus.",focused:"Focused",ifTrueThisElementCurrentlyHas:"If `true`, this element currently has focus.",canSetValue:"Can set value",whetherTheValueOfThisElementCan:"Whether the value of this element can be set.",liveRegion:"Live region",whetherAndWhatPriorityOfLive:"Whether and what priority of live updates may be expected for this element.",atomicLiveRegions:"Atomic (live regions)",ifThisElementMayReceiveLive:"If this element may receive live updates, whether the entire live region should be presented to the user on changes, or only changed nodes.",relevantLiveRegions:"Relevant (live regions)",ifThisElementMayReceiveLiveUpdates:"If this element may receive live updates, what type of updates should trigger a notification.",busyLiveRegions:"`Busy` (live regions)",whetherThisElementOrItsSubtree:"Whether this element or its subtree are currently being updated (and thus may be in an inconsistent state).",liveRegionRoot:"Live region root",ifThisElementMayReceiveLiveUpdatesThe:"If this element may receive live updates, the root element of the containing live region.",hasAutocomplete:"Has autocomplete",whetherAndWhatTypeOfAutocomplete:"Whether and what type of autocomplete suggestions are currently provided by this element.",hasPopup:"Has popup",whetherThisElementHasCausedSome:"Whether this element has caused some kind of pop-up (such as a menu) to appear.",level:"Level",theHierarchicalLevelOfThis:"The hierarchical level of this element.",multiselectable:"Multi-selectable",whetherAUserMaySelectMoreThanOne:"Whether a user may select more than one option from this widget.",orientation:"Orientation",whetherThisLinearElements:"Whether this linear element's orientation is horizontal or vertical.",multiline:"Multi-line",whetherThisTextBoxMayHaveMore:"Whether this text box may have more than one line.",readonlyString:"Read-only",ifTrueThisElementMayBeInteracted:"If true, this element may be interacted with, but its value cannot be changed.",requiredString:"Required",whetherThisElementIsARequired:"Whether this element is a required field in a form.",minimumValue:"Minimum value",forARangeWidgetTheMinimumAllowed:"For a range widget, the minimum allowed value.",maximumValue:"Maximum value",forARangeWidgetTheMaximumAllowed:"For a range widget, the maximum allowed value.",valueDescription:"Value description",aHumanreadableVersionOfTheValue:"A human-readable version of the value of a range widget (where necessary).",checked:"Checked",whetherThisCheckboxRadioButtonOr:"Whether this checkbox, radio button or tree item is checked, unchecked, or mixed (e.g. has both checked and un-checked children).",expanded:"Expanded",whetherThisElementOrAnother:"Whether this element, or another grouping element it controls, is expanded.",pressed:"Pressed",whetherThisToggleButtonIs:"Whether this toggle button is currently in a pressed state.",selectedString:"Selected",whetherTheOptionRepresentedBy:"Whether the option represented by this element is currently selected.",activeDescendant:"Active descendant",theDescendantOfThisElementWhich:"The descendant of this element which is active; i.e. the element to which focus should be delegated.",elementToWhichTheUserMayChooseTo:"Element to which the user may choose to navigate after this one, instead of the next element in the DOM order.",controls:"Controls",elementOrElementsWhoseContentOr:"Element or elements whose content or presence is/are controlled by this widget.",describedBy:"Described by",elementOrElementsWhichFormThe:"Element or elements which form the description of this element.",labeledBy:"Labeled by",elementOrElementsWhichMayFormThe:"Element or elements which may form the name of this element.",elementOrElementsWhichShouldBe:"Element or elements which should be considered descendants of this element, despite not being descendants in the DOM.",theComputedNameOfThisElement:"The computed name of this element.",role:"Role",indicatesThePurposeOfThisElement:"Indicates the purpose of this element, such as a user interface idiom for a widget, or structural role within a document.",value:"Value",theValueOfThisElementThisMayBe:"The value of this element; this may be user-provided or developer-provided, depending on the element.",help:"Help",theComputedHelpTextForThis:"The computed help text for this element.",description:"Description",theAccessibleDescriptionForThis:"The accessible description for this element.",fromAttribute:"From attribute",valueFromAttribute:"Value from attribute.",implicit:"Implicit",implicitValue:"Implicit value.",fromStyle:"From style",valueFromStyle:"Value from style.",contents:"Contents",valueFromElementContents:"Value from element contents.",fromPlaceholderAttribute:"From placeholder attribute",valueFromPlaceholderAttribute:"Value from placeholder attribute.",relatedElement:"Related element",valueFromRelatedElement:"Value from related element.",fromCaption:"From `caption`",valueFromFigcaptionElement:"Value from `figcaption` element.",fromDescription:"From `description`",valueFromDescriptionElement:"Value from `description` element.",fromLabel:"From `label`",valueFromLabelElement:"Value from `label` element.",fromLabelFor:"From `label` (`for=` attribute)",valueFromLabelElementWithFor:"Value from `label` element with `for=` attribute.",fromLabelWrapped:"From `label` (wrapped)",valueFromLabelElementWrapped:"Value from a wrapping `label` element.",fromLegend:"From `legend`",valueFromLegendElement:"Value from `legend` element.",fromRubyAnnotation:"From ruby annotation",valueFromNativeHtmlRuby:"Value from plain HTML ruby annotation.",valueFromTableCaption:"Value from `table` `caption`.",fromTitle:"From title",valueFromTitleAttribute:"Value from title attribute.",fromNativeHtml:"From native HTML",valueFromNativeHtmlUnknownSource:"Value from native HTML (unknown source)."},u=t.i18n.registerUIStrings("panels/accessibility/AccessibilityStrings.ts",c),m=t.i18n.getLazilyComputedLocalizedString.bind(void 0,u),h={disabled:{name:m(c.disabled),description:m(c.ifTrueThisElementCurrentlyCannot),group:"AXGlobalStates"},invalid:{name:m(c.invalidUserEntry),description:m(c.ifTrueThisElementsUserentered),group:"AXGlobalStates"},editable:{name:m(c.editable),description:m(c.ifAndHowThisElementCanBeEdited)},focusable:{name:m(c.focusable),description:m(c.ifTrueThisElementCanReceiveFocus)},focused:{name:m(c.focused),description:m(c.ifTrueThisElementCurrentlyHas)},settable:{name:m(c.canSetValue),description:m(c.whetherTheValueOfThisElementCan)},live:{name:m(c.liveRegion),description:m(c.whetherAndWhatPriorityOfLive),group:"AXLiveRegionAttributes"},atomic:{name:m(c.atomicLiveRegions),description:m(c.ifThisElementMayReceiveLive),group:"AXLiveRegionAttributes"},relevant:{name:m(c.relevantLiveRegions),description:m(c.ifThisElementMayReceiveLiveUpdates),group:"AXLiveRegionAttributes"},busy:{name:m(c.busyLiveRegions),description:m(c.whetherThisElementOrItsSubtree),group:"AXLiveRegionAttributes"},root:{name:m(c.liveRegionRoot),description:m(c.ifThisElementMayReceiveLiveUpdatesThe),group:"AXLiveRegionAttributes"},autocomplete:{name:m(c.hasAutocomplete),description:m(c.whetherAndWhatTypeOfAutocomplete),group:"AXWidgetAttributes"},haspopup:{name:m(c.hasPopup),description:m(c.whetherThisElementHasCausedSome),group:"AXWidgetAttributes"},level:{name:m(c.level),description:m(c.theHierarchicalLevelOfThis),group:"AXWidgetAttributes"},multiselectable:{name:m(c.multiselectable),description:m(c.whetherAUserMaySelectMoreThanOne),group:"AXWidgetAttributes"},orientation:{name:m(c.orientation),description:m(c.whetherThisLinearElements),group:"AXWidgetAttributes"},multiline:{name:m(c.multiline),description:m(c.whetherThisTextBoxMayHaveMore),group:"AXWidgetAttributes"},readonly:{name:m(c.readonlyString),description:m(c.ifTrueThisElementMayBeInteracted),group:"AXWidgetAttributes"},required:{name:m(c.requiredString),description:m(c.whetherThisElementIsARequired),group:"AXWidgetAttributes"},valuemin:{name:m(c.minimumValue),description:m(c.forARangeWidgetTheMinimumAllowed),group:"AXWidgetAttributes"},valuemax:{name:m(c.maximumValue),description:m(c.forARangeWidgetTheMaximumAllowed),group:"AXWidgetAttributes"},valuetext:{name:m(c.valueDescription),description:m(c.aHumanreadableVersionOfTheValue),group:"AXWidgetAttributes"},checked:{name:m(c.checked),description:m(c.whetherThisCheckboxRadioButtonOr),group:"AXWidgetStates"},expanded:{name:m(c.expanded),description:m(c.whetherThisElementOrAnother),group:"AXWidgetStates"},pressed:{name:m(c.pressed),description:m(c.whetherThisToggleButtonIs),group:"AXWidgetStates"},selected:{name:m(c.selectedString),description:m(c.whetherTheOptionRepresentedBy),group:"AXWidgetStates"},activedescendant:{name:m(c.activeDescendant),description:m(c.theDescendantOfThisElementWhich),group:"AXRelationshipAttributes"},flowto:{name:t.i18n.lockedLazyString("Flows to"),description:m(c.elementToWhichTheUserMayChooseTo),group:"AXRelationshipAttributes"},controls:{name:m(c.controls),description:m(c.elementOrElementsWhoseContentOr),group:"AXRelationshipAttributes"},describedby:{name:m(c.describedBy),description:m(c.elementOrElementsWhichFormThe),group:"AXRelationshipAttributes"},labelledby:{name:m(c.labeledBy),description:m(c.elementOrElementsWhichMayFormThe),group:"AXRelationshipAttributes"},owns:{name:t.i18n.lockedLazyString("Owns"),description:m(c.elementOrElementsWhichShouldBe),group:"AXRelationshipAttributes"},name:{name:t.i18n.lockedLazyString("Name"),description:m(c.theComputedNameOfThisElement),group:"Default"},role:{name:m(c.role),description:m(c.indicatesThePurposeOfThisElement),group:"Default"},value:{name:m(c.value),description:m(c.theValueOfThisElementThisMayBe),group:"Default"},help:{name:m(c.help),description:m(c.theComputedHelpTextForThis),group:"Default"},description:{name:m(c.description),description:m(c.theAccessibleDescriptionForThis),group:"Default"}},p={attribute:{name:m(c.fromAttribute),description:m(c.valueFromAttribute)},implicit:{name:m(c.implicit),description:m(c.implicitValue)},style:{name:m(c.fromStyle),description:m(c.valueFromStyle)},contents:{name:m(c.contents),description:m(c.valueFromElementContents)},placeholder:{name:m(c.fromPlaceholderAttribute),description:m(c.valueFromPlaceholderAttribute)},relatedElement:{name:m(c.relatedElement),description:m(c.valueFromRelatedElement)}},b={description:{name:m(c.fromDescription),description:m(c.valueFromDescriptionElement)},figcaption:{name:m(c.fromCaption),description:m(c.valueFromFigcaptionElement)},label:{name:m(c.fromLabel),description:m(c.valueFromLabelElement)},labelfor:{name:m(c.fromLabelFor),description:m(c.valueFromLabelElementWithFor)},labelwrapped:{name:m(c.fromLabelWrapped),description:m(c.valueFromLabelElementWrapped)},legend:{name:m(c.fromLegend),description:m(c.valueFromLegendElement)},rubyannotation:{name:m(c.fromRubyAnnotation),description:m(c.valueFromNativeHtmlRuby)},tablecaption:{name:m(c.fromCaption),description:m(c.valueFromTableCaption)},title:{name:m(c.fromTitle),description:m(c.valueFromTitleAttribute)},other:{name:m(c.fromNativeHtml),description:m(c.valueFromNativeHtmlUnknownSource)}};var g=Object.freeze({__proto__:null,AXAttributes:h,AXSourceTypes:p,AXNativeSourceTypes:b});const v=new CSSStyleSheet;v.replaceSync(".ax-name{color:var(--sys-color-token-attribute);flex-shrink:0}.ax-readable-name{flex-shrink:0}.ax-readable-string{font-style:italic}.ax-value-string{color:var(--sys-color-token-property-special)}span.ax-internal-role{font-style:italic}#source-order-warning{padding-bottom:0;text-align:left}.source-order-checkbox{margin:2px 2px 2px 5px}\n/*# sourceURL=accessibilityProperties.css */\n");const f=new CSSStyleSheet;f.replaceSync(".value.object-value-node:hover{background-color:var(--sys-color-state-hover-on-subtle)}.object-value-function-prefix,\n.object-value-boolean{color:var(--sys-color-token-attribute-value)}.object-value-function{font-style:italic}.object-value-function.linkified:hover{--override-linkified-hover-background:rgb(0 0 0/10%);background-color:var(--override-linkified-hover-background);cursor:pointer}.theme-with-dark-background .object-value-function.linkified:hover,\n:host-context(.theme-with-dark-background) .object-value-function.linkified:hover{--override-linkified-hover-background:rgb(230 230 230/10%)}.object-value-number{color:var(--sys-color-token-attribute-value)}.object-value-bigint{color:var(--sys-color-token-comment)}.object-value-string,\n.object-value-regexp,\n.object-value-symbol{white-space:pre;unicode-bidi:-webkit-isolate;color:var(--sys-color-token-property-special)}.object-value-node{position:relative;vertical-align:baseline;color:var(--sys-color-token-variable);white-space:nowrap}.object-value-null,\n.object-value-undefined{color:var(--sys-color-state-disabled)}.object-value-unavailable{color:var(--sys-color-token-tag)}.object-value-calculate-value-button:hover{text-decoration:underline}.object-properties-section-custom-section{display:inline-flex;flex-direction:column}.theme-with-dark-background .object-value-number,\n:host-context(.theme-with-dark-background) .object-value-number,\n.theme-with-dark-background .object-value-boolean,\n:host-context(.theme-with-dark-background) .object-value-boolean{--override-primitive-dark-mode-color:hsl(252deg 100% 75%);color:var(--override-primitive-dark-mode-color)}.object-properties-section .object-description{color:var(--sys-color-token-subtle)}.value .object-properties-preview{white-space:nowrap}.name{color:var(--sys-color-token-tag);flex-shrink:0}.object-properties-preview .name{color:var(--sys-color-token-subtle)}@media (forced-colors: active){.object-value-calculate-value-button:hover{forced-color-adjust:none;color:Highlight}}\n/*# sourceURL=objectValue.css */\n");class x extends r.View.SimpleView{axNode;nodeInternal;constructor(e){super(e),this.axNode=null}setAXNode(e){}node(){return this.nodeInternal||null}setNode(e){this.nodeInternal=e}createInfo(e,t){const a=t||"gray-info-message",r=this.element.createChild("div",a);return r.textContent=e,r}createTreeOutline(){const e=new r.TreeOutline.TreeOutlineInShadow;return e.registerCSSFiles([d,v,f]),e.element.classList.add("hidden"),e.hideOverflow(),this.element.appendChild(e.element),e}wasShown(){super.wasShown(),this.registerCSSFiles([v])}}var y=Object.freeze({__proto__:null,AccessibilitySubPane:x});const E={computedProperties:"Computed Properties",noAccessibilityNode:"No accessibility node",accessibilityNodeNotExposed:"Accessibility node not exposed",invalidSource:"Invalid source.",notSpecified:"Not specified",noNodeWithThisId:"No node with this ID.",elementIsHiddenBy:"Element is hidden by active modal dialog: ",elementIsHiddenByChildTree:"Element is hidden by child tree: ",ancestorChildrenAreAll:"Ancestor's children are all presentational: ",elementIsPlaceholder:"Element is {PH1}.",placeholderIsPlaceholderOnAncestor:"{PH1} is {PH2} on ancestor: ",elementHasEmptyAltText:"Element has empty alt text.",noTextContent:"No text content.",elementIsInert:"Element is `inert`.",elementIsInAnInertSubTree:"Element is in an `inert` subtree from ",elementsInheritsPresentational:"Element inherits presentational role from ",partOfLabelElement:"Part of label element: ",labelFor:"Label for ",elementIsNotRendered:"Element is not rendered.",elementIsNotVisible:"Element is not visible.",elementHasPlaceholder:"Element has {PH1}.",elementIsPresentational:"Element is presentational.",elementNotInteresting:"Element not interesting for accessibility."},w=t.i18n.registerUIStrings("panels/accessibility/AccessibilityNodeView.ts",E),A=t.i18n.getLocalizedString.bind(void 0,w);class I extends x{axNode;noNodeInfo;ignoredInfo;treeOutline;ignoredReasonsTree;constructor(){super(A(E.computedProperties)),this.axNode=null,this.contentElement.classList.add("ax-subpane"),this.contentElement.setAttribute("jslog",`${n.section("computed-properties")}`),this.noNodeInfo=this.createInfo(A(E.noAccessibilityNode)),this.ignoredInfo=this.createInfo(A(E.accessibilityNodeNotExposed),"ax-ignored-info hidden"),this.treeOutline=this.createTreeOutline(),this.ignoredReasonsTree=this.createTreeOutline(),this.element.classList.add("accessibility-computed"),this.treeOutline.setFocusable(!0)}setAXNode(e){if(this.axNode===e)return;this.axNode=e;const t=this.treeOutline;t.removeChildren();const a=this.ignoredReasonsTree;if(a.removeChildren(),!e)return t.element.classList.add("hidden"),this.ignoredInfo.classList.add("hidden"),a.element.classList.add("hidden"),this.noNodeInfo.classList.remove("hidden"),void this.element.classList.add("ax-ignored-node-pane");if(e.ignored()){this.noNodeInfo.classList.add("hidden"),t.element.classList.add("hidden"),this.element.classList.add("ax-ignored-node-pane"),this.ignoredInfo.classList.remove("hidden"),a.element.classList.remove("hidden");const n=e.ignoredReasons();for(const t of n)r=t,a.appendChild(new F(r,e));return void(a.firstChild()||a.element.classList.add("hidden"))}var r;function n(a){t.appendChild(new C(a,e))}this.element.classList.remove("ax-ignored-node-pane"),this.ignoredInfo.classList.add("hidden"),a.element.classList.add("hidden"),this.noNodeInfo.classList.add("hidden"),t.element.classList.remove("hidden");for(const t of e.coreProperties())n(t);const i=e.role();if(i){n({name:"role",value:i})}for(const t of e.properties())n(t);const s=t.firstChild();s&&s.select(!0,!1)}setNode(e){super.setNode(e),this.axNode=null}wasShown(){super.wasShown(),this.registerCSSFiles([d])}}class N extends r.TreeOutline.TreeElement{axNode;constructor(e){super(""),this.axNode=e}static createSimpleValueElement(e,t){let a,n;e&&"valueUndefined"!==e&&"computedString"!==e?(a=document.createElement("span"),a.classList.add("monospace")):a=document.createElement("span");return n=e&&T.has(e)?'"'+t.replace(/\n/g,"↵")+'"':String(t),e&&e in k&&a.classList.add(k[e]),a.setTextContentTruncatedIfNeeded(n||""),r.Tooltip.Tooltip.install(a,String(t)||""),a}static createExclamationMark(e){const t=r.UIUtils.createIconLabel({iconName:"warning-filled",color:"var(--icon-warning)"});return r.Tooltip.Tooltip.install(t,e),t}appendNameElement(e){const t=document.createElement("span");if(e in h){const a=h[e];t.textContent=a.name(),r.Tooltip.Tooltip.install(t,a.description()),t.classList.add("ax-readable-name")}else t.textContent=e,t.classList.add("ax-name"),t.classList.add("monospace");this.listItemElement.appendChild(t)}appendValueElement(e){if("idref"===e.type||"node"===e.type||"idrefList"===e.type||"nodeList"===e.type)return void this.appendRelatedNodeListValueElement(e);if(e.sources){const t=e.sources;for(let e=0;e<t.length;e++){const a=t[e],r=new M(a,this.axNode);this.appendChild(r)}this.expand()}const t=N.createSimpleValueElement(e.type,String(e.value));this.listItemElement.appendChild(t)}appendRelatedNode(e,t){const r=new a.DOMModel.DeferredDOMNode(this.axNode.accessibilityModel().target(),e.backendDOMNodeId),n=new L({deferredNode:r,idref:void 0},e);this.appendChild(n)}appendRelatedNodeInline(e){const t=new a.DOMModel.DeferredDOMNode(this.axNode.accessibilityModel().target(),e.backendDOMNodeId),r=new S({deferredNode:t,idref:void 0},e);this.listItemElement.appendChild(r.render())}appendRelatedNodeListValueElement(e){!e.relatedNodes||1!==e.relatedNodes.length||e.value?(e.relatedNodes&&e.relatedNodes.forEach(this.appendRelatedNode,this),e.relatedNodes&&e.relatedNodes.length<=3?this.expand():this.collapse()):this.appendRelatedNodeInline(e.relatedNodes[0])}}const k={attribute:"ax-value-string",boolean:"object-value-boolean",booleanOrUndefined:"object-value-boolean",computedString:"ax-readable-string",idref:"ax-value-string",idrefList:"ax-value-string",integer:"object-value-number",internalRole:"ax-internal-role",number:"ax-value-number",role:"ax-role",string:"ax-value-string",tristate:"object-value-boolean",valueUndefined:"ax-value-undefined"},T=new Set(["string","computedString","idrefList","idref"]);class C extends N{property;toggleOnClick;constructor(e,t){super(t),this.property=e,this.toggleOnClick=!0,this.listItemElement.classList.add("property")}onattach(){this.update()}update(){this.listItemElement.removeChildren(),this.appendNameElement(this.property.name),this.listItemElement.createChild("span","separator").textContent=": ",this.appendValueElement(this.property.value)}}class M extends N{source;constructor(e,t){super(t),this.source=e}onattach(){this.update()}appendRelatedNodeWithIdref(e,t){const r=new a.DOMModel.DeferredDOMNode(this.axNode.accessibilityModel().target(),e.backendDOMNodeId),n=new L({deferredNode:r,idref:t},e);this.appendChild(n)}appendIDRefValueElement(e){if(null===e.value)return;const t=e.relatedNodes||[];if(""===e.value){for(const e of t){const t=e.idref||"";this.appendRelatedNodeWithIdref(e,t)}return}const a=e.value.trim().split(/\s+/);for(const e of a){const r=t.find((t=>t.idref===e));r?this.appendRelatedNodeWithIdref(r,e):1===a.length?this.listItemElement.appendChild(new S({deferredNode:void 0,idref:e}).render()):this.appendChild(new L({deferredNode:void 0,idref:e}))}}appendRelatedNodeListValueElement(e){const t=e.relatedNodes,a=t?t.length:0;"idrefList"===e.type||"idref"===e.type?this.appendIDRefValueElement(e):super.appendRelatedNodeListValueElement(e),a<=3?this.expand():this.collapse()}appendSourceNameElement(e){const t=document.createElement("span"),a=e.type;switch(a){case"attribute":case"placeholder":case"relatedElement":if(e.nativeSource){const a=e.nativeSource;t.textContent=b[a].name(),r.Tooltip.Tooltip.install(t,b[a].description()),t.classList.add("ax-readable-name");break}t.textContent=e.attribute||null,t.classList.add("ax-name"),t.classList.add("monospace");break;default:a in p?(t.textContent=p[a].name(),r.Tooltip.Tooltip.install(t,p[a].description()),t.classList.add("ax-readable-name")):(console.warn(a,"not in AXSourceTypes"),t.textContent=a)}this.listItemElement.appendChild(t)}update(){if(this.listItemElement.removeChildren(),this.source.invalid){const e=N.createExclamationMark(A(E.invalidSource));this.listItemElement.appendChild(e),this.listItemElement.classList.add("ax-value-source-invalid")}else this.source.superseded&&this.listItemElement.classList.add("ax-value-source-unused");if(this.appendSourceNameElement(this.source),this.listItemElement.createChild("span","separator").textContent=": ",this.source.attributeValue)this.appendValueElement(this.source.attributeValue),r.UIUtils.createTextChild(this.listItemElement," ");else if(this.source.nativeSourceValue)this.appendValueElement(this.source.nativeSourceValue),r.UIUtils.createTextChild(this.listItemElement," "),this.source.value&&this.appendValueElement(this.source.value);else if(this.source.value)this.appendValueElement(this.source.value);else{const e=N.createSimpleValueElement("valueUndefined",A(E.notSpecified));this.listItemElement.appendChild(e),this.listItemElement.classList.add("ax-value-source-unused")}this.source.value&&this.source.superseded&&this.listItemElement.classList.add("ax-value-source-superseded")}}class L extends r.TreeOutline.TreeElement{value;axRelatedNodeElement;constructor(e,t){super(""),this.value=t,this.axRelatedNodeElement=new S(e,t),this.selectable=!0}onattach(){this.listItemElement.appendChild(this.axRelatedNodeElement.render()),this.value&&this.value.text&&this.listItemElement.appendChild(N.createSimpleValueElement("computedString",this.value.text))}onenter(){return this.axRelatedNodeElement.revealNode(),!0}}class S{deferredNode;idref;value;constructor(e,t){this.deferredNode=e.deferredNode,this.idref=e.idref,this.value=t}render(){const t=document.createElement("span");if(this.deferredNode){const a=document.createElement("span");t.appendChild(a),this.deferredNode.resolvePromise().then((t=>{e.Linkifier.Linkifier.linkify(t,{tooltip:void 0,preventKeyboardFocus:!0}).then((e=>a.appendChild(e)))}))}else if(this.idref){t.classList.add("invalid");const e=N.createExclamationMark(A(E.noNodeWithThisId));r.UIUtils.createTextChild(e,this.idref),t.appendChild(e)}return t}revealNode(){this.deferredNode&&this.deferredNode.resolvePromise().then((t=>e.Revealer.reveal(t)))}}class F extends N{property;toggleOnClick;reasonElement;constructor(e,t){super(t),this.property=e,this.axNode=t,this.toggleOnClick=!0,this.selectable=!1}static createReasonElement(e,a){let r=null;switch(e){case"activeModalDialog":r=t.i18n.getFormatLocalizedString(w,E.elementIsHiddenBy,{});break;case"hiddenByChildTree":r=t.i18n.getFormatLocalizedString(w,E.elementIsHiddenByChildTree,{});break;case"ancestorIsLeafNode":r=t.i18n.getFormatLocalizedString(w,E.ancestorChildrenAreAll,{});break;case"ariaHiddenElement":{const e=document.createElement("span",{is:"source-code"}).textContent="aria-hidden";r=t.i18n.getFormatLocalizedString(w,E.elementIsPlaceholder,{PH1:e});break}case"ariaHiddenSubtree":{const e=document.createElement("span",{is:"source-code"}).textContent="aria-hidden",a=document.createElement("span",{is:"source-code"}).textContent="true";r=t.i18n.getFormatLocalizedString(w,E.placeholderIsPlaceholderOnAncestor,{PH1:e,PH2:a});break}case"emptyAlt":r=t.i18n.getFormatLocalizedString(w,E.elementHasEmptyAltText,{});break;case"emptyText":r=t.i18n.getFormatLocalizedString(w,E.noTextContent,{});break;case"inertElement":r=t.i18n.getFormatLocalizedString(w,E.elementIsInert,{});break;case"inertSubtree":r=t.i18n.getFormatLocalizedString(w,E.elementIsInAnInertSubTree,{});break;case"inheritsPresentation":r=t.i18n.getFormatLocalizedString(w,E.elementsInheritsPresentational,{});break;case"labelContainer":r=t.i18n.getFormatLocalizedString(w,E.partOfLabelElement,{});break;case"labelFor":r=t.i18n.getFormatLocalizedString(w,E.labelFor,{});break;case"notRendered":r=t.i18n.getFormatLocalizedString(w,E.elementIsNotRendered,{});break;case"notVisible":r=t.i18n.getFormatLocalizedString(w,E.elementIsNotVisible,{});break;case"presentationalRole":{const e=a&&a.role()?.value||"",n=document.createElement("span",{is:"source-code"}).textContent="role="+e;r=t.i18n.getFormatLocalizedString(w,E.elementHasPlaceholder,{PH1:n});break}case"probablyPresentational":r=t.i18n.getFormatLocalizedString(w,E.elementIsPresentational,{});break;case"uninteresting":r=t.i18n.getFormatLocalizedString(w,E.elementNotInteresting,{})}return r&&r.classList.add("ax-reason"),r}onattach(){this.listItemElement.removeChildren(),this.reasonElement=F.createReasonElement(this.property.name,this.axNode),this.reasonElement&&this.listItemElement.appendChild(this.reasonElement);const e=this.property.value;"idref"===e.type&&this.appendRelatedNodeListValueElement(e)}}var R=Object.freeze({__proto__:null,AXNodeSubPane:I,AXNodePropertyTreeElement:N,TypeStyles:k,StringProperties:T,AXNodePropertyTreePropertyElement:C,AXValueSourceTreeElement:M,AXRelatedNodeSourceTreeElement:L,AXRelatedNodeElement:S,AXNodeIgnoredReasonTreeElement:F});const O={attributes:[{name:"aria-activedescendant",type:"IDREF"},{default:"false",name:"aria-atomic",type:"boolean"},{default:"none",enum:["inline","list","both","none"],name:"aria-autocomplete",type:"token"},{name:"aria-braillelabel",type:"string"},{name:"aria-brailleroledescription",type:"string"},{default:"false",name:"aria-busy",type:"boolean"},{default:"undefined",enum:["true","false","mixed","undefined"],name:"aria-checked",type:"token"},{name:"aria-colcount",type:"integer"},{name:"aria-colindex",type:"integer"},{name:"aria-colindextext",type:"string"},{name:"aria-colspan",type:"integer"},{name:"aria-controls",type:"IDREF_list"},{default:"false",enum:["page","step","location","date","time","true","false"],name:"aria-current",type:"token"},{name:"aria-describedby",type:"IDREF_list"},{name:"aria-description",type:"string"},{name:"aria-details",type:"IDREF"},{default:"false",name:"aria-disabled",type:"boolean"},{name:"aria-errormessage",type:"IDREF"},{default:"undefined",enum:["true","false","undefined"],name:"aria-expanded",type:"token"},{name:"aria-flowto",type:"IDREF_list"},{default:"false",enum:["false","true","menu","listbox","tree","grid","dialog"],name:"aria-haspopup",type:"token"},{default:"undefined",enum:["true","false","undefined"],name:"aria-hidden",type:"token"},{default:"false",enum:["grammar","false","spelling","true"],name:"aria-invalid",type:"token"},{name:"aria-keyshortcuts",type:"string"},{name:"aria-label",type:"string"},{name:"aria-labelledby",type:"IDREF_list"},{name:"aria-labeledby",type:"IDREF_list"},{name:"aria-level",type:"integer"},{default:"off",enum:["off","polite","assertive"],name:"aria-live",type:"token"},{default:"false",name:"aria-modal",type:"boolean"},{default:"false",name:"aria-multiline",type:"boolean"},{default:"false",name:"aria-multiselectable",type:"boolean"},{default:"undefined",enum:["horizontal","undefined","vertical"],name:"aria-orientation",type:"token"},{name:"aria-owns",type:"IDREF_list"},{name:"aria-placeholder",type:"string"},{name:"aria-posinset",type:"integer"},{default:"undefined",enum:["true","false","mixed","undefined"],name:"aria-pressed",type:"token"},{default:"false",name:"aria-readonly",type:"boolean"},{default:"additions text",enum:["additions","removals","text","all"],name:"aria-relevant",type:"token_list"},{default:"false",name:"aria-required",type:"boolean"},{name:"aria-roledescription",type:"string"},{name:"aria-rowcount",type:"integer"},{name:"aria-rowindex",type:"integer"},{name:"aria-rowindextext",type:"string"},{name:"aria-rowspan",type:"integer"},{default:"undefined",enum:["true","false","undefined"],name:"aria-selected",type:"token"},{name:"aria-setsize",type:"integer"},{default:"none",enum:["ascending","descending","none","other"],name:"aria-sort",type:"token"},{name:"aria-valuemax",type:"decimal"},{name:"aria-valuemin",type:"decimal"},{name:"aria-valuenow",type:"decimal"},{name:"aria-valuetext",type:"string"},{name:"aria-virtualcontent",type:"string"}],metadata:{attrsNullNamespace:!0,export:"CORE_EXPORT",namespace:"HTML",namespacePrefix:"xhtml",namespaceURI:"http://www.w3.org/1999/xhtml"},roles:[{implicitValues:{"aria-atomic":"true","aria-live":"assertive"},name:"alert",nameFrom:["author"],superclasses:["section"]},{name:"alertdialog",nameFrom:["author"],nameRequired:!0,superclasses:["alert","dialog"]},{name:"application",nameFrom:["author"],nameRequired:!0,superclasses:["structure"]},{name:"article",nameFrom:["author"],superclasses:["document"],supportedAttributes:["aria-posinset","aria-setsize"]},{name:"banner",nameFrom:["author"],superclasses:["landmark"]},{childrenPresentational:!0,name:"button",nameFrom:["contents","author"],nameRequired:!0,superclasses:["command"],supportedAttributes:["aria-expanded","aria-pressed"]},{name:"cell",namefrom:["contents","author"],scope:"row",superclasses:["section"],supportedAttributes:["aria-colindex","aria-colspan","aria-rowindex","aria-rowspan"]},{implicitValues:{"aria-checked":!1},name:"checkbox",nameFrom:["contents","author"],nameRequired:!0,requiredAttributes:["aria-checked"],superclasses:["input"],supportedAttributes:["aria-readonly"]},{name:"columnheader",nameFrom:["contents","author"],nameRequired:!0,scope:["row"],superclasses:["gridcell","sectionhead","widget"],supportedAttributes:["aria-sort"]},{implicitValues:{"aria-expanded":"false","aria-haspopup":"listbox"},mustContain:["textbox"],name:"combobox",nameFrom:["author"],nameRequired:!0,requiredAttributes:["aria-controls","aria-expanded"],superclasses:["select"],supportedAttributes:["aria-autocomplete","aria-readonly","aria-required"]},{abstract:!0,name:"command",nameFrom:["author"],superclasses:["widget"]},{name:"complementary",nameFrom:["author"],superclasses:["landmark"]},{abstract:!0,name:"composite",nameFrom:["author"],superclasses:["widget"],supportedAttributes:["aria-activedescendant"]},{name:"contentinfo",nameFrom:["author"],superclasses:["landmark"]},{name:"definition",nameFrom:["author"],superclasses:["section"]},{name:"dialog",nameFrom:["author"],nameRequired:!0,superclasses:["window"]},{name:"directory",nameFrom:["author"],superclasses:["list"]},{name:"document",nameFrom:["author"],nameRequired:!1,superclasses:["structure"],supportedAttributes:["aria-expanded"]},{mustContain:["article"],name:"feed",nameFrom:["author"],nameRequired:!1,superclasses:["list"]},{name:"figure",nameRequired:!1,namefrom:["author"],superclasses:["section"]},{name:"form",nameFrom:["author"],superclasses:["landmark"]},{mustContain:["row"],name:"grid",nameFrom:["author"],nameRequired:!0,superclasses:["composite","table"],supportedAttributes:["aria-level","aria-multiselectable","aria-readonly"]},{name:"gridcell",nameFrom:["contents","author"],nameRequired:!0,scope:["row"],superclasses:["cell","widget"],supportedAttributes:["aria-readonly","aria-required","aria-selected"]},{name:"group",nameFrom:["author"],superclasses:["section"],supportedAttributes:["aria-activedescendant"]},{implicitValues:{"aria-level":"2"},name:"heading",nameRequired:!0,namefrom:["contents","author"],superclasses:["sectionhead"],supportedAttributes:["aria-level"]},{childrenPresentational:!0,name:"img",nameFrom:["author"],nameRequired:!0,superclasses:["section"]},{abstract:!0,name:"input",nameFrom:["author"],superclasses:["widget"]},{abstract:!0,name:"landmark",nameFrom:["author"],nameRequired:!1,superclasses:["section"]},{name:"link",nameFrom:["contents","author"],nameRequired:!0,superclasses:["command"],supportedAttributes:["aria-expanded"]},{implicitValues:{"aria-orientation":"vertical"},mustContain:["listitem"],name:"list",nameFrom:["author"],superclasses:["section"]},{implicitValues:{"aria-orientation":"vertical"},mustContain:["option"],name:"listbox",nameFrom:["author"],nameRequired:!0,superclasses:["select"],supportedAttributes:["aria-multiselectable","aria-readonly","aria-required"]},{name:"listitem",nameFrom:["author"],scope:["group","list"],superclasses:["section"],supportedAttributes:["aria-level","aria-posinset","aria-setsize"]},{implicitValues:{"aria-live":"polite"},name:"log",nameFrom:["author"],nameRequired:!0,superclasses:["section"]},{name:"main",nameFrom:["author"],superclasses:["landmark"]},{name:"marquee",nameFrom:["author"],nameRequired:!0,superclasses:["section"]},{childrenPresentational:!0,name:"math",nameFrom:["author"],nameRequired:!0,superclasses:["section"]},{implicitValues:{"aria-orientation":"vertical"},mustContain:["group","menuitemradio","menuitem","menuitemcheckbox","menuitemradio"],name:"menu",nameFrom:["author"],superclasses:["select"]},{implicitValues:{"aria-orientation":"horizontal"},mustContain:["menuitem","menuitemradio","menuitemcheckbox"],name:"menubar",nameFrom:["author"],superclasses:["menu"]},{name:"menuitem",nameFrom:["contents","author"],nameRequired:!0,scope:["group","menu","menubar"],superclasses:["command"]},{childrenPresentational:!0,implicitValues:{"aria-checked":!1},name:"menuitemcheckbox",nameFrom:["contents","author"],nameRequired:!0,scope:["menu","menubar"],superclasses:["checkbox","menuitem"]},{childrenPresentational:!0,implicitValues:{"aria-checked":!1},name:"menuitemradio",nameFrom:["contents","author"],nameRequired:!0,scope:["menu","menubar","group"],superclasses:["menuitemcheckbox","radio"]},{name:"navigation",nameFrom:["author"],superclasses:["landmark"]},{name:"none",superclasses:["structure"]},{name:"note",nameFrom:["author"],superclasses:["section"]},{childrenPresentational:!0,implicitValues:{"aria-selected":"false"},name:"option",nameFrom:["contents","author"],nameRequired:!0,requiredAttributes:["aria-selected"],scope:["listbox"],superclasses:["input"],supportedAttributes:["aria-checked","aria-posinset","aria-setsize"]},{name:"presentation",superclasses:["structure"]},{childrenPresentational:!0,name:"progressbar",nameFrom:["author"],nameRequired:!0,superclasses:["range"]},{childrenPresentational:!0,implicitValues:{"aria-checked":"false"},name:"radio",nameFrom:["contents","author"],nameRequired:!0,requiredAttributes:["aria-checked"],superclasses:["input"],supportedAttributes:["aria-posinset","aria-setsize"]},{mustContain:["radio"],name:"radiogroup",nameFrom:["author"],nameRequired:!0,superclasses:["select"],supportedAttributes:["aria-readonly","aria-required"]},{abstract:!0,name:"range",nameFrom:["author"],superclasses:["widget"],supportedAttributes:["aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"]},{name:"region",nameFrom:["author"],nameRequired:!0,superclasses:["landmark"]},{abstract:!0,name:"roletype",supportedAttributes:["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-dropeffect","aria-errormessage","aria-flowto","aria-grabbed","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"]},{mustContain:["cell","columnheader","gridcell","rowheader"],name:"row",nameFrom:["contents","author"],scope:["grid","rowgroup","table","treegrid"],superclasses:["group","widget"],supportedAttributes:["aria-colindex","aria-level","aria-rowindex","aria-selected","aria-setsize","aria-posinset"]},{mustContain:["row"],name:"rowgroup",nameFrom:["contents","author"],scope:["grid","table","treegrid"],superclasses:["structure"]},{name:"rowheader",nameFrom:["contents","author"],nameRequired:!0,scope:["row"],superclasses:["cell","gridcell","sectionhead"],supportedAttributes:["aria-sort"]},{childrenPresentational:!0,implicitValues:{"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},name:"scrollbar",nameFrom:["author"],nameRequired:!1,requiredAttributes:["aria-controls","aria-orientation","aria-valuemax","aria-valuemin","aria-valuenow"],superclasses:["range"]},{name:"search",nameFrom:["author"],superclasses:["landmark"]},{name:"searchbox",nameFrom:["author"],nameRequired:!0,superclasses:["textbox"]},{abstract:!0,name:"section",superclasses:["structure"],supportedAttributes:["aria-expanded"]},{abstract:!0,name:"sectionhead",nameFrom:["contents","author"],superclasses:["structure"],supportedAttributes:["aria-expanded"]},{abstract:!0,name:"select",nameFrom:["author"],superclasses:["composite","group"]},{name:"separator",nameFrom:["author"],superclasses:["structure"],supportedAttributes:["aria-orientation","aria-valuemin","aria-valuemax","aria-valuenow","aria-valuetext"]},{childrenPresentational:!0,implicitValues:{"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},name:"slider",nameFrom:["author"],nameRequired:!0,requiredAttributes:["aria-valuemax","aria-valuemin","aria-valuenow"],superclasses:["input","range"],supportedAttributes:["aria-orientation"]},{implicitValues:{"aria-valuenow":"0"},name:"spinbutton",nameFrom:["author"],nameRequired:!0,requiredAttributes:["aria-valuemax","aria-valuemin","aria-valuenow"],superclasses:["composite","input","range"],supportedAttributes:["aria-required","aria-readonly"]},{implicitValues:{"aria-atomic":"true","aria-live":"polite"},name:"status",nameFrom:["author"],superclasses:["section"]},{abstract:!0,name:"structure",superclasses:["roletype"]},{childrenPresentational:!0,implicitValues:{"aria-checked":"false"},name:"switch",nameFrom:["contents","author"],nameRequired:!0,requiredAttributes:["aria-checked"],superclasses:["checkbox"]},{childrenPresentational:!0,implicitValues:{"aria-selected":"false"},name:"tab",nameFrom:["contents","author"],scope:["tablist"],superclasses:["sectionhead","widget"],supportedAttributes:["aria-selected"]},{mustContain:["row"],name:"table",nameFrom:["author"],nameRequired:!0,superclasses:["section"],supportedAttributes:["aria-colcount","aria-rowcount"]},{implicitValues:{"aria-orientation":"horizontal"},mustContain:["tab"],name:"tablist",nameFrom:["author"],superclasses:["composite"],supportedAttributes:["aria-level","aria-multiselectable","aria-orientation"]},{name:"tabpanel",nameFrom:["author"],nameRequired:!0,superclasses:["section"]},{name:"term",nameFrom:["author"],superclasses:["section"]},{name:"textbox",nameFrom:["author"],nameRequired:!0,superclasses:["input"],supportedAttributes:["aria-activedescendant","aria-autocomplete","aria-multiline","aria-placeholder","aria-readonly","aria-required"]},{name:"timer",nameFrom:["author"],superclasses:["status"]},{implicitValues:{"aria-orientation":"horizontal"},name:"toolbar",nameFrom:["author"],superclasses:["group"],supportedAttributes:["aria-orientation"]},{name:"tooltip",nameFrom:["contents","author"],nameRequired:!0,superclasses:["section"]},{implicitValues:{"aria-orientation":"vertical"},mustContain:["group","treeitem"],name:"tree",nameFrom:["author"],nameRequired:!0,superclasses:["select"],supportedAttributes:["aria-multiselectable","aria-required"]},{mustContain:["row"],name:"treegrid",nameFrom:["author"],nameRequired:!0,superclasses:["grid","tree"]},{name:"treeitem",nameFrom:["contents","author"],nameRequired:!0,scope:["group","tree"],superclasses:["listitem","option"]},{abstract:!0,name:"widget",superclasses:["roletype"]},{abstract:!0,name:"window",nameFrom:["author"],superclasses:["roletype"],supportedAttributes:["aria-expanded","aria-modal"]}]};class P{attributes;roleNames;constructor(e){this.attributes=new Map,this.roleNames=[],e&&this.initialize(e)}initialize(e){const t=e.attributes,a=["true","false"];for(const e of t)"boolean"===e.type&&(e.enum=a),this.attributes.set(e.name,new B(e));this.roleNames=e.roles.map((e=>e.name))}valuesForProperty(e){const t=this.attributes.get(e);return t?t.getEnum():"role"===e?this.roleNames:[]}}let D;function V(){return D||(D=new P(O||null)),D}class B{enum;constructor(e){this.enum=[],e.enum&&(this.enum=e.enum)}getEnum(){return this.enum}}var W=Object.freeze({__proto__:null,ARIAMetadata:P,ariaMetadata:V,Attribute:B});const q={ariaAttributes:"ARIA Attributes",noAriaAttributes:"No ARIA attributes"},H=t.i18n.registerUIStrings("panels/accessibility/ARIAAttributesView.ts",q),j=t.i18n.getLocalizedString.bind(void 0,H);class U extends x{noPropertiesInfo;treeOutline;constructor(){super(j(q.ariaAttributes)),this.noPropertiesInfo=this.createInfo(j(q.noAriaAttributes)),this.treeOutline=this.createTreeOutline(),this.element.setAttribute("jslog",`${n.section("aria-attributes")}`)}setNode(e){if(super.setNode(e),this.treeOutline.removeChildren(),!e)return;const t=e.domModel().target(),a=e.attributes();for(let e=0;e<a.length;++e){const r=a[e];this.isARIAAttribute(r)&&this.treeOutline.appendChild(new X(this,r,t))}const r=0!==this.treeOutline.rootElement().childCount();this.noPropertiesInfo.classList.toggle("hidden",r),this.treeOutline.element.classList.toggle("hidden",!r)}isARIAAttribute(e){return _.has(e.name)}}class X extends r.TreeOutline.TreeElement{parentPane;attribute;nameElement;valueElement;prompt;constructor(e,t,a){super(""),this.parentPane=e,this.attribute=t,this.selectable=!1}static createARIAValueElement(e){const t=document.createElement("span");return t.classList.add("monospace"),t.setTextContentTruncatedIfNeeded(e||""),t}onattach(){this.populateListItem(),this.listItemElement.addEventListener("click",this.mouseClick.bind(this))}populateListItem(){this.listItemElement.removeChildren(),this.appendNameElement(this.attribute.name),this.listItemElement.createChild("span","separator").textContent=": ",this.appendAttributeValueElement(this.attribute.value)}appendNameElement(e){this.nameElement=document.createElement("span"),this.nameElement.textContent=e,this.nameElement.classList.add("ax-name"),this.nameElement.classList.add("monospace"),this.listItemElement.appendChild(this.nameElement)}appendAttributeValueElement(e){this.valueElement=X.createARIAValueElement(e),this.listItemElement.appendChild(this.valueElement)}mouseClick(e){e.target!==this.listItemElement&&(e.consume(!0),this.startEditing())}startEditing(){const e=this.valueElement;if(!e||r.UIUtils.isBeingEdited(e))return;const t=e.textContent||"";const a=this.nameElement.textContent||"";this.prompt=new z(V().valuesForProperty(a),this),this.prompt.setAutocompletionTimeout(0);const n=this.prompt.attachAndStartEditing(e,function(e,t){const a=t.target.textContent||"";this.editingCommitted(a,e)}.bind(this,t));n.addEventListener("keydown",(e=>this.editingValueKeyDown(t,e)),!1);const i=e.getComponentSelection();i&&i.selectAllChildren(e)}removePrompt(){this.prompt&&(this.prompt.detach(),delete this.prompt)}editingCommitted(e,t){if(this.removePrompt(),e!==t){this.parentPane.node().setAttributeValue(this.attribute.name,e)}}editingCancelled(){this.removePrompt(),this.populateListItem()}editingValueKeyDown(e,t){if(!t.handled){if("Enter"===t.key){const a=t.target;return this.editingCommitted(a.textContent||"",e),void t.consume()}return s.KeyboardUtilities.isEscKey(t)?(this.editingCancelled(),void t.consume()):void 0}}}class z extends r.TextPrompt.TextPrompt{ariaCompletions;treeElement;constructor(e,t){super(),this.initialize(this.buildPropertyCompletions.bind(this)),this.ariaCompletions=e,this.treeElement=t}async buildPropertyCompletions(e,t,a){return(t=t.toLowerCase())||a||!e?this.ariaCompletions.filter((e=>e.startsWith(t))).map((e=>({text:e,title:void 0,subtitle:void 0,priority:void 0,isSecondary:void 0,subtitleRenderer:void 0,selectionRange:void 0,hideGhostText:void 0,iconElement:void 0}))):[]}}const _=new Set(["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-dropeffect","aria-errormessage","aria-expanded","aria-flowto","aria-grabbed","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"]);var K=Object.freeze({__proto__:null,ARIAAttributesPane:U,ARIAAttributesTreeElement:X,ARIAAttributePrompt:z});const G=new CSSStyleSheet;G.replaceSync('.ax-breadcrumbs-ignored-node{font-style:italic;opacity:70%}.ax-breadcrumbs{padding-top:1px;margin:0;position:relative}.ax-breadcrumbs .ax-node{align-items:center;margin-top:1px;min-height:16px;overflow-x:hidden;padding-left:4px;padding-right:4px;padding-top:1px;position:relative;text-overflow:ellipsis;white-space:nowrap}.ax-breadcrumbs .ax-node span{flex-shrink:0;text-overflow:ellipsis;white-space:nowrap}.ax-breadcrumbs .ax-node .wrapper{padding-left:12px;overflow-x:hidden}.ax-breadcrumbs .ax-node::before{mask-image:var(--image-file-chevron-right);mask-repeat:no-repeat;background-color:var(--icon-default);transform:scale(0.7);content:"";text-shadow:none;margin-left:-4px;margin-top:-2px;height:16px;width:16px;position:absolute;display:inline-block}.ax-breadcrumbs .ax-node:not(.parent):not(.children-unloaded)::before{background-color:transparent}.ax-breadcrumbs .ax-node.parent::before{mask-image:var(--image-file-chevron-down)}.ax-breadcrumbs .ax-node.no-dom-node{opacity:70%}.ax-breadcrumbs .ax-node.children-unloaded::before{mask-position:0 1px;width:13px;opacity:40%}.ax-breadcrumbs .ax-node .selection{display:none;z-index:-1}.ax-breadcrumbs .ax-node.inspected .selection{display:block;background-color:var(--sys-color-neutral-container)}.ax-breadcrumbs .ax-node.inspected:focus .selection{background-color:var(--sys-color-tonal-container)}.ax-breadcrumbs .ax-node.parent.inspected:focus::before{background-color:var(--sys-color-on-tonal-container)}.ax-breadcrumbs .ax-node.inspected:focus{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}.ax-breadcrumbs .ax-node:not(.inspected):hover{background-color:var(--sys-color-state-hover-on-subtle)}.ax-breadcrumbs .ax-node:not(.inspected):focus{background-color:var(--sys-color-state-focus-highlight)}.ax-breadcrumbs .ax-node.inspected:focus *{color:inherit}.ax-breadcrumbs .ax-node.preselected:not(.inspected) .selection,\n.ax-breadcrumbs .ax-node.hovered:not(.inspected) .selection{display:block;left:2px;right:2px;background-color:var(--sys-color-state-hover-on-subtle);border-radius:5px}.ax-breadcrumbs .ax-node.preselected:not(.inspected):focus .selection{border:1px solid var(--sys-color-primary)}@media (forced-colors: active){.ax-value-source-unused,\n  .ax-breadcrumbs .ax-node.children-unloaded::before{opacity:100%}.ax-breadcrumbs .ax-node.parent::before,\n  .ax-breadcrumbs .ax-node.children-unloaded::before{forced-color-adjust:none;background-color:ButtonText}.ax-breadcrumbs .ax-node.parent.inspected::before,\n  .ax-breadcrumbs .ax-node.parent.inspected:focus::before{background-color:HighlightText}.ax-breadcrumbs .ax-node.inspected .selection{forced-color-adjust:none;background:Highlight!important}.ax-breadcrumbs .ax-node.inspected .wrapper{forced-color-adjust:none;color:HighlightText}.ax-breadcrumbs .ax-node.preselected:not(.inspected) .selection,\n  .ax-breadcrumbs .ax-node.hovered:not(.inspected) .selection,\n  .ax-breadcrumbs .ax-node.hovered:not(.inspected) .wrapper,\n  .ax-breadcrumbs .ax-node:focus-visible:not(.inspected) .wrapper{forced-color-adjust:none;background-color:Highlight;color:HighlightText;border-radius:0}.ax-breadcrumbs .ax-node.parent.hovered:not(.inspected)::before,\n  .ax-breadcrumbs .ax-node.parent:focus-visible:not(.inspected)::before,\n  .ax-breadcrumbs .ax-node.children-unloaded:focus-visible:not(.inspected)::before,\n  .ax-breadcrumbs .ax-node.hovered:not(.inspected).children-unloaded::before{background-color:HighlightText}}\n/*# sourceURL=axBreadcrumbs.css */\n');const $={accessibilityTree:"Accessibility Tree",scrollIntoView:"Scroll into view",ignored:"Ignored",fullTreeExperimentName:"Enable full-page accessibility tree",fullTreeExperimentDescription:"The accessibility tree moved to the top right corner of the DOM tree.",reloadRequired:"Reload required before the change takes effect."},J=t.i18n.registerUIStrings("panels/accessibility/AXBreadcrumbsPane.ts",$),Q=t.i18n.getLocalizedString.bind(void 0,J);class Y extends x{axSidebarView;preselectedBreadcrumb;inspectedNodeBreadcrumb;collapsingBreadcrumbId;hoveredBreadcrumb;rootElement;#e=!1;constructor(e){super(Q($.accessibilityTree)),this.element.classList.add("ax-subpane"),this.element.tabIndex=-1,this.element.setAttribute("jslog",`${n.section("accessibility-tree")}`),this.axSidebarView=e,this.preselectedBreadcrumb=null,this.inspectedNodeBreadcrumb=null,this.collapsingBreadcrumbId=-1,this.rootElement=this.element.createChild("div","ax-breadcrumbs"),this.hoveredBreadcrumb=null;const t=new l.PreviewToggle.PreviewToggle;t.setAttribute("jslog",`${n.toggle("full-accessibility-tree")}`);const a=Q($.fullTreeExperimentName),s="full-accessibility-tree",d=e=>{o.userMetrics.experimentChanged(s,e),r.InspectorView.InspectorView.instance().displayReloadRequiredWarning(Q($.reloadRequired))};if(i.Runtime.experiments.isEnabled(s)){this.#e=!0;const e="https://g.co/devtools/a11y-tree-feedback";return t.data={name:a,helperText:Q($.fullTreeExperimentDescription),feedbackURL:e,experiment:s,onChangeCallback:d},void this.element.appendChild(t)}t.data={name:a,helperText:null,feedbackURL:null,experiment:s,onChangeCallback:d},this.element.prepend(t),r.ARIAUtils.markAsTree(this.rootElement),this.rootElement.addEventListener("keydown",this.onKeyDown.bind(this),!0),this.rootElement.addEventListener("mousemove",this.onMouseMove.bind(this),!1),this.rootElement.addEventListener("mouseleave",this.onMouseLeave.bind(this),!1),this.rootElement.addEventListener("click",this.onClick.bind(this),!1),this.rootElement.addEventListener("contextmenu",this.contextMenuEventFired.bind(this),!1),this.rootElement.addEventListener("focusout",this.onFocusOut.bind(this),!1)}focus(){this.inspectedNodeBreadcrumb?this.inspectedNodeBreadcrumb.nodeElement().focus():this.element.focus()}setAXNode(e){if(this.#e)return;const t=this.element.hasFocus();if(super.setAXNode(e),this.rootElement.removeChildren(),!e)return;const a=[];let r=e;for(;r;)a.push(r),r=r.parentNode();a.reverse();let n=0,i=null;for(r of(this.inspectedNodeBreadcrumb=null,a)){if(r!==e&&r.ignored()&&r.parentNode())continue;const t=new ee(r,n,r===e);i?i.appendChild(t):this.rootElement.appendChild(t.element()),i=t,n++,this.inspectedNodeBreadcrumb=t}function s(e,t,a){if(t.ignored())return void t.children().map((t=>s(e,t,a)));const r=new ee(t,a,!1);e.appendChild(r);for(const e of t.children())s(r,e,a+1)}if(this.inspectedNodeBreadcrumb&&this.inspectedNodeBreadcrumb.setPreselected(!0,t),this.setPreselectedBreadcrumb(this.inspectedNodeBreadcrumb),this.inspectedNodeBreadcrumb&&!e.ignored())for(const t of e.children())s(this.inspectedNodeBreadcrumb,t,n),t.backendDOMNodeId()===this.collapsingBreadcrumbId&&this.setPreselectedBreadcrumb(this.inspectedNodeBreadcrumb.lastChild());this.collapsingBreadcrumbId=-1}willHide(){this.setPreselectedBreadcrumb(null)}onKeyDown(e){const t=this.preselectedBreadcrumb;if(!t)return;const a=e;if(!a.composedPath().some((e=>e===t.element())))return;if(a.shiftKey||a.metaKey||a.ctrlKey)return;let r=!1;"ArrowUp"!==a.key||a.altKey?"ArrowDown"!==a.key||a.altKey?"ArrowLeft"!==a.key||a.altKey?("Enter"===a.key||"ArrowRight"===a.key&&!a.altKey&&t.axNode().hasOnlyUnloadedChildren())&&(r=this.inspectDOMNode(t.axNode())):t.hasExpandedChildren()?this.collapseBreadcrumb(t):r=this.preselectParent():r=this.preselectNext():r=this.preselectPrevious(),r&&a.consume(!0)}preselectPrevious(){if(!this.preselectedBreadcrumb)return!1;const e=this.preselectedBreadcrumb.previousBreadcrumb();return!!e&&(this.setPreselectedBreadcrumb(e),!0)}preselectNext(){if(!this.preselectedBreadcrumb)return!1;const e=this.preselectedBreadcrumb.nextBreadcrumb();return!!e&&(this.setPreselectedBreadcrumb(e),!0)}preselectParent(){if(!this.preselectedBreadcrumb)return!1;const e=this.preselectedBreadcrumb.parentBreadcrumb();return!!e&&(this.setPreselectedBreadcrumb(e),!0)}setPreselectedBreadcrumb(e){if(e===this.preselectedBreadcrumb)return;const t=this.element.hasFocus();this.preselectedBreadcrumb&&this.preselectedBreadcrumb.setPreselected(!1,t),this.preselectedBreadcrumb=e||this.inspectedNodeBreadcrumb,this.preselectedBreadcrumb&&this.preselectedBreadcrumb.setPreselected(!0,t),!e&&t&&a.OverlayModel.OverlayModel.hideDOMNodeHighlight()}collapseBreadcrumb(e){if(!e.parentBreadcrumb())return;const t=e.axNode().backendDOMNodeId();null!==t&&(this.collapsingBreadcrumbId=t);const a=e.parentBreadcrumb();a&&this.inspectDOMNode(a.axNode())}onMouseLeave(e){this.setHoveredBreadcrumb(null)}onMouseMove(e){const t=e.target;if(!t)return;const a=t.enclosingNodeOrSelfWithClass("ax-breadcrumb");if(!a)return void this.setHoveredBreadcrumb(null);const r=Z.get(a);r&&r.isDOMNode()&&this.setHoveredBreadcrumb(r)}onFocusOut(e){this.preselectedBreadcrumb&&e.target===this.preselectedBreadcrumb.nodeElement()&&this.setPreselectedBreadcrumb(null)}onClick(e){const t=e.target;if(!t)return;const a=t.enclosingNodeOrSelfWithClass("ax-breadcrumb");if(!a)return void this.setHoveredBreadcrumb(null);const r=Z.get(a);return r?r.inspected()?(this.collapseBreadcrumb(r),r.nodeElement().focus(),void n.logClick(r.expandLoggable,e)):void(r.isDOMNode()&&(this.inspectDOMNode(r.axNode()),n.logClick(r.expandLoggable,e))):void 0}setHoveredBreadcrumb(e){if(e===this.hoveredBreadcrumb)return;this.hoveredBreadcrumb&&this.hoveredBreadcrumb.setHovered(!1);const t=this.node();e?e.setHovered(!0):t&&t.id&&t.domModel().overlayModel().nodeHighlightRequested({nodeId:t.id}),this.hoveredBreadcrumb=e}inspectDOMNode(t){if(!t.isDOMNode())return!1;const a=t.deferredDOMNode();return a&&a.resolve((t=>{this.axSidebarView.setNode(t,!0),e.Revealer.reveal(t,!0)})),!0}contextMenuEventFired(e){const t=e.target;if(!t)return;const a=t.enclosingNodeOrSelfWithClass("ax-breadcrumb");if(!a)return;const n=Z.get(a);if(!n)return;const i=n.axNode();if(!i.isDOMNode()||!i.deferredDOMNode())return;const s=new r.ContextMenu.ContextMenu(e);s.viewSection().appendItem(Q($.scrollIntoView),(()=>{const e=i.deferredDOMNode();e&&e.resolvePromise().then((e=>{e&&e.scrollIntoView()}))}),{jslogContext:"scroll-into-view"});const o=i.deferredDOMNode();o&&s.appendApplicableItems(o),s.show()}wasShown(){super.wasShown(),this.registerCSSFiles([G])}}const Z=new WeakMap;class ee{axNodeInternal;elementInternal;nodeElementInternal;nodeWrapper;selectionElement;childrenGroupElement;children;hovered;preselectedInternal;parent;inspectedInternal;expandLoggable={};constructor(e,t,a){if(this.axNodeInternal=e,this.elementInternal=document.createElement("div"),this.elementInternal.classList.add("ax-breadcrumb"),this.elementInternal.setAttribute("jslog",`${n.treeItem().track({click:!0,keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|Enter"})}`),Z.set(this.elementInternal,this),this.nodeElementInternal=document.createElement("div"),this.nodeElementInternal.classList.add("ax-node"),r.ARIAUtils.markAsTreeitem(this.nodeElementInternal),this.nodeElementInternal.tabIndex=-1,this.elementInternal.appendChild(this.nodeElementInternal),this.nodeWrapper=document.createElement("div"),this.nodeWrapper.classList.add("wrapper"),this.nodeElementInternal.appendChild(this.nodeWrapper),this.selectionElement=document.createElement("div"),this.selectionElement.classList.add("selection"),this.selectionElement.classList.add("fill"),this.nodeElementInternal.appendChild(this.selectionElement),this.childrenGroupElement=document.createElement("div"),this.childrenGroupElement.classList.add("children"),r.ARIAUtils.markAsGroup(this.childrenGroupElement),this.elementInternal.appendChild(this.childrenGroupElement),this.children=[],this.hovered=!1,this.preselectedInternal=!1,this.parent=null,this.inspectedInternal=a,this.nodeElementInternal.classList.toggle("inspected",a),this.nodeElementInternal.style.paddingLeft=16*t+4+"px",this.axNodeInternal.ignored())this.appendIgnoredNodeElement();else{this.appendRoleElement(this.axNodeInternal.role());const e=this.axNodeInternal.name();e&&e.value&&(this.nodeWrapper.createChild("span","separator").textContent=" ",this.appendNameElement(e.value))}!this.axNodeInternal.ignored()&&this.axNodeInternal.hasOnlyUnloadedChildren()&&(this.nodeElementInternal.classList.add("children-unloaded"),r.ARIAUtils.setExpanded(this.nodeElementInternal,!1),n.registerLoggable(this.expandLoggable,`${n.expand()}`,this.elementInternal)),this.axNodeInternal.isDOMNode()||this.nodeElementInternal.classList.add("no-dom-node")}element(){return this.elementInternal}nodeElement(){return this.nodeElementInternal}appendChild(e){this.children.push(e),e.setParent(this),this.nodeElementInternal.classList.add("parent"),r.ARIAUtils.setExpanded(this.nodeElementInternal,!0),this.childrenGroupElement.appendChild(e.element()),n.registerLoggable(this.expandLoggable,`${n.expand()}`,this.elementInternal)}hasExpandedChildren(){return this.children.length}setParent(e){this.parent=e}preselected(){return this.preselectedInternal}setPreselected(e,t){this.preselectedInternal!==e&&(this.preselectedInternal=e,this.nodeElementInternal.classList.toggle("preselected",e),this.nodeElementInternal.tabIndex=e?0:-1,this.preselectedInternal&&(t&&this.nodeElementInternal.focus(),this.inspectedInternal?a.OverlayModel.OverlayModel.hideDOMNodeHighlight():this.axNodeInternal.highlightDOMNode()))}setHovered(e){this.hovered!==e&&(this.hovered=e,this.nodeElementInternal.classList.toggle("hovered",e),this.hovered&&(this.nodeElementInternal.classList.toggle("hovered",!0),this.axNodeInternal.highlightDOMNode()))}axNode(){return this.axNodeInternal}inspected(){return this.inspectedInternal}isDOMNode(){return this.axNodeInternal.isDOMNode()}nextBreadcrumb(){if(this.children.length)return this.children[0];const e=this.element().nextSibling;return e&&Z.get(e)||null}previousBreadcrumb(){const e=this.element().previousSibling;return e?Z.get(e)||null:this.parent}parentBreadcrumb(){return this.parent}lastChild(){return this.children[this.children.length-1]}appendNameElement(e){const t=document.createElement("span");t.textContent='"'+e+'"',t.classList.add("ax-readable-string"),this.nodeWrapper.appendChild(t)}appendRoleElement(e){if(!e)return;const t=document.createElement("span");t.classList.add("monospace"),t.classList.add(te[e.type]),t.setTextContentTruncatedIfNeeded(e.value||""),this.nodeWrapper.appendChild(t)}appendIgnoredNodeElement(){const e=document.createElement("span");e.classList.add("monospace"),e.textContent=Q($.ignored),e.classList.add("ax-breadcrumbs-ignored-node"),this.nodeWrapper.appendChild(e)}}const te={internalRole:"ax-internal-role",role:"ax-role"};var ae=Object.freeze({__proto__:null,AXBreadcrumbsPane:Y,AXBreadcrumb:ee,RoleStyles:te});const re={sourceOrderViewer:"Source Order Viewer",noSourceOrderInformation:"No source order information available",thereMayBeADelayInDisplaying:"There may be a delay in displaying source order for elements with many children",showSourceOrder:"Show source order"},ne=t.i18n.registerUIStrings("panels/accessibility/SourceOrderView.ts",re),ie=t.i18n.getLocalizedString.bind(void 0,ne);class se extends x{noNodeInfo;warning;checked;checkboxLabel;checkboxElement;overlayModel;constructor(){super(ie(re.sourceOrderViewer)),this.element.setAttribute("jslog",`${n.section("source-order-viewer")}`),this.noNodeInfo=this.createInfo(ie(re.noSourceOrderInformation)),this.warning=this.createInfo(ie(re.thereMayBeADelayInDisplaying)),this.warning.id="source-order-warning",this.checked=!1,this.checkboxLabel=r.UIUtils.CheckboxLabel.create(ie(re.showSourceOrder),!1),this.checkboxElement=this.checkboxLabel.checkboxElement,this.checkboxLabel.classList.add("source-order-checkbox"),this.checkboxLabel.setAttribute("jslog",`${n.toggle().track({click:!0})}`),this.checkboxElement.addEventListener("click",this.checkboxClicked.bind(this),!1),this.element.appendChild(this.checkboxLabel),this.nodeInternal=null,this.overlayModel=null}async setNodeAsync(e){if(this.checkboxLabel.classList.contains("hidden")||(this.checked=this.checkboxElement.checked),this.checkboxElement.checked=!1,this.checkboxClicked(),super.setNode(e),!this.nodeInternal)return void(this.overlayModel=null);let t=!1;const a=this.nodeInternal.childNodeCount();if(a>0){this.nodeInternal.children()||await this.nodeInternal.getSubtree(1,!1);t=this.nodeInternal.children().some((e=>e.nodeType()===Node.ELEMENT_NODE))}this.noNodeInfo.classList.toggle("hidden",t),this.warning.classList.toggle("hidden",a<300),this.checkboxLabel.classList.toggle("hidden",!t),t?(this.overlayModel=this.nodeInternal.domModel().overlayModel(),this.checkboxElement.checked=this.checked,this.checkboxClicked()):this.overlayModel=null}checkboxClicked(){this.nodeInternal&&this.overlayModel&&(this.checkboxElement.checked?(o.userMetrics.actionTaken(o.UserMetrics.Action.SourceOrderViewActivated),this.overlayModel.highlightSourceOrderInOverlay(this.nodeInternal)):this.overlayModel.hideSourceOrderInOverlay())}}let oe;class le extends r.ThrottledWidget.ThrottledWidget{nodeInternal;axNodeInternal;skipNextPullNode;sidebarPaneStack;breadcrumbsSubPane=null;ariaSubPane;axNodeSubPane;sourceOrderSubPane;constructor(e){super(!1,e),this.nodeInternal=null,this.axNodeInternal=null,this.skipNextPullNode=!1,this.sidebarPaneStack=r.ViewManager.ViewManager.instance().createStackLocation(),this.breadcrumbsSubPane=new Y(this),this.sidebarPaneStack.showView(this.breadcrumbsSubPane),this.ariaSubPane=new U,this.sidebarPaneStack.showView(this.ariaSubPane),this.axNodeSubPane=new I,this.sidebarPaneStack.showView(this.axNodeSubPane),this.sourceOrderSubPane=new se,this.sidebarPaneStack.showView(this.sourceOrderSubPane),this.sidebarPaneStack.widget().show(this.element),r.Context.Context.instance().addFlavorChangeListener(a.DOMModel.DOMNode,this.pullNode,this),this.pullNode()}static instance(e){return oe&&!e?.forceNew||(oe=new le(e?.throttlingTimeout)),oe}node(){return this.nodeInternal}axNode(){return this.axNodeInternal}setNode(e,t){this.skipNextPullNode=Boolean(t),this.nodeInternal=e,this.update()}accessibilityNodeCallback(e){e&&(this.axNodeInternal=e,e.isDOMNode()?this.sidebarPaneStack.showView(this.ariaSubPane,this.axNodeSubPane):this.sidebarPaneStack.removeView(this.ariaSubPane),this.axNodeSubPane&&this.axNodeSubPane.setAXNode(e),this.breadcrumbsSubPane&&this.breadcrumbsSubPane.setAXNode(e))}async doUpdate(){const e=this.node();if(this.axNodeSubPane.setNode(e),this.ariaSubPane.setNode(e),this.breadcrumbsSubPane&&this.breadcrumbsSubPane.setNode(e),this.sourceOrderSubPane.setNodeAsync(e),!e)return;const t=e.domModel().target().model(a.AccessibilityModel.AccessibilityModel);t&&(i.Runtime.experiments.isEnabled("full-accessibility-tree")||t.clear(),await t.requestPartialAXTree(e),this.accessibilityNodeCallback(t.axNodeForDOMNode(e)))}wasShown(){super.wasShown(),this.doUpdate(),a.TargetManager.TargetManager.instance().addModelListener(a.DOMModel.DOMModel,a.DOMModel.Events.AttrModified,this.onNodeChange,this,{scoped:!0}),a.TargetManager.TargetManager.instance().addModelListener(a.DOMModel.DOMModel,a.DOMModel.Events.AttrRemoved,this.onNodeChange,this,{scoped:!0}),a.TargetManager.TargetManager.instance().addModelListener(a.DOMModel.DOMModel,a.DOMModel.Events.CharacterDataModified,this.onNodeChange,this,{scoped:!0}),a.TargetManager.TargetManager.instance().addModelListener(a.DOMModel.DOMModel,a.DOMModel.Events.ChildNodeCountUpdated,this.onNodeChange,this,{scoped:!0})}willHide(){a.TargetManager.TargetManager.instance().removeModelListener(a.DOMModel.DOMModel,a.DOMModel.Events.AttrModified,this.onNodeChange,this),a.TargetManager.TargetManager.instance().removeModelListener(a.DOMModel.DOMModel,a.DOMModel.Events.AttrRemoved,this.onNodeChange,this),a.TargetManager.TargetManager.instance().removeModelListener(a.DOMModel.DOMModel,a.DOMModel.Events.CharacterDataModified,this.onNodeChange,this),a.TargetManager.TargetManager.instance().removeModelListener(a.DOMModel.DOMModel,a.DOMModel.Events.ChildNodeCountUpdated,this.onNodeChange,this)}pullNode(){this.skipNextPullNode?this.skipNextPullNode=!1:this.setNode(r.Context.Context.instance().flavor(a.DOMModel.DOMNode))}onNodeChange(e){if(!this.node())return;const t=e.data,r=t instanceof a.DOMModel.DOMNode?t:t.node;this.node()===r&&this.update()}}var de=Object.freeze({__proto__:null,AccessibilitySidebarView:le});export{K as ARIAAttributesView,W as ARIAMetadata,ae as AXBreadcrumbsPane,R as AccessibilityNodeView,de as AccessibilitySidebarView,g as AccessibilityStrings,y as AccessibilitySubPane};
