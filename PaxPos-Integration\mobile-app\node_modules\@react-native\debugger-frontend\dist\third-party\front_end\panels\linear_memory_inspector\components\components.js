import*as e from"../../../core/i18n/i18n.js";import*as t from"../../../ui/components/icon_button/icon_button.js";import*as o from"../../../ui/lit-html/lit-html.js";import*as r from"../../../ui/visual_logging/visual_logging.js";import*as s from"../../../core/common/common.js";import*as n from"../../../core/platform/platform.js";import*as i from"../../../ui/components/input/input.js";const a=new CSSStyleSheet;a.replaceSync(".highlight-chip-list{min-height:20px;display:flex;flex-wrap:wrap;justify-content:left;align-items:center;background-color:var(--sys-color-cdt-base-container);margin:8px 0;gap:8px;row-gap:6px}.highlight-chip{background:var(--sys-color-cdt-base-container);border:1px solid var(--sys-color-divider);height:18px;border-radius:4px;flex:0 0 auto;max-width:250px;position:relative;padding:0 6px}.highlight-chip:hover{background-color:var(--sys-color-state-hover-on-subtle)}.delete-highlight-container{display:none;height:100%;position:absolute;right:0;top:0;border-radius:4px;width:24px;align-items:center;justify-content:center}.delete-highlight-button{cursor:pointer;width:13px;height:13px;border:none;background-color:transparent;display:flex;align-items:center;justify-content:center}.delete-highlight-button:hover{background-color:var(--sys-color-state-hover-on-subtle);border-radius:50%}.jump-to-highlight-button{cursor:pointer;padding:0;border:none;background:none;height:100%;align-items:center;max-width:100%;overflow:hidden}.delete-highlight-button devtools-icon{width:13px;height:13px;display:flex;align-items:center;justify-content:center;border-radius:50%}.source-code{font-family:var(--source-code-font-family);font-size:var(--source-code-font-size);overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:var(--sys-color-on-surface)}.value{color:var(--sys-color-token-tag)}.separator{white-space:pre;flex-shrink:0}.highlight-chip.focused{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px}.highlight-chip:hover > .delete-highlight-container{display:flex;background:linear-gradient(90deg,transparent 0%,rgb(241 243 244) 25%)}.highlight-chip.focused:hover > .delete-highlight-container{background:linear-gradient(90deg,transparent 0%,rgb(231 241 253) 25%)}:host-context(.theme-with-dark-background) .highlight-chip:hover > .delete-highlight-container{display:flex;background:linear-gradient(90deg,transparent 0%,rgb(41 42 45) 25%)}:host-context(.theme-with-dark-background) .highlight-chip.focused:hover > .delete-highlight-container{background:linear-gradient(90deg,transparent 0%,rgb(48 55 68) 25%)}\n/*# sourceURL=linearMemoryHighlightChipList.css */\n");const l={jumpToAddress:"Jump to this memory",deleteHighlight:"Stop highlighting this memory"},d=e.i18n.registerUIStrings("panels/linear_memory_inspector/components/LinearMemoryHighlightChipList.ts",l),c=e.i18n.getLocalizedString.bind(void 0,d),{render:h,html:g}=o;class p extends Event{static eventName="deletememoryhighlight";data;constructor(e){super(p.eventName,{bubbles:!0,composed:!0}),this.data=e}}class u extends Event{static eventName="jumptohighlightedmemory";data;constructor(e){super(u.eventName),this.data=e}}class m extends HTMLElement{static litTagName=o.literal`devtools-linear-memory-highlight-chip-list`;#e=this.attachShadow({mode:"open"});#t=[];#o;connectedCallback(){this.#e.adoptedStyleSheets=[a]}set data(e){this.#t=e.highlightInfos,this.#o=e.focusedMemoryHighlight,this.#r()}#r(){const e=[];for(const t of this.#t)e.push(this.#s(t));const t=g`
            <div class="highlight-chip-list">
              ${e}
            </div>
        `;h(t,this.#e,{host:this})}#s(e){const s=e.name||"<anonymous>",n=e.type,i={focused:e===this.#o,"highlight-chip":!0};return g`
      <div class=${o.Directives.classMap(i)}>
        <button class="jump-to-highlight-button" title=${c(l.jumpToAddress)}
            jslog=${r.action("linear-memory-inspector.jump-to-highlight").track({click:!0})}
            @click=${()=>this.#n(e.startAddress)}>
          <span class="source-code">
            <span class="value">${s}</span><span class="separator">: </span><span>${n}</span>
          </span>
        </button>
        <div class="delete-highlight-container">
          <button class="delete-highlight-button" title=${c(l.deleteHighlight)}
              jslog=${r.action("linear-memory-inspector.delete-highlight").track({click:!0})}
              @click=${()=>this.#i(e)}>
            <${t.Icon.Icon.litTagName} .data=${{iconName:"cross",color:"var(--icon-default-hover)",width:"16px"}}>
            </${t.Icon.Icon.litTagName}>
          </button>
        </div>
      </div>
    `}#n(e){this.dispatchEvent(new u(e))}#i(e){this.dispatchEvent(new p(e))}}customElements.define("devtools-linear-memory-highlight-chip-list",m);var y=Object.freeze({__proto__:null,DeleteMemoryHighlightEvent:p,JumpToHighlightedMemoryEvent:u,LinearMemoryHighlightChipList:m});const v=new CSSStyleSheet;v.replaceSync(":host{flex:auto;display:flex}.view{width:100%;display:flex;flex:1;flex-direction:column;font-family:var(--monospace-font-family);font-size:var(--monospace-font-size);padding:9px 12px 9px 7px}devtools-linear-memory-inspector-viewer{justify-content:center}devtools-linear-memory-inspector-navigator + devtools-linear-memory-inspector-viewer{margin-top:12px}.value-interpreter{display:flex}\n/*# sourceURL=linearMemoryInspector.css */\n");const f=new CSSStyleSheet;f.replaceSync(".navigator{min-height:24px;display:flex;flex-wrap:nowrap;justify-content:space-between;overflow:hidden;align-items:center;background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface)}.navigator-item{display:flex;white-space:nowrap;overflow:hidden}.address-input{text-align:center;outline:none;color:var(--sys-color-on-surface);border:1px solid var(--sys-color-neutral-outline);background:transparent}.address-input.invalid{color:var(--sys-color-error)}.navigator-button{display:flex;width:20px;height:20px;background:transparent;overflow:hidden;border:none;padding:0;outline:none;justify-content:center;align-items:center;cursor:pointer}.navigator-button:disabled devtools-icon{opacity:50%}.navigator-button:enabled:hover devtools-icon{color:var(--icon-default-hover)}.navigator-button:enabled:focus devtools-icon{color:var(--icon-default-hover)}\n/*# sourceURL=linearMemoryNavigator.css */\n");const b={enterAddress:"Enter address",goBackInAddressHistory:"Go back in address history",goForwardInAddressHistory:"Go forward in address history",previousPage:"Previous page",nextPage:"Next page",refresh:"Refresh"},w=e.i18n.registerUIStrings("panels/linear_memory_inspector/components/LinearMemoryNavigator.ts",b),x=e.i18n.getLocalizedString.bind(void 0,w),{render:k,html:S}=o;class $ extends Event{static eventName="addressinputchanged";data;constructor(e,t){super($.eventName),this.data={address:e,mode:t}}}class T extends Event{static eventName="pagenavigation";data;constructor(e){super(T.eventName,{}),this.data=e}}class I extends Event{static eventName="historynavigation";data;constructor(e){super(I.eventName,{}),this.data=e}}class M extends Event{static eventName="refreshrequested";constructor(){super(M.eventName,{})}}class E extends HTMLElement{static litTagName=o.literal`devtools-linear-memory-inspector-navigator`;#e=this.attachShadow({mode:"open"});#a="0";#l=void 0;#d=!0;#c=!1;#h=!1;connectedCallback(){this.#e.adoptedStyleSheets=[f]}set data(e){this.#a=e.address,this.#l=e.error,this.#d=e.valid,this.#c=e.canGoBackInHistory,this.#h=e.canGoForwardInHistory,this.#r();const t=this.#e.querySelector(".address-input");t&&("Submitted"===e.mode?t.blur():"InvalidSubmit"===e.mode&&t.select())}#r(){const e=S`
      <div class="navigator">
        <div class="navigator-item">
          ${this.#g({icon:"undo",title:x(b.goBackInAddressHistory),event:new I("Backward"),enabled:this.#c,jslogContext:"linear-memory-inspector.history-back"})}
          ${this.#g({icon:"redo",title:x(b.goForwardInAddressHistory),event:new I("Forward"),enabled:this.#h,jslogContext:"linear-memory-inspector.history-forward"})}
        </div>
        <div class="navigator-item">
          ${this.#g({icon:"chevron-left",title:x(b.previousPage),event:new T("Backward"),enabled:!0,jslogContext:"linear-memory-inspector.previous-page"})}
          ${this.#p()}
          ${this.#g({icon:"chevron-right",title:x(b.nextPage),event:new T("Forward"),enabled:!0,jslogContext:"linear-memory-inspector.next-page"})}
        </div>
        ${this.#g({icon:"refresh",title:x(b.refresh),event:new M,enabled:!0,jslogContext:"linear-memory-inspector.refresh"})}
      </div>
      `;k(e,this.#e,{host:this})}#p(){const e={"address-input":!0,invalid:!this.#d};return S`
      <input class=${o.Directives.classMap(e)} data-input="true" .value=${this.#a}
        jslog=${r.textField("linear-memory-inspector.address").track({change:!0})}
        title=${this.#d?x(b.enterAddress):this.#l} @change=${this.#u.bind(this,"Submitted")} @input=${this.#u.bind(this,"Edit")}/>`}#u(e,t){const o=t.target;this.dispatchEvent(new $(o.value,e))}#g(e){return S`
      <button class="navigator-button" ?disabled=${!e.enabled}
        jslog=${r.action().track({click:!0,keydown:"Enter"}).context(e.jslogContext)}
        data-button=${e.event.type} title=${e.title}
        @click=${this.dispatchEvent.bind(this,e.event)}>
        <${t.Icon.Icon.litTagName} name=${e.icon}></${t.Icon.Icon.litTagName}>
      </button>`}}customElements.define("devtools-linear-memory-inspector-navigator",E);var N=Object.freeze({__proto__:null,AddressInputChangedEvent:$,PageNavigationEvent:T,HistoryNavigationEvent:I,RefreshRequestedEvent:M,LinearMemoryNavigator:E});const A=new CSSStyleSheet;A.replaceSync(":host{flex:auto;display:flex}.value-interpreter{border:1px solid var(--sys-color-divider);background-color:var(--sys-color-cdt-base-container);overflow:hidden;width:400px}.settings-toolbar{min-height:26px;display:flex;flex-wrap:nowrap;justify-content:space-between;padding-left:12px;padding-right:12px;align-items:center}.settings-toolbar-button{padding:0;width:20px;height:20px;border:none;outline:none;background-color:transparent}.settings-toolbar-button.active devtools-icon{color:var(--icon-toggled)}.divider{display:block;height:1px;margin-bottom:12px;background-color:var(--sys-color-divider)}\n/*# sourceURL=linearMemoryValueInterpreter.css */\n");const z=new CSSStyleSheet;z.replaceSync('*{box-sizing:border-box;min-width:0;min-height:0}:root{height:100%;overflow:hidden;--legacy-accent-color:#1a73e8;--legacy-accent-fg-color:#1a73e8;--legacy-accent-color-hover:#3b86e8;--legacy-accent-fg-color-hover:#1567d3;--legacy-active-control-bg-color:#5a5a5a;--legacy-focus-bg-color:hsl(214deg 40% 92%);--legacy-focus-ring-inactive-shadow-color:#e0e0e0;--legacy-input-validation-error:#db1600;--legacy-toolbar-hover-bg-color:#eaeaea;--legacy-selection-fg-color:#fff;--legacy-selection-bg-color:var(--legacy-accent-color);--legacy-selection-inactive-fg-color:#5a5a5a;--legacy-selection-inactive-bg-color:#dadada;--legacy-divider-border:1px solid var(--sys-color-divider);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-focus-ring-active-shadow:0 0 0 1px var(--legacy-accent-color);--legacy-item-selection-bg-color:#cfe8fc;--legacy-item-selection-inactive-bg-color:#e0e0e0;--monospace-font-size:10px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace;--sys-motion-duration-short4:200ms;--sys-motion-duration-medium2:300ms;--sys-motion-duration-long2:500ms;--sys-motion-easing-emphasized:cubic-bezier(0.2,0,0,1);--sys-motion-easing-emphasized-decelerate:cubic-bezier(0.05,0.7,0.1,1);--sys-motion-easing-emphasized-accelerate:cubic-bezier(0.2,0,0,1)}.theme-with-dark-background{color-scheme:dark;--legacy-accent-color:#0e639c;--legacy-accent-fg-color:#ccc;--legacy-accent-fg-color-hover:#fff;--legacy-accent-color-hover:rgb(17 119 187);--legacy-active-control-bg-color:#cdcdcd;--legacy-focus-bg-color:hsl(214deg 19% 27%);--legacy-focus-ring-inactive-shadow-color:#5a5a5a;--legacy-toolbar-hover-bg-color:#202020;--legacy-selection-fg-color:#cdcdcd;--legacy-selection-inactive-fg-color:#cdcdcd;--legacy-selection-inactive-bg-color:hsl(0deg 0% 28%);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-item-selection-bg-color:hsl(207deg 88% 22%);--legacy-item-selection-inactive-bg-color:#454545}body{--default-font-family:".SFNSDisplay-Regular","Helvetica Neue","Lucida Grande",sans-serif;height:100%;width:100%;position:relative;overflow:hidden;margin:0;cursor:default;font-family:var(--default-font-family);font-size:12px;tab-size:4;user-select:none;color:var(--sys-color-on-surface);background:var(--sys-color-cdt-base-container)}.platform-linux{--default-font-family:"Google Sans Text","Google Sans",system-ui,sans-serif}.platform-mac{--default-font-family:system-ui,sans-serif}.platform-windows{--default-font-family:system-ui,sans-serif}:focus{outline-width:0}.platform-mac,\n:host-context(.platform-mac){--monospace-font-size:11px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace}.platform-windows,\n:host-context(.platform-windows){--monospace-font-size:12px;--monospace-font-family:monospace;--source-code-font-size:12px;--source-code-font-family:monospace}.platform-linux,\n:host-context(.platform-linux){--monospace-font-size:11px;--monospace-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace;--source-code-font-size:11px;--source-code-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace}.monospace{font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)!important}.source-code{font-family:var(--source-code-font-family);font-size:var(--source-code-font-size)!important;white-space:pre-wrap}.source-code .devtools-link.text-button{max-width:100%;overflow:hidden;text-overflow:ellipsis}img{-webkit-user-drag:none}iframe,\na img{border:none}.fill{position:absolute;top:0;left:0;right:0;bottom:0}iframe.fill{width:100%;height:100%}.widget{position:relative;flex:auto;contain:style}.hbox{display:flex;flex-direction:row!important;position:relative}.vbox{display:flex;flex-direction:column!important;position:relative}.view-container > .toolbar{border-bottom:1px solid var(--sys-color-divider)}.flex-auto{flex:auto}.flex-none{flex:none}.flex-centered{display:flex;align-items:center;justify-content:center}.overflow-auto{overflow:auto;background-color:var(--sys-color-cdt-base-container)}iframe.widget{position:absolute;width:100%;height:100%;left:0;right:0;top:0;bottom:0}.hidden{display:none!important}.highlighted-search-result{border-radius:1px;background-color:var(--sys-color-yellow-container);outline:1px solid var(--sys-color-yellow-container)}.link{cursor:pointer;text-decoration:underline;color:var(--sys-color-primary);outline-offset:2px}button,\ninput,\nselect{font-family:inherit;font-size:inherit}select option,\nselect optgroup,\ninput{background-color:var(--sys-color-cdt-base-container)}input{color:inherit;&[type="checkbox"]{position:relative;&:hover::after,\n    &:active::before{content:"";height:24px;width:24px;border-radius:var(--sys-shape-corner-full);position:absolute;top:-6px;left:-6px}&:not(.-theme-preserve){accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary)}&:not(:disabled):hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:not(:disabled):active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:not(:disabled):focus-visible{outline:none;&::before{content:"";height:15px;width:15px;border-radius:5px;position:absolute;top:-3.5px;left:-3.5px;border:2px solid var(--sys-color-state-focus-ring)}}&.small:hover::after,\n    &.small:active::before{height:12px;width:12px;top:0;left:0;border-radius:2px}}}input::placeholder{--override-input-placeholder-color:rgb(0 0 0/54%);color:var(--override-input-placeholder-color)}.theme-with-dark-background input::placeholder,\n:host-context(.theme-with-dark-background) input::placeholder{--override-input-placeholder-color:rgb(230 230 230/54%)}.harmony-input:not([type]),\n.harmony-input[type="number"],\n.harmony-input[type="text"]{padding:3px 6px;height:24px;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;&.error-input,\n  &:invalid{border-color:var(--sys-color-error)}&:not(.error-input):not(:invalid):focus{border-color:var(--sys-color-state-focus-ring)}&:not(.error-input):not(:invalid):hover:not(:focus){background:var(--sys-color-state-hover-on-subtle)}}.highlighted-search-result.current-search-result{--override-current-search-result-background-color:rgb(255 127 0/80%);border-radius:1px;padding:1px;margin:-1px;background-color:var(--override-current-search-result-background-color)}.dimmed{opacity:60%}.editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;padding-left:2px;margin-left:-2px;padding-right:2px;margin-right:-2px;margin-bottom:-1px;padding-bottom:1px;opacity:100%!important}.editing,\n.editing *{color:var(--sys-color-on-surface)!important;text-decoration:none!important}.chrome-select{appearance:none;user-select:none;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;color:var(--sys-color-on-surface);font:inherit;margin:0;outline:none;padding-right:20px;padding-left:6px;background-image:var(--image-file-arrow-drop-down-light);background-color:var(--sys-color-surface);background-position:right center;background-repeat:no-repeat;min-height:24px;min-width:80px}.chrome-select:disabled{opacity:38%}.theme-with-dark-background .chrome-select,\n:host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.chrome-select:enabled{&:hover{background-color:var(--sys-color-state-hover-on-subtle)}&:active{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:focus{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px}}@media (forced-colors: active) and (prefers-color-scheme: light){.chrome-select{background-image:var(--image-file-arrow-drop-down-light)}.theme-with-dark-background .chrome-select,\n  :host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-light)}}@media (forced-colors: active) and (prefers-color-scheme: dark){.chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.theme-with-dark-background .chrome-select,\n  :host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}}.chrome-select-label{margin:0 22px;flex:none}.chrome-select-label p p{margin-top:0;color:var(--sys-color-token-subtle)}.settings-select{margin:0}.chrome-select optgroup,\n.chrome-select option{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface)}.gray-info-message{text-align:center;font-style:italic;padding:6px;color:var(--sys-color-token-subtle);white-space:nowrap}span[is="dt-icon-label"]{flex:none}.full-widget-dimmed-banner a{color:inherit}.full-widget-dimmed-banner{color:var(--sys-color-token-subtle);background-color:var(--sys-color-cdt-base-container);display:flex;justify-content:center;align-items:center;text-align:center;padding:20px;position:absolute;top:0;right:0;bottom:0;left:0;font-size:13px;overflow:auto;z-index:500}.dot::before{content:var(--image-file-empty);width:6px;height:6px;border-radius:50%;outline:1px solid var(--icon-gap-default);left:9px;position:absolute;top:9px;z-index:1}.green::before{background-color:var(--sys-color-green-bright)}.purple::before{background-color:var(--sys-color-purple-bright)}.expandable-inline-button{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface);cursor:pointer;border-radius:3px}.undisplayable-text,\n.expandable-inline-button{border:none;padding:1px 3px;margin:0 2px;font-size:11px;font-family:sans-serif;white-space:nowrap;display:inline-block}.undisplayable-text::after,\n.expandable-inline-button::after{content:attr(data-text)}.undisplayable-text{color:var(--sys-color-state-disabled);font-style:italic}.expandable-inline-button:hover,\n.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-hover-on-subtle)}.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-focus-highlight)}::selection{background-color:var(--sys-color-tonal-container)}.reload-warning{align-self:center;margin-left:10px}button.link{border:none;background:none;padding:3px}button.link:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:var(--sys-shape-corner-full)}.theme-with-dark-background button.link:focus-visible,\n:host-context(.theme-with-dark-background) button.link:focus-visible{--override-link-focus-background-color:rgb(230 230 230/8%)}@media (forced-colors: active){.dimmed,\n  .chrome-select:disabled{opacity:100%}.harmony-input:not([type]),\n  .harmony-input[type="number"],\n  .harmony-input[type="text"]{border:1px solid ButtonText}.harmony-input:not([type]):focus,\n  .harmony-input[type="number"]:focus,\n  .harmony-input[type="text"]:focus{border:1px solid Highlight}}input.custom-search-input::-webkit-search-cancel-button{appearance:none;width:16px;height:15px;margin-right:0;opacity:70%;mask-image:var(--image-file-cross-circle-filled);mask-position:center;mask-repeat:no-repeat;mask-size:99%;background-color:var(--icon-default)}input.custom-search-input::-webkit-search-cancel-button:hover{opacity:99%}.spinner::before{display:block;width:var(--dimension,24px);height:var(--dimension,24px);border:var(--override-spinner-size,3px) solid var(--override-spinner-color,var(--sys-color-token-subtle));border-radius:12px;clip:rect(0,var(--clip-size,15px),var(--clip-size,15px),0);content:"";position:absolute;animation:spinner-animation 1s linear infinite;box-sizing:border-box}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}.adorner-container{display:inline-flex;vertical-align:middle}.adorner-container.hidden{display:none}.adorner-container devtools-adorner{margin-left:3px}:host-context(.theme-with-dark-background) devtools-adorner{--override-adorner-border-color:var(--sys-color-tonal-outline);--override-adorner-focus-border-color:var(--sys-color-state-focus-ring);--override-adorner-active-background-color:var(--sys-color-state-riple-neutral-on-subtle)}.panel{display:flex;overflow:hidden;position:absolute;top:0;left:0;right:0;bottom:0;z-index:0;background-color:var(--sys-color-cdt-base-container)}.panel-sidebar{overflow-x:hidden;background-color:var(--sys-color-cdt-base-container)}iframe.extension{flex:auto;width:100%;height:100%}iframe.panel.extension{display:block;height:100%}@media (forced-colors: active){:root{--legacy-accent-color:Highlight;--legacy-focus-ring-inactive-shadow-color:ButtonText}}\n/*# sourceURL=inspectorCommon.css */\n');const C=new CSSStyleSheet;C.replaceSync(":host{flex:auto;display:flex}.value-types{width:100%;display:grid;grid-template-columns:auto auto 1fr;gap:4px 24px;min-height:24px;overflow:hidden;padding:2px 12px;align-items:baseline;justify-content:start}.value-type-cell{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:flex;flex-direction:column;min-height:24px}.value-type-value-with-link{display:flex;align-items:center}.value-type-cell-no-mode{grid-column:1/3}.jump-to-button{display:flex;width:20px;height:20px;border:none;padding:0;outline:none;justify-content:center;align-items:center;cursor:pointer;background-color:var(--sys-color-cdt-base-container)}.signed-divider{width:1px;height:15px;background-color:var(--sys-color-divider);margin:0 4px}.selectable-text{user-select:text}.selectable-text::selection{background-color:var(--sys-color-tonal-container)}\n/*# sourceURL=valueInterpreterDisplay.css */\n");const L={notApplicable:"N/A"},j=e.i18n.registerUIStrings("panels/linear_memory_inspector/components/ValueInterpreterDisplayUtils.ts",L),R=e.i18n.getLocalizedString.bind(void 0,j);function B(){return new Map(H)}const H=new Map([["Integer 8-bit","dec"],["Integer 16-bit","dec"],["Integer 32-bit","dec"],["Integer 64-bit","dec"],["Float 32-bit","dec"],["Float 64-bit","dec"],["Pointer 32-bit","hex"],["Pointer 64-bit","hex"]]),_=["dec","hex","oct","sci"];function O(t){return e.i18n.lockedString(t)}function P(e,t){switch(e){case"Integer 8-bit":case"Integer 16-bit":case"Integer 32-bit":case"Integer 64-bit":return"dec"===t||"hex"===t||"oct"===t;case"Float 32-bit":case"Float 64-bit":return"sci"===t||"dec"===t;case"Pointer 32-bit":case"Pointer 64-bit":return"hex"===t;default:return n.assertNever(e,`Unknown value type: ${e}`)}}function V(e){switch(e){case"Integer 8-bit":case"Integer 16-bit":case"Integer 32-bit":case"Integer 64-bit":case"Float 32-bit":case"Float 64-bit":return!0;default:return!1}}function U(e,t,o){if(!F(e))return console.error(`Requesting address of a non-pointer type: ${e}.\n`),NaN;try{const r=new DataView(t),s="Little Endian"===o;return"Pointer 32-bit"===e?r.getUint32(0,s):r.getBigUint64(0,s)}catch(e){return NaN}}function F(e){return"Pointer 32-bit"===e||"Pointer 64-bit"===e}function D(e){if(!e.mode)return console.error(`No known way of showing value for ${e.type}`),R(L.notApplicable);const t=new DataView(e.buffer),o="Little Endian"===e.endianness;let r;try{switch(e.type){case"Integer 8-bit":return r=e.signed?t.getInt8(0):t.getUint8(0),G(r,e.mode);case"Integer 16-bit":return r=e.signed?t.getInt16(0,o):t.getUint16(0,o),G(r,e.mode);case"Integer 32-bit":return r=e.signed?t.getInt32(0,o):t.getUint32(0,o),G(r,e.mode);case"Integer 64-bit":return r=e.signed?t.getBigInt64(0,o):t.getBigUint64(0,o),G(r,e.mode);case"Float 32-bit":return r=t.getFloat32(0,o),q(r,e.mode);case"Float 64-bit":return r=t.getFloat64(0,o),q(r,e.mode);case"Pointer 32-bit":return r=t.getUint32(0,o),G(r,"hex");case"Pointer 64-bit":return r=t.getBigUint64(0,o),G(r,"hex");default:return n.assertNever(e.type,`Unknown value type: ${e.type}`)}}catch(e){return R(L.notApplicable)}}function q(e,t){switch(t){case"dec":return e.toFixed(2).toString();case"sci":return e.toExponential(2).toString();default:throw new Error(`Unknown mode for floats: ${t}.`)}}function G(e,t){switch(t){case"dec":return e.toString();case"hex":return e<0?R(L.notApplicable):"0x"+e.toString(16).toUpperCase();case"oct":return e<0?R(L.notApplicable):e.toString(8);default:throw new Error(`Unknown mode for integers: ${t}.`)}}var J=Object.freeze({__proto__:null,VALUE_INTEPRETER_MAX_NUM_BYTES:8,getDefaultValueTypeMapping:B,VALUE_TYPE_MODE_LIST:_,valueTypeToLocalizedString:O,isValidMode:P,isNumber:V,getPointerAddress:U,isPointer:F,format:D,formatFloat:q,formatInteger:G});const K={unsignedValue:"`Unsigned` value",changeValueTypeMode:"Change mode",signedValue:"`Signed` value",jumpToPointer:"Jump to address",addressOutOfRange:"Address out of memory range"},X=e.i18n.registerUIStrings("panels/linear_memory_inspector/components/ValueInterpreterDisplay.ts",K),W=e.i18n.getLocalizedString.bind(void 0,X),{render:Y,html:Q}=o,Z=Array.from(B().keys());class ee extends Event{static eventName="valuetypemodechanged";data;constructor(e,t){super(ee.eventName,{composed:!0}),this.data={type:e,mode:t}}}class te extends Event{static eventName="jumptopointeraddress";data;constructor(e){super(te.eventName,{composed:!0}),this.data=e}}class oe extends HTMLElement{static litTagName=o.literal`devtools-linear-memory-inspector-interpreter-display`;#e=this.attachShadow({mode:"open"});#m="Little Endian";#y=new ArrayBuffer(0);#v=new Set;#f=B();#b=0;constructor(){super(),this.#e.adoptedStyleSheets=[z]}connectedCallback(){this.#e.adoptedStyleSheets=[C]}set data(e){this.#y=e.buffer,this.#m=e.endianness,this.#v=e.valueTypes,this.#b=e.memoryLength,e.valueTypeModes&&e.valueTypeModes.forEach(((e,t)=>{P(t,e)&&this.#f.set(t,e)})),this.#r()}#r(){Y(Q`
      <div class="value-types">
        ${Z.map((e=>this.#v.has(e)?this.#w(e):""))}
      </div>
    `,this.#e,{host:this})}#w(e){if(V(e))return this.#x(e);if(F(e))return this.#k(e);throw new Error(`No known way to format ${e}`)}#k(o){const s=this.#S({type:o,signed:!1}),n=U(o,this.#y,this.#m),i=Number.isNaN(n)||BigInt(n)>=BigInt(this.#b),a=W(i?K.addressOutOfRange:K.jumpToPointer),l=i?"var(--icon-default)":"var(--icon-link)";return Q`
      <span class="value-type-cell-no-mode value-type-cell selectable-text">${e.i18n.lockedString(o)}</span>
      <div class="value-type-cell">
        <div class="value-type-value-with-link" data-value="true">
        <span class="selectable-text">${s}</span>
          ${Q`
              <button class="jump-to-button" data-jump="true" title=${a} ?disabled=${i}
                jslog=${r.action("linear-memory-inspector.jump-to-address").track({click:!0})}
                @click=${this.#$.bind(this,Number(n))}>
                <${t.Icon.Icon.litTagName} .data=${{iconName:"open-externally",color:l,width:"16px"}}>
                </${t.Icon.Icon.litTagName}>
              </button>`}
        </div>
      </div>
    `}#$(e){this.dispatchEvent(new te(e))}#x(t){return Q`
      <span class="value-type-cell selectable-text">${e.i18n.lockedString(t)}</span>
      <div>
        <select title=${W(K.changeValueTypeMode)}
          data-mode-settings="true"
          class="chrome-select"
          style="border: none; background-color: transparent; cursor: pointer; color: var(--sys-color-token-subtle);"
          jslog=${r.dropDown("linear-memory-inspector.value-type-mode").track({change:!0})}
          @change=${this.#T.bind(this,t)}>
            ${_.filter((e=>P(t,e))).map((o=>Q`
                <option value=${o} .selected=${this.#f.get(t)===o}
                        jslog=${r.item(o).track({click:!0})}>${e.i18n.lockedString(o)}
                </option>`))}
        </select>
      </div>
      ${this.#I(t)}
    `}#I(e){const t=this.#S({type:e,signed:!1}),o=this.#S({type:e,signed:!0}),r=this.#f.get(e),s=o!==t&&"hex"!==r&&"oct"!==r,n=Q`<span class="value-type-cell selectable-text"  title=${W(K.unsignedValue)} data-value="true">${t}</span>`;if(!s)return n;const i="Integer 32-bit"===e||"Integer 64-bit"===e,a=Q`<span class="selectable-text" data-value="true" title=${W(K.signedValue)}>${o}</span>`;return i?Q`
        <div class="value-type-cell">
          ${n}
          ${a}
        </div>
        `:Q`
      <div class="value-type-cell" style="flex-direction: row;">
        ${n}
        <span class="signed-divider"></span>
        ${a}
      </div>
    `}#T(e,t){t.preventDefault();const o=t.target.value;this.dispatchEvent(new ee(e,o))}#S(e){const t=this.#f.get(e.type);return D({buffer:this.#y,type:e.type,endianness:this.#m,signed:e.signed||!1,mode:t})}}customElements.define("devtools-linear-memory-inspector-interpreter-display",oe);var re=Object.freeze({__proto__:null,ValueTypeModeChangedEvent:ee,JumpToPointerAddressEvent:te,ValueInterpreterDisplay:oe});const se=new CSSStyleSheet;se.replaceSync(":host{flex:auto;display:flex;min-height:20px}.settings{display:flex;flex-wrap:wrap;margin:0 12px 12px;column-gap:45px;row-gap:15px}.value-types-selection{display:flex;flex-direction:column}.group{font-weight:bold;margin-bottom:11px}.type-label{white-space:nowrap}.group + .type-label{margin-top:5px}.type-label input{margin:0 6px 0 0;padding:0}.type-label + .type-label{margin-top:6px}\n/*# sourceURL=valueInterpreterSettings.css */\n");const{render:ne,html:ie}=o,ae={otherGroup:"Other"},le=e.i18n.registerUIStrings("panels/linear_memory_inspector/components/ValueInterpreterSettings.ts",ae),de=e.i18n.getLocalizedString.bind(void 0,le),ce=new Map([["Integer",["Integer 8-bit","Integer 16-bit","Integer 32-bit","Integer 64-bit"]],["Floating point",["Float 32-bit","Float 64-bit"]],["Other",["Pointer 32-bit","Pointer 64-bit"]]]);class he extends Event{static eventName="typetoggle";data;constructor(e,t){super(he.eventName),this.data={type:e,checked:t}}}class ge extends HTMLElement{static litTagName=o.literal`devtools-linear-memory-inspector-interpreter-settings`;#e=this.attachShadow({mode:"open"});#v=new Set;connectedCallback(){this.#e.adoptedStyleSheets=[i.checkboxStyles,se]}set data(e){this.#v=e.valueTypes,this.#r()}#r(){ne(ie`
      <div class="settings" jslog=${r.pane("settings")}>
       ${[...ce.keys()].map((e=>ie`
          <div class="value-types-selection">
            <span class="group">${function(e){return"Other"===e?de(ae.otherGroup):e}(e)}</span>
            ${this.#M(e)}
          </div>
        `))}
      </div>
      `,this.#e,{host:this})}#M(e){const t=ce.get(e);if(!t)throw new Error(`Unknown group ${e}`);return ie`
      ${t.map((e=>ie`
          <label class="type-label" title=${O(e)}>
            <input data-input="true" type="checkbox" .checked=${this.#v.has(e)} @change=${t=>this.#E(e,t)} jslog=${r.toggle().track({change:!0}).context(n.StringUtilities.toKebabCase(e))}>
            <span data-title="true">${O(e)}</span>
          </label>
     `))}`}#E(e,t){const o=t.target;this.dispatchEvent(new he(e,o.checked))}}customElements.define("devtools-linear-memory-inspector-interpreter-settings",ge);var pe=Object.freeze({__proto__:null,TypeToggleEvent:he,ValueInterpreterSettings:ge});const ue={toggleValueTypeSettings:"Toggle value type settings",changeEndianness:"Change `Endianness`"},me=e.i18n.registerUIStrings("panels/linear_memory_inspector/components/LinearMemoryValueInterpreter.ts",ue),ye=e.i18n.getLocalizedString.bind(void 0,me),{render:ve,html:fe}=o;class be extends Event{static eventName="endiannesschanged";data;constructor(e){super(be.eventName),this.data=e}}class we extends Event{static eventName="valuetypetoggled";data;constructor(e,t){super(we.eventName),this.data={type:e,checked:t}}}class xe extends HTMLElement{static litTagName=o.literal`devtools-linear-memory-inspector-interpreter`;#e=this.attachShadow({mode:"open"});#m="Little Endian";#y=new ArrayBuffer(0);#v=new Set;#f=new Map;#b=0;#N=!1;connectedCallback(){this.#e.adoptedStyleSheets=[A]}set data(e){this.#m=e.endianness,this.#y=e.value,this.#v=e.valueTypes,this.#f=e.valueTypeModes||new Map,this.#b=e.memoryLength,this.#r()}#r(){ve(fe`
      <div class="value-interpreter">
        <div class="settings-toolbar">
          ${this.#A()}
          <button data-settings="true" class="settings-toolbar-button ${this.#N?"active":""}"
              title=${ye(ue.toggleValueTypeSettings)} @click=${this.#z}
              jslog=${r.toggleSubpane("linear-memory-inspector.toggle-value-settings").track({click:!0})}>
            <${t.Icon.Icon.litTagName} name=${this.#N?"gear-filled":"gear"}></${t.Icon.Icon.litTagName}>
          </button>
        </div>
        <span class="divider"></span>
        <div>
          ${this.#N?fe`
              <${ge.litTagName}
                .data=${{valueTypes:this.#v}}
                @typetoggle=${this.#E}>
              </${ge.litTagName}>`:fe`
              <${oe.litTagName}
                .data=${{buffer:this.#y,valueTypes:this.#v,endianness:this.#m,valueTypeModes:this.#f,memoryLength:this.#b}}>
              </${oe.litTagName}>`}
        </div>
      </div>
    `,this.#e,{host:this})}#C(e){e.preventDefault();const t=e.target.value;this.dispatchEvent(new be(t))}#A(){const t=this.#C.bind(this);return fe`
    <label data-endianness-setting="true" title=${ye(ue.changeEndianness)}>
      <select class="chrome-select"
        jslog=${r.dropDown("linear-memory-inspector.endianess").track({change:!0})}
        style="border: none; background-color: transparent; cursor: pointer;"
        data-endianness="true" @change=${t}>
        ${["Little Endian","Big Endian"].map((t=>fe`<option value=${t} .selected=${this.#m===t}
            jslog=${r.item(n.StringUtilities.toKebabCase(t)).track({click:!0})}>${e.i18n.lockedString(t)}</option>`))}
      </select>
    </label>
    `}#z(){this.#N=!this.#N,this.#r()}#E(e){this.dispatchEvent(new we(e.data.type,e.data.checked))}}customElements.define("devtools-linear-memory-inspector-interpreter",xe);var ke=Object.freeze({__proto__:null,EndiannessChangedEvent:be,ValueTypeToggledEvent:we,LinearMemoryValueInterpreter:xe});const Se=/^0x[a-fA-F0-9]+$/,$e=/^0$|[1-9]\d*$/;function Te(e){const t=e.number.toString(16).padStart(e.pad,"0").toUpperCase();return e.prefix?"0x"+t:t}function Ie(e){return Te({number:e,pad:8,prefix:!0})}function Me(e){const t=e.match(Se),o=e.match($e);let r;return t&&t[0].length===e.length?r=parseInt(e,16):o&&o[0].length===e.length&&(r=parseInt(e,10)),r}var Ee=Object.freeze({__proto__:null,HEXADECIMAL_REGEXP:Se,DECIMAL_REGEXP:$e,toHexString:Te,formatAddress:Ie,parseAddress:Me});const Ne=new CSSStyleSheet;Ne.replaceSync(":host{flex:auto;display:flex;min-height:20px}.view{overflow:hidden;text-overflow:ellipsis;box-sizing:border-box;background:var(--sys-color-cdt-base-container);outline:none}.row{display:flex;height:20px;align-items:center}.cell{text-align:center;border:1px solid transparent;border-radius:2px;&.focused-area{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}&.selected{border-color:var(--sys-color-state-focus-ring);color:var(--sys-color-on-tonal-container);background-color:var(--sys-color-state-focus-select)}}.byte-cell{min-width:21px;color:var(--sys-color-on-surface)}.byte-group-margin{margin-left:var(--byte-group-margin)}.text-cell{min-width:14px;color:var(--sys-color-on-surface-subtle)}.address{color:var(--sys-color-state-disabled)}.address.selected{font-weight:bold;color:var(--sys-color-on-surface)}.divider{width:1px;height:inherit;background-color:var(--sys-color-divider);margin:0 4px}.highlight-area{background-color:var(--sys-color-surface-variant)}\n/*# sourceURL=linearMemoryViewer.css */\n");const{render:Ae,html:ze}=o;class Ce extends Event{static eventName="byteselected";data;constructor(e){super(Ce.eventName),this.data=e}}class Le extends Event{static eventName="resize";data;constructor(e){super(Le.eventName),this.data=e}}class je extends HTMLElement{static litTagName=o.literal`devtools-linear-memory-inspector-viewer`;#e=this.attachShadow({mode:"open"});#L=new ResizeObserver((()=>this.#j()));#R=!1;#B=new Uint8Array;#a=0;#H=0;#_;#o;#O=1;#P=4;#V=!0;#U=void 0;set data(e){if(e.address<e.memoryOffset||e.address>e.memoryOffset+e.memory.length||e.address<0)throw new Error("Address is out of bounds.");if(e.memoryOffset<0)throw new Error("Memory offset has to be greater or equal to zero.");this.#B=e.memory,this.#a=e.address,this.#_=e.highlightInfo,this.#o=e.focusedMemoryHighlight,this.#H=e.memoryOffset,this.#V=e.focus,this.#F()}connectedCallback(){this.style.setProperty("--byte-group-margin","8px"),this.#e.adoptedStyleSheets=[Ne]}disconnectedCallback(){this.#R=!1,this.#L.disconnect()}#F(){this.#D(),this.#r(),this.#q(),this.#G()}#q(){if(this.#V){const e=this.#e.querySelector(".view");e&&e.focus()}}#j(){this.#F(),this.dispatchEvent(new Le(this.#P*this.#O))}#D(){if(0===this.clientWidth||0===this.clientHeight||!this.shadowRoot)return this.#P=4,void(this.#O=1);const e=this.shadowRoot.querySelector(".byte-cell"),t=this.shadowRoot.querySelector(".text-cell"),o=this.shadowRoot.querySelector(".divider"),r=this.shadowRoot.querySelector(".row"),s=this.shadowRoot.querySelector(".address");if(!(e&&t&&o&&r&&s))return this.#P=4,void(this.#O=1);const n=4*(e.getBoundingClientRect().width+t.getBoundingClientRect().width)+8,i=o.getBoundingClientRect().width,a=e.getBoundingClientRect().left-s.getBoundingClientRect().left,l=this.clientWidth-1-a-i;if(l<n)return this.#P=4,void(this.#O=1);this.#P=4*Math.floor(l/n),this.#O=Math.floor(this.clientHeight/r.clientHeight)}#G(){this.#L&&!this.#R&&(this.#L.observe(this),this.#R=!0)}#r(){const e=r.section().track({keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|PageUp|PageDown"}).context("linear-memory-inspector.viewer");Ae(ze`
      <div class="view" tabindex="0" @keydown=${this.#J} jslog=${e}>
        ${this.#K()}
      </div>
      `,this.#e,{host:this})}#J(e){const t=e;let o;"ArrowUp"===t.code?o=this.#a-this.#P:"ArrowDown"===t.code?o=this.#a+this.#P:"ArrowLeft"===t.code?o=this.#a-1:"ArrowRight"===t.code?o=this.#a+1:"PageUp"===t.code?o=this.#a-this.#P*this.#O:"PageDown"===t.code&&(o=this.#a+this.#P*this.#O),void 0!==o&&o!==this.#U&&(this.#U=o,this.dispatchEvent(new Ce(o)))}#K(){const e=[];for(let t=0;t<this.#O;++t)e.push(this.#X(t));return ze`${e}`}#X(e){const{startIndex:t,endIndex:r}={startIndex:e*this.#P,endIndex:(e+1)*this.#P},s={address:!0,selected:Math.floor((this.#a-this.#H)/this.#P)===e};return ze`
    <div class="row">
      <span class=${o.Directives.classMap(s)}>${Te({number:t+this.#H,pad:8,prefix:!1})}</span>
      <span class="divider"></span>
      ${this.#W(t,r)}
      <span class="divider"></span>
      ${this.#Y(t,r)}
    </div>
    `}#W(e,t){const s=[];for(let n=e;n<t;++n){const t=n+this.#H,i={cell:!0,"byte-cell":!0,"byte-group-margin":n!==e&&(n-e)%4==0,selected:n===this.#a-this.#H,"highlight-area":this.#Q(t),"focused-area":this.#Z(t)},a=n<this.#B.length,l=a?ze`${Te({number:this.#B[n],pad:2,prefix:!1})}`:"",d=a?this.#ee.bind(this,t):"",c=r.tableCell("linear-memory-inspector.byte-cell").track({click:!0});s.push(ze`<span class=${o.Directives.classMap(i)} @click=${d} jslog=${c}>${l}</span>`)}return ze`${s}`}#Y(e,t){const s=[];for(let n=e;n<t;++n){const e=n+this.#H,t=this.#Q(e),i=this.#Z(e),a={cell:!0,"text-cell":!0,selected:this.#a-this.#H===n,"highlight-area":t,"focused-area":i},l=n<this.#B.length,d=l?ze`${this.#te(this.#B[n])}`:"",c=l?this.#ee.bind(this,n+this.#H):"",h=r.tableCell("linear-memory-inspector.text-cell").track({click:!0});s.push(ze`<span class=${o.Directives.classMap(a)} @click=${c} jslog=${h}>${d}</span>`)}return ze`${s}`}#te(e){return e>=20&&e<=127?String.fromCharCode(e):"."}#ee(e){this.dispatchEvent(new Ce(e))}#Q(e){return void 0!==this.#_&&(this.#_.startAddress<=e&&e<this.#_.startAddress+this.#_.size)}#Z(e){return!!this.#o&&(this.#o.startAddress<=e&&e<this.#o.startAddress+this.#o.size)}}customElements.define("devtools-linear-memory-inspector-viewer",je);var Re=Object.freeze({__proto__:null,ByteSelectedEvent:Ce,ResizeEvent:Le,LinearMemoryViewer:je});const{render:Be,html:He}=o,_e={addressHasToBeANumberBetweenSAnd:"Address has to be a number between {PH1} and {PH2}"},Oe=e.i18n.registerUIStrings("panels/linear_memory_inspector/components/LinearMemoryInspector.ts",_e),Pe=e.i18n.getLocalizedString.bind(void 0,Oe);class Ve extends Event{static eventName="memoryrequest";data;constructor(e,t,o){super(Ve.eventName),this.data={start:e,end:t,address:o}}}class Ue extends Event{static eventName="addresschanged";data;constructor(e){super(Ue.eventName),this.data=e}}class Fe extends Event{static eventName="settingschanged";data;constructor(e){super(Fe.eventName),this.data=e}}class De{#a=0;#oe;constructor(e,t){if(e<0)throw new Error("Address should be a greater or equal to zero");this.#a=e,this.#oe=t}valid(){return!0}reveal(){this.#oe(this.#a)}}class qe extends HTMLElement{static litTagName=o.literal`devtools-linear-memory-inspector-inspector`;#e=this.attachShadow({mode:"open"});#re=new s.SimpleHistoryManager.SimpleHistoryManager(10);#B=new Uint8Array;#H=0;#se=0;#a=-1;#_;#ne="Submitted";#ie=`${this.#a}`;#ae=4;#le=B();#v=new Set(this.#le.keys());#m="Little Endian";connectedCallback(){this.#e.adoptedStyleSheets=[v]}set data(e){if(e.address<e.memoryOffset||e.address>e.memoryOffset+e.memory.length||e.address<0)throw new Error("Address is out of bounds.");if(e.memoryOffset<0)throw new Error("Memory offset has to be greater or equal to zero.");if(e.highlightInfo){if(e.highlightInfo.size<0)throw new Error("Object size has to be greater than or equal to zero");if(e.highlightInfo.startAddress<0||e.highlightInfo.startAddress>=e.outerMemoryLength)throw new Error("Object start address is out of bounds.")}this.#B=e.memory,this.#H=e.memoryOffset,this.#se=e.outerMemoryLength,this.#le=e.valueTypeModes||this.#le,this.#v=e.valueTypes||this.#v,this.#m=e.endianness||this.#m,this.#_=e.highlightInfo,this.#de(e.address),this.#r()}#r(){const{start:e,end:t}=this.#ce(this.#a,this.#ae),o="Submitted"===this.#ne?Ie(this.#a):this.#ie,r=this.#he(o),s=Pe(_e.addressHasToBeANumberBetweenSAnd,{PH1:Ie(0),PH2:Ie(this.#se)}),n=r?void 0:s,i=this.#re.canRollback(),a=this.#re.canRollover(),l=this.#_?[this.#_]:[],d=this.#ge(l,this.#a);Be(He`
      <div class="view">
        <${E.litTagName}
          .data=${{address:o,valid:r,mode:this.#ne,error:n,canGoBackInHistory:i,canGoForwardInHistory:a}}
          @refreshrequested=${this.#pe}
          @addressinputchanged=${this.#u}
          @pagenavigation=${this.#ue}
          @historynavigation=${this.#me}></${E.litTagName}>
          <${m.litTagName}
          .data=${{highlightInfos:l,focusedMemoryHighlight:d}}
          @jumptohighlightedmemory=${this.#ye}>
          </${m.litTagName}>
        <${je.litTagName}
          .data=${{memory:this.#B.slice(e-this.#H,t-this.#H),address:this.#a,memoryOffset:e,focus:"Submitted"===this.#ne,highlightInfo:this.#_,focusedMemoryHighlight:d}}
          @byteselected=${this.#ve}
          @resize=${this.#j}>
        </${je.litTagName}>
      </div>
      <div class="value-interpreter">
        <${xe.litTagName}
          .data=${{value:this.#B.slice(this.#a-this.#H,this.#a+8).buffer,valueTypes:this.#v,valueTypeModes:this.#le,endianness:this.#m,memoryLength:this.#se}}
          @valuetypetoggled=${this.#fe}
          @valuetypemodechanged=${this.#be}
          @endiannesschanged=${this.#we}
          @jumptopointeraddress=${this.#ye}
          >
        </${xe.litTagName}/>
      </div>
      `,this.#e,{host:this})}#ye(e){e.stopPropagation(),this.#ne="Submitted";const t=Math.max(0,Math.min(e.data,this.#se-1));this.#xe(t)}#pe(){const{start:e,end:t}=this.#ce(this.#a,this.#ae);this.dispatchEvent(new Ve(e,t,this.#a))}#ve(e){this.#ne="Submitted";const t=Math.max(0,Math.min(e.data,this.#se-1));this.#xe(t)}#ke(){return{valueTypes:this.#v,modes:this.#le,endianness:this.#m}}#we(e){this.#m=e.data,this.dispatchEvent(new Fe(this.#ke())),this.#r()}#he(e){const t=Me(e);return void 0!==t&&t>=0&&t<this.#se}#u(e){const{address:t,mode:o}=e.data,r=this.#he(t),s=Me(t);if(this.#ie=t,void 0!==s&&r)return this.#ne=o,void this.#xe(s);this.#ne="Submitted"!==o||r?"Edit":"InvalidSubmit",this.#r()}#fe(e){const{type:t,checked:o}=e.data;o?this.#v.add(t):this.#v.delete(t),this.dispatchEvent(new Fe(this.#ke())),this.#r()}#be(e){e.stopImmediatePropagation();const{type:t,mode:o}=e.data;this.#le.set(t,o),this.dispatchEvent(new Fe(this.#ke())),this.#r()}#me(e){return"Forward"===e.data?this.#re.rollover():this.#re.rollback()}#ue(e){const t="Forward"===e.data?this.#a+this.#ae:this.#a-this.#ae,o=Math.max(0,Math.min(t,this.#se-1));this.#xe(o)}#xe(e){e<0||e>=this.#se?console.warn(`Specified address is out of bounds: ${e}`):(this.#de(e),this.#F())}#ce(e,t){const o=Math.floor(e/t)*t;return{start:o,end:Math.min(o+t,this.#se)}}#j(e){this.#ae=e.data,this.#F()}#F(){const{start:e,end:t}=this.#ce(this.#a,this.#ae);e<this.#H||t>this.#H+this.#B.length?this.dispatchEvent(new Ve(e,t,this.#a)):this.#r()}#de(e){if(this.#a===e)return;const t=new De(e,(()=>this.#xe(e)));this.#re.push(t),this.#a=e,this.dispatchEvent(new Ue(this.#a))}#ge(e,t){let o;for(const r of e)r.startAddress<=t&&t<r.startAddress+r.size&&(o?r.size<o.size&&(o=r):o=r);return o}}customElements.define("devtools-linear-memory-inspector-inspector",qe);var Ge=Object.freeze({__proto__:null,MemoryRequestEvent:Ve,AddressChangedEvent:Ue,SettingsChangedEvent:Fe,LinearMemoryInspector:qe}),Je=Object.freeze({__proto__:null});export{y as LinearMemoryHighlightChipList,Ge as LinearMemoryInspector,Ee as LinearMemoryInspectorUtils,N as LinearMemoryNavigator,ke as LinearMemoryValueInterpreter,Re as LinearMemoryViewer,Je as LinearMemoryViewerUtils,re as ValueInterpreterDisplay,J as ValueInterpreterDisplayUtils,pe as ValueInterpreterSettings};
