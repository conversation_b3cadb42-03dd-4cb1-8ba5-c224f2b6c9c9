{"version": 3, "file": "Zocial.js", "sourceRoot": "", "sources": ["../src/Zocial.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,qDAAqD,CAAC;AACvE,OAAO,QAAQ,MAAM,0DAA0D,CAAC;AAEhF,eAAe,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["\"use client\";\n\nimport createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Zocial.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Zocial.json';\n\nexport default createIconSet(glyphMap, 'zocial', font);\n"]}