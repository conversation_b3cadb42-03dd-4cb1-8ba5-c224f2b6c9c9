{"version": 3, "file": "withAndroidManifestIcons.js", "names": ["_configPlugins", "data", "require", "withAndroidManifestIcons", "config", "withAndroidManifest", "modResults", "setRoundIconManifest", "exports", "manifest", "isAdaptive", "android", "adaptiveIcon", "application", "AndroidConfig", "Manifest", "getMainApplicationOrThrow", "$"], "sources": ["../../../src/plugins/icons/withAndroidManifestIcons.ts"], "sourcesContent": ["import { AndroidConfig, ConfigPlugin, withAndroidManifest } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\nexport const withAndroidManifestIcons: ConfigPlugin = (config) =>\n  withAndroidManifest(config, (config) => {\n    config.modResults = setRoundIconManifest(config, config.modResults);\n    return config;\n  });\n\nexport function setRoundIconManifest(\n  config: Pick<ExpoConfig, 'android'>,\n  manifest: AndroidConfig.Manifest.AndroidManifest\n): AndroidConfig.Manifest.AndroidManifest {\n  const isAdaptive = !!config.android?.adaptiveIcon;\n  const application = AndroidConfig.Manifest.getMainApplicationOrThrow(manifest);\n\n  if (isAdaptive) {\n    application.$['android:roundIcon'] = '@mipmap/ic_launcher_round';\n  } else {\n    delete application.$['android:roundIcon'];\n  }\n  return manifest;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,MAAME,wBAAsC,GAAIC,MAAM,IAC3D,IAAAC,oCAAmB,EAACD,MAAM,EAAGA,MAAM,IAAK;EACtCA,MAAM,CAACE,UAAU,GAAGC,oBAAoB,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;EACnE,OAAOF,MAAM;AACf,CAAC,CAAC;AAACI,OAAA,CAAAL,wBAAA,GAAAA,wBAAA;AAEE,SAASI,oBAAoBA,CAClCH,MAAmC,EACnCK,QAAgD,EACR;EACxC,MAAMC,UAAU,GAAG,CAAC,CAACN,MAAM,CAACO,OAAO,EAAEC,YAAY;EACjD,MAAMC,WAAW,GAAGC,8BAAa,CAACC,QAAQ,CAACC,yBAAyB,CAACP,QAAQ,CAAC;EAE9E,IAAIC,UAAU,EAAE;IACdG,WAAW,CAACI,CAAC,CAAC,mBAAmB,CAAC,GAAG,2BAA2B;EAClE,CAAC,MAAM;IACL,OAAOJ,WAAW,CAACI,CAAC,CAAC,mBAAmB,CAAC;EAC3C;EACA,OAAOR,QAAQ;AACjB", "ignoreList": []}