// Same as fr
import formatDistance from "../fr/_lib/formatDistance/index.js";
import formatRelative from "../fr/_lib/formatRelative/index.js";
import localize from "../fr/_lib/localize/index.js";
import match from "../fr/_lib/match/index.js";
// Unique for fr-CA
import formatLong from "./_lib/formatLong/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary French locale (Canada).
 * @language French
 * @iso-639-2 fra
 * <AUTHOR> [@izeau]{@link https://github.com/izeau}
 * <AUTHOR> [@fbonzon]{@link https://github.com/fbonzon}
 * <AUTHOR> [@gpetrioli]{@link https://github.com/gpetrioli}
 */
var locale = {
  code: 'fr-CA',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  // Unique for fr-CA
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1
  }
};
export default locale;