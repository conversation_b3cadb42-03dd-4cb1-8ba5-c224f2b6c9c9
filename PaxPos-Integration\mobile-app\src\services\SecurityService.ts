import * as Crypto from 'expo-crypto';
import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LoggingService } from './LoggingService';

export interface AuthToken {
  token: string;
  refreshToken: string;
  expiresAt: Date;
  userId: string;
  role: string;
  permissions: string[];
}

export interface SecurityConfig {
  tokenExpiration: number; // in milliseconds
  refreshTokenExpiration: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  encryptionKey: string;
}

export class SecurityService {
  private logger: LoggingService;
  private config: SecurityConfig;
  private currentToken: AuthToken | null = null;

  constructor() {
    this.logger = new LoggingService();
    this.config = {
      tokenExpiration: 8 * 60 * 60 * 1000, // 8 hours
      refreshTokenExpiration: 30 * 24 * 60 * 60 * 1000, // 30 days
      maxLoginAttempts: 5,
      lockoutDuration: 15 * 60 * 1000, // 15 minutes
      encryptionKey: '' // Will be generated
    };
  }

  async initialize(): Promise<void> {
    try {
      // Generate or retrieve encryption key
      await this.initializeEncryption();
      
      // Load existing token if available
      await this.loadStoredToken();
      
      this.logger.info('Security service initialized successfully');
    } catch (error) {
      this.logger.error('Security service initialization failed', error);
      throw error;
    }
  }

  private async initializeEncryption(): Promise<void> {
    try {
      let encryptionKey = await SecureStore.getItemAsync('encryptionKey');
      
      if (!encryptionKey) {
        // Generate new encryption key
        encryptionKey = await Crypto.digestStringAsync(
          Crypto.CryptoDigestAlgorithm.SHA256,
          `${Date.now()}_${Math.random()}_${await Crypto.getRandomBytesAsync(32)}`
        );
        
        await SecureStore.setItemAsync('encryptionKey', encryptionKey);
      }
      
      this.config.encryptionKey = encryptionKey;
    } catch (error) {
      this.logger.error('Failed to initialize encryption', error);
      throw error;
    }
  }

  private async loadStoredToken(): Promise<void> {
    try {
      const tokenData = await SecureStore.getItemAsync('authToken');
      if (tokenData) {
        const token = JSON.parse(tokenData);
        
        // Check if token is still valid
        if (new Date(token.expiresAt) > new Date()) {
          this.currentToken = {
            ...token,
            expiresAt: new Date(token.expiresAt)
          };
        } else {
          // Token expired, try to refresh
          await this.refreshToken();
        }
      }
    } catch (error) {
      this.logger.warn('Failed to load stored token', error);
      await this.clearStoredToken();
    }
  }

  async authenticate(username: string, password: string): Promise<AuthToken> {
    try {
      // Check for account lockout
      await this.checkAccountLockout(username);
      
      // Hash password for comparison
      const passwordHash = await this.hashPassword(password);
      
      // Validate credentials (this would typically call your user service)
      const user = await this.validateCredentials(username, passwordHash);
      
      if (!user) {
        await this.recordFailedLogin(username);
        throw new Error('Invalid credentials');
      }

      // Clear failed login attempts
      await this.clearFailedLogins(username);
      
      // Generate tokens
      const token = await this.generateToken(user);
      
      // Store token securely
      await this.storeToken(token);
      
      this.currentToken = token;
      
      this.logger.info('User authenticated successfully', { 
        userId: user.id, 
        username: user.username 
      });
      
      return token;
    } catch (error) {
      this.logger.error('Authentication failed', error);
      throw error;
    }
  }

  private async checkAccountLockout(username: string): Promise<void> {
    const lockoutKey = `lockout_${username}`;
    const lockoutData = await AsyncStorage.getItem(lockoutKey);
    
    if (lockoutData) {
      const { lockedUntil } = JSON.parse(lockoutData);
      if (new Date() < new Date(lockedUntil)) {
        throw new Error('Account is temporarily locked due to too many failed login attempts');
      } else {
        // Lockout expired, clear it
        await AsyncStorage.removeItem(lockoutKey);
      }
    }
  }

  private async recordFailedLogin(username: string): Promise<void> {
    const attemptsKey = `attempts_${username}`;
    const attemptsData = await AsyncStorage.getItem(attemptsKey);
    
    let attempts = 1;
    if (attemptsData) {
      const { count, firstAttempt } = JSON.parse(attemptsData);
      attempts = count + 1;
    }
    
    await AsyncStorage.setItem(attemptsKey, JSON.stringify({
      count: attempts,
      firstAttempt: new Date().toISOString()
    }));
    
    // Lock account if max attempts reached
    if (attempts >= this.config.maxLoginAttempts) {
      const lockoutKey = `lockout_${username}`;
      const lockedUntil = new Date(Date.now() + this.config.lockoutDuration);
      
      await AsyncStorage.setItem(lockoutKey, JSON.stringify({
        lockedUntil: lockedUntil.toISOString()
      }));
      
      this.logger.warn('Account locked due to failed login attempts', { 
        username, 
        attempts 
      });
    }
  }

  private async clearFailedLogins(username: string): Promise<void> {
    const attemptsKey = `attempts_${username}`;
    await AsyncStorage.removeItem(attemptsKey);
  }

  private async validateCredentials(username: string, passwordHash: string): Promise<any> {
    // This would typically call your MongoDB service to validate user
    // For now, return a mock user for demonstration
    if (username === 'admin' && passwordHash) {
      return {
        id: 'user_admin',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['*'] // Admin has all permissions
      };
    }
    
    return null;
  }

  private async generateToken(user: any): Promise<AuthToken> {
    const tokenData = {
      userId: user.id,
      username: user.username,
      role: user.role,
      permissions: user.permissions,
      issuedAt: Date.now(),
      expiresAt: Date.now() + this.config.tokenExpiration
    };
    
    // Create token (in production, use proper JWT)
    const token = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      JSON.stringify(tokenData) + this.config.encryptionKey
    );
    
    const refreshTokenData = {
      userId: user.id,
      issuedAt: Date.now(),
      expiresAt: Date.now() + this.config.refreshTokenExpiration
    };
    
    const refreshToken = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      JSON.stringify(refreshTokenData) + this.config.encryptionKey
    );
    
    return {
      token,
      refreshToken,
      expiresAt: new Date(tokenData.expiresAt),
      userId: user.id,
      role: user.role,
      permissions: user.permissions
    };
  }

  private async storeToken(token: AuthToken): Promise<void> {
    try {
      await SecureStore.setItemAsync('authToken', JSON.stringify(token));
    } catch (error) {
      this.logger.error('Failed to store auth token', error);
      throw error;
    }
  }

  private async clearStoredToken(): Promise<void> {
    try {
      await SecureStore.deleteItemAsync('authToken');
      this.currentToken = null;
    } catch (error) {
      this.logger.warn('Failed to clear stored token', error);
    }
  }

  async refreshToken(): Promise<AuthToken | null> {
    try {
      if (!this.currentToken?.refreshToken) {
        return null;
      }
      
      // Validate refresh token (simplified)
      // In production, you'd validate against your backend
      
      // Generate new token
      const user = {
        id: this.currentToken.userId,
        role: this.currentToken.role,
        permissions: this.currentToken.permissions
      };
      
      const newToken = await this.generateToken(user);
      await this.storeToken(newToken);
      
      this.currentToken = newToken;
      
      this.logger.info('Token refreshed successfully', { userId: user.id });
      
      return newToken;
    } catch (error) {
      this.logger.error('Token refresh failed', error);
      await this.clearStoredToken();
      return null;
    }
  }

  async logout(): Promise<void> {
    try {
      await this.clearStoredToken();
      this.logger.info('User logged out successfully');
    } catch (error) {
      this.logger.error('Logout failed', error);
      throw error;
    }
  }

  async validateRequest(request: any): Promise<boolean> {
    try {
      // Basic request validation
      if (!request.headers) {
        return false;
      }
      
      // Check for suspicious patterns
      const suspiciousPatterns = [
        /script/i,
        /javascript/i,
        /eval\(/i,
        /exec\(/i,
        /<script/i
      ];
      
      const requestString = JSON.stringify(request);
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(requestString)) {
          this.logger.warn('Suspicious request detected', { 
            url: request.url,
            pattern: pattern.toString()
          });
          return false;
        }
      }
      
      return true;
    } catch (error) {
      this.logger.error('Request validation failed', error);
      return false;
    }
  }

  async verifyAuthentication(request: any): Promise<boolean> {
    try {
      const authHeader = request.headers?.authorization;
      if (!authHeader) {
        return false;
      }
      
      const token = authHeader.replace('Bearer ', '');
      
      // Verify token
      if (!this.currentToken || this.currentToken.token !== token) {
        return false;
      }
      
      // Check if token is expired
      if (new Date() > this.currentToken.expiresAt) {
        // Try to refresh token
        const refreshedToken = await this.refreshToken();
        return refreshedToken !== null;
      }
      
      return true;
    } catch (error) {
      this.logger.error('Authentication verification failed', error);
      return false;
    }
  }

  async hashPassword(password: string): Promise<string> {
    try {
      // Add salt to password
      const salt = await Crypto.getRandomBytesAsync(16);
      const saltedPassword = password + salt.toString();
      
      // Hash with SHA-256 (in production, use bcrypt or similar)
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        saltedPassword
      );
      
      return hash;
    } catch (error) {
      this.logger.error('Password hashing failed', error);
      throw error;
    }
  }

  async encryptData(data: string): Promise<string> {
    try {
      // Simple encryption (in production, use proper encryption library)
      const encrypted = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        data + this.config.encryptionKey
      );
      
      return encrypted;
    } catch (error) {
      this.logger.error('Data encryption failed', error);
      throw error;
    }
  }

  async decryptData(encryptedData: string): Promise<string> {
    try {
      // This is a simplified implementation
      // In production, use proper symmetric encryption
      return encryptedData;
    } catch (error) {
      this.logger.error('Data decryption failed', error);
      throw error;
    }
  }

  // Permission checking
  hasPermission(permission: string): boolean {
    if (!this.currentToken) {
      return false;
    }
    
    // Admin has all permissions
    if (this.currentToken.permissions.includes('*')) {
      return true;
    }
    
    return this.currentToken.permissions.includes(permission);
  }

  hasRole(role: string): boolean {
    return this.currentToken?.role === role;
  }

  // Getters
  get isAuthenticated(): boolean {
    return this.currentToken !== null && new Date() < this.currentToken.expiresAt;
  }

  get currentUser(): { userId: string; role: string; permissions: string[] } | null {
    if (!this.currentToken) {
      return null;
    }
    
    return {
      userId: this.currentToken.userId,
      role: this.currentToken.role,
      permissions: this.currentToken.permissions
    };
  }

  get authToken(): string | null {
    return this.currentToken?.token || null;
  }
}
