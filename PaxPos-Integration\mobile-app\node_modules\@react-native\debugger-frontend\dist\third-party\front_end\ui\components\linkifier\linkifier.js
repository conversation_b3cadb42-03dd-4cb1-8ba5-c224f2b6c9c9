import*as e from"../../../core/platform/platform.js";import*as t from"../../lit-html/lit-html.js";import*as i from"../render_coordinator/render_coordinator.js";import*as r from"../../../models/bindings/bindings.js";const n=new CSSStyleSheet;n.replaceSync(".link:link,\n.link:visited{color:var(--sys-color-primary);text-decoration:underline;cursor:pointer;outline-offset:2px}\n/*# sourceURL=linkifierImpl.css */\n");const o=i.RenderCoordinator.RenderCoordinator.instance();class s extends Event{data;static eventName="linkifieractivated";constructor(e){super(s.eventName,{bubbles:!0,composed:!0}),this.data=e,this.data=e}}class l extends HTMLElement{static litTagName=t.literal`devtools-linkifier`;#e=this.attachShadow({mode:"open"});#t=e.DevToolsPath.EmptyUrlString;#i;#r;#n;set data(e){if(this.#t=e.url,this.#i=e.lineNumber,this.#r=e.columnNumber,this.#n=e.linkText,!this.#t)throw new Error("Cannot construct a Linkifier without providing a valid string URL.");this.#o()}connectedCallback(){this.#e.adoptedStyleSheets=[n]}#s(e){e.preventDefault();const t=new s({url:this.#t,lineNumber:this.#i,columnNumber:this.#r});this.dispatchEvent(t)}async#o(){const e=this.#n??function(e,t){if(e){let i=`${r.ResourceUtils.displayNameForURL(e)}`;return void 0!==t&&(i+=`:${t+1}`),i}throw new Error("New linkifier component error: don't know how to generate link text for given arguments")}(this.#t,this.#i);await o.write((()=>{t.render(t.html`<a class="link" href=${this.#t} @click=${this.#s}><slot>${e}</slot></a>`,this.#e,{host:this})}))}}customElements.define("devtools-linkifier",l);var a=Object.freeze({__proto__:null,LinkifierClick:s,Linkifier:l});export{a as Linkifier};
