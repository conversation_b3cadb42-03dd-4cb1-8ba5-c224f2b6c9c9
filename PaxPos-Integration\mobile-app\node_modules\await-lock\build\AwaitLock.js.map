{"version": 3, "file": "AwaitLock.js", "sourceRoot": "", "sources": ["../src/AwaitLock.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;GAEG;AACH,MAAqB,SAAS;IAA9B;QACE,8BAAqB,KAAK,EAAC;QAC3B,sCAAqC,IAAI,GAAG,EAAE,EAAC;IAsFjD,CAAC;IApFC;;;OAGG;IACH,IAAI,QAAQ;QACV,OAAO,uBAAA,IAAI,2BAAU,CAAC;IACxB,CAAC;IAED;;;;;;;;;OASG;IACH,YAAY,CAAC,EAAE,OAAO,KAA2B,EAAE;QACjD,IAAI,CAAC,uBAAA,IAAI,2BAAU,EAAE;YACnB,uBAAA,IAAI,uBAAa,IAAI,MAAA,CAAC;YACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QAED,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC7B,uBAAA,IAAI,mCAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,QAAoB,CAAC;QACzB,IAAI,KAAoC,CAAC;QAEzC,OAAO,OAAO,CAAC,IAAI,CAAO;YACxB,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBACtB,QAAQ,GAAG,GAAG,EAAE;oBACd,YAAY,CAAC,KAAK,CAAC,CAAC;oBACpB,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC;gBACF,uBAAA,IAAI,mCAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACvC,CAAC,CAAC;YACF,IAAI,OAAO,CAAO,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBAC9B,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;oBACtB,uBAAA,IAAI,mCAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACxC,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;gBAClD,CAAC,EAAE,OAAO,CAAC,CAAC;YACd,CAAC,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,UAAU;QACR,IAAI,CAAC,uBAAA,IAAI,2BAAU,EAAE;YACnB,uBAAA,IAAI,uBAAa,IAAI,MAAA,CAAC;YACtB,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,OAAO;QACL,IAAI,CAAC,uBAAA,IAAI,2BAAU,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACtD;QAED,IAAI,uBAAA,IAAI,mCAAkB,CAAC,IAAI,GAAG,CAAC,EAAE;YACnC,6CAA6C;YAC7C,MAAM,CAAC,OAAO,CAAC,GAAG,uBAAA,IAAI,mCAAkB,CAAC;YACzC,uBAAA,IAAI,mCAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACvC,OAAO,EAAE,CAAC;SACX;aAAM;YACL,uBAAA,IAAI,uBAAa,KAAK,MAAA,CAAC;SACxB;IACH,CAAC;CACF;AAxFD,4BAwFC"}