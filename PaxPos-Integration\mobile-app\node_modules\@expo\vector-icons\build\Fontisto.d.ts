declare const iconSet: import("./createIconSet").Icon<"link" | "email" | "search" | "forward" | "info" | "check" | "close" | "question" | "pause" | "home" | "laptop" | "star" | "filter" | "save" | "picture" | "phone" | "paperclip" | "qrcode" | "eye" | "camera" | "windows" | "export" | "heart" | "calculator" | "chrome" | "github" | "upload" | "download" | "play" | "calendar" | "database" | "hourglass" | "key" | "flag" | "car" | "wallet" | "android" | "earth" | "wifi" | "google" | "amazon" | "codepen" | "dropbox" | "gitlab" | "skype" | "twitter" | "behance" | "dribbble" | "instagram" | "slack" | "500px" | "adjust" | "app-store" | "archive" | "arrow-down" | "arrow-left" | "arrow-right" | "arrow-up" | "baidu" | "bell" | "bookmark" | "bug" | "clock" | "code" | "compass" | "copy" | "credit-card" | "crop" | "eraser" | "facebook" | "flash" | "flickr" | "folder" | "foursquare" | "google-drive" | "google-play" | "houzz" | "keyboard" | "language" | "linkedin" | "magnet" | "map" | "medium" | "mic" | "mobile" | "onedrive" | "paper-plane" | "paypal" | "pinterest" | "print" | "rainbow" | "reply" | "rocket" | "rss" | "scissors" | "share" | "shield" | "shopping-bag" | "shopping-basket" | "soundcloud" | "spotify" | "stopwatch" | "stumbleupon" | "suitcase" | "swarm" | "tablet" | "thermometer" | "ticket" | "trash" | "tripadvisor" | "tumblr" | "tv" | "vimeo" | "vine" | "vk" | "yelp" | "comment" | "like" | "redo" | "spinner" | "undo" | "airplay" | "anchor" | "bar-chart" | "bold" | "columns" | "film" | "italic" | "power" | "sun" | "trello" | "truck" | "twitch" | "umbrella" | "underline" | "wind" | "volume-off" | "volume-down" | "volume-up" | "font" | "text-height" | "text-width" | "outdent" | "indent" | "map-marker" | "backward" | "stop" | "step-forward" | "eject" | "crosshairs" | "ban" | "asterisk" | "fire" | "plane" | "random" | "comments" | "star-half" | "strikethrough" | "google-plus" | "caret-down" | "caret-up" | "caret-left" | "caret-right" | "sitemap" | "paste" | "stethoscope" | "ambulance" | "angle-left" | "angle-right" | "angle-up" | "angle-down" | "desktop" | "quote-left" | "quote-right" | "superscript" | "subscript" | "maxcdn" | "html5" | "css3" | "euro" | "gbp" | "dollar" | "inr" | "yen" | "rouble" | "krw" | "bitcoin" | "youtube-play" | "stack-overflow" | "bitbucket" | "apple" | "linux" | "female" | "male" | "wheelchair" | "wordpress" | "yahoo" | "reddit" | "delicious" | "digg" | "joomla" | "paw" | "steam" | "recycle" | "automobile" | "taxi" | "deviantart" | "jsfiddle" | "circle-o-notch" | "git" | "hacker-news" | "history" | "paragraph" | "tty" | "google-wallet" | "at" | "area-chart" | "line-chart" | "toggle-off" | "toggle-on" | "bicycle" | "bus" | "ils" | "skyatlas" | "ship" | "user-secret" | "motorcycle" | "heartbeat" | "venus" | "mars" | "mercury" | "intersex" | "transgender" | "transgender-alt" | "venus-double" | "mars-double" | "venus-mars" | "mars-stroke" | "mars-stroke-v" | "mars-stroke-h" | "neuter" | "genderless" | "whatsapp" | "hotel" | "train" | "subway" | "opencart" | "battery-full" | "battery-three-quarters" | "battery-half" | "battery-quarter" | "battery-empty" | "hourglass-start" | "hourglass-half" | "hourglass-end" | "gg" | "odnoklassniki" | "safari" | "firefox" | "opera" | "internet-explorer" | "commenting" | "edge" | "usb" | "product-hunt" | "hashtag" | "bluetooth-b" | "blind" | "audio-description" | "braille" | "deaf" | "low-vision" | "snapchat" | "quora" | "telegram" | "podcast" | "imdb" | "meetup" | "ruby" | "ellipse" | "line" | "algolia" | "apple-pay" | "atlassian" | "aws" | "blogger" | "cloudflare" | "cpanel" | "dailymotion" | "deskpro" | "discord" | "discourse" | "ember" | "flipboard" | "grunt" | "gulp" | "invision" | "java" | "jenkins" | "jira" | "kickstarter" | "laravel" | "less" | "magento" | "microsoft" | "npm" | "periscope" | "php" | "playstation" | "python" | "raspberry-pi" | "react" | "sass" | "shopify" | "sourcetree" | "swift" | "uber" | "ubuntu" | "unity" | "viber" | "vuejs" | "wix" | "xbox" | "yandex-international" | "yandex" | "yarn" | "dizzy" | "hospital" | "lightbulb" | "snowflake" | "atom" | "cocktail" | "dna" | "helicopter" | "map-marker-alt" | "meteor" | "mobile-alt" | "passport" | "pills" | "prescription" | "tablet-alt" | "tablets" | "ticket-alt" | "volume-mute" | "bandage" | "person" | "tent" | "acrobat-reader" | "applemusic" | "bing" | "bower" | "disqus" | "graphql" | "hexo" | "hipchat" | "icq" | "jekyll" | "json" | "livestream" | "messenger" | "onenote" | "mongodb" | "netflix" | "nginx" | "origin" | "pingdom" | "rails" | "redis" | "redux" | "saucelabs" | "scorp" | "sentry" | "shazam" | "sinaweibo" | "slides" | "sublimetext" | "ted" | "tesla" | "tinder" | "treehouse" | "twoo" | "udacity" | "webstorm" | "adobe" | "babel" | "coffeescript" | "electronjs" | "mysql" | "oracle" | "unreal-engine" | "webpack" | "angelist" | "dockers" | "envato" | "stylus" | "travis" | "visual-studio" | "ampproject" | "angularjs" | "hangout" | "jquery" | "nodejs" | "svn" | "wetransfer" | "wifi-logo" | "wikipedia" | "cocoapods" | "composer" | "bell-alt" | "bookmark-alt" | "close-a" | "cursor" | "date" | "favorite" | "locked" | "minus-a" | "more-v-a" | "more-v" | "move-h-a" | "move-h" | "nav-icon-a" | "nav-icon-grid-a" | "nav-icon-grid" | "nav-icon-list-a" | "nav-icon-list" | "nav-icon" | "navigate" | "persons" | "plus-a" | "quote-a-left" | "quote-a-right" | "share-a" | "unlocked" | "world-o" | "world" | "zoom" | "pinboard" | "zoom-minus" | "zoom-plus" | "checkbox-active" | "checkbox-passive" | "radio-btn-active" | "radio-btn-passive" | "shopping-bag-1" | "shopping-barcode" | "shopping-basket-add" | "shopping-basket-remove" | "shopping-package" | "shopping-pos-machine" | "shopping-sale" | "shopping-store" | "angle-dobule-down" | "angle-dobule-left" | "angle-dobule-right" | "angle-dobule-up" | "arrow-down-l" | "arrow-expand" | "arrow-h" | "arrow-left-l" | "arrow-move" | "arrow-resize" | "arrow-return-left" | "arrow-return-right" | "arrow-right-l" | "arrow-swap" | "arrow-up-l" | "arrow-v" | "fi" | "fontisto" | "equalizer" | "headphone" | "music-note" | "play-list" | "player-settings" | "record" | "step-backwrad" | "broken-link" | "center-align" | "file-1" | "file-2" | "import" | "justify" | "left-align" | "link2" | "list-1" | "list-2" | "preview" | "print2" | "right-align" | "save-1" | "scissors2" | "table-1" | "table-2" | "cloud-down" | "cloud-refresh" | "cloud-up" | "cloudy-gusts" | "cloudy" | "day-cloudy" | "day-haze" | "day-lightning" | "day-rain" | "day-snow" | "day-sunny" | "fog" | "horizon-alt" | "horizon" | "lightning" | "lightnings" | "night-alt-cloudy" | "night-alt-lightning" | "night-alt-rain" | "night-alt-snow" | "night-clear" | "rain" | "rains" | "snow" | "snows" | "confused" | "expressionless" | "frowning" | "heart-eyes" | "laughing" | "mad" | "nervous" | "neutral" | "open-mouth" | "rage" | "slightly-smile" | "smiley" | "smiling" | "stuck-out-tongue" | "sunglasses" | "surprised" | "tongue" | "wink" | "zipper-mouth" | "aids" | "bed-patient" | "blood-drop" | "blood-test" | "blood" | "doctor" | "drug-pack" | "first-aid-alt" | "heart-alt" | "heartbeat-alt" | "helicopter-ambulance" | "injection-syringe" | "laboratory" | "nurse" | "nursing-home" | "paralysis-disability" | "pulse" | "surgical-knife" | "test-bottle" | "test-tube-alt" | "test-tube" | "thermometer-alt" | "american-express" | "iyzigo" | "mastercard" | "paypal-p" | "payu" | "troy" | "visa" | "dinners-club" | "discover" | "jcb" | "dislike" | "fa-american-sign-language-interpreting" | "universal-acces" | "pie-chart-1" | "pie-chart-2" | "tl" | "metro" | "yacht" | "beach-slipper" | "bus-ticket" | "compass-alt" | "direction-sign" | "do-not-disturb" | "flotation-ring" | "holiday-village" | "hot-air-balloon" | "hotel-alt" | "island" | "money-symbol" | "parasol" | "passport-alt" | "photograph" | "plane-ticket" | "room" | "sait-boat" | "snorkel" | "suitcase-alt" | "sunglasses-alt" | "swimsuit" | "train-ticket" | "propeller-1" | "propeller-2" | "propeller-3" | "propeller-4" | "spinner-cog" | "spinner-fidget" | "spinner-refresh" | "spinner-rotate-forward" | "snowflake-1" | "snowflake-2" | "snowflake-3" | "snowflake-4" | "snowflake-5" | "snowflake-6" | "snowflake-7" | "snowflake-8" | "curve" | "rectangle", "Fontisto">;
export default iconSet;
//# sourceMappingURL=Fontisto.d.ts.map