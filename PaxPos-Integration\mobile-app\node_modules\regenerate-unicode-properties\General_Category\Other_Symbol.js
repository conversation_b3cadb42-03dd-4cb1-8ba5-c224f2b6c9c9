const set = require('regenerate')(0xA6, 0xA9, 0xAE, 0xB0, 0x482, 0x6DE, 0x6E9, 0x7F6, 0x9FA, 0xB70, 0xBFA, 0xC7F, 0xD4F, 0xD79, 0xF13, 0xF34, 0xF36, 0xF38, 0x166D, 0x1940, 0x2114, 0x2125, 0x2127, 0x2129, 0x212E, 0x214A, 0x214F, 0x21D3, 0x3004, 0x3020, 0x31EF, 0x3250, 0xA839, 0xFDCF, 0xFFE4, 0xFFE8, 0x101A0, 0x10AC8, 0x1173F, 0x16B45, 0x1BC9C, 0x1D245, 0x1E14F, 0x1ECAC, 0x1ED2E, 0x1F7F0);
set.addRange(0x58D, 0x58E).addRange(0x60E, 0x60F).addRange(0x6FD, 0x6FE).addRange(0xBF3, 0xBF8).addRange(0xF01, 0xF03).addRange(0xF15, 0xF17).addRange(0xF1A, 0xF1F).addRange(0xFBE, 0xFC5).addRange(0xFC7, 0xFCC).addRange(0xFCE, 0xFCF).addRange(0xFD5, 0xFD8).addRange(0x109E, 0x109F).addRange(0x1390, 0x1399).addRange(0x19DE, 0x19FF).addRange(0x1B61, 0x1B6A).addRange(0x1B74, 0x1B7C).addRange(0x2100, 0x2101).addRange(0x2103, 0x2106).addRange(0x2108, 0x2109).addRange(0x2116, 0x2117).addRange(0x211E, 0x2123).addRange(0x213A, 0x213B).addRange(0x214C, 0x214D).addRange(0x218A, 0x218B).addRange(0x2195, 0x2199).addRange(0x219C, 0x219F).addRange(0x21A1, 0x21A2).addRange(0x21A4, 0x21A5).addRange(0x21A7, 0x21AD).addRange(0x21AF, 0x21CD).addRange(0x21D0, 0x21D1).addRange(0x21D5, 0x21F3).addRange(0x2300, 0x2307).addRange(0x230C, 0x231F).addRange(0x2322, 0x2328).addRange(0x232B, 0x237B).addRange(0x237D, 0x239A).addRange(0x23B4, 0x23DB).addRange(0x23E2, 0x2429).addRange(0x2440, 0x244A).addRange(0x249C, 0x24E9).addRange(0x2500, 0x25B6).addRange(0x25B8, 0x25C0).addRange(0x25C2, 0x25F7).addRange(0x2600, 0x266E).addRange(0x2670, 0x2767).addRange(0x2794, 0x27BF).addRange(0x2800, 0x28FF).addRange(0x2B00, 0x2B2F).addRange(0x2B45, 0x2B46).addRange(0x2B4D, 0x2B73);
set.addRange(0x2B76, 0x2B95).addRange(0x2B97, 0x2BFF).addRange(0x2CE5, 0x2CEA).addRange(0x2E50, 0x2E51).addRange(0x2E80, 0x2E99).addRange(0x2E9B, 0x2EF3).addRange(0x2F00, 0x2FD5).addRange(0x2FF0, 0x2FFF).addRange(0x3012, 0x3013).addRange(0x3036, 0x3037).addRange(0x303E, 0x303F).addRange(0x3190, 0x3191).addRange(0x3196, 0x319F).addRange(0x31C0, 0x31E5).addRange(0x3200, 0x321E).addRange(0x322A, 0x3247).addRange(0x3260, 0x327F).addRange(0x328A, 0x32B0).addRange(0x32C0, 0x33FF).addRange(0x4DC0, 0x4DFF).addRange(0xA490, 0xA4C6).addRange(0xA828, 0xA82B).addRange(0xA836, 0xA837).addRange(0xAA77, 0xAA79).addRange(0xFD40, 0xFD4F).addRange(0xFDFD, 0xFDFF).addRange(0xFFED, 0xFFEE).addRange(0xFFFC, 0xFFFD).addRange(0x10137, 0x1013F).addRange(0x10179, 0x10189).addRange(0x1018C, 0x1018E).addRange(0x10190, 0x1019C).addRange(0x101D0, 0x101FC).addRange(0x10877, 0x10878).addRange(0x11FD5, 0x11FDC).addRange(0x11FE1, 0x11FF1).addRange(0x16B3C, 0x16B3F).addRange(0x1CC00, 0x1CCEF).addRange(0x1CD00, 0x1CEB3).addRange(0x1CF50, 0x1CFC3).addRange(0x1D000, 0x1D0F5).addRange(0x1D100, 0x1D126).addRange(0x1D129, 0x1D164).addRange(0x1D16A, 0x1D16C).addRange(0x1D183, 0x1D184).addRange(0x1D18C, 0x1D1A9).addRange(0x1D1AE, 0x1D1EA).addRange(0x1D200, 0x1D241).addRange(0x1D300, 0x1D356).addRange(0x1D800, 0x1D9FF).addRange(0x1DA37, 0x1DA3A);
set.addRange(0x1DA6D, 0x1DA74).addRange(0x1DA76, 0x1DA83).addRange(0x1DA85, 0x1DA86).addRange(0x1F000, 0x1F02B).addRange(0x1F030, 0x1F093).addRange(0x1F0A0, 0x1F0AE).addRange(0x1F0B1, 0x1F0BF).addRange(0x1F0C1, 0x1F0CF).addRange(0x1F0D1, 0x1F0F5).addRange(0x1F10D, 0x1F1AD).addRange(0x1F1E6, 0x1F202).addRange(0x1F210, 0x1F23B).addRange(0x1F240, 0x1F248).addRange(0x1F250, 0x1F251).addRange(0x1F260, 0x1F265).addRange(0x1F300, 0x1F3FA).addRange(0x1F400, 0x1F6D7).addRange(0x1F6DC, 0x1F6EC).addRange(0x1F6F0, 0x1F6FC).addRange(0x1F700, 0x1F776).addRange(0x1F77B, 0x1F7D9).addRange(0x1F7E0, 0x1F7EB).addRange(0x1F800, 0x1F80B).addRange(0x1F810, 0x1F847).addRange(0x1F850, 0x1F859).addRange(0x1F860, 0x1F887).addRange(0x1F890, 0x1F8AD).addRange(0x1F8B0, 0x1F8BB).addRange(0x1F8C0, 0x1F8C1).addRange(0x1F900, 0x1FA53).addRange(0x1FA60, 0x1FA6D).addRange(0x1FA70, 0x1FA7C).addRange(0x1FA80, 0x1FA89).addRange(0x1FA8F, 0x1FAC6).addRange(0x1FACE, 0x1FADC).addRange(0x1FADF, 0x1FAE9).addRange(0x1FAF0, 0x1FAF8).addRange(0x1FB00, 0x1FB92).addRange(0x1FB94, 0x1FBEF);
exports.characters = set;
