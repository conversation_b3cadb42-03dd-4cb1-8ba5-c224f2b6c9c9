// Copyright 2022-present 650 Industries. All rights reserved.

/**
 Native equivalent of `Int8Array` in JavaScript, an array of two's-complement 8-bit signed integers.
 */
public final class Int8Array: GenericTypedArray<Int8> {}

/**
 Native equivalent of `Int16Array` in JavaScript, an array of two's-complement 16-bit signed integers.
 */
public final class Int16Array: GenericTypedArray<Int16> {}

/**
 Native equivalent of `Int32Array` in JavaScript, an array of two's-complement 32-bit signed integers.
 */
public final class Int32Array: GenericTypedArray<Int32> {}

/**
 Native equivalent of `Uint8Array` in JavaScript, an array of 8-bit unsigned integers.
 */
public final class Uint8Array: GenericTypedArray<UInt8> {}

/**
 Native equivalent of `Uint8ClampedArray` in JavaScript, an array of 8-bit unsigned integers clamped to 0-255.
 */
public final class Uint8ClampedArray: GenericTypedArray<UInt8> {}

/**
 Native equivalent of `Uint16Array` in JavaScript, an array of 16-bit unsigned integers.
 */
public final class Uint16Array: GenericTypedArray<UInt16> {}

/**
 Native equivalent of `Uint32Array` in JavaScript, an array of 32-bit unsigned integers.
 */
public final class Uint32Array: GenericTypedArray<UInt32> {}

/**
 Native equivalent of `Float32Array` in JavaScript, an array of 32-bit floating point numbers.
 */
public final class Float32Array: GenericTypedArray<Float32> {}

/**
 Native equivalent of `Float64Array` in JavaScript, an array of 64-bit floating point numbers.
 */
public final class Float64Array: GenericTypedArray<Float64> {}

/**
 Native equivalent of `BigInt64Array` in JavaScript, an array of 64-bit signed integers.
 */
public final class BigInt64Array: GenericTypedArray<Int64> {}

/**
 Native equivalent of `BigUint64Array` in JavaScript, an array of 64-bit unsigned integers.
 */
public final class BigUint64Array: GenericTypedArray<UInt64> {}
