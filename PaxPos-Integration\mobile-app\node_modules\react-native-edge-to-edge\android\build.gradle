buildscript {
    ext.safeExtGet = {prop, fallback ->
        rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
    }
    repositories {
        google()
        gradlePluginPortal()
    }
    dependencies {
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${safeExtGet("kotlinVersion", "1.8.0")}")
        classpath("com.android.tools.build:gradle:8.1.1")
    }
}

def isNewArchitectureEnabled() {
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

apply plugin: "com.android.library"
apply plugin: "kotlin-android"

if (isNewArchitectureEnabled()) {
    apply plugin: "com.facebook.react"
}

android {
    buildToolsVersion safeExtGet("buildToolsVersion", "34.0.0")
    compileSdkVersion safeExtGet("compileSdkVersion", 34)

    if (project.android.hasProperty("namespace")) {
        namespace "com.zoontek.rnedgetoedge"

        buildFeatures {
            buildConfig true
        }
        sourceSets {
            main {
                manifest.srcFile "src/main/AndroidManifestNew.xml"
            }
        }
    }
    defaultConfig {
        buildConfigField("boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString())
        minSdkVersion safeExtGet("minSdkVersion", 23)
        targetSdkVersion safeExtGet("targetSdkVersion", 34)
    }
    lintOptions {
        abortOnError false
    }
    sourceSets {
        main {
            if (isNewArchitectureEnabled()) {
                java.srcDirs += ["src/newarch"]
            } else {
                java.srcDirs += ["src/oldarch"]
            }
        }
    }
}

repositories {
    maven {
        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
        url("$rootDir/../node_modules/react-native/android")
    }
    mavenCentral()
    google()
}

dependencies {
    //noinspection GradleDynamicVersion
    implementation "com.facebook.react:react-native:+" // From node_modules
    implementation "com.google.android.material:material:${safeExtGet("materialVersion", "1.12.0")}"
}
