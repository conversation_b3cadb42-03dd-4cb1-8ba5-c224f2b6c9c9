buildscript {
  repositories {
    mavenCentral()
    google()
  }

  dependencies {
    classpath 'com.android.tools.build:gradle:4.1.1'
  }
}

apply plugin: 'com.android.library'

def getExtOrDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['EncryptedStorage_' + name]
}

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['EncryptedStorage_' + name]).toInteger()
}

android {
  compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')
  buildToolsVersion getExtOrDefault('buildToolsVersion')

  defaultConfig {
    minSdkVersion 21
    targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
    versionCode 50
    versionName "4.0.3"
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
  }

  buildTypes {
    release {
      minifyEnabled false
    }
  }
  lintOptions {
    abortOnError false
    disable 'GradleCompatible'
  }
  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }
}

repositories {
  mavenCentral()
  google()

  def found = false
  def defaultDir = null
  def androidSourcesName = 'React Native sources'

  if (rootProject.ext.has('reactNativeAndroidRoot')) {
    defaultDir = rootProject.ext.get('reactNativeAndroidRoot')
  } else {
    defaultDir = new File(
            projectDir,
            '/../../../node_modules/react-native/android'
    )
  }

  if (defaultDir.exists()) {
    maven {
      url defaultDir.toString()
      name androidSourcesName
    }

    logger.info(":${project.name}:reactNativeAndroidRoot ${defaultDir.canonicalPath}")
    found = true
  } else {
    def parentDir = rootProject.projectDir

    1.upto(5, {
      if (found) return true
      parentDir = parentDir.parentFile

      def androidSourcesDir = new File(
              parentDir,
              'node_modules/react-native'
      )

      def androidPrebuiltBinaryDir = new File(
              parentDir,
              'node_modules/react-native/android'
      )

      if (androidPrebuiltBinaryDir.exists()) {
        maven {
          url androidPrebuiltBinaryDir.toString()
          name androidSourcesName
        }

        logger.info(":${project.name}:reactNativeAndroidRoot ${androidPrebuiltBinaryDir.canonicalPath}")
        found = true
      } else if (androidSourcesDir.exists()) {
        maven {
          url androidSourcesDir.toString()
          name androidSourcesName
        }

        logger.info(":${project.name}:reactNativeAndroidRoot ${androidSourcesDir.canonicalPath}")
        found = true
      }
    })
  }

  if (!found) {
    throw new GradleException(
            "${project.name}: unable to locate React Native android sources. " +
                    "Ensure you have you installed React Native as a dependency in your project and try again."
    )
  }
}

dependencies {
  // noinspection GradleDynamicVersion
  api 'com.facebook.react:react-native:+'

  implementation "androidx.security:security-crypto:1.1.0-alpha03"

  testImplementation 'junit:junit:4.13.1'

  androidTestImplementation 'androidx.test.ext:junit:1.1.2'
  //noinspection GradleDependency
  androidTestImplementation 'org.mockito:mockito-android:3.4.6'
  androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
}
