{"version": 3, "names": ["NativeModules", "RNEncryptedStorage", "Error", "EncryptedStorage", "setItem", "key", "value", "cb", "then", "catch", "getItem", "removeItem", "clear"], "sources": ["EncryptedStorage.ts"], "sourcesContent": ["/* eslint-disable no-dupe-class-members */\n\nimport { NativeModules } from 'react-native';\nconst { RNEncryptedStorage } = NativeModules;\n\nif (!RNEncryptedStorage) {\n  throw new Error('RNEncryptedStorage is undefined');\n}\n\nexport type StorageErrorCallback = (error?: Error) => void;\nexport type StorageValueCallback = (error?: Error, value?: string) => void;\n\nexport default class EncryptedStorage {\n  /**\n   * Writes data to the disk, using SharedPreferences or KeyChain, depending on the platform.\n   * @param {string} key - A string that will be associated to the value for later retrieval.\n   * @param {string} value - The data to store.\n   */\n  static setItem(key: string, value: string): Promise<void>;\n\n  /**\n   * Writes data to the disk, using SharedPreferences or KeyChain, depending on the platform.\n   * @param {string} key - A string that will be associated to the value for later retrieval.\n   * @param {string} value - The data to store.\n   * @param {Function} cb - The function to call when the operation completes.\n   */\n  static setItem(key: string, value: string, cb: StorageErrorCallback): void;\n  static setItem(\n    key: string,\n    value: string,\n    cb?: StorageErrorCallback\n  ): void | Promise<void> {\n    if (cb) {\n      RNEncryptedStorage.setItem(key, value).then(cb).catch(cb);\n      return;\n    }\n\n    return RNEncryptedStorage.setItem(key, value);\n  }\n\n  /**\n   * Retrieves data from the disk, using SharedPreferences or KeyChain, depending on the platform and returns it as the specified type.\n   * @param {string} key - A string that is associated to a value.\n   */\n  static getItem(key: string): Promise<string | null>;\n\n  /**\n   * Retrieves data from the disk, using SharedPreferences or KeyChain, depending on the platform and returns it as the specified type.\n   * @param {string} key - A string that is associated to a value.\n   * @param {Function} cb - The function to call when the operation completes.\n   */\n  static getItem(key: string, cb: StorageValueCallback): void;\n  static getItem(\n    key: string,\n    cb?: StorageValueCallback\n  ): void | Promise<string | null> {\n    if (cb) {\n      RNEncryptedStorage.getItem(key).then(cb).catch(cb);\n      return;\n    }\n\n    return RNEncryptedStorage.getItem(key);\n  }\n\n  /**\n   * Deletes data from the disk, using SharedPreferences or KeyChain, depending on the platform.\n   * @param {string} key - A string that is associated to a value.\n   */\n  static removeItem(key: string): Promise<void>;\n\n  /**\n   * Deletes data from the disk, using SharedPreferences or KeyChain, depending on the platform.\n   * @param {string} key - A string that is associated to a value.\n   * @param {Function} cb - The function to call when the operation completes.\n   */\n  static removeItem(key: string, cb: StorageErrorCallback): void;\n  static removeItem(\n    key: string,\n    cb?: StorageErrorCallback\n  ): void | Promise<void> {\n    if (cb) {\n      RNEncryptedStorage.removeItem(key).then(cb).catch(cb);\n      return;\n    }\n\n    return RNEncryptedStorage.removeItem(key);\n  }\n\n  /**\n   * Clears all data from disk, using SharedPreferences or KeyChain, depending on the platform.\n   */\n  static clear(): Promise<void>;\n\n  /**\n   * Clears all data from disk, using SharedPreferences or KeyChain, depending on the platform.\n   * @param {Function} cb - The function to call when the operation completes.\n   */\n  static clear(cb: StorageErrorCallback): void;\n  static clear(cb?: StorageErrorCallback): void | Promise<void> {\n    if (cb) {\n      RNEncryptedStorage.clear().then(cb).catch(cb);\n      return;\n    }\n\n    return RNEncryptedStorage.clear();\n  }\n}\n"], "mappings": "AAAA;;AAEA,SAASA,aAAa,QAAQ,cAAc;AAC5C,MAAM;EAAEC;AAAmB,CAAC,GAAGD,aAAa;AAE5C,IAAI,CAACC,kBAAkB,EAAE;EACvB,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;AACpD;AAKA,eAAe,MAAMC,gBAAgB,CAAC;EACpC;AACF;AACA;AACA;AACA;;EAGE;AACF;AACA;AACA;AACA;AACA;;EAEE,OAAOC,OAAO,CACZC,GAAW,EACXC,KAAa,EACbC,EAAyB,EACH;IACtB,IAAIA,EAAE,EAAE;MACNN,kBAAkB,CAACG,OAAO,CAACC,GAAG,EAAEC,KAAK,CAAC,CAACE,IAAI,CAACD,EAAE,CAAC,CAACE,KAAK,CAACF,EAAE,CAAC;MACzD;IACF;IAEA,OAAON,kBAAkB,CAACG,OAAO,CAACC,GAAG,EAAEC,KAAK,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;;EASE,OAAOI,OAAO,CACZL,GAAW,EACXE,EAAyB,EACM;IAC/B,IAAIA,EAAE,EAAE;MACNN,kBAAkB,CAACS,OAAO,CAACL,GAAG,CAAC,CAACG,IAAI,CAACD,EAAE,CAAC,CAACE,KAAK,CAACF,EAAE,CAAC;MAClD;IACF;IAEA,OAAON,kBAAkB,CAACS,OAAO,CAACL,GAAG,CAAC;EACxC;;EAEA;AACF;AACA;AACA;;EASE,OAAOM,UAAU,CACfN,GAAW,EACXE,EAAyB,EACH;IACtB,IAAIA,EAAE,EAAE;MACNN,kBAAkB,CAACU,UAAU,CAACN,GAAG,CAAC,CAACG,IAAI,CAACD,EAAE,CAAC,CAACE,KAAK,CAACF,EAAE,CAAC;MACrD;IACF;IAEA,OAAON,kBAAkB,CAACU,UAAU,CAACN,GAAG,CAAC;EAC3C;;EAEA;AACF;AACA;;EAQE,OAAOO,KAAK,CAACL,EAAyB,EAAwB;IAC5D,IAAIA,EAAE,EAAE;MACNN,kBAAkB,CAACW,KAAK,EAAE,CAACJ,IAAI,CAACD,EAAE,CAAC,CAACE,KAAK,CAACF,EAAE,CAAC;MAC7C;IACF;IAEA,OAAON,kBAAkB,CAACW,KAAK,EAAE;EACnC;AACF"}