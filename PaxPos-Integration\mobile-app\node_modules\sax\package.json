{"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "version": "1.4.1", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov -j4", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": "git://github.com/isaacs/sax-js.git", "files": ["lib/sax.js", "LICENSE", "README.md"], "devDependencies": {"tap": "^15.1.6"}, "tap": {"statements": 79, "branches": 75, "functions": 80, "lines": 79}}