import * as SQLite from 'expo-sqlite';
import { LoggingService } from './LoggingService';

export interface Transaction {
  id: string;
  amount: number;
  transType: 'SALE' | 'REFUND' | 'VOID' | 'AUTH';
  tenderType: 'CREDIT' | 'DEBIT' | 'CASH' | 'CHECK';
  status: 'success' | 'failed' | 'pending' | 'voided';
  authCode?: string;
  referenceNumber?: string;
  resultCode?: string;
  receiptData?: string;
  customerData?: string;
  metadata?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InventoryItem {
  id: string;
  sku: string;
  name: string;
  description?: string;
  price: number;
  cost: number;
  quantity: number;
  category: string;
  barcode?: string;
  imageUrl?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'manager' | 'cashier';
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SystemLog {
  id: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  category: string;
  metadata?: string;
  timestamp: string;
}

export class DatabaseService {
  private db: SQLite.WebSQLDatabase | null = null;
  private logger: LoggingService;

  constructor() {
    this.logger = new LoggingService();
  }

  async initialize(): Promise<void> {
    try {
      this.db = SQLite.openDatabase('pax_pos.db');
      await this.createTables();
      await this.seedInitialData();
      this.logger.info('Database initialized successfully');
    } catch (error) {
      this.logger.error('Database initialization failed', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const tables = [
      // Transactions table
      `CREATE TABLE IF NOT EXISTS transactions (
        id TEXT PRIMARY KEY,
        amount REAL NOT NULL,
        transType TEXT NOT NULL,
        tenderType TEXT NOT NULL,
        status TEXT NOT NULL,
        authCode TEXT,
        referenceNumber TEXT,
        resultCode TEXT,
        receiptData TEXT,
        customerData TEXT,
        metadata TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Inventory table
      `CREATE TABLE IF NOT EXISTS inventory (
        id TEXT PRIMARY KEY,
        sku TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        quantity INTEGER NOT NULL,
        category TEXT NOT NULL,
        barcode TEXT,
        imageUrl TEXT,
        isActive INTEGER DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        passwordHash TEXT NOT NULL,
        role TEXT NOT NULL,
        isActive INTEGER DEFAULT 1,
        lastLogin TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // System logs table
      `CREATE TABLE IF NOT EXISTS system_logs (
        id TEXT PRIMARY KEY,
        level TEXT NOT NULL,
        message TEXT NOT NULL,
        category TEXT NOT NULL,
        metadata TEXT,
        timestamp TEXT NOT NULL
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        category TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Sales table (for reporting)
      `CREATE TABLE IF NOT EXISTS sales (
        id TEXT PRIMARY KEY,
        transactionId TEXT NOT NULL,
        itemId TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unitPrice REAL NOT NULL,
        totalPrice REAL NOT NULL,
        discount REAL DEFAULT 0,
        tax REAL DEFAULT 0,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (transactionId) REFERENCES transactions (id),
        FOREIGN KEY (itemId) REFERENCES inventory (id)
      )`
    ];

    for (const tableSQL of tables) {
      await this.executeSQL(tableSQL);
    }

    // Create indexes for better performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions (createdAt)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions (status)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_sku ON inventory (sku)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory (category)',
      'CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON system_logs (timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_logs_level ON system_logs (level)',
      'CREATE INDEX IF NOT EXISTS idx_sales_transaction ON sales (transactionId)',
      'CREATE INDEX IF NOT EXISTS idx_sales_item ON sales (itemId)'
    ];

    for (const indexSQL of indexes) {
      await this.executeSQL(indexSQL);
    }
  }

  private async seedInitialData(): Promise<void> {
    // Check if data already exists
    const userCount = await this.count('users');
    if (userCount > 0) return;

    // Create default admin user
    await this.createUser({
      id: 'admin-001',
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: '$2b$10$encrypted_password_hash', // In production, properly hash passwords
      role: 'admin',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    // Create sample inventory items
    const sampleItems = [
      {
        id: 'item-001',
        sku: 'COFFEE-001',
        name: 'Coffee - Regular',
        description: 'Regular coffee, 12oz',
        price: 2.99,
        cost: 0.50,
        quantity: 100,
        category: 'Beverages',
        barcode: '123456789012',
        isActive: true
      },
      {
        id: 'item-002',
        sku: 'SANDWICH-001',
        name: 'Turkey Sandwich',
        description: 'Turkey sandwich with lettuce and tomato',
        price: 8.99,
        cost: 3.50,
        quantity: 50,
        category: 'Food',
        barcode: '123456789013',
        isActive: true
      }
    ];

    for (const item of sampleItems) {
      await this.createInventoryItem({
        ...item,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    // Create default settings
    const defaultSettings = [
      { key: 'pax_terminal_ip', value: '*************', category: 'terminal' },
      { key: 'pax_terminal_port', value: '8080', category: 'terminal' },
      { key: 'receipt_printer_enabled', value: 'true', category: 'printing' },
      { key: 'tax_rate', value: '0.08', category: 'business' },
      { key: 'currency', value: 'USD', category: 'business' },
      { key: 'business_name', value: 'PAX POS Store', category: 'business' },
      { key: 'business_address', value: '123 Main St, City, State 12345', category: 'business' }
    ];

    for (const setting of defaultSettings) {
      await this.setSetting(setting.key, setting.value, setting.category);
    }
  }

  // Transaction operations
  async createTransaction(transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<Transaction> {
    const id = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();
    
    const newTransaction: Transaction = {
      ...transaction,
      id,
      createdAt: now,
      updatedAt: now
    };

    const sql = `INSERT INTO transactions (
      id, amount, transType, tenderType, status, authCode, referenceNumber, 
      resultCode, receiptData, customerData, metadata, createdAt, updatedAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    await this.executeSQL(sql, [
      newTransaction.id,
      newTransaction.amount,
      newTransaction.transType,
      newTransaction.tenderType,
      newTransaction.status,
      newTransaction.authCode,
      newTransaction.referenceNumber,
      newTransaction.resultCode,
      newTransaction.receiptData,
      newTransaction.customerData,
      newTransaction.metadata,
      newTransaction.createdAt,
      newTransaction.updatedAt
    ]);

    return newTransaction;
  }

  async getTransactions(limit: number = 50, offset: number = 0): Promise<Transaction[]> {
    const sql = `SELECT * FROM transactions ORDER BY createdAt DESC LIMIT ? OFFSET ?`;
    const result = await this.executeSQL(sql, [limit, offset]);
    return result.rows._array;
  }

  async getTransaction(id: string): Promise<Transaction | null> {
    const sql = `SELECT * FROM transactions WHERE id = ?`;
    const result = await this.executeSQL(sql, [id]);
    return result.rows._array[0] || null;
  }

  async updateTransaction(id: string, updates: Partial<Transaction>): Promise<boolean> {
    const fields = Object.keys(updates).filter(key => key !== 'id' && key !== 'createdAt');
    const values = fields.map(field => updates[field as keyof Transaction]);
    
    const sql = `UPDATE transactions SET ${fields.map(field => `${field} = ?`).join(', ')}, updatedAt = ? WHERE id = ?`;
    
    await this.executeSQL(sql, [...values, new Date().toISOString(), id]);
    return true;
  }

  // Inventory operations
  async createInventoryItem(item: Omit<InventoryItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<InventoryItem> {
    const id = `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();
    
    const newItem: InventoryItem = {
      ...item,
      id,
      createdAt: now,
      updatedAt: now
    };

    const sql = `INSERT INTO inventory (
      id, sku, name, description, price, cost, quantity, category, 
      barcode, imageUrl, isActive, createdAt, updatedAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    await this.executeSQL(sql, [
      newItem.id, newItem.sku, newItem.name, newItem.description,
      newItem.price, newItem.cost, newItem.quantity, newItem.category,
      newItem.barcode, newItem.imageUrl, newItem.isActive ? 1 : 0,
      newItem.createdAt, newItem.updatedAt
    ]);

    return newItem;
  }

  async getInventory(category?: string): Promise<InventoryItem[]> {
    let sql = `SELECT * FROM inventory WHERE isActive = 1`;
    const params: any[] = [];
    
    if (category) {
      sql += ` AND category = ?`;
      params.push(category);
    }
    
    sql += ` ORDER BY name`;
    
    const result = await this.executeSQL(sql, params);
    return result.rows._array.map(item => ({
      ...item,
      isActive: Boolean(item.isActive)
    }));
  }

  // User operations
  async createUser(user: Omit<User, 'createdAt' | 'updatedAt'> & { passwordHash: string }): Promise<User> {
    const now = new Date().toISOString();
    
    const sql = `INSERT INTO users (
      id, username, email, passwordHash, role, isActive, lastLogin, createdAt, updatedAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    await this.executeSQL(sql, [
      user.id, user.username, user.email, user.passwordHash,
      user.role, user.isActive ? 1 : 0, user.lastLogin, now, now
    ]);

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      createdAt: now,
      updatedAt: now
    };
  }

  async getUserByUsername(username: string): Promise<(User & { passwordHash: string }) | null> {
    const sql = `SELECT * FROM users WHERE username = ? AND isActive = 1`;
    const result = await this.executeSQL(sql, [username]);
    const user = result.rows._array[0];
    
    if (user) {
      return {
        ...user,
        isActive: Boolean(user.isActive)
      };
    }
    
    return null;
  }

  // Settings operations
  async setSetting(key: string, value: string, category: string): Promise<void> {
    const sql = `INSERT OR REPLACE INTO settings (key, value, category, updatedAt) VALUES (?, ?, ?, ?)`;
    await this.executeSQL(sql, [key, value, category, new Date().toISOString()]);
  }

  async getSetting(key: string): Promise<string | null> {
    const sql = `SELECT value FROM settings WHERE key = ?`;
    const result = await this.executeSQL(sql, [key]);
    return result.rows._array[0]?.value || null;
  }

  async getSettings(category?: string): Promise<Record<string, string>> {
    let sql = `SELECT key, value FROM settings`;
    const params: any[] = [];
    
    if (category) {
      sql += ` WHERE category = ?`;
      params.push(category);
    }
    
    const result = await this.executeSQL(sql, params);
    const settings: Record<string, string> = {};
    
    result.rows._array.forEach(row => {
      settings[row.key] = row.value;
    });
    
    return settings;
  }

  // Logging operations
  async addLog(log: Omit<SystemLog, 'id' | 'timestamp'>): Promise<void> {
    const id = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = new Date().toISOString();
    
    const sql = `INSERT INTO system_logs (id, level, message, category, metadata, timestamp) VALUES (?, ?, ?, ?, ?, ?)`;
    await this.executeSQL(sql, [id, log.level, log.message, log.category, log.metadata, timestamp]);
  }

  async getLogs(level?: string, limit: number = 100): Promise<SystemLog[]> {
    let sql = `SELECT * FROM system_logs`;
    const params: any[] = [];
    
    if (level) {
      sql += ` WHERE level = ?`;
      params.push(level);
    }
    
    sql += ` ORDER BY timestamp DESC LIMIT ?`;
    params.push(limit);
    
    const result = await this.executeSQL(sql, params);
    return result.rows._array;
  }

  // Utility methods
  private async executeSQL(sql: string, params: any[] = []): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      this.db.transaction(tx => {
        tx.executeSql(
          sql,
          params,
          (_, result) => resolve(result),
          (_, error) => {
            this.logger.error('SQL execution error', { sql, params, error });
            reject(error);
            return false;
          }
        );
      });
    });
  }

  private async count(table: string): Promise<number> {
    const result = await this.executeSQL(`SELECT COUNT(*) as count FROM ${table}`);
    return result.rows._array[0].count;
  }

  async close(): Promise<void> {
    if (this.db) {
      // SQLite databases are automatically closed when the app terminates
      this.db = null;
      this.logger.info('Database connection closed');
    }
  }
}
