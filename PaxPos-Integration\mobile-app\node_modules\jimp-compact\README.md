<div align="center">
  <img width="200" height="200"
    src="https://s3.amazonaws.com/pix.iemoji.com/images/emoji/apple/ios-11/256/crayon.png">
  <h1><PERSON><PERSON> Compact</h1>
  <p>Compact Version of <a href="https://github.com/oliver-moran/jimp">Jim<PERSON></a></p>
</div>

[![automated](https://flat.badgen.net/badge/publish/automated/green)](#)
[![circle ci](https://flat.badgen.net/circleci/github/nuxt-community/jimp-compact)](https://circleci.com/gh/nuxt-community/jimp-compact)
[![npm version](https://flat.badgen.net/npm/v/jimp-compact)](https://www.npmjs.com/package/jimp-compact)
[![npm downloads](https://flat.badgen.net/npm/dt/jimp-compact)](https://www.npmjs.com/package/jimp-compact)
[![install size](https://flat.badgen.net/packagephobia/install/jimp-compact)](https://packagephobia.now.sh/result?p=jimp-compact)

🔥 Compiled using [zeit/ncc](https://github.com/zeit/ncc)

## Why?

This package has **32x** smaller install size with **all** features of original jimp (Jimp install size is [~16.3MB](https://packagephobia.now.sh/result?p=jimp))


## Usage

Install and import/require `jimp-compact` instead of `jimp` NPM package.

See [jimp docs](https://github.com/oliver-moran/jimp/tree/master/packages/jimp) for full usage.

## License

MIT - Based on [Jimp](https://github.com/oliver-moran/jimp/blob/master/LICENSE)
