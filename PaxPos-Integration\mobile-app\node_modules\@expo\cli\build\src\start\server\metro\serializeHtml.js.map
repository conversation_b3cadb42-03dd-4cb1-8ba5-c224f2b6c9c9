{"version": 3, "sources": ["../../../../../src/start/server/metro/serializeHtml.ts"], "sourcesContent": ["import { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport { RouteNode } from 'expo-router/build/Route';\n\nconst debug = require('debug')('expo:metro:html') as typeof console.log;\n\nexport function serializeHtmlWithAssets({\n  resources,\n  template,\n  devBundleUrl,\n  baseUrl,\n  route,\n  isExporting,\n  hydrate,\n}: {\n  resources: SerialAsset[];\n  template: string;\n  /** asset prefix used for deploying to non-standard origins like GitHub pages. */\n  baseUrl: string;\n  devBundleUrl?: string;\n  route?: RouteNode;\n  isExporting: boolean;\n  hydrate?: boolean;\n}): string {\n  if (!resources) {\n    return '';\n  }\n  return htmlFromSerialAssets(resources, {\n    isExporting,\n    template,\n    baseUrl,\n    bundleUrl: isExporting ? undefined : devBundleUrl,\n    route,\n    hydrate,\n  });\n}\n\n/**\n * Combine the path segments of a URL.\n * This filters out empty segments and avoids duplicate slashes when joining.\n * If base url is empty, it will be treated as a root path, adding `/` to the beginning.\n */\nfunction combineUrlPath(baseUrl: string, ...segments: string[]) {\n  return [baseUrl || '/', ...segments]\n    .filter(Boolean)\n    .map((segment, index) => {\n      const segmentIsBaseUrl = index === 0;\n      // Do not remove leading slashes from baseUrl\n      return segment.replace(segmentIsBaseUrl ? /\\/+$/g : /^\\/+|\\/+$/g, '');\n    })\n    .join('/');\n}\n\nfunction htmlFromSerialAssets(\n  assets: SerialAsset[],\n  {\n    isExporting,\n    template,\n    baseUrl,\n    bundleUrl,\n    route,\n    hydrate,\n  }: {\n    isExporting: boolean;\n    template: string;\n    baseUrl: string;\n    /** This is dev-only. */\n    bundleUrl?: string;\n    route?: RouteNode;\n    hydrate?: boolean;\n  }\n) {\n  // Combine the CSS modules into tags that have hot refresh data attributes.\n  const styleString = assets\n    .filter((asset) => asset.type.startsWith('css'))\n    .map(({ type, metadata, filename, source }) => {\n      if (type === 'css') {\n        if (isExporting) {\n          return [\n            `<link rel=\"preload\" href=\"${combineUrlPath(baseUrl, filename)}\" as=\"style\">`,\n            `<link rel=\"stylesheet\" href=\"${combineUrlPath(baseUrl, filename)}\">`,\n          ].join('');\n        } else {\n          return `<style data-expo-css-hmr=\"${metadata.hmrId}\">` + source + '\\n</style>';\n        }\n      }\n      // External link tags will be passed through as-is.\n      return source;\n    })\n    .join('');\n\n  const jsAssets = assets.filter((asset) => asset.type === 'js');\n\n  const scripts = bundleUrl\n    ? `<script src=\"${bundleUrl}\" defer></script>`\n    : jsAssets\n        .map(({ filename, metadata }) => {\n          // TODO: Mark dependencies of the HTML and include them to prevent waterfalls.\n          if (metadata.isAsync) {\n            // We have the data required to match async chunks to the route's HTML file.\n            if (\n              route?.entryPoints &&\n              metadata.modulePaths &&\n              Array.isArray(route.entryPoints) &&\n              Array.isArray(metadata.modulePaths)\n            ) {\n              // TODO: Handle module IDs like `expo-router/build/views/Unmatched.js`\n              const doesAsyncChunkContainRouteEntryPoint = route.entryPoints.some((entryPoint) =>\n                (metadata.modulePaths as string[]).includes(entryPoint)\n              );\n              if (!doesAsyncChunkContainRouteEntryPoint) {\n                return '';\n              }\n              debug('Linking async chunk %s to HTML for route %s', filename, route.contextKey);\n              // Pass through to the next condition.\n            } else {\n              return '';\n            }\n            // Mark async chunks as defer so they don't block the page load.\n            // return `<script src=\"${combineUrlPath(baseUrl, filename)\" defer></script>`;\n          }\n\n          return `<script src=\"${combineUrlPath(baseUrl, filename)}\" defer></script>`;\n        })\n        .join('');\n\n  if (hydrate) {\n    const hydrateScript = `<script type=\"module\">globalThis.__EXPO_ROUTER_HYDRATE__=true;</script>`;\n    template = template.replace('</head>', `${hydrateScript}</head>`);\n  }\n\n  return template\n    .replace('</head>', `${styleString}</head>`)\n    .replace('</body>', `${scripts}\\n</body>`);\n}\n"], "names": ["serializeHtmlWithAssets", "debug", "require", "resources", "template", "devBundleUrl", "baseUrl", "route", "isExporting", "hydrate", "htmlFromSerialAssets", "bundleUrl", "undefined", "combineUrlPath", "segments", "filter", "Boolean", "map", "segment", "index", "segmentIsBaseUrl", "replace", "join", "assets", "styleString", "asset", "type", "startsWith", "metadata", "filename", "source", "hmrId", "jsAssets", "scripts", "isAsync", "entryPoints", "modulePaths", "Array", "isArray", "doesAsyncChunkContainRouteEntryPoint", "some", "entryPoint", "includes", "<PERSON><PERSON>ey", "hydrateScript"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;AAFhB,MAAMC,QAAQC,QAAQ,SAAS;AAExB,SAASF,wBAAwB,EACtCG,SAAS,EACTC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,WAAW,EACXC,OAAO,EAUR;IACC,IAAI,CAACN,WAAW;QACd,OAAO;IACT;IACA,OAAOO,qBAAqBP,WAAW;QACrCK;QACAJ;QACAE;QACAK,WAAWH,cAAcI,YAAYP;QACrCE;QACAE;IACF;AACF;AAEA;;;;CAIC,GACD,SAASI,eAAeP,OAAe,EAAE,GAAGQ,QAAkB;IAC5D,OAAO;QAACR,WAAW;WAAQQ;KAAS,CACjCC,MAAM,CAACC,SACPC,GAAG,CAAC,CAACC,SAASC;QACb,MAAMC,mBAAmBD,UAAU;QACnC,6CAA6C;QAC7C,OAAOD,QAAQG,OAAO,CAACD,mBAAmB,UAAU,cAAc;IACpE,GACCE,IAAI,CAAC;AACV;AAEA,SAASZ,qBACPa,MAAqB,EACrB,EACEf,WAAW,EACXJ,QAAQ,EACRE,OAAO,EACPK,SAAS,EACTJ,KAAK,EACLE,OAAO,EASR;IAED,2EAA2E;IAC3E,MAAMe,cAAcD,OACjBR,MAAM,CAAC,CAACU,QAAUA,MAAMC,IAAI,CAACC,UAAU,CAAC,QACxCV,GAAG,CAAC,CAAC,EAAES,IAAI,EAAEE,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAE;QACxC,IAAIJ,SAAS,OAAO;YAClB,IAAIlB,aAAa;gBACf,OAAO;oBACL,CAAC,0BAA0B,EAAEK,eAAeP,SAASuB,UAAU,aAAa,CAAC;oBAC7E,CAAC,6BAA6B,EAAEhB,eAAeP,SAASuB,UAAU,EAAE,CAAC;iBACtE,CAACP,IAAI,CAAC;YACT,OAAO;gBACL,OAAO,CAAC,0BAA0B,EAAEM,SAASG,KAAK,CAAC,EAAE,CAAC,GAAGD,SAAS;YACpE;QACF;QACA,mDAAmD;QACnD,OAAOA;IACT,GACCR,IAAI,CAAC;IAER,MAAMU,WAAWT,OAAOR,MAAM,CAAC,CAACU,QAAUA,MAAMC,IAAI,KAAK;IAEzD,MAAMO,UAAUtB,YACZ,CAAC,aAAa,EAAEA,UAAU,iBAAiB,CAAC,GAC5CqB,SACGf,GAAG,CAAC,CAAC,EAAEY,QAAQ,EAAED,QAAQ,EAAE;QAC1B,8EAA8E;QAC9E,IAAIA,SAASM,OAAO,EAAE;YACpB,4EAA4E;YAC5E,IACE3B,CAAAA,yBAAAA,MAAO4B,WAAW,KAClBP,SAASQ,WAAW,IACpBC,MAAMC,OAAO,CAAC/B,MAAM4B,WAAW,KAC/BE,MAAMC,OAAO,CAACV,SAASQ,WAAW,GAClC;gBACA,sEAAsE;gBACtE,MAAMG,uCAAuChC,MAAM4B,WAAW,CAACK,IAAI,CAAC,CAACC,aACnE,AAACb,SAASQ,WAAW,CAAcM,QAAQ,CAACD;gBAE9C,IAAI,CAACF,sCAAsC;oBACzC,OAAO;gBACT;gBACAtC,MAAM,+CAA+C4B,UAAUtB,MAAMoC,UAAU;YAC/E,sCAAsC;YACxC,OAAO;gBACL,OAAO;YACT;QACA,gEAAgE;QAChE,8EAA8E;QAChF;QAEA,OAAO,CAAC,aAAa,EAAE9B,eAAeP,SAASuB,UAAU,iBAAiB,CAAC;IAC7E,GACCP,IAAI,CAAC;IAEZ,IAAIb,SAAS;QACX,MAAMmC,gBAAgB,CAAC,uEAAuE,CAAC;QAC/FxC,WAAWA,SAASiB,OAAO,CAAC,WAAW,GAAGuB,cAAc,OAAO,CAAC;IAClE;IAEA,OAAOxC,SACJiB,OAAO,CAAC,WAAW,GAAGG,YAAY,OAAO,CAAC,EAC1CH,OAAO,CAAC,WAAW,GAAGY,QAAQ,SAAS,CAAC;AAC7C"}