import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/legacy.js";import*as i from"../../core/sdk/sdk.js";import*as n from"../../third_party/react-devtools/react-devtools.js";import*as o from"../../core/common/common.js";import*as s from"../../models/workspace/workspace.js";import*as r from"../../models/bindings/bindings.js";import*as a from"../../models/logs/logs.js";import*as l from"../../core/host/host.js";import*as d from"../../models/react_native/react_native.js";class c extends i.SDKModel.SDKModel{static FUSEBOX_BINDING_NAMESPACE="react-devtools";#e;#t;#i=new Set;#n=!1;#o=!1;#s=null;#r=null;constructor(e){super(e),this.#e={listen:e=>(this.#i.add(e),()=>{this.#i.delete(e)}),send:(e,t)=>{this.#a({event:e,payload:t})}};const t=e.model(d.ReactDevToolsBindingsModel.ReactDevToolsBindingsModel);if(null===t)throw new Error("Failed to construct ReactDevToolsModel: ReactDevToolsBindingsModel was null");this.#t=t,t.addEventListener("BackendExecutionContextCreated",this.#l,this),t.addEventListener("BackendExecutionContextUnavailable",this.#d,this),t.addEventListener("BackendExecutionContextDestroyed",this.#c,this),window.addEventListener("beforeunload",this.#h)}dispose(){this.#s?.removeListener("reloadAppForProfiling",this.#m),this.#s?.shutdown(),this.#t.removeEventListener("BackendExecutionContextCreated",this.#l,this),this.#t.removeEventListener("BackendExecutionContextUnavailable",this.#d,this),this.#t.removeEventListener("BackendExecutionContextDestroyed",this.#c,this),window.removeEventListener("beforeunload",this.#h),this.#s=null,this.#r=null,this.#i.clear()}ensureInitialized(){this.#n||(this.#n=!0,this.#u())}async#u(){try{const e=this.#t;await e.enable(),e.subscribeToDomainMessages(c.FUSEBOX_BINDING_NAMESPACE,(e=>this.#g(e))),await e.initializeDomain(c.FUSEBOX_BINDING_NAMESPACE),this.#o=!0,this.#p()}catch(e){this.dispatchEventToListeners("InitializationFailed",e.message)}}isInitialized(){return this.#o}getBridgeOrThrow(){if(null===this.#s)throw new Error("Failed to get bridge from ReactDevToolsModel: bridge was null");return this.#s}getStoreOrThrow(){if(null===this.#r)throw new Error("Failed to get store from ReactDevToolsModel: store was null");return this.#r}#g(e){if(e)for(const t of this.#i)t(e)}async#a(e){const t=this.#t;if(!t)throw new Error("Failed to send message from ReactDevToolsModel: ReactDevToolsBindingsModel was null");return t.sendMessage(c.FUSEBOX_BINDING_NAMESPACE,e)}#h=()=>{this.#s?.shutdown()};#l(){const e=this.#t;if(!e)throw new Error("ReactDevToolsModel failed to handle BackendExecutionContextCreated event: ReactDevToolsBindingsModel was null");e.isEnabled()?this.#p():this.ensureInitialized()}#p(){this.#s=n.createBridge(this.#e),this.#r=n.createStore(this.#s,{supportsReloadAndProfile:!0}),this.#s.addListener("reloadAppForProfiling",this.#m),this.dispatchEventToListeners("InitializationCompleted")}#m(){const e=i.TargetManager.TargetManager.instance().primaryPageTarget();e?.pageAgent().invoke_reload({ignoreCache:!0})}#d({data:e}){this.dispatchEventToListeners("InitializationFailed",e)}#c(){this.#s?.shutdown(),this.#s=null,this.#r=null,this.#i.clear(),this.dispatchEventToListeners("Destroyed")}}i.SDKModel.SDKModel.register(c,{capabilities:4,autostart:!1});var h=Object.freeze({__proto__:null,ReactDevToolsModel:c});const m={sendFeedback:"[FB-only] Send feedback"},u=e.i18n.registerUIStrings("panels/react_devtools/ReactDevToolsViewBase.ts",m),g=e.i18n.getLocalizedString.bind(void 0,u);function p(e,t){const{sourceURL:i,line:n,column:l}=t||e;!async function(e,t,i){const n=s.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(e);if(n){const e=await r.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().normalizeUILocation(n.uiLocation(t,i));return void o.Revealer.reveal(e)}const l=r.ResourceUtils.resourceForURL(e);if(l)return void o.Revealer.reveal(l);const d=a.NetworkLog.NetworkLog.instance().requestForURL(e);if(!d)throw new Error("Could not find resource for "+e);o.Revealer.reveal(d)}(i,n-1,l-1)}class v extends t.View.SimpleView{#v;#w=null;constructor(e,t){super(t),this.#v=e,this.#E(),i.TargetManager.TargetManager.instance().observeModels(c,this),this.element.style.userSelect="text"}wasShown(){super.wasShown(),this.registerCSSFiles([n.CSS])}modelAdded(e){this.#w=e,e.addEventListener("InitializationCompleted",this.#f,this),e.addEventListener("InitializationFailed",this.#b,this),e.addEventListener("Destroyed",this.#C,this),e.isInitialized()?this.#D():e.ensureInitialized()}modelRemoved(e){e.removeEventListener("InitializationCompleted",this.#f,this),e.removeEventListener("InitializationFailed",this.#b,this),e.removeEventListener("Destroyed",this.#C,this)}#f(){this.#D()}#b({data:e}){this.#x(e)}#C(){this.#E()}#D(){this.#B();const e=this.#w;if(null===e)throw new Error("Attempted to render React DevTools panel, but the model was null");const t=window.matchMedia("(prefers-color-scheme: dark)").matches;("components"===this.#v?n.initializeComponents:n.initializeProfiler)(this.contentElement,{bridge:e.getBridgeOrThrow(),store:e.getStoreOrThrow(),theme:t?"dark":"light",canViewElementSourceFunction:()=>!0,viewElementSourceFunction:p})}#E(){this.#B();const e=document.createElement("div");e.setAttribute("style","display: flex; flex: 1; justify-content: center; align-items: center");const t=document.createElement("span");t.classList.add("spinner"),e.appendChild(t),this.contentElement.appendChild(e)}#x(e){this.#B();const i=document.createElement("div");i.setAttribute("style","display: flex; flex: 1; flex-direction: column; justify-content: center; align-items: center");const n=document.createElement("div");n.setAttribute("style","font-size: 3rem"),n.innerHTML="❗";const o=document.createElement("p");if(o.setAttribute("style","user-select: all"),o.innerHTML=e,i.appendChild(n),i.appendChild(o),this.contentElement.appendChild(i),globalThis.FB_ONLY__reactNativeFeedbackLink){const e=globalThis.FB_ONLY__reactNativeFeedbackLink,n=t.UIUtils.createTextButton(g(m.sendFeedback),(()=>{l.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(e)}),{className:"primary-button",jslogContext:"sendFeedback"});i.appendChild(n)}}#B(){this.contentElement.removeChildren()}}const w={title:"⚛️ Components (React DevTools)"},E=e.i18n.registerUIStrings("panels/react_devtools/ReactDevToolsComponentsView.ts",w),f=e.i18n.getLocalizedString.bind(void 0,E);var b=Object.freeze({__proto__:null,ReactDevToolsComponentsViewImpl:class extends v{constructor(){super("components",f(w.title))}}});const C={title:"⚛️ Profiler (React DevTools)"},D=e.i18n.registerUIStrings("panels/react_devtools/ReactDevToolsProfilerView.ts",C),x=e.i18n.getLocalizedString.bind(void 0,D);var B=Object.freeze({__proto__:null,ReactDevToolsProfilerViewImpl:class extends v{constructor(){super("profiler",x(C.title))}}});export{b as ReactDevToolsComponentsView,h as ReactDevToolsModel,B as ReactDevToolsProfilerView};
