import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { MongoDBService } from './MongoDBService';
import { LoggingService } from './LoggingService';

export interface SyncConfig {
  syncInterval: number; // in milliseconds
  maxRetries: number;
  retryDelay: number;
  batchSize: number;
  enableAutoSync: boolean;
  syncOnlyOnWifi: boolean;
}

export interface SyncStatus {
  isOnline: boolean;
  lastSyncTime: Date | null;
  pendingTransactions: number;
  pendingInventoryUpdates: number;
  syncInProgress: boolean;
  lastError: string | null;
}

export interface PendingSyncItem {
  id: string;
  type: 'transaction' | 'inventory' | 'user' | 'setting';
  action: 'create' | 'update' | 'delete';
  data: any;
  timestamp: Date;
  retryCount: number;
}

export class SyncService {
  private mongoService: MongoDBService;
  private logger: LoggingService;
  private config: SyncConfig;
  private syncStatus: SyncStatus;
  private syncInterval: NodeJS.Timeout | null = null;
  private isInitialized: boolean = false;

  constructor(mongoService: MongoDBService) {
    this.mongoService = mongoService;
    this.logger = new LoggingService();
    
    this.config = {
      syncInterval: 60000, // 1 minute
      maxRetries: 3,
      retryDelay: 5000, // 5 seconds
      batchSize: 50,
      enableAutoSync: true,
      syncOnlyOnWifi: false
    };

    this.syncStatus = {
      isOnline: false,
      lastSyncTime: null,
      pendingTransactions: 0,
      pendingInventoryUpdates: 0,
      syncInProgress: false,
      lastError: null
    };
  }

  async initialize(): Promise<void> {
    try {
      // Load configuration
      await this.loadConfig();
      
      // Load sync status
      await this.loadSyncStatus();
      
      // Set up network monitoring
      this.setupNetworkMonitoring();
      
      // Start auto sync if enabled
      if (this.config.enableAutoSync) {
        this.startAutoSync();
      }
      
      this.isInitialized = true;
      this.logger.info('Sync service initialized successfully', {
        config: this.config,
        status: this.syncStatus
      });

    } catch (error) {
      this.logger.error('Sync service initialization failed', error);
      throw error;
    }
  }

  private async loadConfig(): Promise<void> {
    try {
      const configData = await AsyncStorage.getItem('syncConfig');
      if (configData) {
        const savedConfig = JSON.parse(configData);
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      this.logger.warn('Failed to load sync config, using defaults', error);
    }
  }

  private async saveConfig(): Promise<void> {
    try {
      await AsyncStorage.setItem('syncConfig', JSON.stringify(this.config));
    } catch (error) {
      this.logger.error('Failed to save sync config', error);
    }
  }

  private async loadSyncStatus(): Promise<void> {
    try {
      const statusData = await AsyncStorage.getItem('syncStatus');
      if (statusData) {
        const savedStatus = JSON.parse(statusData);
        this.syncStatus = {
          ...this.syncStatus,
          ...savedStatus,
          lastSyncTime: savedStatus.lastSyncTime ? new Date(savedStatus.lastSyncTime) : null,
          syncInProgress: false // Reset on startup
        };
      }

      // Count pending items
      await this.updatePendingCounts();

    } catch (error) {
      this.logger.warn('Failed to load sync status', error);
    }
  }

  private async saveSyncStatus(): Promise<void> {
    try {
      await AsyncStorage.setItem('syncStatus', JSON.stringify(this.syncStatus));
    } catch (error) {
      this.logger.error('Failed to save sync status', error);
    }
  }

  private setupNetworkMonitoring(): void {
    NetInfo.addEventListener(state => {
      const wasOnline = this.syncStatus.isOnline;
      this.syncStatus.isOnline = state.isConnected || false;

      if (!wasOnline && this.syncStatus.isOnline) {
        this.logger.info('Network connection restored, triggering sync');
        this.triggerSync();
      }

      if (!this.syncStatus.isOnline) {
        this.logger.warn('Network connection lost');
      }
    });
  }

  private startAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.triggerSync();
    }, this.config.syncInterval);

    this.logger.info('Auto sync started', { interval: this.config.syncInterval });
  }

  private stopAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    this.logger.info('Auto sync stopped');
  }

  async triggerSync(): Promise<boolean> {
    if (this.syncStatus.syncInProgress) {
      this.logger.debug('Sync already in progress, skipping');
      return false;
    }

    if (!this.syncStatus.isOnline) {
      this.logger.debug('No network connection, skipping sync');
      return false;
    }

    if (this.config.syncOnlyOnWifi) {
      const netInfo = await NetInfo.fetch();
      if (netInfo.type !== 'wifi') {
        this.logger.debug('Not on WiFi, skipping sync');
        return false;
      }
    }

    return await this.performSync();
  }

  private async performSync(): Promise<boolean> {
    this.syncStatus.syncInProgress = true;
    this.syncStatus.lastError = null;

    try {
      this.logger.info('Starting sync process');

      // Sync transactions
      await this.syncTransactions();

      // Sync inventory updates
      await this.syncInventoryUpdates();

      // Sync user updates
      await this.syncUserUpdates();

      // Sync settings
      await this.syncSettings();

      // Update sync status
      this.syncStatus.lastSyncTime = new Date();
      await this.updatePendingCounts();
      await this.saveSyncStatus();

      this.logger.info('Sync completed successfully', {
        syncTime: this.syncStatus.lastSyncTime,
        pendingTransactions: this.syncStatus.pendingTransactions,
        pendingInventoryUpdates: this.syncStatus.pendingInventoryUpdates
      });

      return true;

    } catch (error) {
      this.syncStatus.lastError = error.message;
      this.logger.error('Sync failed', error);
      return false;

    } finally {
      this.syncStatus.syncInProgress = false;
      await this.saveSyncStatus();
    }
  }

  private async syncTransactions(): Promise<void> {
    const pendingTransactions = await this.getPendingItems('transaction');
    
    for (const item of pendingTransactions.slice(0, this.config.batchSize)) {
      try {
        switch (item.action) {
          case 'create':
            await this.mongoService.createTransaction(item.data);
            break;
          case 'update':
            await this.mongoService.updateTransaction(item.data.id, item.data);
            break;
          // Transactions are typically not deleted, only voided
        }

        await this.removePendingItem(item.id);
        this.logger.debug('Transaction synced successfully', { itemId: item.id });

      } catch (error) {
        await this.handleSyncError(item, error);
      }
    }
  }

  private async syncInventoryUpdates(): Promise<void> {
    const pendingUpdates = await this.getPendingItems('inventory');
    
    for (const item of pendingUpdates.slice(0, this.config.batchSize)) {
      try {
        switch (item.action) {
          case 'create':
            await this.mongoService.createInventoryItem(item.data);
            break;
          case 'update':
            await this.mongoService.updateInventoryItem(item.data.id, item.data);
            break;
          // Handle delete if needed
        }

        await this.removePendingItem(item.id);
        this.logger.debug('Inventory item synced successfully', { itemId: item.id });

      } catch (error) {
        await this.handleSyncError(item, error);
      }
    }
  }

  private async syncUserUpdates(): Promise<void> {
    const pendingUpdates = await this.getPendingItems('user');
    
    for (const item of pendingUpdates.slice(0, this.config.batchSize)) {
      try {
        switch (item.action) {
          case 'create':
            await this.mongoService.createUser(item.data);
            break;
          // Handle other user operations as needed
        }

        await this.removePendingItem(item.id);
        this.logger.debug('User synced successfully', { itemId: item.id });

      } catch (error) {
        await this.handleSyncError(item, error);
      }
    }
  }

  private async syncSettings(): Promise<void> {
    const pendingSettings = await this.getPendingItems('setting');
    
    for (const item of pendingSettings.slice(0, this.config.batchSize)) {
      try {
        // Sync settings to store configuration
        await this.mongoService.updateStore(item.data);

        await this.removePendingItem(item.id);
        this.logger.debug('Setting synced successfully', { itemId: item.id });

      } catch (error) {
        await this.handleSyncError(item, error);
      }
    }
  }

  private async handleSyncError(item: PendingSyncItem, error: any): Promise<void> {
    item.retryCount++;

    if (item.retryCount >= this.config.maxRetries) {
      this.logger.error('Max retries reached for sync item, removing from queue', {
        itemId: item.id,
        error: error.message
      });
      await this.removePendingItem(item.id);
    } else {
      this.logger.warn('Sync item failed, will retry', {
        itemId: item.id,
        retryCount: item.retryCount,
        error: error.message
      });
      await this.updatePendingItem(item);
    }
  }

  // Public methods for adding items to sync queue
  async addTransactionToSync(transaction: any, action: 'create' | 'update' = 'create'): Promise<void> {
    const syncItem: PendingSyncItem = {
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'transaction',
      action,
      data: transaction,
      timestamp: new Date(),
      retryCount: 0
    };

    await this.addPendingItem(syncItem);
    this.logger.debug('Transaction added to sync queue', { transactionId: transaction.id });
  }

  async addInventoryToSync(item: any, action: 'create' | 'update' | 'delete'): Promise<void> {
    const syncItem: PendingSyncItem = {
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'inventory',
      action,
      data: item,
      timestamp: new Date(),
      retryCount: 0
    };

    await this.addPendingItem(syncItem);
    this.logger.debug('Inventory item added to sync queue', { itemId: item.id });
  }

  async addUserToSync(user: any, action: 'create' | 'update' | 'delete'): Promise<void> {
    const syncItem: PendingSyncItem = {
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'user',
      action,
      data: user,
      timestamp: new Date(),
      retryCount: 0
    };

    await this.addPendingItem(syncItem);
    this.logger.debug('User added to sync queue', { userId: user.id });
  }

  async addSettingToSync(setting: any): Promise<void> {
    const syncItem: PendingSyncItem = {
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'setting',
      action: 'update',
      data: setting,
      timestamp: new Date(),
      retryCount: 0
    };

    await this.addPendingItem(syncItem);
    this.logger.debug('Setting added to sync queue', { setting });
  }

  // Pending items management
  private async addPendingItem(item: PendingSyncItem): Promise<void> {
    try {
      const pendingItems = await this.getPendingItems();
      pendingItems.push(item);
      await AsyncStorage.setItem('pendingSyncItems', JSON.stringify(pendingItems));
      await this.updatePendingCounts();
    } catch (error) {
      this.logger.error('Failed to add pending sync item', error);
    }
  }

  private async updatePendingItem(item: PendingSyncItem): Promise<void> {
    try {
      const pendingItems = await this.getPendingItems();
      const index = pendingItems.findIndex(i => i.id === item.id);
      if (index !== -1) {
        pendingItems[index] = item;
        await AsyncStorage.setItem('pendingSyncItems', JSON.stringify(pendingItems));
      }
    } catch (error) {
      this.logger.error('Failed to update pending sync item', error);
    }
  }

  private async removePendingItem(itemId: string): Promise<void> {
    try {
      const pendingItems = await this.getPendingItems();
      const filteredItems = pendingItems.filter(item => item.id !== itemId);
      await AsyncStorage.setItem('pendingSyncItems', JSON.stringify(filteredItems));
      await this.updatePendingCounts();
    } catch (error) {
      this.logger.error('Failed to remove pending sync item', error);
    }
  }

  private async getPendingItems(type?: string): Promise<PendingSyncItem[]> {
    try {
      const pendingItemsData = await AsyncStorage.getItem('pendingSyncItems');
      if (!pendingItemsData) return [];

      const allItems = JSON.parse(pendingItemsData);
      
      if (type) {
        return allItems.filter((item: PendingSyncItem) => item.type === type);
      }
      
      return allItems;
    } catch (error) {
      this.logger.error('Failed to get pending sync items', error);
      return [];
    }
  }

  private async updatePendingCounts(): Promise<void> {
    try {
      const pendingTransactions = await this.getPendingItems('transaction');
      const pendingInventory = await this.getPendingItems('inventory');
      
      this.syncStatus.pendingTransactions = pendingTransactions.length;
      this.syncStatus.pendingInventoryUpdates = pendingInventory.length;
    } catch (error) {
      this.logger.error('Failed to update pending counts', error);
    }
  }

  // Configuration methods
  async updateConfig(newConfig: Partial<SyncConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfig();

    // Restart auto sync if interval changed
    if (newConfig.syncInterval && this.config.enableAutoSync) {
      this.startAutoSync();
    }

    // Start/stop auto sync based on enableAutoSync
    if (newConfig.enableAutoSync !== undefined) {
      if (newConfig.enableAutoSync) {
        this.startAutoSync();
      } else {
        this.stopAutoSync();
      }
    }

    this.logger.info('Sync configuration updated', { newConfig });
  }

  // Status and control methods
  getStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  getConfig(): SyncConfig {
    return { ...this.config };
  }

  async forceSyncNow(): Promise<boolean> {
    this.logger.info('Force sync triggered');
    return await this.triggerSync();
  }

  async clearPendingItems(): Promise<void> {
    try {
      await AsyncStorage.removeItem('pendingSyncItems');
      await this.updatePendingCounts();
      this.logger.info('All pending sync items cleared');
    } catch (error) {
      this.logger.error('Failed to clear pending sync items', error);
    }
  }

  // Cleanup
  async stop(): Promise<void> {
    this.stopAutoSync();
    this.isInitialized = false;
    this.logger.info('Sync service stopped');
  }

  // Getters
  get isReady(): boolean {
    return this.isInitialized;
  }

  get hasPendingItems(): boolean {
    return this.syncStatus.pendingTransactions > 0 || this.syncStatus.pendingInventoryUpdates > 0;
  }
}
