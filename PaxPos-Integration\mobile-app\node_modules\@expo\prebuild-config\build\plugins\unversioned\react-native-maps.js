"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _configPlugins() {
  const data = require("@expo/config-plugins");
  _configPlugins = function () {
    return data;
  };
  return data;
}
function _resolveFrom() {
  const data = _interopRequireDefault(require("resolve-from"));
  _resolveFrom = function () {
    return data;
  };
  return data;
}
function _createLegacyPlugin() {
  const data = require("./createLegacyPlugin");
  _createLegacyPlugin = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const LOCATION_USAGE = 'Allow $(PRODUCT_NAME) to access your location';

// Copied from expo-location package, this gets used when the
// user has react-native-maps installed but not expo-location.
const withDefaultLocationPermissions = config => {
  const isLinked = !config._internal?.autolinkedModules || config._internal.autolinkedModules.includes('react-native-maps');
  // Only add location permissions if react-native-maps is installed.
  if (config._internal?.projectRoot && _resolveFrom().default.silent(config._internal.projectRoot, 'react-native-maps') && isLinked) {
    config = (0, _configPlugins().withInfoPlist)(config, config => {
      config.modResults.NSLocationWhenInUseUsageDescription = config.modResults.NSLocationWhenInUseUsageDescription || LOCATION_USAGE;
      return config;
    });
    return _configPlugins().AndroidConfig.Permissions.withPermissions(config, ['android.permission.ACCESS_COARSE_LOCATION', 'android.permission.ACCESS_FINE_LOCATION']);
  }
  return config;
};
var _default = exports.default = (0, _createLegacyPlugin().createLegacyPlugin)({
  packageName: 'react-native-maps',
  fallback: [_configPlugins().AndroidConfig.GoogleMapsApiKey.withGoogleMapsApiKey, _configPlugins().IOSConfig.Maps.withMaps, withDefaultLocationPermissions]
});
//# sourceMappingURL=react-native-maps.js.map