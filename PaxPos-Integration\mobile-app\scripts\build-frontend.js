#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🏗️  Building frontend for mobile app...');

const frontendPath = path.join(__dirname, '../../frontend');
const assetsPath = path.join(__dirname, '../assets/frontend');

// Ensure assets directory exists
if (!fs.existsSync(assetsPath)) {
  fs.mkdirSync(assetsPath, { recursive: true });
}

try {
  // Build the React frontend
  console.log('📦 Building React frontend...');
  execSync('npm run build', { 
    cwd: frontendPath, 
    stdio: 'inherit' 
  });

  // Copy built files to mobile assets
  const distPath = path.join(frontendPath, 'dist');
  
  if (fs.existsSync(distPath)) {
    console.log('📁 Copying built files to mobile assets...');
    
    // Copy index.html
    const indexSrc = path.join(distPath, 'index.html');
    const indexDest = path.join(assetsPath, 'index.html');
    if (fs.existsSync(indexSrc)) {
      fs.copyFileSync(indexSrc, indexDest);
      console.log('✅ Copied index.html');
    }

    // Find and copy JS bundle
    const files = fs.readdirSync(path.join(distPath, 'assets'));
    const jsFile = files.find(file => file.endsWith('.js'));
    const cssFile = files.find(file => file.endsWith('.css'));

    if (jsFile) {
      const jsSrc = path.join(distPath, 'assets', jsFile);
      const jsDest = path.join(assetsPath, 'bundle.js');
      fs.copyFileSync(jsSrc, jsDest);
      console.log('✅ Copied JavaScript bundle');
    }

    if (cssFile) {
      const cssSrc = path.join(distPath, 'assets', cssFile);
      const cssDest = path.join(assetsPath, 'bundle.css');
      fs.copyFileSync(cssSrc, cssDest);
      console.log('✅ Copied CSS bundle');
    }

    // Update index.html to use local bundle paths
    let indexContent = fs.readFileSync(indexDest, 'utf8');
    indexContent = indexContent.replace(/\/assets\/[^"]+\.js/g, './bundle.js');
    indexContent = indexContent.replace(/\/assets\/[^"]+\.css/g, './bundle.css');
    fs.writeFileSync(indexDest, indexContent);
    console.log('✅ Updated asset paths in index.html');

  } else {
    throw new Error('Frontend build directory not found');
  }

  console.log('🎉 Frontend build completed successfully!');

} catch (error) {
  console.error('❌ Frontend build failed:', error.message);
  process.exit(1);
}
