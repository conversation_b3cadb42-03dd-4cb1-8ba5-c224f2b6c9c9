{"name": "xml2js", "description": "Simple XML to JavaScript object converter.", "keywords": ["xml", "json"], "homepage": "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js", "version": "0.6.0", "author": "<PERSON><PERSON> <<EMAIL>> (https://xivilization.net)", "contributors": ["maqr <<EMAIL>> (https://github.com/maqr)", "<PERSON> (http://benweaver.com/)", "<PERSON><PERSON> (https://github.com/jaek<PERSON>)", "<PERSON>", "<PERSON><PERSON><PERSON> (http://www.saltwaterc.eu/)", "<PERSON> <<EMAIL>> (http://cartercole.com/)", "<PERSON> <<EMAIL>> (http://www.kurtraschke.com/)", "Contra <<EMAIL>> (https://github.com/Contra)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/mdiniz)", "<PERSON> (https://github.com/mhart)", "<PERSON> <<EMAIL>> (http://zacharyscott.net/)", "<PERSON> (https://github.com/raoul<PERSON><PERSON>)", "Salsita Software (http://www.salsitasoft.com/)", "<PERSON> <<EMAIL>> (http://www.emotive.com/)", "<PERSON> <<EMAIL>> (http://weibo.com/shyvo)", "<PERSON> <<EMAIL>> (https://github.com/Sitin)", "<PERSON> <<EMAIL>> (https://github.com/christav)", "<PERSON> <<EMAIL>> (http://f2e.us/)", "<PERSON> <<EMAIL>> (http://www.bitstorm.it/)", "<PERSON> (http://jacksenechal.com/)", "<PERSON> <<EMAIL>> (https://github.com/hoelzl)", "<PERSON> <<EMAIL>> (http://www.creynders.be/)", "<PERSON> (https://github.com/tsgautier)", "<PERSON> (https://github.com/toddrbryan)", "<PERSON><PERSON> <<EMAIL>> (http://leoreavidar.com/)", "<PERSON> <<EMAIL>> (http://www.actionshrimp.com/)", "<PERSON><PERSON> <<EMAIL>>", "Candle <<EMAIL>>", "<PERSON> <<EMAIL>> (http://jes.st)", "<PERSON> <<<EMAIL>> (http://compton.nu/)", "<PERSON><PERSON><PERSON> (http://rocha.la/)", "<PERSON> (https://github.com/mi<PERSON><PERSON><PERSON>)", "<PERSON> (https://github.com/ryedin)", "<PERSON> <<EMAIL>> (https://github.com/elaberge)", "<PERSON> <<EMAIL>> (https://twitter.com/benjamincoe)", "<PERSON> (https://github.com/cressie176)", "<PERSON> <<EMAIL>> (http://www.hacksrus.net/)", "<PERSON> <<EMAIL>> (http://fiznool.com/)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/tflanagan)", "<PERSON> <<EMAIL>> (https://github.com/Tim<PERSON>)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/TrySound)", "<PERSON> <<EMAIL>> (http://codesleuth.co.uk/)", "<PERSON> (https://github.com/nmaquet)", "<PERSON><PERSON> (http://lovell.info/)", "d3adc0d3 (https://github.com/d3adc0d3)", "<PERSON> (https://github.com/autopulated)"], "main": "./lib/xml2js", "files": ["lib"], "directories": {"lib": "./lib"}, "scripts": {"build": "cake build", "test": "zap", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "doc": "cake doc"}, "repository": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js.git"}, "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "devDependencies": {"coffeescript": ">=1.10.0 <2", "coveralls": "^3.0.1", "diff": ">=1.0.8", "docco": ">=0.6.2", "nyc": ">=2.2.1", "zap": ">=0.2.9 <1"}, "engines": {"node": ">=4.0.0"}, "license": "MIT"}