{"name": "react-native-encrypted-storage", "version": "4.0.3", "description": "A React Native wrapper over SharedPreferences and Keychain to provide a secure alternative to Async Storage", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib", "android", "ios", "react-native-encrypted-storage.podspec", "!android/build", "!android/.idea", "!android/.gradle", "!android/.settings", "!android/local.properties", "!ios/build", "!**/__mocks__", "!**/*.test.*"], "scripts": {"test": "jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "prepare": "bob build", "example": "yarn --cwd example", "pods": "cd example && pod-install --quiet", "bootstrap": "yarn example && yarn && yarn pods"}, "keywords": ["react-native", "ios", "android"], "repository": "https://github.com/emeraldsanto/react-native-encrypted-storage", "author": "emeraldsanto <<EMAIL>> (https://github.com/emeraldsanto)", "license": "MIT", "bugs": {"url": "https://github.com/emeraldsanto/react-native-encrypted-storage/issues"}, "homepage": "https://github.com/emeraldsanto/react-native-encrypted-storage#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@types/jest": "26.0.0", "@types/react": "16.9.19", "@types/react-native": "0.62.13", "eslint": "7.2.0", "eslint-config-prettier": "7.0.0", "eslint-plugin-prettier": "3.1.3", "jest": "26.0.1", "pod-install": "0.1.0", "prettier": "2.0.5", "react": "16.13.1", "react-native": "0.63.4", "react-native-builder-bob": "0.17.1", "typescript": "4.1.3"}, "peerDependencies": {"react": "*", "react-native": "*"}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "eslintConfig": {"root": true, "extends": ["@react-native-community", "prettier"], "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}}