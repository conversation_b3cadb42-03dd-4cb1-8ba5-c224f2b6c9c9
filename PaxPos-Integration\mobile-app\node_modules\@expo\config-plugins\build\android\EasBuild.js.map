{"version": 3, "file": "EasBuild.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_EasBuildGradleScript", "Paths", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "APPLY_EAS_GRADLE", "hasApplyLine", "content", "applyLine", "replace", "split", "some", "line", "getEasBuildGradlePath", "projectRoot", "path", "join", "configureEasBuildAsync", "buildGradlePath", "getAppBuildGradleFilePath", "easGradlePath", "fs", "promises", "writeFile", "gradleScript", "buildGradleContent", "readFile", "hasEasGradleApply", "trim", "isEasBuildGradleConfiguredAsync", "hasEasGradleFile", "existsSync"], "sources": ["../../src/android/EasBuild.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport gradleScript from './EasBuildGradleScript';\nimport * as Paths from './Paths';\n\nconst APPLY_EAS_GRADLE = 'apply from: \"./eas-build.gradle\"';\n\nfunction hasApplyLine(content: string, applyLine: string): boolean {\n  return (\n    content\n      .replace(/\\r\\n/g, '\\n')\n      .split('\\n')\n      // Check for both single and double quotes\n      .some((line) => line === applyLine || line === applyLine.replace(/\"/g, \"'\"))\n  );\n}\n\nexport function getEasBuildGradlePath(projectRoot: string): string {\n  return path.join(projectRoot, 'android', 'app', 'eas-build.gradle');\n}\n\nexport async function configureEasBuildAsync(projectRoot: string): Promise<void> {\n  const buildGradlePath = Paths.getAppBuildGradleFilePath(projectRoot);\n  const easGradlePath = getEasBuildGradlePath(projectRoot);\n\n  await fs.promises.writeFile(easGradlePath, gradleScript);\n\n  const buildGradleContent = await fs.promises.readFile(path.join(buildGradlePath), 'utf8');\n\n  const hasEasGradleApply = hasApplyLine(buildGradleContent, APPLY_EAS_GRADLE);\n\n  if (!hasEasGradleApply) {\n    await fs.promises.writeFile(\n      buildGradlePath,\n      `${buildGradleContent.trim()}\\n${APPLY_EAS_GRADLE}\\n`\n    );\n  }\n}\n\nexport async function isEasBuildGradleConfiguredAsync(projectRoot: string): Promise<boolean> {\n  const buildGradlePath = Paths.getAppBuildGradleFilePath(projectRoot);\n  const easGradlePath = getEasBuildGradlePath(projectRoot);\n\n  const hasEasGradleFile = await fs.existsSync(easGradlePath);\n\n  const buildGradleContent = await fs.promises.readFile(path.join(buildGradlePath), 'utf8');\n  const hasEasGradleApply = hasApplyLine(buildGradleContent, APPLY_EAS_GRADLE);\n\n  return hasEasGradleApply && hasEasGradleFile;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,sBAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,qBAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiC,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAf,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAEjC,MAAMmB,gBAAgB,GAAG,kCAAkC;AAE3D,SAASC,YAAYA,CAACC,OAAe,EAAEC,SAAiB,EAAW;EACjE,OACED,OAAO,CACJE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBC,KAAK,CAAC,IAAI;EACX;EAAA,CACCC,IAAI,CAAEC,IAAI,IAAKA,IAAI,KAAKJ,SAAS,IAAII,IAAI,KAAKJ,SAAS,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAElF;AAEO,SAASI,qBAAqBA,CAACC,WAAmB,EAAU;EACjE,OAAOC,eAAI,CAACC,IAAI,CAACF,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,CAAC;AACrE;AAEO,eAAeG,sBAAsBA,CAACH,WAAmB,EAAiB;EAC/E,MAAMI,eAAe,GAAGnC,KAAK,CAAD,CAAC,CAACoC,yBAAyB,CAACL,WAAW,CAAC;EACpE,MAAMM,aAAa,GAAGP,qBAAqB,CAACC,WAAW,CAAC;EAExD,MAAMO,aAAE,CAACC,QAAQ,CAACC,SAAS,CAACH,aAAa,EAAEI,+BAAY,CAAC;EAExD,MAAMC,kBAAkB,GAAG,MAAMJ,aAAE,CAACC,QAAQ,CAACI,QAAQ,CAACX,eAAI,CAACC,IAAI,CAACE,eAAe,CAAC,EAAE,MAAM,CAAC;EAEzF,MAAMS,iBAAiB,GAAGrB,YAAY,CAACmB,kBAAkB,EAAEpB,gBAAgB,CAAC;EAE5E,IAAI,CAACsB,iBAAiB,EAAE;IACtB,MAAMN,aAAE,CAACC,QAAQ,CAACC,SAAS,CACzBL,eAAe,EACf,GAAGO,kBAAkB,CAACG,IAAI,CAAC,CAAC,KAAKvB,gBAAgB,IACnD,CAAC;EACH;AACF;AAEO,eAAewB,+BAA+BA,CAACf,WAAmB,EAAoB;EAC3F,MAAMI,eAAe,GAAGnC,KAAK,CAAD,CAAC,CAACoC,yBAAyB,CAACL,WAAW,CAAC;EACpE,MAAMM,aAAa,GAAGP,qBAAqB,CAACC,WAAW,CAAC;EAExD,MAAMgB,gBAAgB,GAAG,MAAMT,aAAE,CAACU,UAAU,CAACX,aAAa,CAAC;EAE3D,MAAMK,kBAAkB,GAAG,MAAMJ,aAAE,CAACC,QAAQ,CAACI,QAAQ,CAACX,eAAI,CAACC,IAAI,CAACE,eAAe,CAAC,EAAE,MAAM,CAAC;EACzF,MAAMS,iBAAiB,GAAGrB,YAAY,CAACmB,kBAAkB,EAAEpB,gBAAgB,CAAC;EAE5E,OAAOsB,iBAAiB,IAAIG,gBAAgB;AAC9C", "ignoreList": []}