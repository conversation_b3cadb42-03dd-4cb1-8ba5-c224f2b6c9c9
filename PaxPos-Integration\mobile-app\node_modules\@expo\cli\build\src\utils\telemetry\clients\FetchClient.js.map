{"version": 3, "sources": ["../../../../../src/utils/telemetry/clients/FetchClient.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { URL } from 'node:url';\nimport { Agent, RetryAgent, type RequestInfo, type RequestInit } from 'undici';\n\nimport { fetch } from '../../fetch';\nimport { TelemetryClient, TelemetryClientStrategy, TelemetryRecordInternal } from '../types';\nimport { TELEMETRY_ENDPOINT, TELEMETRY_TARGET } from '../utils/constants';\n\ntype Fetch = typeof fetch;\n\ntype FetchClientOptions = {\n  /** The fetch method for sending events, should handle retries and timeouts */\n  fetch?: Fetch;\n  /** The endpoint for recorded events */\n  url?: string;\n  /** The telemetry target for all events */\n  target?: string;\n};\n\ntype FetchClientEntry = Promise<void> & {\n  records: TelemetryRecordInternal[];\n  controller: AbortController;\n};\n\nexport class FetchClient implements TelemetryClient {\n  /** This client should be used for long-running commands */\n  readonly strategy: TelemetryClientStrategy = 'instant';\n  /** The fetch instance used to transport telemetry to the backend */\n  private fetch: Fetch;\n  /** The endpoint to send events to */\n  private url: string;\n  /** Additional headers to send with every event */\n  private headers: RequestInit['headers'];\n  /** All records that are queued and being sent */\n  private entries: Set<FetchClientEntry> = new Set();\n\n  constructor({\n    fetch = createTelemetryFetch(),\n    url = TELEMETRY_ENDPOINT,\n    target = TELEMETRY_TARGET,\n  }: FetchClientOptions = {}) {\n    this.fetch = fetch;\n    this.url = url;\n    this.headers = {\n      accept: 'application/json',\n      'content-type': 'application/json',\n      'user-agent': `expo-cli/${process.env.__EXPO_VERSION}`,\n      authorization: 'Basic ' + Buffer.from(`${target}:`).toString('base64'),\n    };\n  }\n\n  private queue(\n    records: TelemetryRecordInternal[],\n    controller: AbortController,\n    request: ReturnType<typeof fetch>\n  ) {\n    const entry: FetchClientEntry = mutePromise(request) as any;\n    entry.finally(() => this.entries.delete(entry));\n    entry.controller = controller;\n    entry.records = records;\n\n    this.entries.add(entry);\n\n    return entry;\n  }\n\n  record(record: TelemetryRecordInternal[]) {\n    const records = Array.isArray(record) ? record : [record];\n\n    if (!records.length) return;\n\n    const controller = new AbortController();\n    const body = JSON.stringify({\n      sentAt: new Date(),\n      batch: records,\n    });\n\n    return this.queue(\n      records,\n      controller,\n      this.fetch(this.url, {\n        body,\n        method: 'POST',\n        signal: controller.signal,\n        headers: this.headers,\n      })\n    );\n  }\n\n  flush() {\n    return mutePromise(Promise.all(this.entries));\n  }\n\n  abort() {\n    const records: TelemetryRecordInternal[] = [];\n\n    this.entries.forEach((entry) => {\n      try {\n        entry.controller.abort();\n        records.push(...entry.records);\n      } catch {\n        // Ignore abort errors\n      }\n    });\n\n    return records;\n  }\n}\n\nfunction createTelemetryFetch(): typeof fetch {\n  const agent = new RetryAgent(new Agent(), {\n    maxRetries: 3,\n    retryAfter: true,\n    minTimeout: 500,\n    maxTimeout: 2000,\n    timeoutFactor: 2,\n  });\n\n  return (info: RequestInfo | URL, init: RequestInit = {}) =>\n    fetch(extractUrl(info), { ...init, dispatcher: agent });\n}\n\n/** Extract the URL string from either `RequestInfo` or `URL` */\nfunction extractUrl(info: RequestInfo | URL) {\n  if (typeof info === 'string') return info;\n  if ('url' in info) return info.url;\n  return info.toString();\n}\n\n/** Mute a promise by removing the original return type and hide errors */\nfunction mutePromise(promise: Promise<any>) {\n  return promise.then(\n    () => {},\n    () => {}\n  );\n}\n"], "names": ["FetchClient", "constructor", "fetch", "createTelemetryFetch", "url", "TELEMETRY_ENDPOINT", "target", "TELEMETRY_TARGET", "strategy", "entries", "Set", "headers", "accept", "process", "env", "__EXPO_VERSION", "authorization", "<PERSON><PERSON><PERSON>", "from", "toString", "queue", "records", "controller", "request", "entry", "mutePromise", "finally", "delete", "add", "record", "Array", "isArray", "length", "AbortController", "body", "JSON", "stringify", "sentAt", "Date", "batch", "method", "signal", "flush", "Promise", "all", "abort", "for<PERSON>ach", "push", "agent", "RetryAgent", "Agent", "maxRetries", "retryAfter", "minTimeout", "maxTimeout", "timeoutFactor", "info", "init", "extractUrl", "dispatcher", "promise", "then"], "mappings": ";;;;+BAwBaA;;;eAAAA;;;;yBAxBU;;;;;;;yBAE+C;;;;;;uBAEhD;2BAE+B;AAkB9C,MAAMA;IAYXC,YAAY,EACVC,QAAQC,sBAAsB,EAC9BC,MAAMC,6BAAkB,EACxBC,SAASC,2BAAgB,EACN,GAAG,CAAC,CAAC,CAAE;QAf5B,yDAAyD,QAChDC,WAAoC;QAO7C,+CAA+C,QACvCC,UAAiC,IAAIC;QAO3C,IAAI,CAACR,KAAK,GAAGA;QACb,IAAI,CAACE,GAAG,GAAGA;QACX,IAAI,CAACO,OAAO,GAAG;YACbC,QAAQ;YACR,gBAAgB;YAChB,cAAc,CAAC,SAAS,EAAEC,QAAQC,GAAG,CAACC,cAAc,EAAE;YACtDC,eAAe,WAAWC,oBAAM,CAACC,IAAI,CAAC,GAAGZ,OAAO,CAAC,CAAC,EAAEa,QAAQ,CAAC;QAC/D;IACF;IAEQC,MACNC,OAAkC,EAClCC,UAA2B,EAC3BC,OAAiC,EACjC;QACA,MAAMC,QAA0BC,YAAYF;QAC5CC,MAAME,OAAO,CAAC,IAAM,IAAI,CAACjB,OAAO,CAACkB,MAAM,CAACH;QACxCA,MAAMF,UAAU,GAAGA;QACnBE,MAAMH,OAAO,GAAGA;QAEhB,IAAI,CAACZ,OAAO,CAACmB,GAAG,CAACJ;QAEjB,OAAOA;IACT;IAEAK,OAAOA,MAAiC,EAAE;QACxC,MAAMR,UAAUS,MAAMC,OAAO,CAACF,UAAUA,SAAS;YAACA;SAAO;QAEzD,IAAI,CAACR,QAAQW,MAAM,EAAE;QAErB,MAAMV,aAAa,IAAIW;QACvB,MAAMC,OAAOC,KAAKC,SAAS,CAAC;YAC1BC,QAAQ,IAAIC;YACZC,OAAOlB;QACT;QAEA,OAAO,IAAI,CAACD,KAAK,CACfC,SACAC,YACA,IAAI,CAACpB,KAAK,CAAC,IAAI,CAACE,GAAG,EAAE;YACnB8B;YACAM,QAAQ;YACRC,QAAQnB,WAAWmB,MAAM;YACzB9B,SAAS,IAAI,CAACA,OAAO;QACvB;IAEJ;IAEA+B,QAAQ;QACN,OAAOjB,YAAYkB,QAAQC,GAAG,CAAC,IAAI,CAACnC,OAAO;IAC7C;IAEAoC,QAAQ;QACN,MAAMxB,UAAqC,EAAE;QAE7C,IAAI,CAACZ,OAAO,CAACqC,OAAO,CAAC,CAACtB;YACpB,IAAI;gBACFA,MAAMF,UAAU,CAACuB,KAAK;gBACtBxB,QAAQ0B,IAAI,IAAIvB,MAAMH,OAAO;YAC/B,EAAE,OAAM;YACN,sBAAsB;YACxB;QACF;QAEA,OAAOA;IACT;AACF;AAEA,SAASlB;IACP,MAAM6C,QAAQ,IAAIC,CAAAA,SAAS,YAAC,CAAC,IAAIC,CAAAA,SAAI,OAAC,IAAI;QACxCC,YAAY;QACZC,YAAY;QACZC,YAAY;QACZC,YAAY;QACZC,eAAe;IACjB;IAEA,OAAO,CAACC,MAAyBC,OAAoB,CAAC,CAAC,GACrDvD,IAAAA,YAAK,EAACwD,WAAWF,OAAO;YAAE,GAAGC,IAAI;YAAEE,YAAYX;QAAM;AACzD;AAEA,8DAA8D,GAC9D,SAASU,WAAWF,IAAuB;IACzC,IAAI,OAAOA,SAAS,UAAU,OAAOA;IACrC,IAAI,SAASA,MAAM,OAAOA,KAAKpD,GAAG;IAClC,OAAOoD,KAAKrC,QAAQ;AACtB;AAEA,wEAAwE,GACxE,SAASM,YAAYmC,OAAqB;IACxC,OAAOA,QAAQC,IAAI,CACjB,KAAO,GACP,KAAO;AAEX"}