{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/MobileImageMounterClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Debug from 'debug';\nimport * as fs from 'fs';\nimport { Socket } from 'net';\n\nimport { ResponseError, ServiceClient } from './ServiceClient';\nimport type { LockdownCommand, LockdownResponse } from '../protocol/LockdownProtocol';\nimport { isLockdownResponse, LockdownProtocolClient } from '../protocol/LockdownProtocol';\n\nconst debug = Debug('expo:apple-device:client:mobile_image_mounter');\n\nexport type MIMMountResponse = LockdownResponse;\n\nexport interface MIMMessage extends LockdownCommand {\n  ImageType: string;\n}\n\nexport interface MIMLookupResponse extends LockdownResponse {\n  ImageSignature?: string;\n}\n\nexport interface MIMUploadCompleteResponse extends LockdownResponse {\n  Status: 'Complete';\n}\n\nexport interface MIMUploadReceiveBytesResponse extends LockdownResponse {\n  Status: 'ReceiveBytesAck';\n}\n\nfunction isMIMUploadCompleteResponse(resp: any): resp is MIMUploadCompleteResponse {\n  return resp.Status === 'Complete';\n}\n\nfunction isMIMUploadReceiveBytesResponse(resp: any): resp is MIMUploadReceiveBytesResponse {\n  return resp.Status === 'ReceiveBytesAck';\n}\n\nexport class MobileImageMounterClient extends ServiceClient<LockdownProtocolClient<MIMMessage>> {\n  constructor(socket: Socket) {\n    super(socket, new LockdownProtocolClient(socket));\n  }\n\n  async mountImage(imagePath: string, imageSig: Buffer) {\n    debug(`mountImage: ${imagePath}`);\n\n    const resp = await this.protocolClient.sendMessage({\n      Command: 'MountImage',\n      ImagePath: imagePath,\n      ImageSignature: imageSig,\n      ImageType: 'Developer',\n    });\n\n    if (!isLockdownResponse(resp) || resp.Status !== 'Complete') {\n      throw new ResponseError(`There was an error mounting ${imagePath} on device`, resp);\n    }\n  }\n\n  async uploadImage(imagePath: string, imageSig: Buffer) {\n    debug(`uploadImage: ${imagePath}`);\n\n    const imageSize = fs.statSync(imagePath).size;\n    return this.protocolClient.sendMessage(\n      {\n        Command: 'ReceiveBytes',\n        ImageSize: imageSize,\n        ImageSignature: imageSig,\n        ImageType: 'Developer',\n      },\n      (resp: any, resolve, reject) => {\n        if (isMIMUploadReceiveBytesResponse(resp)) {\n          const imageStream = fs.createReadStream(imagePath);\n          imageStream.pipe(this.protocolClient.socket, { end: false });\n          imageStream.on('error', (err) => reject(err));\n        } else if (isMIMUploadCompleteResponse(resp)) {\n          resolve();\n        } else {\n          reject(\n            new ResponseError(`There was an error uploading image ${imagePath} to the device`, resp)\n          );\n        }\n      }\n    );\n  }\n\n  async lookupImage() {\n    debug('lookupImage');\n\n    return this.protocolClient.sendMessage<MIMLookupResponse>({\n      Command: 'LookupImage',\n      ImageType: 'Developer',\n    });\n  }\n}\n"], "names": ["MobileImageMounterClient", "debug", "Debug", "isMIMUploadCompleteResponse", "resp", "Status", "isMIMUploadReceiveBytesResponse", "ServiceClient", "constructor", "socket", "LockdownProtocolClient", "mountImage", "imagePath", "imageSig", "protocolClient", "sendMessage", "Command", "ImagePath", "ImageSignature", "ImageType", "isLockdownResponse", "ResponseError", "uploadImage", "imageSize", "fs", "statSync", "size", "ImageSize", "resolve", "reject", "imageStream", "createReadStream", "pipe", "end", "on", "err", "lookupImage"], "mappings": "AAAA;;;;;;CAMC;;;;+BAqCYA;;;eAAAA;;;;gEApCK;;;;;;;iEACE;;;;;;+BAGyB;kCAEc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AAoBpB,SAASC,4BAA4BC,IAAS;IAC5C,OAAOA,KAAKC,MAAM,KAAK;AACzB;AAEA,SAASC,gCAAgCF,IAAS;IAChD,OAAOA,KAAKC,MAAM,KAAK;AACzB;AAEO,MAAML,iCAAiCO,4BAAa;IACzDC,YAAYC,MAAc,CAAE;QAC1B,KAAK,CAACA,QAAQ,IAAIC,wCAAsB,CAACD;IAC3C;IAEA,MAAME,WAAWC,SAAiB,EAAEC,QAAgB,EAAE;QACpDZ,MAAM,CAAC,YAAY,EAAEW,WAAW;QAEhC,MAAMR,OAAO,MAAM,IAAI,CAACU,cAAc,CAACC,WAAW,CAAC;YACjDC,SAAS;YACTC,WAAWL;YACXM,gBAAgBL;YAChBM,WAAW;QACb;QAEA,IAAI,CAACC,IAAAA,oCAAkB,EAAChB,SAASA,KAAKC,MAAM,KAAK,YAAY;YAC3D,MAAM,IAAIgB,4BAAa,CAAC,CAAC,4BAA4B,EAAET,UAAU,UAAU,CAAC,EAAER;QAChF;IACF;IAEA,MAAMkB,YAAYV,SAAiB,EAAEC,QAAgB,EAAE;QACrDZ,MAAM,CAAC,aAAa,EAAEW,WAAW;QAEjC,MAAMW,YAAYC,MAAGC,QAAQ,CAACb,WAAWc,IAAI;QAC7C,OAAO,IAAI,CAACZ,cAAc,CAACC,WAAW,CACpC;YACEC,SAAS;YACTW,WAAWJ;YACXL,gBAAgBL;YAChBM,WAAW;QACb,GACA,CAACf,MAAWwB,SAASC;YACnB,IAAIvB,gCAAgCF,OAAO;gBACzC,MAAM0B,cAAcN,MAAGO,gBAAgB,CAACnB;gBACxCkB,YAAYE,IAAI,CAAC,IAAI,CAAClB,cAAc,CAACL,MAAM,EAAE;oBAAEwB,KAAK;gBAAM;gBAC1DH,YAAYI,EAAE,CAAC,SAAS,CAACC,MAAQN,OAAOM;YAC1C,OAAO,IAAIhC,4BAA4BC,OAAO;gBAC5CwB;YACF,OAAO;gBACLC,OACE,IAAIR,4BAAa,CAAC,CAAC,mCAAmC,EAAET,UAAU,cAAc,CAAC,EAAER;YAEvF;QACF;IAEJ;IAEA,MAAMgC,cAAc;QAClBnC,MAAM;QAEN,OAAO,IAAI,CAACa,cAAc,CAACC,WAAW,CAAoB;YACxDC,SAAS;YACTG,WAAW;QACb;IACF;AACF"}