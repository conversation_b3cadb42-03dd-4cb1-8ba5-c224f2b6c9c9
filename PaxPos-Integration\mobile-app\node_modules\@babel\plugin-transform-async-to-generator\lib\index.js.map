{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperRemapAsyncToGenerator", "_helperModuleImports", "_core", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_api$assumption2", "assertVersion", "method", "module", "noNewArrows", "assumption", "ignoreFunctionLength", "name", "visitor", "Function", "path", "state", "node", "async", "generator", "wrapAsync", "methodWrapper", "t", "cloneNode", "addNamed", "remapAsyncToGenerator", "addHelper"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport remapAsyncToGenerator from \"@babel/helper-remap-async-to-generator\";\nimport { addNamed } from \"@babel/helper-module-imports\";\nimport { types as t } from \"@babel/core\";\n\nexport interface Options {\n  method?: string;\n  module?: string;\n}\n\ntype State = {\n  methodWrapper?: t.Identifier | t.SequenceExpression;\n};\n\nexport default declare<State>((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { method, module } = options;\n  // Todo(BABEL 8): Consider default it to false\n  const noNewArrows = api.assumption(\"noNewArrows\") ?? true;\n  const ignoreFunctionLength = api.assumption(\"ignoreFunctionLength\") ?? false;\n\n  if (method && module) {\n    return {\n      name: \"transform-async-to-generator\",\n\n      visitor: {\n        Function(path, state) {\n          if (!path.node.async || path.node.generator) return;\n\n          let wrapAsync = state.methodWrapper;\n          if (wrapAsync) {\n            wrapAsync = t.cloneNode(wrapAsync);\n          } else {\n            wrapAsync = state.methodWrapper = addNamed(path, method, module);\n          }\n\n          remapAsyncToGenerator(\n            path,\n            { wrapAsync },\n            noNewArrows,\n            ignoreFunctionLength,\n          );\n        },\n      },\n    };\n  }\n\n  return {\n    name: \"transform-async-to-generator\",\n\n    visitor: {\n      Function(path, state) {\n        if (!path.node.async || path.node.generator) return;\n\n        remapAsyncToGenerator(\n          path,\n          { wrapAsync: state.addHelper(\"asyncToGenerator\") },\n          noNewArrows,\n          ignoreFunctionLength,\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,4BAAA,GAAAD,OAAA;AACA,IAAAE,oBAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAAyC,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAW1B,IAAAC,0BAAO,EAAQ,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACvDH,GAAG,CAACI,aAAa,uCAAoB,CAAC;EAEtC,MAAM;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGL,OAAO;EAElC,MAAMM,WAAW,IAAAL,eAAA,GAAGF,GAAG,CAACQ,UAAU,CAAC,aAAa,CAAC,YAAAN,eAAA,GAAI,IAAI;EACzD,MAAMO,oBAAoB,IAAAN,gBAAA,GAAGH,GAAG,CAACQ,UAAU,CAAC,sBAAsB,CAAC,YAAAL,gBAAA,GAAI,KAAK;EAE5E,IAAIE,MAAM,IAAIC,MAAM,EAAE;IACpB,OAAO;MACLI,IAAI,EAAE,8BAA8B;MAEpCC,OAAO,EAAE;QACPC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAE;UACpB,IAAI,CAACD,IAAI,CAACE,IAAI,CAACC,KAAK,IAAIH,IAAI,CAACE,IAAI,CAACE,SAAS,EAAE;UAE7C,IAAIC,SAAS,GAAGJ,KAAK,CAACK,aAAa;UACnC,IAAID,SAAS,EAAE;YACbA,SAAS,GAAGE,WAAC,CAACC,SAAS,CAACH,SAAS,CAAC;UACpC,CAAC,MAAM;YACLA,SAAS,GAAGJ,KAAK,CAACK,aAAa,GAAG,IAAAG,6BAAQ,EAACT,IAAI,EAAER,MAAM,EAAEC,MAAM,CAAC;UAClE;UAEA,IAAAiB,oCAAqB,EACnBV,IAAI,EACJ;YAAEK;UAAU,CAAC,EACbX,WAAW,EACXE,oBACF,CAAC;QACH;MACF;IACF,CAAC;EACH;EAEA,OAAO;IACLC,IAAI,EAAE,8BAA8B;IAEpCC,OAAO,EAAE;MACPC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAE;QACpB,IAAI,CAACD,IAAI,CAACE,IAAI,CAACC,KAAK,IAAIH,IAAI,CAACE,IAAI,CAACE,SAAS,EAAE;QAE7C,IAAAM,oCAAqB,EACnBV,IAAI,EACJ;UAAEK,SAAS,EAAEJ,KAAK,CAACU,SAAS,CAAC,kBAAkB;QAAE,CAAC,EAClDjB,WAAW,EACXE,oBACF,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}