"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.syncUniqueId = syncUniqueId;
exports.getMacAddress = getMacAddress;
exports.getMacAddressSync = getMacAddressSync;
exports.getReadableVersion = getReadableVersion;
exports.hasNotch = hasNotch;
exports.hasDynamicIsland = hasDynamicIsland;
exports.getTotalDiskCapacityOld = getTotalDiskCapacityOld;
exports.getTotalDiskCapacityOldSync = getTotalDiskCapacityOldSync;
exports.getFreeDiskStorageOld = getFreeDiskStorageOld;
exports.getFreeDiskStorageOldSync = getFreeDiskStorageOldSync;
exports.isLandscape = isLandscape;
exports.isLandscapeSync = isLandscapeSync;
exports.hasSystemFeature = hasSystemFeature;
exports.hasSystemFeatureSync = hasSystemFeatureSync;
exports.isLowBatteryLevel = isLowBatteryLevel;
exports.getDeviceToken = getDeviceToken;
exports.useBatteryLevel = useBatteryLevel;
exports.useBatteryLevelIsLow = useBatteryLevelIsLow;
exports.usePowerState = usePowerState;
exports.useIsHeadphonesConnected = useIsHeadphonesConnected;
exports.useIsWiredHeadphonesConnected = useIsWiredHeadphonesConnected;
exports.useIsBluetoothHeadphonesConnected = useIsBluetoothHeadphonesConnected;
exports.useFirstInstallTime = useFirstInstallTime;
exports.useDeviceName = useDeviceName;
exports.useHasSystemFeature = useHasSystemFeature;
exports.useIsEmulator = useIsEmulator;
exports.useManufacturer = useManufacturer;
exports.useBrightness = useBrightness;
exports.getPowerStateSync = exports.getPowerState = exports.getBatteryLevelSync = exports.getBatteryLevel = exports.getFreeDiskStorageSync = exports.getFreeDiskStorage = exports.getTotalDiskCapacitySync = exports.getTotalDiskCapacity = exports.getMaxMemorySync = exports.getMaxMemory = exports.getTotalMemorySync = exports.getTotalMemory = exports.getCarrierSync = exports.getCarrier = exports.getPhoneNumberSync = exports.getPhoneNumber = exports.getLastUpdateTimeSync = exports.getLastUpdateTime = exports.getInstallReferrerSync = exports.getInstallReferrer = exports.getFirstInstallTimeSync = exports.getFirstInstallTime = exports.hasHmsSync = exports.hasHms = exports.hasGmsSync = exports.hasGms = exports.isPinOrFingerprintSetSync = exports.isPinOrFingerprintSet = exports.isDisplayZoomed = exports.isLowRamDevice = exports.isTablet = exports.isEmulatorSync = exports.isEmulator = exports.getIncrementalSync = exports.getIncremental = exports.getCodenameSync = exports.getCodename = exports.getSecurityPatchSync = exports.getSecurityPatch = exports.getPreviewSdkIntSync = exports.getPreviewSdkInt = exports.getBaseOsSync = exports.getBaseOs = exports.getTypeSync = exports.getType = exports.getTagsSync = exports.getTags = exports.getProductSync = exports.getProduct = exports.getHostNamesSync = exports.getHostNames = exports.getHostSync = exports.getHost = exports.getHardwareSync = exports.getHardware = exports.getFingerprintSync = exports.getFingerprint = exports.getDisplaySync = exports.getDisplay = exports.getDeviceSync = exports.getDevice = exports.getBootloaderSync = exports.getBootloader = exports.getFontScaleSync = exports.getFontScale = exports.getUserAgentSync = exports.getUserAgent = exports.getUsedMemorySync = exports.getUsedMemory = exports.getDeviceNameSync = exports.getDeviceName = exports.getVersion = exports.getBuildNumber = exports.getApplicationName = exports.getInstallerPackageNameSync = exports.getInstallerPackageName = exports.getBundleId = exports.getApiLevelSync = exports.getApiLevel = exports.getBuildIdSync = exports.getBuildId = exports.getSystemVersion = exports.getSystemName = exports.getBrand = exports.getModel = exports.getManufacturerSync = exports.getManufacturer = exports.getDeviceId = exports.isCameraPresentSync = exports.isCameraPresent = exports.getIpAddressSync = exports.getIpAddress = exports.getAndroidIdSync = exports.getAndroidId = exports.getSerialNumberSync = exports.getSerialNumber = exports.getInstanceIdSync = exports.getInstanceId = exports.getUniqueIdSync = exports.getUniqueId = void 0;
exports.default = exports.getBrightnessSync = exports.getBrightness = exports.getAvailableLocationProvidersSync = exports.getAvailableLocationProviders = exports.isTabletMode = exports.getSupportedMediaTypeListSync = exports.getSupportedMediaTypeList = exports.isKeyboardConnectedSync = exports.isKeyboardConnected = exports.isMouseConnectedSync = exports.isMouseConnected = exports.isBluetoothHeadphonesConnectedSync = exports.isBluetoothHeadphonesConnected = exports.isWiredHeadphonesConnectedSync = exports.isWiredHeadphonesConnected = exports.isHeadphonesConnectedSync = exports.isHeadphonesConnected = exports.isLocationEnabledSync = exports.isLocationEnabled = exports.getSystemAvailableFeaturesSync = exports.getSystemAvailableFeatures = exports.supported64BitAbisSync = exports.supported64BitAbis = exports.supported32BitAbisSync = exports.supported32BitAbis = exports.supportedAbisSync = exports.supportedAbis = exports.getDeviceTypeSync = exports.getDeviceType = exports.isAirplaneModeSync = exports.isAirplaneMode = exports.isBatteryChargingSync = exports.isBatteryCharging = void 0;

var _react = require("react");

var _reactNative = require("react-native");

var _asyncHookWrappers = require("./internal/asyncHookWrappers");

var _devicesWithDynamicIsland = _interopRequireDefault(require("./internal/devicesWithDynamicIsland"));

var _devicesWithNotch = _interopRequireDefault(require("./internal/devicesWithNotch"));

var _nativeInterface = _interopRequireDefault(require("./internal/nativeInterface"));

var _supportedPlatformInfo = require("./internal/supported-platform-info");

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

const [getUniqueId, getUniqueIdSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'uniqueId',
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.getUniqueId(),
  syncGetter: () => _nativeInterface.default.getUniqueIdSync(),
  defaultValue: 'unknown'
});
exports.getUniqueIdSync = getUniqueIdSync;
exports.getUniqueId = getUniqueId;
let uniqueId;

async function syncUniqueId() {
  if (_reactNative.Platform.OS === 'ios') {
    uniqueId = await _nativeInterface.default.syncUniqueId();
  } else {
    uniqueId = await getUniqueId();
  }

  return uniqueId;
}

const [getInstanceId, getInstanceIdSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'instanceId',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getInstanceId(),
  syncGetter: () => _nativeInterface.default.getInstanceIdSync(),
  defaultValue: 'unknown'
});
exports.getInstanceIdSync = getInstanceIdSync;
exports.getInstanceId = getInstanceId;
const [getSerialNumber, getSerialNumberSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'serialNumber',
  supportedPlatforms: ['android', 'windows'],
  getter: () => _nativeInterface.default.getSerialNumber(),
  syncGetter: () => _nativeInterface.default.getSerialNumberSync(),
  defaultValue: 'unknown'
});
exports.getSerialNumberSync = getSerialNumberSync;
exports.getSerialNumber = getSerialNumber;
const [getAndroidId, getAndroidIdSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'androidId',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getAndroidId(),
  syncGetter: () => _nativeInterface.default.getAndroidIdSync(),
  defaultValue: 'unknown'
});
exports.getAndroidIdSync = getAndroidIdSync;
exports.getAndroidId = getAndroidId;
const [getIpAddress, getIpAddressSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.getIpAddress(),
  syncGetter: () => _nativeInterface.default.getIpAddressSync(),
  defaultValue: 'unknown'
});
exports.getIpAddressSync = getIpAddressSync;
exports.getIpAddress = getIpAddress;
const [isCameraPresent, isCameraPresentSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'windows', 'web'],
  getter: () => _nativeInterface.default.isCameraPresent(),
  syncGetter: () => _nativeInterface.default.isCameraPresentSync(),
  defaultValue: false
});
exports.isCameraPresentSync = isCameraPresentSync;
exports.isCameraPresent = isCameraPresent;

async function getMacAddress() {
  if (_reactNative.Platform.OS === 'android') {
    return _nativeInterface.default.getMacAddress();
  } else if (_reactNative.Platform.OS === 'ios') {
    return '02:00:00:00:00:00';
  }

  return 'unknown';
}

function getMacAddressSync() {
  if (_reactNative.Platform.OS === 'android') {
    return _nativeInterface.default.getMacAddressSync();
  } else if (_reactNative.Platform.OS === 'ios') {
    return '02:00:00:00:00:00';
  }

  return 'unknown';
}

const getDeviceId = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  defaultValue: 'unknown',
  memoKey: 'deviceId',
  getter: () => _nativeInterface.default.deviceId,
  supportedPlatforms: ['android', 'ios', 'windows']
});

exports.getDeviceId = getDeviceId;
const [getManufacturer, getManufacturerSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'manufacturer',
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _reactNative.Platform.OS == 'ios' ? Promise.resolve('Apple') : _nativeInterface.default.getSystemManufacturer(),
  syncGetter: () => _reactNative.Platform.OS == 'ios' ? 'Apple' : _nativeInterface.default.getSystemManufacturerSync(),
  defaultValue: 'unknown'
});
exports.getManufacturerSync = getManufacturerSync;
exports.getManufacturer = getManufacturer;

const getModel = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  memoKey: 'model',
  defaultValue: 'unknown',
  supportedPlatforms: ['ios', 'android', 'windows'],
  getter: () => _nativeInterface.default.model
});

exports.getModel = getModel;

const getBrand = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  memoKey: 'brand',
  supportedPlatforms: ['android', 'ios', 'windows'],
  defaultValue: 'unknown',
  getter: () => _nativeInterface.default.brand
});

exports.getBrand = getBrand;

const getSystemName = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  defaultValue: 'unknown',
  supportedPlatforms: ['ios', 'android', 'windows'],
  memoKey: 'systemName',
  getter: () => _reactNative.Platform.select({
    ios: _nativeInterface.default.systemName,
    android: 'Android',
    windows: 'Windows',
    default: 'unknown'
  })
});

exports.getSystemName = getSystemName;

const getSystemVersion = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  defaultValue: 'unknown',
  getter: () => _nativeInterface.default.systemVersion,
  supportedPlatforms: ['android', 'ios', 'windows'],
  memoKey: 'systemVersion'
});

exports.getSystemVersion = getSystemVersion;
const [getBuildId, getBuildIdSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'buildId',
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.getBuildId(),
  syncGetter: () => _nativeInterface.default.getBuildIdSync(),
  defaultValue: 'unknown'
});
exports.getBuildIdSync = getBuildIdSync;
exports.getBuildId = getBuildId;
const [getApiLevel, getApiLevelSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'apiLevel',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getApiLevel(),
  syncGetter: () => _nativeInterface.default.getApiLevelSync(),
  defaultValue: -1
});
exports.getApiLevelSync = getApiLevelSync;
exports.getApiLevel = getApiLevel;

const getBundleId = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  memoKey: 'bundleId',
  supportedPlatforms: ['android', 'ios', 'windows'],
  defaultValue: 'unknown',
  getter: () => _nativeInterface.default.bundleId
});

exports.getBundleId = getBundleId;
const [getInstallerPackageName, getInstallerPackageNameSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'installerPackageName',
  supportedPlatforms: ['android', 'windows', 'ios'],
  getter: () => _nativeInterface.default.getInstallerPackageName(),
  syncGetter: () => _nativeInterface.default.getInstallerPackageNameSync(),
  defaultValue: 'unknown'
});
exports.getInstallerPackageNameSync = getInstallerPackageNameSync;
exports.getInstallerPackageName = getInstallerPackageName;

const getApplicationName = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  memoKey: 'appName',
  defaultValue: 'unknown',
  getter: () => _nativeInterface.default.appName,
  supportedPlatforms: ['android', 'ios', 'windows']
});

exports.getApplicationName = getApplicationName;

const getBuildNumber = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  memoKey: 'buildNumber',
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.buildNumber,
  defaultValue: 'unknown'
});

exports.getBuildNumber = getBuildNumber;

const getVersion = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  memoKey: 'version',
  defaultValue: 'unknown',
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.appVersion
});

exports.getVersion = getVersion;

function getReadableVersion() {
  return getVersion() + '.' + getBuildNumber();
}

const [getDeviceName, getDeviceNameSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.getDeviceName(),
  syncGetter: () => _nativeInterface.default.getDeviceNameSync(),
  defaultValue: 'unknown'
});
exports.getDeviceNameSync = getDeviceNameSync;
exports.getDeviceName = getDeviceName;
const [getUsedMemory, getUsedMemorySync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'windows', 'web'],
  getter: () => _nativeInterface.default.getUsedMemory(),
  syncGetter: () => _nativeInterface.default.getUsedMemorySync(),
  defaultValue: -1
});
exports.getUsedMemorySync = getUsedMemorySync;
exports.getUsedMemory = getUsedMemory;

const getUserAgent = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoAsync)({
  memoKey: 'userAgent',
  defaultValue: 'unknown',
  supportedPlatforms: ['android', 'ios', 'web'],
  getter: () => _nativeInterface.default.getUserAgent()
});

exports.getUserAgent = getUserAgent;

const getUserAgentSync = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  memoKey: 'userAgentSync',
  defaultValue: 'unknown',
  supportedPlatforms: ['android', 'web'],
  getter: () => _nativeInterface.default.getUserAgentSync()
});

exports.getUserAgentSync = getUserAgentSync;
const [getFontScale, getFontScaleSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.getFontScale(),
  syncGetter: () => _nativeInterface.default.getFontScaleSync(),
  defaultValue: -1
});
exports.getFontScaleSync = getFontScaleSync;
exports.getFontScale = getFontScale;
const [getBootloader, getBootloaderSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'bootloader',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getBootloader(),
  syncGetter: () => _nativeInterface.default.getBootloaderSync(),
  defaultValue: 'unknown'
});
exports.getBootloaderSync = getBootloaderSync;
exports.getBootloader = getBootloader;
const [getDevice, getDeviceSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  getter: () => _nativeInterface.default.getDevice(),
  syncGetter: () => _nativeInterface.default.getDeviceSync(),
  defaultValue: 'unknown',
  memoKey: 'device',
  supportedPlatforms: ['android']
});
exports.getDeviceSync = getDeviceSync;
exports.getDevice = getDevice;
const [getDisplay, getDisplaySync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'display',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getDisplay(),
  syncGetter: () => _nativeInterface.default.getDisplaySync(),
  defaultValue: 'unknown'
});
exports.getDisplaySync = getDisplaySync;
exports.getDisplay = getDisplay;
const [getFingerprint, getFingerprintSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'fingerprint',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getFingerprint(),
  syncGetter: () => _nativeInterface.default.getFingerprintSync(),
  defaultValue: 'unknown'
});
exports.getFingerprintSync = getFingerprintSync;
exports.getFingerprint = getFingerprint;
const [getHardware, getHardwareSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'hardware',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getHardware(),
  syncGetter: () => _nativeInterface.default.getHardwareSync(),
  defaultValue: 'unknown'
});
exports.getHardwareSync = getHardwareSync;
exports.getHardware = getHardware;
const [getHost, getHostSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'host',
  supportedPlatforms: ['android', 'windows'],
  getter: () => _nativeInterface.default.getHost(),
  syncGetter: () => _nativeInterface.default.getHostSync(),
  defaultValue: 'unknown'
});
exports.getHostSync = getHostSync;
exports.getHost = getHost;
const [getHostNames, getHostNamesSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'hostNames',
  supportedPlatforms: ['windows'],
  getter: () => _nativeInterface.default.getHostNames(),
  syncGetter: () => _nativeInterface.default.getHostNamesSync(),
  defaultValue: []
});
exports.getHostNamesSync = getHostNamesSync;
exports.getHostNames = getHostNames;
const [getProduct, getProductSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'product',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getProduct(),
  syncGetter: () => _nativeInterface.default.getProductSync(),
  defaultValue: 'unknown'
});
exports.getProductSync = getProductSync;
exports.getProduct = getProduct;
const [getTags, getTagsSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'tags',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getTags(),
  syncGetter: () => _nativeInterface.default.getTagsSync(),
  defaultValue: 'unknown'
});
exports.getTagsSync = getTagsSync;
exports.getTags = getTags;
const [getType, getTypeSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'type',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getType(),
  syncGetter: () => _nativeInterface.default.getTypeSync(),
  defaultValue: 'unknown'
});
exports.getTypeSync = getTypeSync;
exports.getType = getType;
const [getBaseOs, getBaseOsSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'baseOs',
  supportedPlatforms: ['android', 'web', 'windows'],
  getter: () => _nativeInterface.default.getBaseOs(),
  syncGetter: () => _nativeInterface.default.getBaseOsSync(),
  defaultValue: 'unknown'
});
exports.getBaseOsSync = getBaseOsSync;
exports.getBaseOs = getBaseOs;
const [getPreviewSdkInt, getPreviewSdkIntSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'previewSdkInt',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getPreviewSdkInt(),
  syncGetter: () => _nativeInterface.default.getPreviewSdkIntSync(),
  defaultValue: -1
});
exports.getPreviewSdkIntSync = getPreviewSdkIntSync;
exports.getPreviewSdkInt = getPreviewSdkInt;
const [getSecurityPatch, getSecurityPatchSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'securityPatch',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getSecurityPatch(),
  syncGetter: () => _nativeInterface.default.getSecurityPatchSync(),
  defaultValue: 'unknown'
});
exports.getSecurityPatchSync = getSecurityPatchSync;
exports.getSecurityPatch = getSecurityPatch;
const [getCodename, getCodenameSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'codeName',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getCodename(),
  syncGetter: () => _nativeInterface.default.getCodenameSync(),
  defaultValue: 'unknown'
});
exports.getCodenameSync = getCodenameSync;
exports.getCodename = getCodename;
const [getIncremental, getIncrementalSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'incremental',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getIncremental(),
  syncGetter: () => _nativeInterface.default.getIncrementalSync(),
  defaultValue: 'unknown'
});
exports.getIncrementalSync = getIncrementalSync;
exports.getIncremental = getIncremental;
const [isEmulator, isEmulatorSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'emulator',
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.isEmulator(),
  syncGetter: () => _nativeInterface.default.isEmulatorSync(),
  defaultValue: false
});
exports.isEmulatorSync = isEmulatorSync;
exports.isEmulator = isEmulator;

const isTablet = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  defaultValue: false,
  supportedPlatforms: ['android', 'ios', 'windows'],
  memoKey: 'tablet',
  getter: () => _nativeInterface.default.isTablet
});

exports.isTablet = isTablet;

const isLowRamDevice = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  defaultValue: false,
  supportedPlatforms: ['android'],
  memoKey: 'lowRam',
  getter: () => _nativeInterface.default.isLowRamDevice
});

exports.isLowRamDevice = isLowRamDevice;

const isDisplayZoomed = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
  defaultValue: false,
  supportedPlatforms: ['ios'],
  memoKey: 'zoomed',
  getter: () => _nativeInterface.default.isDisplayZoomed
});

exports.isDisplayZoomed = isDisplayZoomed;
const [isPinOrFingerprintSet, isPinOrFingerprintSetSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.isPinOrFingerprintSet(),
  syncGetter: () => _nativeInterface.default.isPinOrFingerprintSetSync(),
  defaultValue: false
});
exports.isPinOrFingerprintSetSync = isPinOrFingerprintSetSync;
exports.isPinOrFingerprintSet = isPinOrFingerprintSet;
let notch;

function hasNotch() {
  if (notch === undefined) {
    let _brand = getBrand();

    let _model = getModel();

    notch = _devicesWithNotch.default.findIndex(item => item.brand.toLowerCase() === _brand.toLowerCase() && item.model.toLowerCase() === _model.toLowerCase()) !== -1;
  }

  return notch;
}

let dynamicIsland;

function hasDynamicIsland() {
  if (dynamicIsland === undefined) {
    let _brand = getBrand();

    let _model = getModel();

    dynamicIsland = _devicesWithDynamicIsland.default.findIndex(item => item.brand.toLowerCase() === _brand.toLowerCase() && item.model.toLowerCase() === _model.toLowerCase()) !== -1;
  }

  return dynamicIsland;
}

const [hasGms, hasGmsSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.hasGms(),
  syncGetter: () => _nativeInterface.default.hasGmsSync(),
  defaultValue: false
});
exports.hasGmsSync = hasGmsSync;
exports.hasGms = hasGms;
const [hasHms, hasHmsSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.hasHms(),
  syncGetter: () => _nativeInterface.default.hasHmsSync(),
  defaultValue: false
});
exports.hasHmsSync = hasHmsSync;
exports.hasHms = hasHms;
const [getFirstInstallTime, getFirstInstallTimeSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'firstInstallTime',
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.getFirstInstallTime(),
  syncGetter: () => _nativeInterface.default.getFirstInstallTimeSync(),
  defaultValue: -1
});
exports.getFirstInstallTimeSync = getFirstInstallTimeSync;
exports.getFirstInstallTime = getFirstInstallTime;
const [getInstallReferrer, getInstallReferrerSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'installReferrer',
  supportedPlatforms: ['android', 'windows', 'web'],
  getter: () => _nativeInterface.default.getInstallReferrer(),
  syncGetter: () => _nativeInterface.default.getInstallReferrerSync(),
  defaultValue: 'unknown'
});
exports.getInstallReferrerSync = getInstallReferrerSync;
exports.getInstallReferrer = getInstallReferrer;
const [getLastUpdateTime, getLastUpdateTimeSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'lastUpdateTime',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getLastUpdateTime(),
  syncGetter: () => _nativeInterface.default.getLastUpdateTimeSync(),
  defaultValue: -1
});
exports.getLastUpdateTimeSync = getLastUpdateTimeSync;
exports.getLastUpdateTime = getLastUpdateTime;
const [getPhoneNumber, getPhoneNumberSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getPhoneNumber(),
  syncGetter: () => _nativeInterface.default.getPhoneNumberSync(),
  defaultValue: 'unknown'
});
exports.getPhoneNumberSync = getPhoneNumberSync;
exports.getPhoneNumber = getPhoneNumber;
const [getCarrier, getCarrierSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios'],
  getter: () => _nativeInterface.default.getCarrier(),
  syncGetter: () => _nativeInterface.default.getCarrierSync(),
  defaultValue: 'unknown'
});
exports.getCarrierSync = getCarrierSync;
exports.getCarrier = getCarrier;
const [getTotalMemory, getTotalMemorySync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'totalMemory',
  supportedPlatforms: ['android', 'ios', 'windows', 'web'],
  getter: () => _nativeInterface.default.getTotalMemory(),
  syncGetter: () => _nativeInterface.default.getTotalMemorySync(),
  defaultValue: -1
});
exports.getTotalMemorySync = getTotalMemorySync;
exports.getTotalMemory = getTotalMemory;
const [getMaxMemory, getMaxMemorySync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: 'maxMemory',
  supportedPlatforms: ['android', 'windows', 'web'],
  getter: () => _nativeInterface.default.getMaxMemory(),
  syncGetter: () => _nativeInterface.default.getMaxMemorySync(),
  defaultValue: -1
});
exports.getMaxMemorySync = getMaxMemorySync;
exports.getMaxMemory = getMaxMemory;
const [getTotalDiskCapacity, getTotalDiskCapacitySync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'windows', 'web'],
  getter: () => _nativeInterface.default.getTotalDiskCapacity(),
  syncGetter: () => _nativeInterface.default.getTotalDiskCapacitySync(),
  defaultValue: -1
});
exports.getTotalDiskCapacitySync = getTotalDiskCapacitySync;
exports.getTotalDiskCapacity = getTotalDiskCapacity;

async function getTotalDiskCapacityOld() {
  if (_reactNative.Platform.OS === 'android') {
    return _nativeInterface.default.getTotalDiskCapacityOld();
  }

  if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'windows' || _reactNative.Platform.OS === 'web') {
    return getTotalDiskCapacity();
  }

  return -1;
}

function getTotalDiskCapacityOldSync() {
  if (_reactNative.Platform.OS === 'android') {
    return _nativeInterface.default.getTotalDiskCapacityOldSync();
  }

  if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'windows' || _reactNative.Platform.OS === 'web') {
    return getTotalDiskCapacitySync();
  }

  return -1;
}

const [getFreeDiskStorage, getFreeDiskStorageSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'windows', 'web'],
  getter: () => _nativeInterface.default.getFreeDiskStorage(),
  syncGetter: () => _nativeInterface.default.getFreeDiskStorageSync(),
  defaultValue: -1
});
exports.getFreeDiskStorageSync = getFreeDiskStorageSync;
exports.getFreeDiskStorage = getFreeDiskStorage;

async function getFreeDiskStorageOld() {
  if (_reactNative.Platform.OS === 'android') {
    return _nativeInterface.default.getFreeDiskStorageOld();
  }

  if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'windows' || _reactNative.Platform.OS === 'web') {
    return getFreeDiskStorage();
  }

  return -1;
}

function getFreeDiskStorageOldSync() {
  if (_reactNative.Platform.OS === 'android') {
    return _nativeInterface.default.getFreeDiskStorageOldSync();
  }

  if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'windows' || _reactNative.Platform.OS === 'web') {
    return getFreeDiskStorageSync();
  }

  return -1;
}

const [getBatteryLevel, getBatteryLevelSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'windows', 'web'],
  getter: () => _nativeInterface.default.getBatteryLevel(),
  syncGetter: () => _nativeInterface.default.getBatteryLevelSync(),
  defaultValue: -1
});
exports.getBatteryLevelSync = getBatteryLevelSync;
exports.getBatteryLevel = getBatteryLevel;
const [getPowerState, getPowerStateSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['ios', 'android', 'windows', 'web'],
  getter: () => _nativeInterface.default.getPowerState(),
  syncGetter: () => _nativeInterface.default.getPowerStateSync(),
  defaultValue: {}
});
exports.getPowerStateSync = getPowerStateSync;
exports.getPowerState = getPowerState;
const [isBatteryCharging, isBatteryChargingSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'windows', 'web'],
  getter: () => _nativeInterface.default.isBatteryCharging(),
  syncGetter: () => _nativeInterface.default.isBatteryChargingSync(),
  defaultValue: false
});
exports.isBatteryChargingSync = isBatteryChargingSync;
exports.isBatteryCharging = isBatteryCharging;

async function isLandscape() {
  return Promise.resolve(isLandscapeSync());
}

function isLandscapeSync() {
  const {
    height,
    width
  } = _reactNative.Dimensions.get('window');

  return width >= height;
}

const [isAirplaneMode, isAirplaneModeSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'web'],
  getter: () => _nativeInterface.default.isAirplaneMode(),
  syncGetter: () => _nativeInterface.default.isAirplaneModeSync(),
  defaultValue: false
});
exports.isAirplaneModeSync = isAirplaneModeSync;
exports.isAirplaneMode = isAirplaneMode;

const getDeviceType = () => {
  return (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
    memoKey: 'deviceType',
    supportedPlatforms: ['android', 'ios', 'windows'],
    defaultValue: 'unknown',
    getter: () => _nativeInterface.default.deviceType
  });
};

exports.getDeviceType = getDeviceType;

const getDeviceTypeSync = () => {
  return (0, _supportedPlatformInfo.getSupportedPlatformInfoSync)({
    memoKey: 'deviceType',
    supportedPlatforms: ['android', 'ios', 'windows'],
    defaultValue: 'unknown',
    getter: () => _nativeInterface.default.deviceType
  });
};

exports.getDeviceTypeSync = getDeviceTypeSync;
const [supportedAbis, supportedAbisSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: '_supportedAbis',
  supportedPlatforms: ['android', 'ios', 'windows'],
  getter: () => _nativeInterface.default.getSupportedAbis(),
  syncGetter: () => _nativeInterface.default.getSupportedAbisSync(),
  defaultValue: []
});
exports.supportedAbisSync = supportedAbisSync;
exports.supportedAbis = supportedAbis;
const [supported32BitAbis, supported32BitAbisSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: '_supported32BitAbis',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getSupported32BitAbis(),
  syncGetter: () => _nativeInterface.default.getSupported32BitAbisSync(),
  defaultValue: []
});
exports.supported32BitAbisSync = supported32BitAbisSync;
exports.supported32BitAbis = supported32BitAbis;
const [supported64BitAbis, supported64BitAbisSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  memoKey: '_supported64BitAbis',
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getSupported64BitAbis(),
  syncGetter: () => _nativeInterface.default.getSupported64BitAbisSync(),
  defaultValue: []
});
exports.supported64BitAbisSync = supported64BitAbisSync;
exports.supported64BitAbis = supported64BitAbis;

async function hasSystemFeature(feature) {
  if (_reactNative.Platform.OS === 'android') {
    return _nativeInterface.default.hasSystemFeature(feature);
  }

  return false;
}

function hasSystemFeatureSync(feature) {
  if (_reactNative.Platform.OS === 'android') {
    return _nativeInterface.default.hasSystemFeatureSync(feature);
  }

  return false;
}

function isLowBatteryLevel(level) {
  if (_reactNative.Platform.OS === 'android') {
    return level < 0.15;
  }

  return level < 0.2;
}

const [getSystemAvailableFeatures, getSystemAvailableFeaturesSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getSystemAvailableFeatures(),
  syncGetter: () => _nativeInterface.default.getSystemAvailableFeaturesSync(),
  defaultValue: []
});
exports.getSystemAvailableFeaturesSync = getSystemAvailableFeaturesSync;
exports.getSystemAvailableFeatures = getSystemAvailableFeatures;
const [isLocationEnabled, isLocationEnabledSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios', 'web'],
  getter: () => _nativeInterface.default.isLocationEnabled(),
  syncGetter: () => _nativeInterface.default.isLocationEnabledSync(),
  defaultValue: false
});
exports.isLocationEnabledSync = isLocationEnabledSync;
exports.isLocationEnabled = isLocationEnabled;
const [isHeadphonesConnected, isHeadphonesConnectedSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios'],
  getter: () => _nativeInterface.default.isHeadphonesConnected(),
  syncGetter: () => _nativeInterface.default.isHeadphonesConnectedSync(),
  defaultValue: false
});
exports.isHeadphonesConnectedSync = isHeadphonesConnectedSync;
exports.isHeadphonesConnected = isHeadphonesConnected;
const [isWiredHeadphonesConnected, isWiredHeadphonesConnectedSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios'],
  getter: () => _nativeInterface.default.isWiredHeadphonesConnected(),
  syncGetter: () => _nativeInterface.default.isWiredHeadphonesConnectedSync(),
  defaultValue: false
});
exports.isWiredHeadphonesConnectedSync = isWiredHeadphonesConnectedSync;
exports.isWiredHeadphonesConnected = isWiredHeadphonesConnected;
const [isBluetoothHeadphonesConnected, isBluetoothHeadphonesConnectedSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios'],
  getter: () => _nativeInterface.default.isBluetoothHeadphonesConnected(),
  syncGetter: () => _nativeInterface.default.isBluetoothHeadphonesConnectedSync(),
  defaultValue: false
});
exports.isBluetoothHeadphonesConnectedSync = isBluetoothHeadphonesConnectedSync;
exports.isBluetoothHeadphonesConnected = isBluetoothHeadphonesConnected;
const [isMouseConnected, isMouseConnectedSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['windows'],
  getter: () => _nativeInterface.default.isMouseConnected(),
  syncGetter: () => _nativeInterface.default.isMouseConnectedSync(),
  defaultValue: false
});
exports.isMouseConnectedSync = isMouseConnectedSync;
exports.isMouseConnected = isMouseConnected;
const [isKeyboardConnected, isKeyboardConnectedSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['windows'],
  getter: () => _nativeInterface.default.isKeyboardConnected(),
  syncGetter: () => _nativeInterface.default.isKeyboardConnectedSync(),
  defaultValue: false
});
exports.isKeyboardConnectedSync = isKeyboardConnectedSync;
exports.isKeyboardConnected = isKeyboardConnected;
const [getSupportedMediaTypeList, getSupportedMediaTypeListSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android'],
  getter: () => _nativeInterface.default.getSupportedMediaTypeList(),
  syncGetter: () => _nativeInterface.default.getSupportedMediaTypeListSync(),
  defaultValue: []
});
exports.getSupportedMediaTypeListSync = getSupportedMediaTypeListSync;
exports.getSupportedMediaTypeList = getSupportedMediaTypeList;

const isTabletMode = () => (0, _supportedPlatformInfo.getSupportedPlatformInfoAsync)({
  supportedPlatforms: ['windows'],
  getter: () => _nativeInterface.default.isTabletMode(),
  defaultValue: false
});

exports.isTabletMode = isTabletMode;
const [getAvailableLocationProviders, getAvailableLocationProvidersSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['android', 'ios'],
  getter: () => _nativeInterface.default.getAvailableLocationProviders(),
  syncGetter: () => _nativeInterface.default.getAvailableLocationProvidersSync(),
  defaultValue: {}
});
exports.getAvailableLocationProvidersSync = getAvailableLocationProvidersSync;
exports.getAvailableLocationProviders = getAvailableLocationProviders;
const [getBrightness, getBrightnessSync] = (0, _supportedPlatformInfo.getSupportedPlatformInfoFunctions)({
  supportedPlatforms: ['ios'],
  getter: () => _nativeInterface.default.getBrightness(),
  syncGetter: () => _nativeInterface.default.getBrightnessSync(),
  defaultValue: -1
});
exports.getBrightnessSync = getBrightnessSync;
exports.getBrightness = getBrightness;

async function getDeviceToken() {
  if (_reactNative.Platform.OS === 'ios') {
    return _nativeInterface.default.getDeviceToken();
  }

  return 'unknown';
}

const deviceInfoEmitter = new _reactNative.NativeEventEmitter(_reactNative.NativeModules.RNDeviceInfo);

function useBatteryLevel() {
  const [batteryLevel, setBatteryLevel] = (0, _react.useState)(null);
  (0, _react.useEffect)(() => {
    const setInitialValue = async () => {
      const initialValue = await getBatteryLevel();
      setBatteryLevel(initialValue);
    };

    const onChange = level => {
      setBatteryLevel(level);
    };

    setInitialValue();
    const subscription = deviceInfoEmitter.addListener('RNDeviceInfo_batteryLevelDidChange', onChange);
    return () => subscription.remove();
  }, []);
  return batteryLevel;
}

function useBatteryLevelIsLow() {
  const [batteryLevelIsLow, setBatteryLevelIsLow] = (0, _react.useState)(null);
  (0, _react.useEffect)(() => {
    const setInitialValue = async () => {
      const initialValue = await getBatteryLevel();
      isLowBatteryLevel(initialValue) && setBatteryLevelIsLow(initialValue);
    };

    setInitialValue();

    const onChange = level => {
      setBatteryLevelIsLow(level);
    };

    const subscription = deviceInfoEmitter.addListener('RNDeviceInfo_batteryLevelIsLow', onChange);
    return () => subscription.remove();
  }, []);
  return batteryLevelIsLow;
}

function usePowerState() {
  const [powerState, setPowerState] = (0, _react.useState)({});
  (0, _react.useEffect)(() => {
    const setInitialValue = async () => {
      const initialValue = await getPowerState();
      setPowerState(initialValue);
    };

    const onChange = state => {
      setPowerState(state);
    };

    setInitialValue();
    const subscription = deviceInfoEmitter.addListener('RNDeviceInfo_powerStateDidChange', onChange);
    return () => subscription.remove();
  }, []);
  return powerState;
}

function useIsHeadphonesConnected() {
  return (0, _asyncHookWrappers.useOnEvent)('RNDeviceInfo_headphoneConnectionDidChange', isHeadphonesConnected, false);
}

function useIsWiredHeadphonesConnected() {
  return (0, _asyncHookWrappers.useOnEvent)('RNDeviceInfo_headphoneWiredConnectionDidChange', isWiredHeadphonesConnected, false);
}

function useIsBluetoothHeadphonesConnected() {
  return (0, _asyncHookWrappers.useOnEvent)('RNDeviceInfo_headphoneBluetoothConnectionDidChange', isBluetoothHeadphonesConnected, false);
}

function useFirstInstallTime() {
  return (0, _asyncHookWrappers.useOnMount)(getFirstInstallTime, -1);
}

function useDeviceName() {
  return (0, _asyncHookWrappers.useOnMount)(getDeviceName, 'unknown');
}

function useHasSystemFeature(feature) {
  const asyncGetter = (0, _react.useCallback)(() => hasSystemFeature(feature), [feature]);
  return (0, _asyncHookWrappers.useOnMount)(asyncGetter, false);
}

function useIsEmulator() {
  return (0, _asyncHookWrappers.useOnMount)(isEmulator, false);
}

function useManufacturer() {
  return (0, _asyncHookWrappers.useOnMount)(getManufacturer, 'unknown');
}

function useBrightness() {
  const [brightness, setBrightness] = (0, _react.useState)(null);
  (0, _react.useEffect)(() => {
    const setInitialValue = async () => {
      const initialValue = await getBrightness();
      setBrightness(initialValue);
    };

    const onChange = value => {
      setBrightness(value);
    };

    setInitialValue();
    const subscription = deviceInfoEmitter.addListener('RNDeviceInfo_brightnessDidChange', onChange);
    return () => subscription.remove();
  }, []);
  return brightness;
}

const DeviceInfo = {
  getAndroidId,
  getAndroidIdSync,
  getApiLevel,
  getApiLevelSync,
  getApplicationName,
  getAvailableLocationProviders,
  getAvailableLocationProvidersSync,
  getBaseOs,
  getBaseOsSync,
  getBatteryLevel,
  getBatteryLevelSync,
  getBootloader,
  getBootloaderSync,
  getBrand,
  getBuildId,
  getBuildIdSync,
  getBuildNumber,
  getBundleId,
  getCarrier,
  getCarrierSync,
  getCodename,
  getCodenameSync,
  getDevice,
  getDeviceId,
  getDeviceName,
  getDeviceNameSync,
  getDeviceSync,
  getDeviceToken,
  getDeviceType,
  getDisplay,
  getDisplaySync,
  getFingerprint,
  getFingerprintSync,
  getFirstInstallTime,
  getFirstInstallTimeSync,
  getFontScale,
  getFontScaleSync,
  getFreeDiskStorage,
  getFreeDiskStorageOld,
  getFreeDiskStorageSync,
  getFreeDiskStorageOldSync,
  getHardware,
  getHardwareSync,
  getHost,
  getHostSync,
  getHostNames,
  getHostNamesSync,
  getIncremental,
  getIncrementalSync,
  getInstallerPackageName,
  getInstallerPackageNameSync,
  getInstallReferrer,
  getInstallReferrerSync,
  getInstanceId,
  getInstanceIdSync,
  getIpAddress,
  getIpAddressSync,
  getLastUpdateTime,
  getLastUpdateTimeSync,
  getMacAddress,
  getMacAddressSync,
  getManufacturer,
  getManufacturerSync,
  getMaxMemory,
  getMaxMemorySync,
  getModel,
  getPhoneNumber,
  getPhoneNumberSync,
  getPowerState,
  getPowerStateSync,
  getPreviewSdkInt,
  getPreviewSdkIntSync,
  getProduct,
  getProductSync,
  getReadableVersion,
  getSecurityPatch,
  getSecurityPatchSync,
  getSerialNumber,
  getSerialNumberSync,
  getSystemAvailableFeatures,
  getSystemAvailableFeaturesSync,
  getSystemName,
  getSystemVersion,
  getTags,
  getTagsSync,
  getTotalDiskCapacity,
  getTotalDiskCapacityOld,
  getTotalDiskCapacitySync,
  getTotalDiskCapacityOldSync,
  getTotalMemory,
  getTotalMemorySync,
  getType,
  getTypeSync,
  getUniqueId,
  getUniqueIdSync,
  getUsedMemory,
  getUsedMemorySync,
  getUserAgent,
  getUserAgentSync,
  getVersion,
  getBrightness,
  getBrightnessSync,
  hasGms,
  hasGmsSync,
  hasHms,
  hasHmsSync,
  hasNotch,
  hasDynamicIsland,
  hasSystemFeature,
  hasSystemFeatureSync,
  isAirplaneMode,
  isAirplaneModeSync,
  isBatteryCharging,
  isBatteryChargingSync,
  isCameraPresent,
  isCameraPresentSync,
  isEmulator,
  isEmulatorSync,
  isHeadphonesConnected,
  isHeadphonesConnectedSync,
  isWiredHeadphonesConnected,
  isWiredHeadphonesConnectedSync,
  isBluetoothHeadphonesConnected,
  isBluetoothHeadphonesConnectedSync,
  isLandscape,
  isLandscapeSync,
  isLocationEnabled,
  isLocationEnabledSync,
  isPinOrFingerprintSet,
  isPinOrFingerprintSetSync,
  isMouseConnected,
  isMouseConnectedSync,
  isKeyboardConnected,
  isKeyboardConnectedSync,
  isTabletMode,
  isTablet,
  isLowRamDevice,
  isDisplayZoomed,
  supported32BitAbis,
  supported32BitAbisSync,
  supported64BitAbis,
  supported64BitAbisSync,
  supportedAbis,
  supportedAbisSync,
  syncUniqueId,
  useBatteryLevel,
  useBatteryLevelIsLow,
  useDeviceName,
  useFirstInstallTime,
  useHasSystemFeature,
  useIsEmulator,
  usePowerState,
  useManufacturer,
  useIsHeadphonesConnected,
  useIsWiredHeadphonesConnected,
  useIsBluetoothHeadphonesConnected,
  useBrightness,
  getSupportedMediaTypeList,
  getSupportedMediaTypeListSync
};
var _default = DeviceInfo;
exports.default = _default;
//# sourceMappingURL=index.js.map