import React, { useState } from 'react';
import { usePaxHardware } from '../hooks/usePaxHardware';
import { PaxReceiptData } from '../services/integratedPaxService';

interface PaxPaymentTerminalProps {
  onPaymentComplete?: (result: any) => void;
  onReceiptPrinted?: (success: boolean) => void;
}

const PaxPaymentTerminal: React.FC<PaxPaymentTerminalProps> = ({
  onPaymentComplete,
  onReceiptPrinted
}) => {
  const { isReady, isProcessing, processPayment, printReceipt, checkStatus, error } = usePaxHardware();
  const [amount, setAmount] = useState<string>('');
  const [lastTransaction, setLastTransaction] = useState<any>(null);

  const handlePayment = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    try {
      const paymentRequest = {
        amount: parseFloat(amount),
        transactionId: `TXN_${Date.now()}`
      };

      const result = await processPayment(paymentRequest);
      
      if (result.success) {
        setLastTransaction(result);
        onPaymentComplete?.(result);
        alert(`Payment successful! Transaction ID: ${result.transactionId}`);
      } else {
        alert(`Payment failed: ${result.message}`);
      }
    } catch (err) {
      alert(`Payment error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handlePrintReceipt = async () => {
    if (!lastTransaction) {
      alert('No transaction to print receipt for');
      return;
    }

    try {
      const receiptData: PaxReceiptData = {
        transactionId: lastTransaction.transactionId,
        amount: parseFloat(amount),
        cardInfo: lastTransaction.cardInfo,
        timestamp: new Date().toISOString(),
        merchantInfo: {
          name: 'Your Store Name',
          address: '123 Store Street, City, Country'
        }
      };

      const success = await printReceipt(receiptData);
      onReceiptPrinted?.(success);
      
      if (success) {
        alert('Receipt printed successfully!');
      } else {
        alert('Failed to print receipt');
      }
    } catch (err) {
      alert(`Print error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleRefreshStatus = async () => {
    await checkStatus();
  };

  return (
    <div className="pax-payment-terminal">
      <div className="terminal-header">
        <h2>PAX Payment Terminal</h2>
        <div className="status-indicator">
          <span className={`status-dot ${isReady ? 'ready' : 'not-ready'}`}></span>
          <span className="status-text">
            {isReady ? 'Terminal Ready' : 'Terminal Not Ready'}
          </span>
          <button onClick={handleRefreshStatus} className="refresh-btn">
            Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <strong>Error:</strong> {error}
        </div>
      )}

      <div className="payment-section">
        <div className="amount-input">
          <label htmlFor="amount">Amount (£):</label>
          <input
            id="amount"
            type="number"
            step="0.01"
            min="0"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder="0.00"
            disabled={!isReady || isProcessing}
          />
        </div>

        <button
          onClick={handlePayment}
          disabled={!isReady || isProcessing || !amount}
          className="payment-btn"
        >
          {isProcessing ? 'Processing...' : 'Process Payment'}
        </button>
      </div>

      {lastTransaction && (
        <div className="transaction-details">
          <h3>Last Transaction</h3>
          <div className="transaction-info">
            <p><strong>Transaction ID:</strong> {lastTransaction.transactionId}</p>
            <p><strong>Amount:</strong> £{amount}</p>
            <p><strong>Auth Code:</strong> {lastTransaction.authCode}</p>
            {lastTransaction.cardInfo && (
              <>
                <p><strong>Card:</strong> {lastTransaction.cardInfo.brand} ****{lastTransaction.cardInfo.last4}</p>
                <p><strong>Entry Method:</strong> {lastTransaction.cardInfo.entryMethod}</p>
              </>
            )}
          </div>
          
          <button
            onClick={handlePrintReceipt}
            disabled={!isReady}
            className="print-btn"
          >
            Print Receipt
          </button>
        </div>
      )}

      <div className="terminal-info">
        <h4>Terminal Information</h4>
        <p><strong>IP Address:</strong> **************</p>
        <p><strong>Model:</strong> PAX A920PFG</p>
        <p><strong>Status:</strong> {isReady ? 'Connected' : 'Disconnected'}</p>
      </div>

      <style>{`
        .pax-payment-terminal {
          max-width: 500px;
          margin: 0 auto;
          padding: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-family: Arial, sans-serif;
        }

        .terminal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 10px;
          border-bottom: 1px solid #eee;
        }

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .status-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
        }

        .status-dot.ready {
          background-color: #4CAF50;
        }

        .status-dot.not-ready {
          background-color: #f44336;
        }

        .refresh-btn {
          padding: 4px 8px;
          font-size: 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background: white;
          cursor: pointer;
        }

        .error-message {
          background-color: #ffebee;
          color: #c62828;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 20px;
        }

        .payment-section {
          margin-bottom: 20px;
        }

        .amount-input {
          margin-bottom: 15px;
        }

        .amount-input label {
          display: block;
          margin-bottom: 5px;
          font-weight: bold;
        }

        .amount-input input {
          width: 100%;
          padding: 10px;
          font-size: 16px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }

        .payment-btn {
          width: 100%;
          padding: 15px;
          font-size: 16px;
          font-weight: bold;
          color: white;
          background-color: #2196F3;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }

        .payment-btn:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }

        .transaction-details {
          background-color: #f5f5f5;
          padding: 15px;
          border-radius: 4px;
          margin-bottom: 20px;
        }

        .transaction-info p {
          margin: 5px 0;
        }

        .print-btn {
          width: 100%;
          padding: 10px;
          margin-top: 10px;
          background-color: #4CAF50;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }

        .print-btn:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }

        .terminal-info {
          background-color: #f9f9f9;
          padding: 15px;
          border-radius: 4px;
        }

        .terminal-info h4 {
          margin-top: 0;
        }

        .terminal-info p {
          margin: 5px 0;
        }
      `}</style>
    </div>
  );
};

export default PaxPaymentTerminal;
