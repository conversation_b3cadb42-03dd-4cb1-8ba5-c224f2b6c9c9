{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/protocol/LockdownProtocol.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport plist from '@expo/plist';\nimport Debug from 'debug';\nimport { Socket } from 'net';\n\nimport type { ProtocolWriter } from './AbstractProtocol';\nimport { PlistProtocolReader, ProtocolClient, ProtocolReaderFactory } from './AbstractProtocol';\nimport { CommandError } from '../../../../utils/errors';\n\nconst debug = Debug('expo:apple-device:protocol:lockdown');\nexport const LOCKDOWN_HEADER_SIZE = 4;\n\nexport interface LockdownCommand {\n  Command: string;\n  [key: string]: any;\n}\n\nexport interface LockdownResponse {\n  Status: string;\n  [key: string]: any;\n}\n\nexport interface LockdownErrorResponse {\n  Error: string;\n  Request?: string;\n  Service?: string;\n}\n\nexport interface LockdownRequest {\n  Request: string;\n  [key: string]: any;\n}\n\nfunction isDefined(val: any) {\n  return typeof val !== 'undefined';\n}\n\nexport function isLockdownResponse(resp: any): resp is LockdownResponse {\n  return isDefined(resp.Status);\n}\n\nexport function isLockdownErrorResponse(resp: any): resp is LockdownErrorResponse {\n  return isDefined(resp.Error);\n}\n\nexport class LockdownProtocolClient<\n  MessageType extends LockdownRequest | LockdownCommand = LockdownRequest,\n> extends ProtocolClient<MessageType> {\n  constructor(socket: Socket) {\n    super(socket, new ProtocolReaderFactory(LockdownProtocolReader), new LockdownProtocolWriter());\n  }\n}\n\nexport class LockdownProtocolReader extends PlistProtocolReader {\n  constructor(callback: (data: any) => any) {\n    super(LOCKDOWN_HEADER_SIZE, callback);\n  }\n\n  parseHeader(data: Buffer) {\n    return data.readUInt32BE(0);\n  }\n\n  parseBody(data: Buffer) {\n    const resp = super.parseBody(data);\n    debug(`Response: ${JSON.stringify(resp)}`);\n    if (isLockdownErrorResponse(resp)) {\n      if (resp.Error === 'DeviceLocked') {\n        throw new CommandError('APPLE_DEVICE_LOCKED', 'Device is currently locked.');\n      }\n\n      if (resp.Error === 'InvalidService') {\n        let errorMessage = `${resp.Error}: ${resp.Service} (request: ${resp.Request})`;\n        if (resp.Service === 'com.apple.debugserver') {\n          errorMessage +=\n            '\\nTry reconnecting your device. You can also debug service logs with `export DEBUG=expo:xdl:ios:*`';\n        }\n        throw new CommandError('APPLE_DEVICE_LOCKDOWN', errorMessage);\n      }\n\n      throw new CommandError('APPLE_DEVICE_LOCKDOWN', resp.Error);\n    }\n    return resp;\n  }\n}\n\nexport class LockdownProtocolWriter implements ProtocolWriter {\n  write(socket: Socket, plistData: any) {\n    debug(`socket write: ${JSON.stringify(plistData)}`);\n    const plistMessage = plist.build(plistData);\n    const header = Buffer.alloc(LOCKDOWN_HEADER_SIZE);\n    header.writeUInt32BE(plistMessage.length, 0);\n    socket.write(header);\n    socket.write(plistMessage);\n  }\n}\n"], "names": ["LOCKDOWN_HEADER_SIZE", "LockdownProtocolClient", "LockdownProtocolReader", "LockdownProtocolWriter", "isLockdownErrorResponse", "isLockdownResponse", "debug", "Debug", "isDefined", "val", "resp", "Status", "Error", "ProtocolClient", "constructor", "socket", "ProtocolReaderFactory", "PlistProtocolReader", "callback", "parse<PERSON><PERSON><PERSON>", "data", "readUInt32BE", "parseBody", "JSON", "stringify", "CommandError", "errorMessage", "Service", "Request", "write", "plistData", "plistMessage", "plist", "build", "header", "<PERSON><PERSON><PERSON>", "alloc", "writeUInt32BE", "length"], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;IAWYA,oBAAoB;eAApBA;;IAmCAC,sBAAsB;eAAtBA;;IAQAC,sBAAsB;eAAtBA;;IAgCAC,sBAAsB;eAAtBA;;IA5CGC,uBAAuB;eAAvBA;;IAJAC,kBAAkB;eAAlBA;;;;gEApCE;;;;;;;gEACA;;;;;;kCAIyD;wBAC9C;;;;;;AAE7B,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AACb,MAAMP,uBAAuB;AAuBpC,SAASQ,UAAUC,GAAQ;IACzB,OAAO,OAAOA,QAAQ;AACxB;AAEO,SAASJ,mBAAmBK,IAAS;IAC1C,OAAOF,UAAUE,KAAKC,MAAM;AAC9B;AAEO,SAASP,wBAAwBM,IAAS;IAC/C,OAAOF,UAAUE,KAAKE,KAAK;AAC7B;AAEO,MAAMX,+BAEHY,gCAAc;IACtBC,YAAYC,MAAc,CAAE;QAC1B,KAAK,CAACA,QAAQ,IAAIC,uCAAqB,CAACd,yBAAyB,IAAIC;IACvE;AACF;AAEO,MAAMD,+BAA+Be,qCAAmB;IAC7DH,YAAYI,QAA4B,CAAE;QACxC,KAAK,CAAClB,sBAAsBkB;IAC9B;IAEAC,YAAYC,IAAY,EAAE;QACxB,OAAOA,KAAKC,YAAY,CAAC;IAC3B;IAEAC,UAAUF,IAAY,EAAE;QACtB,MAAMV,OAAO,KAAK,CAACY,UAAUF;QAC7Bd,MAAM,CAAC,UAAU,EAAEiB,KAAKC,SAAS,CAACd,OAAO;QACzC,IAAIN,wBAAwBM,OAAO;YACjC,IAAIA,KAAKE,KAAK,KAAK,gBAAgB;gBACjC,MAAM,IAAIa,oBAAY,CAAC,uBAAuB;YAChD;YAEA,IAAIf,KAAKE,KAAK,KAAK,kBAAkB;gBACnC,IAAIc,eAAe,GAAGhB,KAAKE,KAAK,CAAC,EAAE,EAAEF,KAAKiB,OAAO,CAAC,WAAW,EAAEjB,KAAKkB,OAAO,CAAC,CAAC,CAAC;gBAC9E,IAAIlB,KAAKiB,OAAO,KAAK,yBAAyB;oBAC5CD,gBACE;gBACJ;gBACA,MAAM,IAAID,oBAAY,CAAC,yBAAyBC;YAClD;YAEA,MAAM,IAAID,oBAAY,CAAC,yBAAyBf,KAAKE,KAAK;QAC5D;QACA,OAAOF;IACT;AACF;AAEO,MAAMP;IACX0B,MAAMd,MAAc,EAAEe,SAAc,EAAE;QACpCxB,MAAM,CAAC,cAAc,EAAEiB,KAAKC,SAAS,CAACM,YAAY;QAClD,MAAMC,eAAeC,gBAAK,CAACC,KAAK,CAACH;QACjC,MAAMI,SAASC,OAAOC,KAAK,CAACpC;QAC5BkC,OAAOG,aAAa,CAACN,aAAaO,MAAM,EAAE;QAC1CvB,OAAOc,KAAK,CAACK;QACbnB,OAAOc,KAAK,CAACE;IACf;AACF"}