{"name": "babel-plugin-istanbul", "version": "6.1.1", "author": "Thai Pangsakulyanont @dtinth", "license": "BSD-3-<PERSON><PERSON>", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "main": "lib/index.js", "files": ["lib"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "devDependencies": {"@babel/cli": "^7.7.5", "@babel/core": "^7.7.5", "@babel/plugin-transform-modules-commonjs": "^7.7.5", "@babel/register": "^7.7.4", "chai": "^4.2.0", "coveralls": "^3.0.9", "cross-env": "^6.0.3", "mocha": "^6.2.2", "nyc": "^15.0.0", "pmock": "^0.2.3", "standard": "^14.3.1"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "release": "babel src --out-dir lib", "pretest": "standard && npm run release", "test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --timeout 5000 test/*.js", "prepublish": "npm test && npm run release"}, "standard": {"ignore": ["fixtures/*.js"]}, "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git"}, "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "engines": {"node": ">=8"}}