import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as o from"../../core/i18n/i18n.js";import*as r from"../../core/platform/platform.js";import*as a from"../../core/protocol_client/protocol_client.js";import*as s from"../../core/sdk/sdk.js";import*as i from"../../models/bindings/bindings.js";import*as n from"../../models/text_utils/text_utils.js";import*as d from"../../ui/components/data_grid/data_grid.js";import*as l from"../../ui/components/icon_button/icon_button.js";import*as m from"../../ui/legacy/components/source_frame/source_frame.js";import*as c from"../../ui/legacy/legacy.js";import*as p from"../../ui/lit-html/lit-html.js";import*as g from"../../ui/visual_logging/visual_logging.js";import*as h from"./components/components.js";const u=new CSSStyleSheet;u.replaceSync(".protocol-monitor .protocol-monitor-toolbar{border-bottom:1px solid var(--sys-color-divider)}.protocol-monitor .protocol-monitor-bottom-toolbar{border-top:1px solid var(--sys-color-divider)}\n/*# sourceURL=protocolMonitor.css */\n");const b={method:"Method",type:"Type",request:"Request",response:"Response",timestamp:"Timestamp",elapsedTime:"Elapsed time",target:"Target",record:"Record",clearAll:"Clear all",filter:"Filter",documentation:"Documentation",editAndResend:"Edit and resend",sMs:"{PH1} ms",noMessageSelected:"No message selected",save:"Save",session:"Session",sendRawCDPCommand:"Send a raw `CDP` command",sendRawCDPCommandExplanation:"Format: `'Domain.commandName'` for a command without parameters, or `'{\"command\":\"Domain.commandName\", \"parameters\": {...}}'` as a JSON object for a command with parameters. `'cmd'`/`'method'` and `'args'`/`'params'`/`'arguments'` are also supported as alternative keys for the `JSON` object.",selectTarget:"Select a target",showCDPCommandEditor:"Show CDP command editor",hideCDPCommandEditor:"Hide  CDP command editor",CDPCommandEditorShown:"CDP command editor shown",CDPCommandEditorHidden:"CDP command editor hidden"},I=o.i18n.registerUIStrings("panels/protocol_monitor/ProtocolMonitor.ts",b),w=o.i18n.getLocalizedString.bind(void 0,I),v=e=>p.html`${w(b.sMs,{PH1:String(e)})}`,S=e=>{const t=new Map;for(const o of e)for(const e of Object.keys(o.metadata))t.set(e,o.metadata[e]);return t},T=S(a.InspectorBackend.inspectorBackend.agentPrototypes.values()),f=a.InspectorBackend.inspectorBackend.typeMap,y=a.InspectorBackend.inspectorBackend.enumMap;class C extends(e.ObjectWrapper.eventMixin(c.Widget.VBox)){started;startTime;requestTimeForId;dataGridRowForId;infoWidget;dataGridIntegrator;filterParser;suggestionBuilder;textFilterUI;selector;#e=new x;#t;#o;constructor(e){super(!0),this.started=!1,this.startTime=0,this.dataGridRowForId=new Map,this.requestTimeForId=new Map;const o=new c.Toolbar.Toolbar("protocol-monitor-toolbar",this.contentElement);o.element.setAttribute("jslog",`${g.toolbar("top")}`),this.contentElement.classList.add("protocol-monitor");const r=new c.Toolbar.ToolbarToggle(w(b.record),"record-start","record-stop","protocol-monitor.toggle-recording");r.addEventListener("Click",(()=>{r.setToggled(!r.toggled()),this.setRecording(r.toggled())})),r.setToggleWithRedColor(!0),o.appendToolbarItem(r),r.setToggled(!0);const a=new c.Toolbar.ToolbarButton(w(b.clearAll),"clear",void 0,"protocol-monitor.clear-all");a.addEventListener("Click",(()=>{this.dataGridIntegrator.update({...this.dataGridIntegrator.data(),rows:[]}),this.infoWidget.render(null)})),o.appendToolbarItem(a);const s=new c.Toolbar.ToolbarButton(w(b.save),"download",void 0,"protocol-monitor.save");s.addEventListener("Click",(()=>{this.saveAsFile()})),o.appendToolbarItem(s),this.selector=this.#r(),this.infoWidget=new P;const i={paddingRowsCount:100,showScrollbar:!0,columns:[{id:"type",title:w(b.type),sortable:!0,widthWeighting:1,visible:!0,hideable:!0,styles:{"text-align":"center"}},{id:"method",title:w(b.method),sortable:!1,widthWeighting:5,visible:!0,hideable:!1},{id:"request",title:w(b.request),sortable:!1,widthWeighting:5,visible:!0,hideable:!0},{id:"response",title:w(b.response),sortable:!1,widthWeighting:5,visible:!0,hideable:!0},{id:"elapsed-time",title:w(b.elapsedTime),sortable:!0,widthWeighting:2,visible:!1,hideable:!0},{id:"timestamp",title:w(b.timestamp),sortable:!0,widthWeighting:5,visible:!1,hideable:!0},{id:"target",title:w(b.target),sortable:!0,widthWeighting:5,visible:!1,hideable:!0},{id:"session",title:w(b.session),sortable:!0,widthWeighting:5,visible:!1,hideable:!0}],rows:[],contextMenus:{bodyRow:(o,r,a)=>{const s=d.DataGridUtils.getRowEntryForColumnId(a,"method"),i=d.DataGridUtils.getRowEntryForColumnId(a,"type");o.editSection().appendItem(w(b.editAndResend),(()=>{if(!s.value)return;const t=this.infoWidget.request,o=this.infoWidget.targetId,r=String(s.value);"OnlyMain"===e.showMode()&&e.toggleSidebar(),this.dispatchEventToListeners("CommandChange",{command:r,parameters:t,targetId:o})}),{jslogContext:"edit-and-resend",disabled:"sent"!==i.title}),o.editSection().appendItem(w(b.filter),(()=>{const e=d.DataGridUtils.getRowEntryForColumnId(a,"method");this.textFilterUI.setValue(`method:${e.value}`,!0)}),{jslogContext:"filter"}),o.footerSection().appendItem(w(b.documentation),(()=>{if(!s.value)return;const[e,o]=String(s.value).split("."),r="sent"===i.title?"method":"event";t.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(`https://chromedevtools.github.io/devtools-protocol/tot/${e}#${r}-${o}`)}),{jslogContext:"documentation"})}}};this.dataGridIntegrator=new d.DataGridControllerIntegrator.DataGridControllerIntegrator(i),this.dataGridIntegrator.dataGrid.addEventListener("cellfocused",(e=>{const t=e.data.row,o={request:d.DataGridUtils.getRowEntryForColumnId(t,"request"),response:d.DataGridUtils.getRowEntryForColumnId(t,"response"),target:d.DataGridUtils.getRowEntryForColumnId(t,"target"),type:d.DataGridUtils.getRowEntryForColumnId(t,"type").title,selectedTab:"request"===e.data.cell.columnId?"request":"response"===e.data.cell.columnId?"response":void 0};this.infoWidget.render(o)})),this.dataGridIntegrator.dataGrid.addEventListener("newuserfiltertext",(e=>{this.textFilterUI.setValue(e.data.filterText,!0)}));const l=new c.SplitWidget.SplitWidget(!0,!0,"protocol-monitor-panel-split",250);l.show(this.contentElement),l.setMainWidget(this.dataGridIntegrator),l.setSidebarWidget(this.infoWidget);const m=["method","request","response","type","target","session"];this.filterParser=new n.TextUtils.FilterParser(m),this.suggestionBuilder=new c.FilterSuggestionBuilder.FilterSuggestionBuilder(m),this.textFilterUI=new c.Toolbar.ToolbarFilter(void 0,1,.2,"",this.suggestionBuilder.completions.bind(this.suggestionBuilder),!0,"filter"),this.textFilterUI.addEventListener("TextChanged",(e=>{const t=e.data,o=this.filterParser.parse(t);this.dataGridIntegrator.update({...this.dataGridIntegrator.data(),filters:o})}));const p=new c.Toolbar.Toolbar("protocol-monitor-bottom-toolbar",this.contentElement);p.element.setAttribute("jslog",`${g.toolbar("bottom")}`),p.appendToolbarItem(e.createShowHideSidebarButton(w(b.showCDPCommandEditor),w(b.hideCDPCommandEditor),w(b.CDPCommandEditorShown),w(b.CDPCommandEditorHidden),"protocol-monitor.toggle-command-editor")),this.#o=this.#a(),p.appendToolbarItem(this.#o),p.appendToolbarItem(this.selector);const h=p.element?.shadowRoot,u=h?.querySelector(".toolbar-input"),I=h?.querySelector(".toolbar-select-container"),v=()=>{const t=e.sidebarWidget();if(!(t instanceof W))return;const o=t.jsonEditor.getCommandJson(),r=t.jsonEditor.targetId;if(r){const e=this.selector.options().findIndex((e=>e.value===r));-1!==e&&(this.selector.setSelectedIndex(e),this.#t=r)}o&&this.#o.setValue(o)};e.addEventListener("ShowModeChanged",(e=>{if("OnlyMain"===e.data)v(),u?.setAttribute("style","display:flex; flex-grow: 1"),I?.setAttribute("style","display:flex");else{const{command:e,parameters:t}=D(this.#o.value());this.dispatchEventToListeners("CommandChange",{command:e,parameters:t,targetId:this.#t}),u?.setAttribute("style","display:none"),I?.setAttribute("style","display:none")}})),o.appendToolbarItem(this.textFilterUI)}#a(){const e=w(b.sendRawCDPCommand),t=e,o=w(b.sendRawCDPCommandExplanation),r=new c.Toolbar.ToolbarInput(e,t,1,.2,o,this.#e.buildTextPromptCompletions,!1,"command-input");return r.addEventListener("EnterPressed",(()=>{this.#e.addEntry(r.value());const{command:e,parameters:t}=D(r.value());this.onCommandSend(e,t,this.#t)})),r}#r(){const e=new c.Toolbar.ToolbarComboBox((()=>{this.#t=e.selectedOption()?.value}),w(b.selectTarget),void 0,"target-selector");e.setMaxWidth(120);const t=s.TargetManager.TargetManager.instance(),o=()=>{e.removeOptions();for(const o of t.targets())e.createOption(`${o.name()} (${o.inspectedURL()})`,o.id())};return t.addEventListener("AvailableTargetsChanged",o),o(),e}onCommandSend(e,t,o){const r=a.InspectorBackend.test,i=s.TargetManager.TargetManager.instance(),n=o?i.targetById(o):null,d=n?n.sessionId:"";r.sendRawMessage(e,t,(()=>{}),d)}wasShown(){this.started||(this.registerCSSFiles([u]),this.started=!0,this.startTime=Date.now(),this.setRecording(!0))}setRecording(e){const t=a.InspectorBackend.test;e?(t.onMessageSent=this.messageSent.bind(this),t.onMessageReceived=this.messageReceived.bind(this)):(t.onMessageSent=null,t.onMessageReceived=null)}targetToString(e){return e?e.decorateLabel(`${e.name()} ${e===s.TargetManager.TargetManager.instance().rootTarget()?"":e.id()}`):""}messageReceived(e,t){if("id"in e&&e.id){const t=this.dataGridRowForId.get(e.id);if(!t)return;const o=this.dataGridIntegrator.data().rows.findIndex((e=>t===e)),r={...t,cells:t.cells.map((t=>{if("response"===t.columnId)return{...t,value:JSON.stringify(e.result||e.error)};if("elapsed-time"===t.columnId){const o=this.requestTimeForId.get(e.id);if(o)return{...t,value:Date.now()-o,renderer:v}}return t}))},a=[...this.dataGridIntegrator.data().rows];return a[o]=r,this.dataGridRowForId.delete(e.id),void this.dataGridIntegrator.update({...this.dataGridIntegrator.data(),rows:a})}const o=t,r=new l.Icon.Icon;r.data={iconName:"arrow-down",color:"var(--icon-request)",width:"16px",height:"16px"};const a={cells:[{columnId:"method",value:e.method,title:e.method},{columnId:"request",value:"",renderer:d.DataGridRenderers.codeBlockRenderer},{columnId:"response",value:JSON.stringify(e.params),renderer:d.DataGridRenderers.codeBlockRenderer},{columnId:"timestamp",value:Date.now()-this.startTime,renderer:v},{columnId:"elapsed-time",value:""},{columnId:"type",value:r,title:"received",renderer:d.DataGridRenderers.iconRenderer},{columnId:"target",value:this.targetToString(o)},{columnId:"session",value:e.sessionId||""}],hidden:!1};this.dataGridIntegrator.update({...this.dataGridIntegrator.data(),rows:this.dataGridIntegrator.data().rows.concat([a])})}messageSent(e,t){const o=t,r=new l.Icon.Icon;r.data={iconName:"arrow-up-down",color:"var(--icon-request-response)",width:"16px",height:"16px"};const a={styles:{"--override-data-grid-row-background-color":"var(--sys-color-surface3)"},cells:[{columnId:"method",value:e.method,title:e.method},{columnId:"request",value:JSON.stringify(e.params),renderer:d.DataGridRenderers.codeBlockRenderer},{columnId:"response",value:"(pending)",renderer:d.DataGridRenderers.codeBlockRenderer},{columnId:"timestamp",value:Date.now()-this.startTime,renderer:v},{columnId:"elapsed-time",value:"(pending)"},{columnId:"type",value:r,title:"sent",renderer:d.DataGridRenderers.iconRenderer},{columnId:"target",value:String(o?.id())},{columnId:"session",value:e.sessionId||""}],hidden:!1};this.requestTimeForId.set(e.id,Date.now()),this.dataGridRowForId.set(e.id,a),this.dataGridIntegrator.update({...this.dataGridIntegrator.data(),rows:this.dataGridIntegrator.data().rows.concat([a])})}async saveAsFile(){const e=new Date,t="ProtocolMonitor-"+r.DateUtilities.toISO8601Compact(e)+".json",o=new i.FileUtils.FileOutputStream;if(!await o.open(t))return;const a=[];for(const e of this.dataGridIntegrator.data().rows){const t=Object.fromEntries(e.cells.map((e=>[e.columnId,e.value])));a.push(t)}o.write(JSON.stringify(a,null,"  ")),o.close()}}class E extends c.Widget.VBox{#s;#i=new W;#n;#d=400;constructor(){super(!0),this.element.setAttribute("jslog",`${g.panel("protocol-monitor").track({resize:!0})}`),this.#s=new c.SplitWidget.SplitWidget(!0,!1,"protocol-monitor-split-container",this.#d),this.#s.show(this.contentElement),this.#n=new C(this.#s),this.#n.addEventListener("CommandChange",(e=>{this.#i.jsonEditor.displayCommand(e.data.command,e.data.parameters,e.data.targetId)})),this.#i.element.style.overflow="hidden",this.#s.setMainWidget(this.#n),this.#s.setSidebarWidget(this.#i),this.#s.hideSidebar(!0),this.#i.addEventListener("CommandSent",(e=>{this.#n.onCommandSend(e.data.command,e.data.parameters,e.data.targetId)}))}}class x{#l=200;#m=new Set;constructor(e){void 0!==e&&(this.#l=e)}buildTextPromptCompletions=async(e,t,o)=>{if(!t&&!o&&e)return[];const r=[...this.#m].reverse();return r.push(...T.keys()),r.filter((e=>e.startsWith(t))).map((e=>({text:e})))};addEntry(e){if(this.#m.has(e)&&this.#m.delete(e),this.#m.add(e),this.#m.size>this.#l){const e=this.#m.values().next().value;this.#m.delete(e)}}}class P extends c.Widget.VBox{tabbedPane;request;targetId="";constructor(){super(),this.tabbedPane=new c.TabbedPane.TabbedPane,this.tabbedPane.appendTab("request",w(b.request),new c.Widget.Widget),this.tabbedPane.appendTab("response",w(b.response),new c.Widget.Widget),this.tabbedPane.show(this.contentElement),this.tabbedPane.selectTab("response"),this.request={},this.render(null)}render(e){if(!(e&&e.request&&e.response&&e.target))return this.tabbedPane.changeTabView("request",new c.EmptyWidget.EmptyWidget(w(b.noMessageSelected))),void this.tabbedPane.changeTabView("response",new c.EmptyWidget.EmptyWidget(w(b.noMessageSelected)));const t=e&&e.type&&"sent"===e.type;this.tabbedPane.setTabEnabled("request",Boolean(t)),t||this.tabbedPane.selectTab("response");const o=JSON.parse(String(e.request.value)||"null");this.request=o,this.targetId=String(e.target.value),this.tabbedPane.changeTabView("request",m.JSONView.JSONView.createViewSync(o));const r="(pending)"===e.response.value?null:JSON.parse(String(e.response.value)||"null");this.tabbedPane.changeTabView("response",m.JSONView.JSONView.createViewSync(r)),e.selectedTab&&this.tabbedPane.selectTab(e.selectedTab)}}class W extends(e.ObjectWrapper.eventMixin(c.Widget.VBox)){jsonEditor;constructor(){super(),this.element.setAttribute("jslog",`${g.pane("command-editor").track({resize:!0})}`),this.jsonEditor=new h.JSONEditor.JSONEditor,this.jsonEditor.metadataByCommand=T,this.jsonEditor.typesByName=f,this.jsonEditor.enumsByName=y,this.element.append(this.jsonEditor),this.jsonEditor.addEventListener(h.JSONEditor.SubmitEditorEvent.eventName,(e=>{this.dispatchEventToListeners("CommandSent",e.data)}))}}function D(e){let t=null;try{t=JSON.parse(e)}catch(e){}return{command:t?t.command||t.method||t.cmd||"":e,parameters:t?.parameters||t?.params||t?.args||t?.arguments||{}}}var G=Object.freeze({__proto__:null,buildProtocolMetadata:S,ProtocolMonitorDataGrid:C,ProtocolMonitorImpl:E,CommandAutocompleteSuggestionProvider:x,InfoWidget:P,EditorWidget:W,parseCommandInput:D});export{G as ProtocolMonitor};
