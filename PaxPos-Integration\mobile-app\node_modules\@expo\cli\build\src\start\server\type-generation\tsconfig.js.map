{"version": 3, "sources": ["../../../../../src/start/server/type-generation/tsconfig.ts"], "sourcesContent": ["import JsonFile, { JSONObject } from '@expo/json-file';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport { Log } from '../../../log';\n\n/**\n * Force updates a project tsconfig with Expo values.\n */\nexport async function forceUpdateTSConfig(projectRoot: string) {\n  // This runs after the TypeScript prerequisite, so we know the tsconfig.json exists\n  const tsConfigPath = path.join(projectRoot, 'tsconfig.json');\n  const { tsConfig, updates } = getTSConfigUpdates(\n    JsonFile.read(tsConfigPath, {\n      json5: true,\n    })\n  );\n\n  await writeUpdates(tsConfigPath, tsConfig, updates);\n}\n\nexport function getTSConfigUpdates(tsConfig: JSONObject) {\n  const updates = new Set<string>();\n\n  if (!tsConfig.include) {\n    tsConfig.include = ['**/*.ts', '**/*.tsx', '.expo/types/**/*.ts', 'expo-env.d.ts'];\n    updates.add('include');\n  } else if (Array.isArray(tsConfig.include)) {\n    if (!tsConfig.include.includes('.expo/types/**/*.ts')) {\n      tsConfig.include = [...tsConfig.include, '.expo/types/**/*.ts'];\n      updates.add('include');\n    }\n\n    if (!tsConfig.include.includes('expo-env.d.ts')) {\n      tsConfig.include = [...tsConfig.include, 'expo-env.d.ts'];\n      updates.add('include');\n    }\n  }\n\n  return { tsConfig, updates };\n}\n\nexport async function forceRemovalTSConfig(projectRoot: string) {\n  // This runs after the TypeScript prerequisite, so we know the tsconfig.json exists\n  const tsConfigPath = path.join(projectRoot, 'tsconfig.json');\n  const { tsConfig, updates } = getTSConfigRemoveUpdates(\n    JsonFile.read(tsConfigPath, {\n      json5: true,\n    })\n  );\n\n  await writeUpdates(tsConfigPath, tsConfig, updates);\n}\n\nexport function getTSConfigRemoveUpdates(tsConfig: JSONObject) {\n  const updates = new Set<string>();\n\n  if (Array.isArray(tsConfig.include)) {\n    const filtered = (tsConfig.include as string[]).filter(\n      (i) => i !== 'expo-env.d.ts' && i !== '.expo/types/**/*.ts'\n    );\n\n    if (filtered.length !== tsConfig.include.length) {\n      updates.add('include');\n    }\n\n    tsConfig.include = filtered;\n  }\n\n  return { tsConfig, updates };\n}\n\nasync function writeUpdates(tsConfigPath: string, tsConfig: JSONObject, updates: Set<string>) {\n  if (updates.size) {\n    await JsonFile.writeAsync(tsConfigPath, tsConfig);\n    for (const update of updates) {\n      Log.log(\n        chalk`{bold TypeScript}: The {cyan tsconfig.json#${update}} property has been updated`\n      );\n    }\n  }\n}\n"], "names": ["forceRemovalTSConfig", "forceUpdateTSConfig", "getTSConfigRemoveUpdates", "getTSConfigUpdates", "projectRoot", "tsConfigPath", "path", "join", "tsConfig", "updates", "JsonFile", "read", "json5", "writeUpdates", "Set", "include", "add", "Array", "isArray", "includes", "filtered", "filter", "i", "length", "size", "writeAsync", "update", "Log", "log", "chalk"], "mappings": ";;;;;;;;;;;IA0CsBA,oBAAoB;eAApBA;;IAjCAC,mBAAmB;eAAnBA;;IA6CNC,wBAAwB;eAAxBA;;IAjCAC,kBAAkB;eAAlBA;;;;gEArBqB;;;;;;;gEACnB;;;;;;;gEACD;;;;;;qBAEG;;;;;;AAKb,eAAeF,oBAAoBG,WAAmB;IAC3D,mFAAmF;IACnF,MAAMC,eAAeC,eAAI,CAACC,IAAI,CAACH,aAAa;IAC5C,MAAM,EAAEI,QAAQ,EAAEC,OAAO,EAAE,GAAGN,mBAC5BO,mBAAQ,CAACC,IAAI,CAACN,cAAc;QAC1BO,OAAO;IACT;IAGF,MAAMC,aAAaR,cAAcG,UAAUC;AAC7C;AAEO,SAASN,mBAAmBK,QAAoB;IACrD,MAAMC,UAAU,IAAIK;IAEpB,IAAI,CAACN,SAASO,OAAO,EAAE;QACrBP,SAASO,OAAO,GAAG;YAAC;YAAW;YAAY;YAAuB;SAAgB;QAClFN,QAAQO,GAAG,CAAC;IACd,OAAO,IAAIC,MAAMC,OAAO,CAACV,SAASO,OAAO,GAAG;QAC1C,IAAI,CAACP,SAASO,OAAO,CAACI,QAAQ,CAAC,wBAAwB;YACrDX,SAASO,OAAO,GAAG;mBAAIP,SAASO,OAAO;gBAAE;aAAsB;YAC/DN,QAAQO,GAAG,CAAC;QACd;QAEA,IAAI,CAACR,SAASO,OAAO,CAACI,QAAQ,CAAC,kBAAkB;YAC/CX,SAASO,OAAO,GAAG;mBAAIP,SAASO,OAAO;gBAAE;aAAgB;YACzDN,QAAQO,GAAG,CAAC;QACd;IACF;IAEA,OAAO;QAAER;QAAUC;IAAQ;AAC7B;AAEO,eAAeT,qBAAqBI,WAAmB;IAC5D,mFAAmF;IACnF,MAAMC,eAAeC,eAAI,CAACC,IAAI,CAACH,aAAa;IAC5C,MAAM,EAAEI,QAAQ,EAAEC,OAAO,EAAE,GAAGP,yBAC5BQ,mBAAQ,CAACC,IAAI,CAACN,cAAc;QAC1BO,OAAO;IACT;IAGF,MAAMC,aAAaR,cAAcG,UAAUC;AAC7C;AAEO,SAASP,yBAAyBM,QAAoB;IAC3D,MAAMC,UAAU,IAAIK;IAEpB,IAAIG,MAAMC,OAAO,CAACV,SAASO,OAAO,GAAG;QACnC,MAAMK,WAAW,AAACZ,SAASO,OAAO,CAAcM,MAAM,CACpD,CAACC,IAAMA,MAAM,mBAAmBA,MAAM;QAGxC,IAAIF,SAASG,MAAM,KAAKf,SAASO,OAAO,CAACQ,MAAM,EAAE;YAC/Cd,QAAQO,GAAG,CAAC;QACd;QAEAR,SAASO,OAAO,GAAGK;IACrB;IAEA,OAAO;QAAEZ;QAAUC;IAAQ;AAC7B;AAEA,eAAeI,aAAaR,YAAoB,EAAEG,QAAoB,EAAEC,OAAoB;IAC1F,IAAIA,QAAQe,IAAI,EAAE;QAChB,MAAMd,mBAAQ,CAACe,UAAU,CAACpB,cAAcG;QACxC,KAAK,MAAMkB,UAAUjB,QAAS;YAC5BkB,QAAG,CAACC,GAAG,CACLC,IAAAA,gBAAK,CAAA,CAAC,2CAA2C,EAAEH,OAAO,2BAA2B,CAAC;QAE1F;IACF;AACF"}