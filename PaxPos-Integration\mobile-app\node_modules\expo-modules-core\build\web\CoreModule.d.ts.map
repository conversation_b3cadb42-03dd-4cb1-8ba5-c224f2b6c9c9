{"version": 3, "file": "CoreModule.d.ts", "sourceRoot": "", "sources": ["../../src/web/CoreModule.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,KAAK,YAAY,IAAI,gBAAgB,EACrC,KAAK,iBAAiB,EACtB,SAAS,EACV,MAAM,iCAAiC,CAAC;AACzC,OAAO,KAAK,EAAE,YAAY,IAAI,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACxF,OAAO,KAAK,EAAE,YAAY,IAAI,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACxF,OAAO,KAAK,EAAE,SAAS,IAAI,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAE/E,qBAAa,YAAY,CAAC,UAAU,SAAS,SAAS,CAAE,YAAW,gBAAgB;IACjF,OAAO,CAAC,SAAS,CAAC,CAAuC;IAEzD,WAAW,CAAC,SAAS,SAAS,MAAM,UAAU,EAC5C,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,GAC9B,iBAAiB;IAuBpB,cAAc,CAAC,SAAS,SAAS,MAAM,UAAU,EAC/C,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,GAC9B,IAAI;IAOP,kBAAkB,CAAC,SAAS,SAAS,MAAM,UAAU,EAAE,SAAS,EAAE,SAAS,GAAG,IAAI;IAQlF,IAAI,CAAC,SAAS,SAAS,MAAM,UAAU,EACrC,SAAS,EAAE,SAAS,EACpB,GAAG,IAAI,EAAE,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,GACzC,IAAI;IAeP,aAAa,CAAC,SAAS,SAAS,MAAM,UAAU,EAAE,SAAS,EAAE,SAAS,GAAG,MAAM;IAI/E,cAAc,CAAC,SAAS,SAAS,MAAM,UAAU,EAAE,SAAS,EAAE,SAAS,GAAG,IAAI;IAE9E,aAAa,CAAC,SAAS,SAAS,MAAM,UAAU,EAAE,SAAS,EAAE,SAAS,GAAG,IAAI;CAC9E;AAED,qBAAa,YAAY,CAAC,UAAU,SAAS,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAC/D,SAAQ,YAAY,CAAC,UAAU,CAC/B,YAAW,gBAAgB;IAE3B,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;IACnB,cAAc,CAAC,EAAE;QAAE,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IAChD,oBAAoB,CAAC,EAAE,MAAM,CAAC;CAC/B;AAED,qBAAa,YAAY,CAAC,UAAU,SAAS,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAC/D,SAAQ,YAAY,CAAC,UAAU,CAC/B,YAAW,gBAAgB;IAE3B,OAAO,IAAI,IAAI;CAGhB;AAED,qBAAa,SAAS,CAClB,cAAc,SAAS,MAAM,GAAG,SAAS,EACzC,UAAU,SAAS,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAErD,SAAQ,YAAY,CAAC,UAAU,CAC/B,YAAW,aAAa,CAAC,cAAc,CAAC;IAExC,aAAa,EAAE,MAAM,CAAa;CACnC"}