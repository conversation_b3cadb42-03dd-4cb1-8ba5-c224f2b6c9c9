{"version": 3, "sources": ["nativeModule.ts"], "names": ["NativeModules", "RNCNetInfo"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,SAAQA,aAAR,QAA4B,cAA5B;AAGA,MAAMC,UAA+B,GAAGD,aAAa,CAACC,UAAtD;AAEA,eAAeA,UAAf", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {NativeModules} from 'react-native';\nimport {NetInfoNativeModule} from './privateTypes';\n\nconst RNCNetInfo: NetInfoNativeModule = NativeModules.RNCNetInfo;\n\nexport default RNCNetInfo;\n"]}