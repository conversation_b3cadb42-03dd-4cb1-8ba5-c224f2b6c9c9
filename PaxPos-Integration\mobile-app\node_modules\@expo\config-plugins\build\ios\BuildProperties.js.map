{"version": 3, "file": "BuildProperties.js", "names": ["_iosPlugins", "data", "require", "createBuildPodfilePropsConfigPlugin", "configToPropertyRules", "name", "withUnknown", "config", "sourceConfig", "withPodfileProperties", "modResults", "updateIosBuildPropertiesFromConfig", "Object", "defineProperty", "value", "withJsEnginePodfileProps", "exports", "propName", "propValueGetter", "ios", "jsEngine", "withNewArchEnabledPodfileProps", "newArchEnabled", "toString", "podfileProperties", "configToProperty", "updateIosBuildProperty", "options", "removePropWhenValueIsNull"], "sources": ["../../src/ios/BuildProperties.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config-types';\n\nimport type { ConfigPlugin } from '../Plugin.types';\nimport { withPodfileProperties } from '../plugins/ios-plugins';\nimport { BuildPropertiesConfig, ConfigToPropertyRuleType } from '../utils/BuildProperties.types';\n\n/**\n * Creates a `withPodfileProperties` config-plugin based on given config to property mapping rules.\n *\n * The factory supports two modes from generic type inference\n * ```ts\n * // config-plugin without `props`, it will implicitly use the expo config as source config.\n * createBuildPodfilePropsConfigPlugin<ExpoConfig>(): ConfigPlugin<void>;\n *\n * // config-plugin with a parameter `props: CustomType`, it will use the `props` as source config.\n * createBuildPodfilePropsConfigPlugin<CustomType>(): ConfigPlugin<CustomType>;\n * ```\n *\n * @param configToPropertyRules config to property mapping rules\n * @param name the config plugin name\n */\nexport function createBuildPodfilePropsConfigPlugin<SourceConfigType extends BuildPropertiesConfig>(\n  configToPropertyRules: ConfigToPropertyRuleType<SourceConfigType>[],\n  name?: string\n) {\n  const withUnknown: ConfigPlugin<SourceConfigType extends ExpoConfig ? void : SourceConfigType> = (\n    config,\n    sourceConfig\n  ) =>\n    withPodfileProperties(config, (config) => {\n      config.modResults = updateIosBuildPropertiesFromConfig(\n        (sourceConfig ?? config) as SourceConfigType,\n        config.modResults,\n        configToPropertyRules\n      );\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\n/**\n * A config-plugin to update `ios/Podfile.properties.json` from the `jsEngine` in expo config\n */\nexport const withJsEnginePodfileProps = createBuildPodfilePropsConfigPlugin<ExpoConfig>(\n  [\n    {\n      propName: 'expo.jsEngine',\n      propValueGetter: (config) => config.ios?.jsEngine ?? config.jsEngine ?? 'hermes',\n    },\n  ],\n  'withJsEnginePodfileProps'\n);\n\n/**\n * A config-plugin to update `ios/Podfile.properties.json` from the `newArchEnabled` in expo config\n */\nexport const withNewArchEnabledPodfileProps = createBuildPodfilePropsConfigPlugin<ExpoConfig>(\n  [\n    {\n      propName: 'newArchEnabled',\n      propValueGetter: (config) =>\n        (config.ios?.newArchEnabled ?? config.newArchEnabled)?.toString(),\n    },\n  ],\n  'withNewArchEnabledPodfileProps'\n);\n\nexport function updateIosBuildPropertiesFromConfig<SourceConfigType extends BuildPropertiesConfig>(\n  config: SourceConfigType,\n  podfileProperties: Record<string, string>,\n  configToPropertyRules: ConfigToPropertyRuleType<SourceConfigType>[]\n) {\n  for (const configToProperty of configToPropertyRules) {\n    const value = configToProperty.propValueGetter(config);\n    updateIosBuildProperty(podfileProperties, configToProperty.propName, value);\n  }\n  return podfileProperties;\n}\n\nexport function updateIosBuildProperty(\n  podfileProperties: Record<string, string>,\n  name: string,\n  value: string | null | undefined,\n  options?: { removePropWhenValueIsNull?: boolean }\n) {\n  if (value) {\n    podfileProperties[name] = value;\n  } else if (options?.removePropWhenValueIsNull) {\n    delete podfileProperties[name];\n  }\n  return podfileProperties;\n}\n"], "mappings": ";;;;;;;;;AAGA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,mCAAmCA,CACjDC,qBAAmE,EACnEC,IAAa,EACb;EACA,MAAMC,WAAwF,GAAGA,CAC/FC,MAAM,EACNC,YAAY,KAEZ,IAAAC,mCAAqB,EAACF,MAAM,EAAGA,MAAM,IAAK;IACxCA,MAAM,CAACG,UAAU,GAAGC,kCAAkC,CACnDH,YAAY,IAAID,MAAM,EACvBA,MAAM,CAACG,UAAU,EACjBN,qBACF,CAAC;IACD,OAAOG,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRO,MAAM,CAACC,cAAc,CAACP,WAAW,EAAE,MAAM,EAAE;MACzCQ,KAAK,EAAET;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;;AAEA;AACA;AACA;AACO,MAAMS,wBAAwB,GAAAC,OAAA,CAAAD,wBAAA,GAAGZ,mCAAmC,CACzE,CACE;EACEc,QAAQ,EAAE,eAAe;EACzBC,eAAe,EAAGX,MAAM,IAAKA,MAAM,CAACY,GAAG,EAAEC,QAAQ,IAAIb,MAAM,CAACa,QAAQ,IAAI;AAC1E,CAAC,CACF,EACD,0BACF,CAAC;;AAED;AACA;AACA;AACO,MAAMC,8BAA8B,GAAAL,OAAA,CAAAK,8BAAA,GAAGlB,mCAAmC,CAC/E,CACE;EACEc,QAAQ,EAAE,gBAAgB;EAC1BC,eAAe,EAAGX,MAAM,IACtB,CAACA,MAAM,CAACY,GAAG,EAAEG,cAAc,IAAIf,MAAM,CAACe,cAAc,GAAGC,QAAQ,CAAC;AACpE,CAAC,CACF,EACD,gCACF,CAAC;AAEM,SAASZ,kCAAkCA,CAChDJ,MAAwB,EACxBiB,iBAAyC,EACzCpB,qBAAmE,EACnE;EACA,KAAK,MAAMqB,gBAAgB,IAAIrB,qBAAqB,EAAE;IACpD,MAAMU,KAAK,GAAGW,gBAAgB,CAACP,eAAe,CAACX,MAAM,CAAC;IACtDmB,sBAAsB,CAACF,iBAAiB,EAAEC,gBAAgB,CAACR,QAAQ,EAAEH,KAAK,CAAC;EAC7E;EACA,OAAOU,iBAAiB;AAC1B;AAEO,SAASE,sBAAsBA,CACpCF,iBAAyC,EACzCnB,IAAY,EACZS,KAAgC,EAChCa,OAAiD,EACjD;EACA,IAAIb,KAAK,EAAE;IACTU,iBAAiB,CAACnB,IAAI,CAAC,GAAGS,KAAK;EACjC,CAAC,MAAM,IAAIa,OAAO,EAAEC,yBAAyB,EAAE;IAC7C,OAAOJ,iBAAiB,CAACnB,IAAI,CAAC;EAChC;EACA,OAAOmB,iBAAiB;AAC1B", "ignoreList": []}