{"name": "expo", "version": "53.0.20", "description": "The Expo SDK", "main": "src/Expo.ts", "module": "src/Expo.ts", "types": "build/Expo.d.ts", "sideEffects": ["*.fx.tsx", "*.fx.web.tsx", "./src/winter/*.ts"], "bin": {"expo": "bin/cli", "expo-modules-autolinking": "bin/autolinking", "fingerprint": "bin/fingerprint"}, "files": ["android", "bin", "build", "ios", "scripts", "src", "AppEntry.js", "Expo.podspec", "bundledNativeModules.json", "expo-module.config.json", "metro-config.js", "metro-config.d.ts", "dom", "config.js", "config.d.ts", "config-plugins.js", "config-plugins.d.ts", "devtools.js", "devtools.d.ts", "fetch.js", "fetch.d.ts", "fingerprint.js", "fingerprint.d.ts", "react-native.config.js", "requiresExtraSetup.json", "tsconfig.base.json", "types", "virtual"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "jest": {"preset": "expo-module-scripts"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo"}, "keywords": ["expo"], "author": "Expo", "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/expo", "dependencies": {"@babel/runtime": "^7.20.0", "@expo/cli": "0.24.20", "@expo/config": "~11.0.13", "@expo/config-plugins": "~10.1.2", "@expo/fingerprint": "0.13.4", "@expo/metro-config": "0.20.17", "@expo/vector-icons": "^14.0.0", "babel-preset-expo": "~13.2.3", "expo-asset": "~11.1.7", "expo-constants": "~17.1.7", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-keep-awake": "~14.1.4", "expo-modules-autolinking": "2.1.14", "expo-modules-core": "2.5.0", "react-native-edge-to-edge": "1.6.0", "whatwg-url-without-unicode": "8.0.0-3"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "~19.0.10", "@types/react-test-renderer": "^19.0.0", "expo-module-scripts": "^4.1.9", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "web-streams-polyfill": "^3.3.2", "ws": "^8.18.0"}, "peerDependencies": {"@expo/dom-webview": "*", "@expo/metro-runtime": "*", "react": "*", "react-native": "*", "react-native-webview": "*"}, "peerDependenciesMeta": {"@expo/dom-webview": {"optional": true}, "@expo/metro-runtime": {"optional": true}, "react-native-webview": {"optional": true}}, "gitHead": "b69736d615d303c2143e7682b37c93665e1ed3d9"}