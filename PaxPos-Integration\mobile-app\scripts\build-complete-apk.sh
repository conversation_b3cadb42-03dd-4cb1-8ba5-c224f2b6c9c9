#!/bin/bash

echo "🏭 Building Production-Grade PAX POS Mobile App"
echo "=============================================="
echo "📱 Full-Featured Enterprise POS Solution"
echo "🔗 MongoDB Integration | 🔐 Security | 📊 Analytics"
echo "🚫 NO EAS REQUIRED - Local Build Only"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the mobile-app directory"
    exit 1
fi

# Step 1: Install dependencies
print_status "Installing mobile app dependencies..."
npm install

if [ $? -ne 0 ]; then
    print_error "Failed to install dependencies"
    exit 1
fi

# Step 2: Build frontend
print_status "Building React frontend..."
node scripts/build-frontend.js

if [ $? -ne 0 ]; then
    print_error "Failed to build frontend"
    exit 1
fi

# Step 3: Install Expo CLI
print_status "Installing Expo CLI..."
npm install -g @expo/cli

if ! command -v expo &> /dev/null; then
    print_error "Failed to install Expo CLI"
    exit 1
fi

# Step 4: Check Android environment
print_status "Checking Android development environment..."

# Check for Android SDK
if [ -z "$ANDROID_HOME" ] && [ -z "$ANDROID_SDK_ROOT" ]; then
    print_warning "ANDROID_HOME not set, trying to find Android SDK..."

    # Common Android SDK locations
    POSSIBLE_PATHS=(
        "$HOME/Android/Sdk"
        "$HOME/Library/Android/sdk"
        "/usr/local/android-sdk"
        "/opt/android-sdk"
        "$USERPROFILE/AppData/Local/Android/Sdk"
    )

    for path in "${POSSIBLE_PATHS[@]}"; do
        if [ -d "$path" ]; then
            export ANDROID_HOME="$path"
            export ANDROID_SDK_ROOT="$path"
            print_status "Found Android SDK at: $path"
            break
        fi
    done

    if [ -z "$ANDROID_HOME" ]; then
        print_error "Android SDK not found!"
        print_error "Please install Android Studio or set ANDROID_HOME"
        exit 1
    fi
fi

# Check for Java
if ! command -v java &> /dev/null; then
    print_error "Java not found! Please install Java 11 or higher"
    exit 1
fi

print_status "✅ Development environment ready"

# Step 5: Clean and prebuild
print_status "Preparing Android project..."
rm -rf android ios
npx expo prebuild --platform android --clear

if [ $? -ne 0 ]; then
    print_error "Prebuild failed"
    exit 1
fi

# Step 6: Build APK using Gradle
print_status "Building production APK..."
print_warning "This may take 10-15 minutes..."

cd android
./gradlew assembleRelease

if [ $? -eq 0 ]; then
    print_status "✅ APK build completed successfully!"

    # Find the APK file in Android build output
    APK_FILE=$(find android/app/build/outputs/apk/release -name "*.apk" -type f | head -1)

    if [ -n "$APK_FILE" ] && [ -f "$APK_FILE" ]; then
        print_status "📦 APK Location: $APK_FILE"

        # Get file size
        APK_SIZE=$(du -h "$APK_FILE" | cut -f1)
        print_status "📏 APK Size: $APK_SIZE"

        # Copy to root directory with descriptive name
        cd ..
        NEW_NAME="pax-pos-enterprise-$(date +%Y%m%d-%H%M%S).apk"
        cp "$APK_FILE" "$NEW_NAME"
        print_status "📋 Copied to: $NEW_NAME"
    else
        print_warning "⚠️ APK file not found in expected location"
        print_info "💡 Check android/app/build/outputs/apk/release/ directory"
    fi
    
    echo ""
    echo "🎉 Production Build Complete!"
    echo "============================="
    echo "🏭 Enterprise PAX POS Mobile App Ready!"
    echo ""
    echo "📱 Installation Instructions:"
    echo "1. Enable 'Unknown Sources' in Android settings"
    echo "2. Transfer the APK to your device"
    echo "3. Tap the APK file to install"
    echo "4. Grant required permissions (Network, Storage)"
    echo ""
    echo "🚀 Production Features Included:"
    echo "✅ Complete React frontend with POS interface"
    echo "✅ Full HTTP server with REST API"
    echo "✅ MongoDB cloud database integration"
    echo "✅ PAX A920 terminal integration (TCP/IP)"
    echo "✅ Enterprise security & authentication"
    echo "✅ Real-time transaction processing"
    echo "✅ Inventory management system"
    echo "✅ Receipt generation & printing"
    echo "✅ Offline support with auto-sync"
    echo "✅ Comprehensive logging & analytics"
    echo "✅ User management & permissions"
    echo "✅ Data encryption & secure storage"
    echo "✅ Network monitoring & error handling"
    echo "✅ Performance monitoring & optimization"
    echo ""
    echo "🔧 Default Credentials:"
    echo "Username: admin"
    echo "Password: admin123"
    echo ""
    echo "🌐 MongoDB Connection:"
    echo "Connected to: cluster0.q7tae.mongodb.net"
    echo "Database: PaxPOSHq"
    echo ""
    
else
    print_error "❌ APK build failed!"
    print_error "Check the error messages above for details"
    print_info "🔧 Troubleshooting:"
    print_info "1. Make sure Android SDK is installed"
    print_info "2. Check ANDROID_HOME environment variable"
    print_info "3. Try: npx expo run:android"
    print_info "4. Ensure Java 11+ is installed"
    cd ..
    exit 1
fi
