{"version": 3, "sources": ["asyncHookWrappers.ts"], "names": ["useOnMount", "asyncGetter", "initialResult", "response", "setResponse", "loading", "result", "getAsync", "deviceInfoEmitter", "NativeEventEmitter", "NativeModules", "RNDeviceInfo", "useOnEvent", "eventName", "initialValueAsyncGetter", "defaultValue", "setResult", "subscription", "addListener", "remove"], "mappings": ";;;;;;;;;AAAA;;AACA;;AAGA;AACA;AACA;AACA;AACA;AACO,SAASA,UAAT,CAAuBC,WAAvB,EAAsDC,aAAtD,EAA4F;AACjG,QAAM,CAACC,QAAD,EAAWC,WAAX,IAA0B,qBAA6B;AAC3DC,IAAAA,OAAO,EAAE,IADkD;AAE3DC,IAAAA,MAAM,EAAEJ;AAFmD,GAA7B,CAAhC;AAKA,wBAAU,MAAM;AACd;AACA,UAAMK,QAAQ,GAAG,YAAY;AAC3B,YAAMD,MAAM,GAAG,MAAML,WAAW,EAAhC;AACAG,MAAAA,WAAW,CAAC;AAAEC,QAAAA,OAAO,EAAE,KAAX;AAAkBC,QAAAA;AAAlB,OAAD,CAAX;AACD,KAHD;;AAKAC,IAAAA,QAAQ;AACT,GARD,EAQG,CAACN,WAAD,CARH;AAUA,SAAOE,QAAP;AACD;;AAEM,MAAMK,iBAAiB,GAAG,IAAIC,+BAAJ,CAAuBC,2BAAcC,YAArC,CAA1B;AAEP;AACA;AACA;AACA;AACA;AACA;;;;AACO,SAASC,UAAT,CACLC,SADK,EAELC,uBAFK,EAGLC,YAHK,EAIe;AACpB,QAAM;AAAEV,IAAAA,OAAF;AAAWC,IAAAA,MAAM,EAAEJ;AAAnB,MAAqCF,UAAU,CAACc,uBAAD,EAA0BC,YAA1B,CAArD;AACA,QAAM,CAACT,MAAD,EAASU,SAAT,IAAsB,qBAAYD,YAAZ,CAA5B,CAFoB,CAIpB;;AACA,wBAAU,MAAM;AACdC,IAAAA,SAAS,CAACd,aAAD,CAAT;AACD,GAFD,EAEG,CAACA,aAAD,CAFH,EALoB,CASpB;AACA;;AACA,wBAAU,MAAM;AACd,UAAMe,YAAY,GAAGT,iBAAiB,CAACU,WAAlB,CAA8BL,SAA9B,EAAyCG,SAAzC,CAArB;AACA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAHD,EAGG,CAACN,SAAD,CAHH,EAXoB,CAgBpB;;AACA,SAAO,oBAAQ,OAAO;AAAER,IAAAA,OAAF;AAAWC,IAAAA;AAAX,GAAP,CAAR,EAAqC,CAACD,OAAD,EAAUC,MAAV,CAArC,CAAP;AACD", "sourcesContent": ["import { useState, useEffect, useMemo } from 'react';\nimport { NativeEventEmitter, NativeModules } from 'react-native';\nimport type { AsyncHookResult } from './types';\n\n/**\n * simple hook wrapper for async functions for 'on-mount / componentDidMount' that only need to fired once\n * @param asyncGetter async function that 'gets' something\n * @param initialResult -1 | false | 'unknown'\n */\nexport function useOnMount<T>(asyncGetter: () => Promise<T>, initialResult: T): AsyncHookResult<T> {\n  const [response, setResponse] = useState<AsyncHookResult<T>>({\n    loading: true,\n    result: initialResult,\n  });\n\n  useEffect(() => {\n    // async function cuz react complains if useEffect's effect param is an async function\n    const getAsync = async () => {\n      const result = await asyncGetter();\n      setResponse({ loading: false, result });\n    };\n\n    getAsync();\n  }, [asyncGetter]);\n\n  return response;\n}\n\nexport const deviceInfoEmitter = new NativeEventEmitter(NativeModules.RNDeviceInfo);\n\n/**\n * simple hook wrapper for handling events\n * @param eventName\n * @param initialValueAsyncGetter\n * @param defaultValue\n */\nexport function useOnEvent<T>(\n  eventName: string,\n  initialValueAsyncGetter: () => Promise<T>,\n  defaultValue: T\n): AsyncHookResult<T> {\n  const { loading, result: initialResult } = useOnMount(initialValueAsyncGetter, defaultValue);\n  const [result, setResult] = useState<T>(defaultValue);\n\n  // sets the result to what the intial value is on mount\n  useEffect(() => {\n    setResult(initialResult);\n  }, [initialResult]);\n\n  // - set up the event listener to set the result\n  // - set up the clean up function to remove subscription on unmount\n  useEffect(() => {\n    const subscription = deviceInfoEmitter.addListener(eventName, setResult);\n    return () => subscription.remove();\n  }, [eventName]);\n\n  // loading will only be true while getting the inital value. After that, it will always be false, but a new result may occur\n  return useMemo(() => ({ loading, result }), [loading, result]);\n}\n"]}