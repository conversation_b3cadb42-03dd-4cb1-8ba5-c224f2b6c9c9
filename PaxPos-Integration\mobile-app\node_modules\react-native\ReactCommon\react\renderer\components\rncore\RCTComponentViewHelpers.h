/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GenerateComponentHObjCpp.js
*/

#import <Foundation/Foundation.h>
#import <React/RCTDefines.h>
#import <React/RCTLog.h>

NS_ASSUME_NONNULL_BEGIN

@protocol RCTActivityIndicatorViewViewProtocol <NSObject>

@end

@protocol RCTAndroidDrawerLayoutViewProtocol <NSObject>
- (void)openDrawer;
- (void)closeDrawer;
@end

RCT_EXTERN inline void RCTAndroidDrawerLayoutHandleCommand(
  id<RCTAndroidDrawerLayoutViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"openDrawer"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidDrawerLayout", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView openDrawer];
  return;
}

if ([commandName isEqualToString:@"closeDrawer"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidDrawerLayout", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView closeDrawer];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidDrawerLayout", commandName);
#endif
}

@protocol RCTAndroidHorizontalScrollContentViewViewProtocol <NSObject>

@end

@protocol RCTAndroidSwipeRefreshLayoutViewProtocol <NSObject>
- (void)setNativeRefreshing:(BOOL)value;
@end

RCT_EXTERN inline void RCTAndroidSwipeRefreshLayoutHandleCommand(
  id<RCTAndroidSwipeRefreshLayoutViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"setNativeRefreshing"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidSwipeRefreshLayout", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"AndroidSwipeRefreshLayout", commandName, @"1st")) {
    return;
  }
#endif
  BOOL value = [(NSNumber *)arg0 boolValue];

  [componentView setNativeRefreshing:value];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidSwipeRefreshLayout", commandName);
#endif
}

@protocol RCTAndroidSwitchViewProtocol <NSObject>
- (void)setNativeValue:(BOOL)value;
@end

RCT_EXTERN inline void RCTAndroidSwitchHandleCommand(
  id<RCTAndroidSwitchViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"setNativeValue"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidSwitch", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"AndroidSwitch", commandName, @"1st")) {
    return;
  }
#endif
  BOOL value = [(NSNumber *)arg0 boolValue];

  [componentView setNativeValue:value];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidSwitch", commandName);
#endif
}

@protocol RCTDebuggingOverlayViewProtocol <NSObject>
- (void)highlightTraceUpdates:(const NSArray *)updates;
- (void)highlightElements:(const NSArray *)elements;
- (void)clearElementsHighlights;
@end

RCT_EXTERN inline void RCTDebuggingOverlayHandleCommand(
  id<RCTDebuggingOverlayViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"highlightTraceUpdates"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"DebuggingOverlay", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSArray class], @"array", @"DebuggingOverlay", commandName, @"1st")) {
    return;
  }
#endif
  const NSArray * updates = (NSArray *)arg0;

  [componentView highlightTraceUpdates:updates];
  return;
}

if ([commandName isEqualToString:@"highlightElements"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"DebuggingOverlay", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSArray class], @"array", @"DebuggingOverlay", commandName, @"1st")) {
    return;
  }
#endif
  const NSArray * elements = (NSArray *)arg0;

  [componentView highlightElements:elements];
  return;
}

if ([commandName isEqualToString:@"clearElementsHighlights"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"DebuggingOverlay", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView clearElementsHighlights];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"DebuggingOverlay", commandName);
#endif
}

@protocol RCTAndroidProgressBarViewProtocol <NSObject>

@end

@protocol RCTPullToRefreshViewViewProtocol <NSObject>
- (void)setNativeRefreshing:(BOOL)refreshing;
@end

RCT_EXTERN inline void RCTPullToRefreshViewHandleCommand(
  id<RCTPullToRefreshViewViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"setNativeRefreshing"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"PullToRefreshView", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"PullToRefreshView", commandName, @"1st")) {
    return;
  }
#endif
  BOOL refreshing = [(NSNumber *)arg0 boolValue];

  [componentView setNativeRefreshing:refreshing];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"PullToRefreshView", commandName);
#endif
}

@protocol RCTInputAccessoryViewProtocol <NSObject>

@end

@protocol RCTModalHostViewViewProtocol <NSObject>

@end

@protocol RCTSafeAreaViewViewProtocol <NSObject>

@end

@protocol RCTSwitchViewProtocol <NSObject>
- (void)setValue:(BOOL)value;
@end

RCT_EXTERN inline void RCTSwitchHandleCommand(
  id<RCTSwitchViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"setValue"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"Switch", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"Switch", commandName, @"1st")) {
    return;
  }
#endif
  BOOL value = [(NSNumber *)arg0 boolValue];

  [componentView setValue:value];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"Switch", commandName);
#endif
}

@protocol RCTUnimplementedNativeViewViewProtocol <NSObject>

@end

NS_ASSUME_NONNULL_END