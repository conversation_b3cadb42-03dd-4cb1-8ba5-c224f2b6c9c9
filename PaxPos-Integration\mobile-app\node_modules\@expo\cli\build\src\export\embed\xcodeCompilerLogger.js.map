{"version": 3, "sources": ["../../../../src/export/embed/xcodeCompilerLogger.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport path from 'path';\n\nimport { Log } from '../../log';\n\nexport function isPossiblyUnableToResolveError(\n  error: any\n): error is { message: string; originModulePath: string; targetModuleName: string } {\n  return (\n    'message' in error &&\n    typeof error.message === 'string' &&\n    'originModulePath' in error &&\n    typeof error.originModulePath === 'string' &&\n    'targetModuleName' in error &&\n    typeof error.targetModuleName === 'string'\n  );\n}\nfunction isPossiblyTransformError(\n  error: any\n): error is { message: string; filename: string; lineNumber: number; column?: number } {\n  return (\n    'message' in error &&\n    typeof error.message === 'string' &&\n    'filename' in error &&\n    typeof error.filename === 'string' &&\n    'lineNumber' in error &&\n    typeof error.lineNumber === 'number'\n  );\n}\n\nexport function getXcodeCompilerErrorMessage(\n  projectRoot: string,\n  error: Error | any\n): string | null {\n  const makeFilepathAbsolute = (filepath: string) =>\n    filepath.startsWith('/') ? filepath : path.join(projectRoot, filepath);\n\n  if (typeof error === 'string') {\n    return makeXcodeCompilerLog('error', error);\n  } else if ('message' in error) {\n    // Metro's `UnableToResolveError`\n    if (isPossiblyUnableToResolveError(error)) {\n      const loc = getLineNumberForStringInFile(error.originModulePath, error.targetModuleName);\n      return makeXcodeCompilerLog('error', error.message, {\n        fileName: error.originModulePath,\n        lineNumber: loc?.lineNumber,\n        column: loc?.column,\n      });\n    } else if (isPossiblyTransformError(error)) {\n      return makeXcodeCompilerLog('error', error.message, {\n        // Metro generally returns the filename as relative from the project root.\n        fileName: makeFilepathAbsolute(error.filename),\n        lineNumber: error.lineNumber,\n        column: error.column,\n      });\n      // TODO: ResourceNotFoundError, GraphNotFoundError, RevisionNotFoundError, AmbiguousModuleResolutionError\n    } else {\n      // Unknown error\n      return makeXcodeCompilerLog('error', error.message);\n    }\n  }\n\n  return null;\n}\n\n/** Log an error that can be parsed by Xcode and related build tools https://developer.apple.com/documentation/xcode/running-custom-scripts-during-a-build#Log-errors-and-warnings-from-your-script */\nexport function logMetroErrorInXcode(projectRoot: string, error: Error | string) {\n  const message = getXcodeCompilerErrorMessage(projectRoot, error);\n  if (message != null) {\n    console.error(message);\n  }\n}\n\nexport function logInXcode(message: string) {\n  Log.log(makeXcodeCompilerLog('note', message));\n}\n\nexport function warnInXcode(message: string) {\n  Log.warn(makeXcodeCompilerLog('warning', message));\n}\n\n// Detect running in xcode build script. This means the logs need to be formatted in a way that Xcode can parse them, it also means that the shell is not reliable or interactive.\n// https://developer.apple.com/documentation/xcode/running-custom-scripts-during-a-build#Access-script-related-files-from-environment-variables\nexport function isExecutingFromXcodebuild() {\n  return !!process.env.BUILT_PRODUCTS_DIR;\n}\n\nfunction makeXcodeCompilerLog(\n  type: 'error' | 'fatal error' | 'warning' | 'note',\n  message: string,\n  {\n    fileName,\n    lineNumber,\n    column,\n  }: {\n    /** Absolute file path to link to in Xcode. */\n    fileName?: string;\n    lineNumber?: number;\n    column?: number;\n  } = {}\n) {\n  if (!isExecutingFromXcodebuild()) {\n    return message;\n  }\n  // TODO: Figure out how to support multi-line logs.\n  const firstLine = message.split('\\n')[0];\n  if (fileName && !fileName?.includes(':')) {\n    return `${fileName}:${lineNumber || 0}:${\n      column != null ? column + ':' : ''\n    } ${type}: ${firstLine}`;\n  }\n  return `${type}: ${firstLine}`;\n}\n\n// TODO: Metro doesn't expose this info even though it knows it.\nfunction getLineNumberForStringInFile(originModulePath: string, targetModuleName: string) {\n  let file;\n  try {\n    file = fs.readFileSync(originModulePath, 'utf8');\n  } catch (error: any) {\n    if (error.code === 'ENOENT' || error.code === 'EISDIR') {\n      // We're probably dealing with a virtualised file system where\n      // `this.originModulePath` doesn't actually exist on disk.\n      // We can't show a code frame, but there's no need to let this I/O\n      // error shadow the original module resolution error.\n      return null;\n    }\n    throw error;\n  }\n  const lines = file.split('\\n');\n  let lineNumber = 0;\n  let column = -1;\n  for (let line = 0; line < lines.length; line++) {\n    const columnLocation = lines[line].lastIndexOf(targetModuleName);\n    if (columnLocation >= 0) {\n      lineNumber = line;\n      column = columnLocation;\n      break;\n    }\n  }\n  return { lineNumber, column };\n}\n"], "names": ["getXcodeCompilerErrorMessage", "isExecutingFromXcodebuild", "isPossiblyUnableToResolveError", "logInXcode", "logMetroErrorInXcode", "warnInXcode", "error", "message", "originModulePath", "targetModuleName", "isPossiblyTransformError", "filename", "lineNumber", "projectRoot", "makeFilepathAbsolute", "filepath", "startsWith", "path", "join", "makeXcodeCompilerLog", "loc", "getLineNumberForStringInFile", "fileName", "column", "console", "Log", "log", "warn", "process", "env", "BUILT_PRODUCTS_DIR", "type", "firstLine", "split", "includes", "file", "fs", "readFileSync", "code", "lines", "line", "length", "columnLocation", "lastIndexOf"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IA+BeA,4BAA4B;eAA5BA;;IAqDAC,yBAAyB;eAAzBA;;IA9EAC,8BAA8B;eAA9BA;;IAoEAC,UAAU;eAAVA;;IAPAC,oBAAoB;eAApBA;;IAWAC,WAAW;eAAXA;;;;gEA7ED;;;;;;;gEACE;;;;;;qBAEG;;;;;;AAEb,SAASH,+BACdI,KAAU;IAEV,OACE,aAAaA,SACb,OAAOA,MAAMC,OAAO,KAAK,YACzB,sBAAsBD,SACtB,OAAOA,MAAME,gBAAgB,KAAK,YAClC,sBAAsBF,SACtB,OAAOA,MAAMG,gBAAgB,KAAK;AAEtC;AACA,SAASC,yBACPJ,KAAU;IAEV,OACE,aAAaA,SACb,OAAOA,MAAMC,OAAO,KAAK,YACzB,cAAcD,SACd,OAAOA,MAAMK,QAAQ,KAAK,YAC1B,gBAAgBL,SAChB,OAAOA,MAAMM,UAAU,KAAK;AAEhC;AAEO,SAASZ,6BACda,WAAmB,EACnBP,KAAkB;IAElB,MAAMQ,uBAAuB,CAACC,WAC5BA,SAASC,UAAU,CAAC,OAAOD,WAAWE,eAAI,CAACC,IAAI,CAACL,aAAaE;IAE/D,IAAI,OAAOT,UAAU,UAAU;QAC7B,OAAOa,qBAAqB,SAASb;IACvC,OAAO,IAAI,aAAaA,OAAO;QAC7B,iCAAiC;QACjC,IAAIJ,+BAA+BI,QAAQ;YACzC,MAAMc,MAAMC,6BAA6Bf,MAAME,gBAAgB,EAAEF,MAAMG,gBAAgB;YACvF,OAAOU,qBAAqB,SAASb,MAAMC,OAAO,EAAE;gBAClDe,UAAUhB,MAAME,gBAAgB;gBAChCI,UAAU,EAAEQ,uBAAAA,IAAKR,UAAU;gBAC3BW,MAAM,EAAEH,uBAAAA,IAAKG,MAAM;YACrB;QACF,OAAO,IAAIb,yBAAyBJ,QAAQ;YAC1C,OAAOa,qBAAqB,SAASb,MAAMC,OAAO,EAAE;gBAClD,0EAA0E;gBAC1Ee,UAAUR,qBAAqBR,MAAMK,QAAQ;gBAC7CC,YAAYN,MAAMM,UAAU;gBAC5BW,QAAQjB,MAAMiB,MAAM;YACtB;QACA,yGAAyG;QAC3G,OAAO;YACL,gBAAgB;YAChB,OAAOJ,qBAAqB,SAASb,MAAMC,OAAO;QACpD;IACF;IAEA,OAAO;AACT;AAGO,SAASH,qBAAqBS,WAAmB,EAAEP,KAAqB;IAC7E,MAAMC,UAAUP,6BAA6Ba,aAAaP;IAC1D,IAAIC,WAAW,MAAM;QACnBiB,QAAQlB,KAAK,CAACC;IAChB;AACF;AAEO,SAASJ,WAAWI,OAAe;IACxCkB,QAAG,CAACC,GAAG,CAACP,qBAAqB,QAAQZ;AACvC;AAEO,SAASF,YAAYE,OAAe;IACzCkB,QAAG,CAACE,IAAI,CAACR,qBAAqB,WAAWZ;AAC3C;AAIO,SAASN;IACd,OAAO,CAAC,CAAC2B,QAAQC,GAAG,CAACC,kBAAkB;AACzC;AAEA,SAASX,qBACPY,IAAkD,EAClDxB,OAAe,EACf,EACEe,QAAQ,EACRV,UAAU,EACVW,MAAM,EAMP,GAAG,CAAC,CAAC;IAEN,IAAI,CAACtB,6BAA6B;QAChC,OAAOM;IACT;IACA,mDAAmD;IACnD,MAAMyB,YAAYzB,QAAQ0B,KAAK,CAAC,KAAK,CAAC,EAAE;IACxC,IAAIX,YAAY,EAACA,4BAAAA,SAAUY,QAAQ,CAAC,OAAM;QACxC,OAAO,GAAGZ,SAAS,CAAC,EAAEV,cAAc,EAAE,CAAC,EACrCW,UAAU,OAAOA,SAAS,MAAM,GACjC,CAAC,EAAEQ,KAAK,EAAE,EAAEC,WAAW;IAC1B;IACA,OAAO,GAAGD,KAAK,EAAE,EAAEC,WAAW;AAChC;AAEA,gEAAgE;AAChE,SAASX,6BAA6Bb,gBAAwB,EAAEC,gBAAwB;IACtF,IAAI0B;IACJ,IAAI;QACFA,OAAOC,aAAE,CAACC,YAAY,CAAC7B,kBAAkB;IAC3C,EAAE,OAAOF,OAAY;QACnB,IAAIA,MAAMgC,IAAI,KAAK,YAAYhC,MAAMgC,IAAI,KAAK,UAAU;YACtD,8DAA8D;YAC9D,0DAA0D;YAC1D,kEAAkE;YAClE,qDAAqD;YACrD,OAAO;QACT;QACA,MAAMhC;IACR;IACA,MAAMiC,QAAQJ,KAAKF,KAAK,CAAC;IACzB,IAAIrB,aAAa;IACjB,IAAIW,SAAS,CAAC;IACd,IAAK,IAAIiB,OAAO,GAAGA,OAAOD,MAAME,MAAM,EAAED,OAAQ;QAC9C,MAAME,iBAAiBH,KAAK,CAACC,KAAK,CAACG,WAAW,CAAClC;QAC/C,IAAIiC,kBAAkB,GAAG;YACvB9B,aAAa4B;YACbjB,SAASmB;YACT;QACF;IACF;IACA,OAAO;QAAE9B;QAAYW;IAAO;AAC9B"}