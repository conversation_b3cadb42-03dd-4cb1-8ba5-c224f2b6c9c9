/**
 * Integration Test for Viva Webhook System
 * 
 * Tests the complete webhook flow including:
 * - Webhook event processing
 * - Transaction updates
 * - Receipt generation
 * - WebSocket broadcasting
 */

const axios = require('axios');
const WebSocket = require('ws');

const BASE_URL = 'http://localhost:3001/api/v1';
const WS_URL = 'ws://localhost:3001/ws';

// Test configuration
const TEST_CONFIG = {
  timeout: 10000,
  retryAttempts: 3,
  retryDelay: 2000
};

class IntegrationTester {
  constructor() {
    this.ws = null;
    this.receivedEvents = [];
    this.testResults = {
      webhookHealth: false,
      webhookVerification: false,
      websocketConnection: false,
      webhookProcessing: false,
      realTimeUpdates: false
    };
  }

  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      try {
        console.log('🔌 Connecting to WebSocket server...');
        this.ws = new WebSocket(WS_URL);

        this.ws.on('open', () => {
          console.log('✅ WebSocket connected successfully');
          this.testResults.websocketConnection = true;
          resolve();
        });

        this.ws.on('message', (data) => {
          try {
            const message = JSON.parse(data.toString());
            console.log(`📨 Received WebSocket message: ${message.type}`);
            this.receivedEvents.push(message);
          } catch (error) {
            console.error('❌ Error parsing WebSocket message:', error);
          }
        });

        this.ws.on('error', (error) => {
          console.error('❌ WebSocket error:', error.message);
          reject(error);
        });

        this.ws.on('close', () => {
          console.log('🔌 WebSocket connection closed');
        });

        // Timeout for connection
        setTimeout(() => {
          if (!this.testResults.websocketConnection) {
            reject(new Error('WebSocket connection timeout'));
          }
        }, TEST_CONFIG.timeout);

      } catch (error) {
        reject(error);
      }
    });
  }

  async testWebhookHealth() {
    try {
      console.log('\n📋 Testing webhook health endpoint...');
      const response = await axios.get(`${BASE_URL}/viva/webhook/health`, {
        timeout: TEST_CONFIG.timeout
      });
      
      if (response.status === 200 && response.data.success) {
        console.log('✅ Webhook health check passed');
        this.testResults.webhookHealth = true;
        return true;
      } else {
        console.log('❌ Webhook health check failed');
        return false;
      }
    } catch (error) {
      console.log('❌ Webhook health check error:', error.message);
      return false;
    }
  }

  async testWebhookVerification() {
    try {
      console.log('\n📋 Testing webhook verification endpoint...');
      const response = await axios.get(`${BASE_URL}/viva/webhook/verify`, {
        timeout: TEST_CONFIG.timeout
      });
      
      if (response.status === 200 && response.data.Key) {
        console.log('✅ Webhook verification endpoint working');
        console.log(`   Verification key: ${response.data.Key.substring(0, 10)}...`);
        this.testResults.webhookVerification = true;
        return true;
      } else {
        console.log('❌ Webhook verification endpoint failed');
        return false;
      }
    } catch (error) {
      console.log('❌ Webhook verification error:', error.message);
      return false;
    }
  }

  async testWebhookProcessing() {
    try {
      console.log('\n📋 Testing webhook event processing...');
      
      // Clear previous events
      this.receivedEvents = [];
      
      const testEvent = {
        Url: "https://demo.vivapayments.com/api/messages/events/1796",
        EventData: {
          Amount: 1500, // €15.00
          MerchantId: "30481af3-63d9-42cd-93ea-1937a972b76d",
          OrderCode: Date.now(), // Unique order code
          StatusId: "F", // Success
          TransactionId: `test-integration-${Date.now()}`,
          InsDate: new Date().toISOString(),
          CardNumber: "414746XXXXXX0133",
          AuthorizationId: "471543",
          ResponseCode: "00",
          CurrencyCode: "978", // EUR
          MerchantTrns: "Integration test payment",
          CustomerTrns: "Test payment for integration",
          CardIssuingBank: "Test Bank",
          ReferenceNumber: 471543,
          RetrievalReferenceNumber: "************"
        },
        Created: new Date().toISOString(),
        CorrelationId: `test-correlation-${Date.now()}`,
        EventTypeId: 1796, // Transaction Payment Created
        RetryCount: 0,
        MessageId: `test-message-${Date.now()}`,
        RecipientId: "test-recipient",
        MessageTypeId: 512
      };

      const response = await axios.post(`${BASE_URL}/viva/webhook`, testEvent, {
        headers: { 'Content-Type': 'application/json' },
        timeout: TEST_CONFIG.timeout
      });

      if (response.status === 200 && response.data.success) {
        console.log('✅ Webhook event processed successfully');
        console.log(`   Transaction ID: ${response.data.transactionId}`);
        console.log(`   Event Type: ${response.data.eventDescription}`);
        this.testResults.webhookProcessing = true;
        
        // Wait for WebSocket events
        await this.waitForWebSocketEvents();
        
        return true;
      } else {
        console.log('❌ Webhook event processing failed');
        return false;
      }
    } catch (error) {
      console.log('❌ Webhook processing error:', error.response?.data || error.message);
      return false;
    }
  }

  async waitForWebSocketEvents() {
    console.log('\n📋 Waiting for real-time WebSocket events...');
    
    return new Promise((resolve) => {
      const startTime = Date.now();
      const checkInterval = setInterval(() => {
        const elapsed = Date.now() - startTime;
        
        // Check for expected events
        const vivaEvents = this.receivedEvents.filter(e => e.type === 'viva_webhook_event');
        const transactionEvents = this.receivedEvents.filter(e => e.type === 'transaction_update');
        const receiptEvents = this.receivedEvents.filter(e => e.type === 'receipt_event');
        
        console.log(`   Events received: ${this.receivedEvents.length} (Viva: ${vivaEvents.length}, Transaction: ${transactionEvents.length}, Receipt: ${receiptEvents.length})`);
        
        if (vivaEvents.length > 0) {
          console.log('✅ Viva webhook event received via WebSocket');
          this.testResults.realTimeUpdates = true;
        }
        
        if (transactionEvents.length > 0) {
          console.log('✅ Transaction update received via WebSocket');
        }
        
        if (receiptEvents.length > 0) {
          console.log('✅ Receipt event received via WebSocket');
        }
        
        // Stop waiting after timeout or when we have events
        if (elapsed > TEST_CONFIG.timeout || this.receivedEvents.length > 0) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 1000);
    });
  }

  async runIntegrationTests() {
    console.log('🚀 Starting Viva Webhook Integration Tests');
    console.log('=' .repeat(60));
    
    try {
      // Test 1: Webhook Health
      await this.testWebhookHealth();
      
      // Test 2: Webhook Verification
      await this.testWebhookVerification();
      
      // Test 3: WebSocket Connection
      try {
        await this.connectWebSocket();
      } catch (error) {
        console.log('⚠️  WebSocket connection failed, continuing without real-time tests');
      }
      
      // Test 4: Webhook Processing
      await this.testWebhookProcessing();
      
      // Close WebSocket
      if (this.ws) {
        this.ws.close();
      }
      
    } catch (error) {
      console.error('💥 Integration test failed:', error);
    }
    
    // Print results
    this.printResults();
  }

  printResults() {
    console.log('\n' + '=' .repeat(60));
    console.log('📊 Integration Test Results:');
    console.log('=' .repeat(60));
    
    const tests = [
      { name: 'Webhook Health Check', result: this.testResults.webhookHealth },
      { name: 'Webhook Verification', result: this.testResults.webhookVerification },
      { name: 'WebSocket Connection', result: this.testResults.websocketConnection },
      { name: 'Webhook Processing', result: this.testResults.webhookProcessing },
      { name: 'Real-time Updates', result: this.testResults.realTimeUpdates }
    ];
    
    let passed = 0;
    let failed = 0;
    
    tests.forEach(test => {
      const status = test.result ? '✅ PASS' : '❌ FAIL';
      console.log(`${test.name.padEnd(25)}: ${status}`);
      if (test.result) passed++; else failed++;
    });
    
    console.log('=' .repeat(60));
    console.log(`Total Tests: ${tests.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / tests.length) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
      console.log('\n🎉 All integration tests passed!');
      console.log('✅ Viva webhook system is fully functional and ready for production.');
    } else if (passed >= 3) {
      console.log('\n⚠️  Most tests passed. Core functionality is working.');
      console.log('💡 Some advanced features may need server to be running.');
    } else {
      console.log('\n❌ Multiple tests failed. Please check server configuration.');
    }
    
    console.log('\n📋 WebSocket Events Received:');
    this.receivedEvents.forEach((event, index) => {
      console.log(`  ${index + 1}. ${event.type} (${new Date(event.timestamp).toLocaleTimeString()})`);
    });
  }
}

// Run integration tests
if (require.main === module) {
  const tester = new IntegrationTester();
  tester.runIntegrationTests().catch(error => {
    console.error('💥 Integration test execution failed:', error);
    process.exit(1);
  });
}

module.exports = IntegrationTester;
