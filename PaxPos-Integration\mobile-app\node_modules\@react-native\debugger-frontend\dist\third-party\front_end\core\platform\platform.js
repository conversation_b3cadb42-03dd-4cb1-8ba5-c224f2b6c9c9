function e(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function t(r,n,o,i,a,s){if(i<=o)return;const l=function(t,r,n,o,i){const a=t[i];e(t,o,i);let s=n;for(let i=n;i<o;++i)r(t[i],a)<0&&(e(t,s,i),++s);return e(t,o,s),s}(r,n,o,i,Math.floor(Math.random()*(i-o))+o);a<l&&t(r,n,o,l-1,a,s),l<s&&t(r,n,l+1,i,a,s)}function r(e,t,r,n){const o=[];let i=0,a=0;for(;i<e.length&&a<t.length;){const s=r(e[i],t[a]);!n&&s||o.push(s<=0?e[i]:t[a]),s<=0&&i++,s>=0&&a++}if(n){for(;i<e.length;)o.push(e[i++]);for(;a<t.length;)o.push(t[a++])}return o}function n(e,t,r,n,o){let i=n||0,a=void 0!==o?o:e.length;for(;i<a;){const n=i+a>>1;r(t,e[n])>0?i=n+1:a=n}return a}function o(e,t,r){const n="END"===r;if(0===e.length)return null;let o=0,i=e.length-1,a=0,s=!1,l=!1,u=0;do{u=o+(i-o)/2,a=n?Math.ceil(u):Math.floor(u),s=t(e[a]),l=s===n,l?o=Math.min(i,a+(o===a?1:0)):i=Math.max(o,a+(i===a?-1:0))}while(i!==o);return t(e[o])?o:null}var i=Object.freeze({__proto__:null,removeElement:(e,t,r)=>{let n=e.indexOf(t);if(-1===n)return!1;if(r)return e.splice(n,1),!0;for(let r=n+1,o=e.length;r<o;++r)e[r]!==t&&(e[n++]=e[r]);return e.length=n,!0},sortRange:function(e,r,n,o,i,a){return 0===n&&o===e.length-1&&0===i&&a>=o?e.sort(r):t(e,r,n,o,i,a),e},binaryIndexOf:(e,t,r)=>{const o=n(e,t,r);return o<e.length&&0===r(t,e[o])?o:-1},intersectOrdered:(e,t,n)=>r(e,t,n,!1),mergeOrdered:(e,t,n)=>r(e,t,n,!0),DEFAULT_COMPARATOR:(e,t)=>e<t?-1:e>t?1:0,lowerBound:n,upperBound:function(e,t,r,n,o){let i=n||0,a=void 0!==o?o:e.length;for(;i<a;){const n=i+a>>1;r(t,e[n])>=0?i=n+1:a=n}return a},nearestIndexFromBeginning:function(e,t){return o(e,t,"BEGINNING")},nearestIndexFromEnd:function(e,t){return o(e,t,"END")},arrayDoesNotContainNullOrUndefined:function(e){return!e.includes(null)&&!e.includes(void 0)}}),a=Object.freeze({__proto__:null});var s=Object.freeze({__proto__:null,isValid:e=>!isNaN(e.getTime()),toISO8601Compact:e=>{function t(e){return(e>9?"":"0")+e}return e.getFullYear()+t(e.getMonth()+1)+t(e.getDate())+"T"+t(e.getHours())+t(e.getMinutes())+t(e.getSeconds())}});var l=Object.freeze({__proto__:null,EmptyUrlString:"",EmptyRawPathString:"",EmptyEncodedPathString:""});var u=Object.freeze({__proto__:null,deepActiveElement:function(e){let t=e.activeElement;for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t},getEnclosingShadowRootForNode:function(e){let t=e.parentNodeOrShadowHost();for(;t;){if(t instanceof ShadowRoot)return t;t=t.parentNodeOrShadowHost()}return null},rangeOfWord:function(e,t,r,n,o){let i,a,s=0,l=0;if(n||(n=e),o&&"backward"!==o&&"both"!==o)i=e,s=t;else{let o=e;for(;o;){if(o===n){i||(i=n);break}if(o.nodeType===Node.TEXT_NODE&&null!==o.nodeValue){for(let n=o===e?t-1:o.nodeValue.length-1;n>=0;--n)if(-1!==r.indexOf(o.nodeValue[n])){i=o,s=n+1;break}}if(i)break;o=o.traversePreviousNode(n)}i||(i=n,s=0)}if(o&&"forward"!==o&&"both"!==o)a=e,l=t;else{let o=e;for(;o;){if(o===n){a||(a=n);break}if(o.nodeType===Node.TEXT_NODE&&null!==o.nodeValue){for(let n=o===e?t:0;n<o.nodeValue.length;++n)if(-1!==r.indexOf(o.nodeValue[n])){a=o,l=n;break}}if(a)break;o=o.traverseNextNode(n)}a||(a=n,l=n.nodeType===Node.TEXT_NODE?n.nodeValue?.length||0:n.childNodes.length)}if(!e.ownerDocument)throw new Error("No `ownerDocument` found for rootNode");const u=e.ownerDocument.createRange();return u.setStart(i,s),u.setEnd(a,l),u}});const c=new Set(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"]);var f=Object.freeze({__proto__:null,ENTER_KEY:"Enter",ESCAPE_KEY:"Escape",TAB_KEY:"Tab",ARROW_KEYS:c,keyIsArrowKey:function(e){return c.has(e)},isEscKey:function(e){return"Escape"===e.key},isEnterOrSpaceKey:function(e){return"Enter"===e.key||" "===e.key}});class h{map=new Map;set(e,t){let r=this.map.get(e);r||(r=new Set,this.map.set(e,r)),r.add(t)}get(e){return this.map.get(e)||new Set}has(e){return this.map.has(e)}hasValue(e,t){const r=this.map.get(e);return!!r&&r.has(t)}get size(){return this.map.size}delete(e,t){const r=this.get(e);if(!r)return!1;const n=r.delete(t);return r.size||this.map.delete(e),n}deleteAll(e){this.map.delete(e)}keysArray(){return[...this.map.keys()]}keys(){return this.map.keys()}valuesArray(){const e=[];for(const t of this.map.values())e.push(...t.values());return e}clear(){this.map.clear()}}var p=Object.freeze({__proto__:null,inverse:function(e){const t=new h;for(const[r,n]of e.entries())t.set(n,r);return t},Multimap:h,getWithDefault:function(e,t,r){let n=e.get(t);return n||(n=r(t),e.set(t,n)),n}});const d=new Set(["application/ecmascript","application/javascript","application/json","application/json+protobuf","application/vnd.dart","application/xml","application/x-aspx","application/x-javascript","application/x-jsp","application/x-httpd-php"]);function g(e,t,r=0){for(let n=r;n<e.length;n++)if(t.includes(e[n]))return n;return-1}function m(e,t,r=0){for(let n=r;n<e.length;n++)if(!t.includes(e[n]))return n;return-1}var b=Object.freeze({__proto__:null,isTextType:function(e){return e.startsWith("text/")||e.endsWith("+json")||e.endsWith("+xml")||d.has(e)},parseContentType:function(e){if("*/*"===e)return{mimeType:null,charset:null};const{mimeType:t,params:r}=function(e){e=e.trim();let t=g(e," \t;(");t<0&&(t=e.length);const r=e.indexOf("/");if(r<0||r>t)return{mimeType:null,params:new Map};const n=e.substring(0,t).toLowerCase(),o=new Map;let i=e.indexOf(";",t);for(;i>=0&&i<e.length;){if(++i,i=m(e," \t",i),i<0)continue;const t=i;if(i=g(e,";=",i),i<0||";"===e[i])continue;const r=e.substring(t,i).toLowerCase();++i,i=m(e," \t",i);let n="";if(!(i<0||";"===e[i])){if('"'!==e[i]){const t=i;i=e.indexOf(";",i);const r=i>=0?i:e.length;n=e.substring(t,r).trimEnd()}else{for(++i;i<e.length&&'"'!==e[i];)"\\"===e[i]&&i+1<e.length&&++i,n+=e[i],++i;i=e.indexOf(";",i)}o.has(r)||o.set(r,n)}}return{mimeType:n,params:o}}(e);return{mimeType:t,charset:r.get("charset")?.toLowerCase().trim()??null}}});const w=(e,t)=>{for(e=Math.round(e),t=Math.round(t);0!==t;){const r=t;t=e%t,e=r}return e},x=new Map([["8∶5","16∶10"]]);var _=Object.freeze({__proto__:null,clamp:(e,t,r)=>{let n=e;return e<t?n=t:e>r&&(n=r),n},mod:(e,t)=>(e%t+t)%t,bytesToString:e=>{if(e<1e3)return`${e.toFixed(0)} B`;const t=e/1e3;if(t<100)return`${t.toFixed(1)} kB`;if(t<1e3)return`${t.toFixed(0)} kB`;const r=t/1e3;return r<100?`${r.toFixed(1)} MB`:`${r.toFixed(0)} MB`},toFixedIfFloating:e=>{if(!e||Number.isNaN(Number(e)))return e;const t=Number(e);return t%1?t.toFixed(3):String(t)},floor:(e,t=0)=>{const r=Math.pow(10,t);return Math.floor(e*r)/r},greatestCommonDivisor:w,aspectRatio:(e,t)=>{const r=w(e,t);0!==r&&(e/=r,t/=r);const n=`${e}∶${t}`;return x.get(n)||n},withThousandsSeparator:function(e){let t=String(e);const r=/(\d+)(\d{3})/;for(;t.match(r);)t=t.replace(r,"$1 $2");return t}});var E=Object.freeze({__proto__:null,promiseWithResolvers:function(){let e,t;return{promise:new Promise(((r,n)=>{e=r,t=n})),resolve:e,reject:t}}});const O=(e,t)=>{let r=!1;for(let n=0;n<t.length;++n)if(-1!==e.indexOf(t.charAt(n))){r=!0;break}if(!r)return String(e);let n="";for(let r=0;r<e.length;++r)-1!==t.indexOf(e.charAt(r))&&(n+="\\"),n+=e.charAt(r);return n},N=(e,t)=>e.toString(16).toUpperCase().padStart(t,"0"),y=new Map([["\b","\\b"],["\f","\\f"],["\n","\\n"],["\r","\\r"],["\t","\\t"],["\v","\\v"],["'","\\'"],["\\","\\\\"],["\x3c!--","\\x3C!--"],["<script","\\x3Cscript"],["</script","\\x3C/script"]]),A=(e,t)=>{const r=[];let n=e.indexOf(t);for(;-1!==n;)r.push(n),n=e.indexOf(t,n+t.length);return r},v=/^([a-z0-9]+(?:-[a-z0-9]+)*\.)*[a-z0-9]+(?:-[a-z0-9]+)*$/,S="^[]{}()\\.^$*+?|-,",C=function(){return S},T=function(e,t){let r="";for(let t=0;t<e.length;++t){const n=e.charAt(t);-1!==C().indexOf(n)&&(r+="\\"),r+=n}return new RegExp(r,t||"")};const U=/[A-Z]{2,}(?=[A-Z0-9][a-z0-9]+|\b|_)|[A-Za-z][0-9]+[a-z]?|[A-Z]?[a-z]+|[0-9][A-Za-z]+|[A-Z]|[0-9]+|[.]/g,M=function(e){return e.match?.(U)?.map((e=>e.toLowerCase())).join("-").replaceAll("-.-",".")||e};var R=Object.freeze({__proto__:null,escapeCharacters:O,formatAsJSLiteral:e=>{const t=/(\\|<(?:!--|\/?script))|(\p{Control})|(\p{Surrogate})/gu,r=/(\\|'|<(?:!--|\/?script))|(\p{Control})|(\p{Surrogate})/gu,n=(e,t,r,n)=>{if(r){if(y.has(r))return y.get(r);return"\\x"+N(r.charCodeAt(0),2)}if(n){return"\\u"+N(n.charCodeAt(0),4)}return t?y.get(t)||"":e};let o="",i="";return e.includes("'")?e.includes('"')?e.includes("`")||e.includes("${")?(i="'",o=e.replaceAll(r,n)):(i="`",o=e.replaceAll(t,n)):(i='"',o=e.replaceAll(t,n)):(i="'",o=e.replaceAll(t,n)),`${i}${o}${i}`},sprintf:(e,...t)=>{let r=0;return e.replaceAll(/%(?:(\d+)\$)?(?:\.(\d*))?([%dfs])/g,((e,n,o,i)=>{if("%"===i)return"%";if(void 0!==n&&(r=parseInt(n,10)-1,r<0))throw new RangeError(`Invalid parameter index ${r+1}`);if(r>=t.length)throw new RangeError(`Expected at least ${r+1} format parameters, but only ${t.length} where given.`);if("s"===i){const e=String(t[r++]);return void 0!==o?e.substring(0,Number(o)):e}let a=Number(t[r++]);return isNaN(a)&&(a=0),"d"===i?String(Math.floor(a)).padStart(Number(o),"0"):void 0!==o?a.toFixed(Number(o)):String(a)}))},toBase64:e=>{function t(e){return e<26?e+65:e<52?e+71:e<62?e-4:62===e?43:63===e?47:65}const r=(new TextEncoder).encode(e.toString()),n=r.length;let o,i="";if(0===n)return i;let a=0;for(let e=0;e<n;e++)o=e%3,a|=r[e]<<(16>>>o&24),2===o&&(i+=String.fromCharCode(t(a>>>18&63),t(a>>>12&63),t(a>>>6&63),t(63&a)),a=0);return 0===o?i+=String.fromCharCode(t(a>>>18&63),t(a>>>12&63),61,61):1===o&&(i+=String.fromCharCode(t(a>>>18&63),t(a>>>12&63),t(a>>>6&63),61)),i},findIndexesOfSubString:A,findLineEndingIndexes:e=>{const t=A(e,"\n");return t.push(e.length),t},isWhitespace:e=>/^\s*$/.test(e),trimURL:(e,t)=>{let r=e.replace(/^(https|http|file):\/\//i,"");return t&&r.toLowerCase().startsWith(t.toLowerCase())&&(r=r.substr(t.length)),r},collapseWhitespace:e=>e.replace(/[\s\xA0]+/g," "),reverse:e=>e.split("").reverse().join(""),replaceControlCharacters:e=>e.replace(/[\0-\x08\x0B\f\x0E-\x1F\x80-\x9F]/g,"�"),countWtf8Bytes:e=>{let t=0;for(let r=0;r<e.length;r++){const n=e.charCodeAt(r);if(n<=127)t++;else if(n<=2047)t+=2;else if(n<55296||57343<n)t+=3;else{if(n<=56319&&r+1<e.length){const n=e.charCodeAt(r+1);if(56320<=n&&n<=57343){t+=4,r++;continue}}t+=3}}return t},stripLineBreaks:e=>e.replace(/(\r)?\n/g,""),isExtendedKebabCase:e=>v.test(e),toTitleCase:e=>e.substring(0,1).toUpperCase()+e.substring(1),removeURLFragment:e=>{const t=new URL(e);return t.hash="",t.toString()},regexSpecialCharacters:C,filterRegex:function(e){let t="^(?:.*\\0)?";for(let r=0;r<e.length;++r){let n=e.charAt(r);-1!==S.indexOf(n)&&(n="\\"+n),t+="[^\\0"+n+"]*"+n}return new RegExp(t,"i")},createSearchRegex:function(e,t,r,n=!1){const o=t?"g":"gi";let i;if(r)try{i=new RegExp(e,o)}catch(e){}return i||(i=T(e,o)),n&&i&&(i=new RegExp(`\\b${i.source}\\b`,o)),i},caseInsensetiveComparator:function(e,t){return(e=e.toUpperCase())===(t=t.toUpperCase())?0:e>t?1:-1},hashCode:function(e){if(!e)return 0;const t=4294967291;let r=0,n=1;for(let o=0;o<e.length;o++){r=(r+n*(1506996573*e.charCodeAt(o)))%t,n=1345575271*n%t}return r=(r+n*(t-1))%t,Math.abs(0|r)},compare:(e,t)=>e>t?1:e<t?-1:0,trimMiddle:(e,t)=>{if(e.length<=t)return String(e);let r=t>>1,n=t-r-1;return e.codePointAt(e.length-n-1)>=65536&&(--n,++r),r>0&&e.codePointAt(r-1)>=65536&&--r,e.substr(0,r)+"…"+e.substr(e.length-n,n)},trimEndWithMaxLength:(e,t)=>e.length<=t?String(e):e.substr(0,t-1)+"…",escapeForRegExp:e=>O(e,S),naturalOrderComparator:(e,t)=>{const r=/^\d+|^\D+/;let n,o,i,a;for(;;){if(!e)return t?-1:0;if(!t)return 1;if(n=e.match(r)[0],o=t.match(r)[0],i=!Number.isNaN(Number(n)),a=!Number.isNaN(Number(o)),i&&!a)return-1;if(a&&!i)return 1;if(i&&a){const e=Number(n)-Number(o);if(e)return e;if(n.length!==o.length)return Number(n)||Number(o)?o.length-n.length:n.length-o.length}else if(n!==o)return n<o?-1:1;e=e.substring(n.length),t=t.substring(o.length)}},base64ToSize:function(e){if(!e)return 0;let t=3*e.length/4;return"="===e[e.length-1]&&t--,e.length>1&&"="===e[e.length-2]&&t--,t},SINGLE_QUOTE:"'",DOUBLE_QUOTE:'"',findUnclosedCssQuote:function(e){let t="";for(let r=0;r<e.length;++r){const n=e[r];"\\"!==n?"'"!==n&&'"'!==n||(t===n?t="":""===t&&(t=n)):r++}return t},countUnmatchedLeftParentheses:e=>{let t=0;for(const r of e)"("===r?t++:")"===r&&t>0&&t--;return t},createPlainTextSearchRegex:T,toLowerCaseString:function(e){return e.toLowerCase()},toKebabCase:M,toKebabCaseKeys:function(e){const t={};for(const[r,n]of Object.entries(e))t[M(r)]=n;return t},replaceLast:function(e,t,r){const n=e.lastIndexOf(t);return-1===n?e:e.slice(0,n)+e.slice(n).replace(t,r)},stringifyWithPrecision:function(e,t=2){if(0===t)return e.toFixed(0);const r=e.toFixed(t).replace(/\.?0*$/,"");return"-0"===r?"0":r},concatBase64:function(e,t){if(0===e.length||!e.endsWith("="))return e+t;const r=e.substring(0,e.length-4),n=e.substring(e.length-4);return r+window.btoa(window.atob(n)+window.atob(t))}});var z=Object.freeze({__proto__:null,secondsToMilliSeconds:function(e){return 1e3*e},milliSecondsToSeconds:function(e){return e/1e3},microSecondsToMilliSeconds:function(e){return e/1e3}});class L extends Uint32Array{getValue(e){return this[e]}setValue(e,t){this[e]=t}asUint32ArrayOrFail(){return this}asArrayOrFail(){throw new Error("Not an array")}}class j{#e;#t;length;constructor(e,t){this.#e=[],this.length=e;let r=1;for(;;){r*=2,this.#t=Math.ceil(e/r);try{if(void 0!==t&&this.#t>t)throw new RangeError;for(let e=0;e<r;++e)this.#e[e]=new Uint32Array(this.#t);return}catch(e){if(this.#t<1e6)throw e}}}getValue(e){if(e>=0&&e<this.length){const t=this.#t;return this.#e[Math.floor(e/t)][e%t]}return this.#e[0][-1]}setValue(e,t){if(e>=0&&e<this.length){const r=this.#t;this.#e[Math.floor(e/r)][e%r]=t}}asUint32ArrayOrFail(){throw new Error("Not a Uint32Array")}asArrayOrFail(){throw new Error("Not an array")}}class F extends Array{getValue(e){return this[e]}setValue(e,t){this[e]=t}asUint32ArrayOrFail(){throw new Error("Not a Uint32Array")}asArrayOrFail(){return this}}class $ extends Uint8Array{constructor(e){super(Math.ceil(e/8))}getBit(e){return 0!==(this[e>>3]&1<<(7&e))}setBit(e){this[e>>3]|=1<<(7&e)}clearBit(e){this[e>>3]&=~(1<<(7&e))}previous(e){for(;e!==e>>3<<3;)if(--e,this.getBit(e))return e;let t;for(t=(e>>3)-1;t>=0&&0===this[t];--t);if(t<0)return-1;for(e=7+(t<<3);e>=t<<3;--e)if(this.getBit(e))return e;throw new Error("Unreachable")}}var B=Object.freeze({__proto__:null,createExpandableBigUint32Array:function(){return new F},createFixedBigUint32Array:function(e,t){try{if(void 0!==t&&e>t)throw new RangeError;return new L(e)}catch{return new j(e,t)}},createBitVector:function(e){return new $(e)}});function k(e,t){if(null==e)throw new Error(`Expected given value to not be null/undefined but it was: ${e}${t?`\n${t}`:""}`)}function V(e,t){throw new Error(t)}function D(e){return e}var I=Object.freeze({__proto__:null,assertNotNullOrUndefined:k,assertNever:V,assertUnhandled:D});var W=Object.freeze({__proto__:null,LocalizedEmptyString:""});class K extends Error{message;constructor(e){super(e),this.message=e}}var P=Object.freeze({__proto__:null,UserVisibleError:K,isUserVisibleError:function(e){return"object"==typeof e&&null!==e&&e instanceof K}});export{i as ArrayUtilities,a as Brand,u as DOMUtilities,s as DateUtilities,l as DevToolsPath,f as KeyboardUtilities,p as MapUtilities,b as MimeType,_ as NumberUtilities,E as PromiseUtilities,R as StringUtilities,z as Timing,I as TypeScriptUtilities,B as TypedArrayUtilities,W as UIString,P as UserVisibleError,V as assertNever,k as assertNotNullOrUndefined,D as assertUnhandled};
