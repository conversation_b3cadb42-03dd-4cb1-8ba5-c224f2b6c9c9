/**
 * Copyright © 2022 650 Industries.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "CreateFileMiddleware", {
    enumerable: true,
    get: function() {
        return CreateFileMiddleware;
    }
});
function _fs() {
    const data = /*#__PURE__*/ _interop_require_default(require("fs"));
    _fs = function() {
        return data;
    };
    return data;
}
function _path() {
    const data = /*#__PURE__*/ _interop_require_default(require("path"));
    _path = function() {
        return data;
    };
    return data;
}
const _ExpoMiddleware = require("./ExpoMiddleware");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const debug = require('debug')('expo:start:server:middleware:createFile');
class CreateFileMiddleware extends _ExpoMiddleware.ExpoMiddleware {
    constructor(projectRoot){
        super(projectRoot, [
            '/_expo/touch'
        ]), this.projectRoot = projectRoot;
    }
    resolvePath(inputPath) {
        return this.resolveExtension(_path().default.join(this.projectRoot, inputPath));
    }
    resolveExtension(inputPath) {
        let resolvedPath = inputPath;
        const extension = _path().default.extname(inputPath);
        if (extension === '.js') {
            // Automatically convert JS files to TS files when added to a project
            // with TypeScript.
            const tsconfigPath = _path().default.join(this.projectRoot, 'tsconfig.json');
            if (_fs().default.existsSync(tsconfigPath)) {
                resolvedPath = resolvedPath.replace(/\.js$/, '.tsx');
            }
        }
        return resolvedPath;
    }
    async parseRawBody(req) {
        const rawBody = await new Promise((resolve, reject)=>{
            let body = '';
            req.on('data', (chunk)=>{
                body += chunk.toString();
            });
            req.on('end', ()=>{
                resolve(body);
            });
            req.on('error', (err)=>{
                reject(err);
            });
        });
        const properties = JSON.parse(rawBody);
        this.assertTouchFileBody(properties);
        return properties;
    }
    assertTouchFileBody(body) {
        if (typeof body !== 'object' || body == null) {
            throw new Error('Expected object');
        }
        if (typeof body.path !== 'string') {
            throw new Error('Expected "path" in body to be string');
        }
        if (typeof body.contents !== 'string') {
            throw new Error('Expected "contents" in body to be string');
        }
    }
    async handleRequestAsync(req, res) {
        if (req.method !== 'POST') {
            res.statusCode = 405;
            res.end('Method Not Allowed');
            return;
        }
        let properties;
        try {
            properties = await this.parseRawBody(req);
        } catch (e) {
            debug('Error parsing request body', e);
            res.statusCode = 400;
            res.end('Bad Request');
            return;
        }
        debug(`Requested: %O`, properties);
        const resolvedPath = properties.absolutePath ? this.resolveExtension(_path().default.resolve(properties.absolutePath)) : this.resolvePath(properties.path);
        if (_fs().default.existsSync(resolvedPath)) {
            res.statusCode = 409;
            res.end('File already exists.');
            return;
        }
        debug(`Resolved path:`, resolvedPath);
        try {
            await _fs().default.promises.mkdir(_path().default.dirname(resolvedPath), {
                recursive: true
            });
            await _fs().default.promises.writeFile(resolvedPath, properties.contents, 'utf8');
        } catch (e) {
            debug('Error writing file', e);
            res.statusCode = 500;
            res.end('Error writing file.');
            return;
        }
        debug(`File created`);
        res.statusCode = 200;
        res.end('OK');
    }
}

//# sourceMappingURL=CreateFileMiddleware.js.map