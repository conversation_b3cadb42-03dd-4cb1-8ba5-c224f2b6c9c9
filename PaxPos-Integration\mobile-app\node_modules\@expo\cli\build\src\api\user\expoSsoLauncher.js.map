{"version": 3, "sources": ["../../../../src/api/user/expoSsoLauncher.ts"], "sourcesContent": ["import assert from 'assert';\nimport openBrowserAsync from 'better-opn';\nimport http from 'http';\nimport { Socket } from 'node:net';\nimport querystring from 'querystring';\n\nimport * as Log from '../../log';\n\nconst successBody = `\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <title>Expo SSO Login</title>\n  <meta charset=\"utf-8\">\n  <style type=\"text/css\">\n    html {\n      margin: 0;\n      padding: 0\n    }\n\n    body {\n      background-color: #fff;\n      font-family: Tahoma,Verdana;\n      font-size: 16px;\n      color: #000;\n      max-width: 100%;\n      box-sizing: border-box;\n      padding: .5rem;\n      margin: 1em;\n      overflow-wrap: break-word\n    }\n  </style>\n</head>\n<body>\n  SSO login complete. You may now close this tab and return to the command prompt.\n</body>\n</html>`;\n\nexport async function getSessionUsingBrowserAuthFlowAsync({\n  expoWebsiteUrl,\n}: {\n  expoWebsiteUrl: string;\n}): Promise<string> {\n  const scheme = 'http';\n  const hostname = 'localhost';\n  const path = '/auth/callback';\n\n  const buildExpoSsoLoginUrl = (port: number): string => {\n    const data = {\n      app_redirect_uri: `${scheme}://${hostname}:${port}${path}`,\n    };\n    const params = querystring.stringify(data);\n    return `${expoWebsiteUrl}/sso-login?${params}`;\n  };\n\n  // Start server and begin auth flow\n  const executeAuthFlow = (): Promise<string> => {\n    return new Promise<string>(async (resolve, reject) => {\n      const connections = new Set<Socket>();\n\n      const server = http.createServer(\n        (request: http.IncomingMessage, response: http.ServerResponse) => {\n          try {\n            if (!(request.method === 'GET' && request.url?.includes(path))) {\n              throw new Error('Unexpected SSO login response.');\n            }\n            const url = new URL(request.url, `http:${request.headers.host}`);\n            const sessionSecret = url.searchParams.get('session_secret');\n\n            if (!sessionSecret) {\n              throw new Error('Request missing session_secret search parameter.');\n            }\n            resolve(sessionSecret);\n            response.writeHead(200, { 'Content-Type': 'text/html' });\n            response.write(successBody);\n            response.end();\n          } catch (error) {\n            reject(error);\n          } finally {\n            server.close();\n            // Ensure that the server shuts down\n            for (const connection of connections) {\n              connection.destroy();\n            }\n          }\n        }\n      );\n\n      server.listen(0, hostname, () => {\n        Log.log('Waiting for browser login...');\n\n        const address = server.address();\n        assert(\n          address !== null && typeof address === 'object',\n          'Server address and port should be set after listening has begun'\n        );\n        const port = address.port;\n        const authorizeUrl = buildExpoSsoLoginUrl(port);\n        openBrowserAsync(authorizeUrl);\n      });\n\n      server.on('connection', (connection) => {\n        connections.add(connection);\n\n        connection.on('close', () => {\n          connections.delete(connection);\n        });\n      });\n    });\n  };\n\n  return await executeAuthFlow();\n}\n"], "names": ["getSessionUsingBrowserAuthFlowAsync", "successBody", "expoWebsiteUrl", "scheme", "hostname", "path", "buildExpoSsoLoginUrl", "port", "data", "app_redirect_uri", "params", "querystring", "stringify", "executeAuthFlow", "Promise", "resolve", "reject", "connections", "Set", "server", "http", "createServer", "request", "response", "method", "url", "includes", "Error", "URL", "headers", "host", "sessionSecret", "searchParams", "get", "writeHead", "write", "end", "error", "close", "connection", "destroy", "listen", "Log", "log", "address", "assert", "authorizeUrl", "openBrowserAsync", "on", "add", "delete"], "mappings": ";;;;+BAsCsBA;;;eAAAA;;;;gEAtCH;;;;;;;gEACU;;;;;;;gEACZ;;;;;;;gEAEO;;;;;;6DAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4Bd,CAAC;AAED,eAAeD,oCAAoC,EACxDE,cAAc,EAGf;IACC,MAAMC,SAAS;IACf,MAAMC,WAAW;IACjB,MAAMC,OAAO;IAEb,MAAMC,uBAAuB,CAACC;QAC5B,MAAMC,OAAO;YACXC,kBAAkB,GAAGN,OAAO,GAAG,EAAEC,SAAS,CAAC,EAAEG,OAAOF,MAAM;QAC5D;QACA,MAAMK,SAASC,sBAAW,CAACC,SAAS,CAACJ;QACrC,OAAO,GAAGN,eAAe,WAAW,EAAEQ,QAAQ;IAChD;IAEA,mCAAmC;IACnC,MAAMG,kBAAkB;QACtB,OAAO,IAAIC,QAAgB,OAAOC,SAASC;YACzC,MAAMC,cAAc,IAAIC;YAExB,MAAMC,SAASC,eAAI,CAACC,YAAY,CAC9B,CAACC,SAA+BC;gBAC9B,IAAI;wBACgCD;oBAAlC,IAAI,CAAEA,CAAAA,QAAQE,MAAM,KAAK,WAASF,eAAAA,QAAQG,GAAG,qBAAXH,aAAaI,QAAQ,CAACrB,MAAI,GAAI;wBAC9D,MAAM,IAAIsB,MAAM;oBAClB;oBACA,MAAMF,MAAM,IAAIG,IAAIN,QAAQG,GAAG,EAAE,CAAC,KAAK,EAAEH,QAAQO,OAAO,CAACC,IAAI,EAAE;oBAC/D,MAAMC,gBAAgBN,IAAIO,YAAY,CAACC,GAAG,CAAC;oBAE3C,IAAI,CAACF,eAAe;wBAClB,MAAM,IAAIJ,MAAM;oBAClB;oBACAZ,QAAQgB;oBACRR,SAASW,SAAS,CAAC,KAAK;wBAAE,gBAAgB;oBAAY;oBACtDX,SAASY,KAAK,CAAClC;oBACfsB,SAASa,GAAG;gBACd,EAAE,OAAOC,OAAO;oBACdrB,OAAOqB;gBACT,SAAU;oBACRlB,OAAOmB,KAAK;oBACZ,oCAAoC;oBACpC,KAAK,MAAMC,cAActB,YAAa;wBACpCsB,WAAWC,OAAO;oBACpB;gBACF;YACF;YAGFrB,OAAOsB,MAAM,CAAC,GAAGrC,UAAU;gBACzBsC,KAAIC,GAAG,CAAC;gBAER,MAAMC,UAAUzB,OAAOyB,OAAO;gBAC9BC,IAAAA,iBAAM,EACJD,YAAY,QAAQ,OAAOA,YAAY,UACvC;gBAEF,MAAMrC,OAAOqC,QAAQrC,IAAI;gBACzB,MAAMuC,eAAexC,qBAAqBC;gBAC1CwC,IAAAA,oBAAgB,EAACD;YACnB;YAEA3B,OAAO6B,EAAE,CAAC,cAAc,CAACT;gBACvBtB,YAAYgC,GAAG,CAACV;gBAEhBA,WAAWS,EAAE,CAAC,SAAS;oBACrB/B,YAAYiC,MAAM,CAACX;gBACrB;YACF;QACF;IACF;IAEA,OAAO,MAAM1B;AACf"}