{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/VscodeRuntimeCallFunctionOn.ts"], "sourcesContent": ["import type Protocol from 'devtools-protocol';\n\nimport { MessageHand<PERSON> } from '../MessageHandler';\nimport { getDebuggerType } from '../getDebuggerType';\nimport type { CdpMessage, DebuggerRequest, DeviceResponse } from '../types';\n\n/**\n * Vscode is trying to inject a script to fetch information about \"Stringy\" variables.\n * Unfortunately, this script causes a <PERSON><PERSON> exception and crashes the app.\n *\n * @see https://github.com/expo/vscode-expo/issues/231\n * @see https://github.com/microsoft/vscode-js-debug/blob/dcccaf3972d675cc1e5c776450bb4c3dc8c178c1/src/adapter/stackTrace.ts#L319-L324\n */\nexport class VscodeRuntimeCallFunctionOnHandler extends MessageHandler {\n  isEnabled() {\n    return getDebuggerType(this.debugger.userAgent) === 'vscode';\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<RuntimeCallFunctionOn>) {\n    if (message.method === 'Runtime.callFunctionOn') {\n      return this.sendToDebugger<DeviceResponse<RuntimeCallFunctionOn>>({\n        id: message.id,\n        result: {\n          // We don't know the `type` and vscode allows `type: undefined`\n          result: { objectId: message.params.objectId } as any,\n        },\n      });\n    }\n\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/v8/Runtime/#method-callFunctionOn */\nexport type RuntimeCallFunctionOn = CdpMessage<\n  'Runtime.callFunctionOn',\n  Protocol.Runtime.CallFunctionOnRequest,\n  Protocol.Runtime.CallFunctionOnResponse\n>;\n"], "names": ["VscodeRuntimeCallFunctionOnHandler", "MessageHandler", "isEnabled", "getDebuggerType", "debugger", "userAgent", "handleDebuggerMessage", "message", "method", "sendToDebugger", "id", "result", "objectId", "params"], "mappings": ";;;;+BAaaA;;;eAAAA;;;gCAXkB;iCACC;AAUzB,MAAMA,2CAA2CC,8BAAc;IACpEC,YAAY;QACV,OAAOC,IAAAA,gCAAe,EAAC,IAAI,CAACC,QAAQ,CAACC,SAAS,MAAM;IACtD;IAEAC,sBAAsBC,OAA+C,EAAE;QACrE,IAAIA,QAAQC,MAAM,KAAK,0BAA0B;YAC/C,OAAO,IAAI,CAACC,cAAc,CAAwC;gBAChEC,IAAIH,QAAQG,EAAE;gBACdC,QAAQ;oBACN,+DAA+D;oBAC/DA,QAAQ;wBAAEC,UAAUL,QAAQM,MAAM,CAACD,QAAQ;oBAAC;gBAC9C;YACF;QACF;QAEA,OAAO;IACT;AACF"}