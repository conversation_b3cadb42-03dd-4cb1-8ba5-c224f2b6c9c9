{"version": 3, "file": "index.js", "names": ["_getPrebuildConfig", "data", "require", "_withDefaultPlugins", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get"], "sources": ["../src/index.ts"], "sourcesContent": ["export { getPrebuildConfigAsync } from './getPrebuildConfig';\nexport * from './plugins/withDefaultPlugins';\n"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAAA,mBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,kBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,IAAAE,mBAAA,GAAAD,OAAA;AAAAE,MAAA,CAAAC,IAAA,CAAAF,mBAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,mBAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,mBAAA,CAAAI,GAAA;IAAA;EAAA;AAAA", "ignoreList": []}