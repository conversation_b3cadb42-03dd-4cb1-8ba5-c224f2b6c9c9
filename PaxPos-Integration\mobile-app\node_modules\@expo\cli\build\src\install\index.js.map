{"version": 3, "sources": ["../../../src/install/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../utils/args';\n\nexport const expoInstall: Command = async (argv) => {\n  const args = assertWithOptionsArgs(\n    {\n      // Other options are parsed manually.\n      '--help': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n    },\n    {\n      argv,\n      // Allow other options, we'll throw an error if unexpected values are passed.\n      permissive: true,\n    }\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Install a module or other package to a project`,\n      `npx expo install`,\n      [\n        `--check     Check which installed packages need to be updated`,\n        '--dev       Save the dependencies as devDependencies',\n        `--fix       Automatically update any invalid package versions`,\n        chalk`--npm       Use npm to install dependencies. {dim Default when package-lock.json exists}`,\n        chalk`--yarn      Use Yarn to install dependencies. {dim Default when yarn.lock exists}`,\n        chalk`--bun       Use bun to install dependencies. {dim Default when bun.lock or bun.lockb exists}`,\n        chalk`--pnpm      Use pnpm to install dependencies. {dim Default when pnpm-lock.yaml exists}`,\n        `-h, --help  Usage info`,\n      ].join('\\n'),\n      [\n        '',\n        chalk`  Additional options can be passed to the underlying install command by using {bold --}`,\n        chalk`    {dim $} npx expo install react -- --verbose`,\n        chalk`    {dim >} yarn add react --verbose`,\n        '',\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo install -h` shows as fast as possible.\n  const { installAsync } = require('./installAsync') as typeof import('./installAsync');\n  const { logCmdError } = require('../utils/errors') as typeof import('../utils/errors');\n  const { resolveArgsAsync } = require('./resolveOptions') as typeof import('./resolveOptions');\n\n  const { variadic, options, extras } = await resolveArgsAsync(process.argv.slice(3)).catch(\n    logCmdError\n  );\n  return installAsync(variadic, options, extras).catch(logCmdError);\n};\n"], "names": ["expoInstall", "argv", "args", "assertWithOptionsArgs", "Boolean", "permissive", "printHelp", "chalk", "join", "installAsync", "require", "logCmdError", "resolveArgsAsync", "variadic", "options", "extras", "process", "slice", "catch"], "mappings": ";;;;;+BAMaA;;;eAAAA;;;;gEALK;;;;;;sBAG+B;;;;;;AAE1C,MAAMA,cAAuB,OAAOC;IACzC,MAAMC,OAAOC,IAAAA,2BAAqB,EAChC;QACE,qCAAqC;QACrC,UAAUC;QACV,UAAU;QACV,MAAM;IACR,GACA;QACEH;QACA,6EAA6E;QAC7EI,YAAY;IACd;IAGF,IAAIH,IAAI,CAAC,SAAS,EAAE;QAClBI,IAAAA,eAAS,EACP,CAAC,8CAA8C,CAAC,EAChD,CAAC,gBAAgB,CAAC,EAClB;YACE,CAAC,6DAA6D,CAAC;YAC/D;YACA,CAAC,6DAA6D,CAAC;YAC/DC,IAAAA,gBAAK,CAAA,CAAC,wFAAwF,CAAC;YAC/FA,IAAAA,gBAAK,CAAA,CAAC,iFAAiF,CAAC;YACxFA,IAAAA,gBAAK,CAAA,CAAC,4FAA4F,CAAC;YACnGA,IAAAA,gBAAK,CAAA,CAAC,sFAAsF,CAAC;YAC7F,CAAC,sBAAsB,CAAC;SACzB,CAACC,IAAI,CAAC,OACP;YACE;YACAD,IAAAA,gBAAK,CAAA,CAAC,uFAAuF,CAAC;YAC9FA,IAAAA,gBAAK,CAAA,CAAC,+CAA+C,CAAC;YACtDA,IAAAA,gBAAK,CAAA,CAAC,oCAAoC,CAAC;YAC3C;SACD,CAACC,IAAI,CAAC;IAEX;IAEA,yFAAyF;IACzF,MAAM,EAAEC,YAAY,EAAE,GAAGC,QAAQ;IACjC,MAAM,EAAEC,WAAW,EAAE,GAAGD,QAAQ;IAChC,MAAM,EAAEE,gBAAgB,EAAE,GAAGF,QAAQ;IAErC,MAAM,EAAEG,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAG,MAAMH,iBAAiBI,QAAQf,IAAI,CAACgB,KAAK,CAAC,IAAIC,KAAK,CACvFP;IAEF,OAAOF,aAAaI,UAAUC,SAASC,QAAQG,KAAK,CAACP;AACvD"}