{"@expo/fingerprint": "~0.13.4", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@expo/ui": "~0.1.1-alpha.10", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-masked-view/masked-view": "0.3.2", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "4.5.6", "@react-native-community/viewpager": "5.0.11", "@react-native-picker/picker": "2.11.1", "@react-native-segmented-control/segmented-control": "2.5.7", "@stripe/stripe-react-native": "0.45.0", "eslint-config-expo": "~9.2.0", "expo-analytics-amplitude": "~11.3.0", "expo-app-auth": "~11.1.0", "expo-app-loader-provider": "~8.0.0", "expo-apple-authentication": "~7.2.4", "expo-application": "~6.1.5", "expo-asset": "~11.1.7", "expo-audio": "~0.4.8", "expo-auth-session": "~6.2.1", "expo-av": "~15.1.7", "expo-background-fetch": "~13.1.6", "expo-background-task": "~0.2.8", "expo-battery": "~9.1.4", "expo-blur": "~14.1.5", "expo-brightness": "~13.1.4", "expo-build-properties": "~0.14.8", "expo-calendar": "~14.1.4", "expo-camera": "~16.1.11", "expo-cellular": "~7.1.5", "expo-checkbox": "~4.1.4", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-contacts": "~14.2.5", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-gl": "~15.1.7", "expo-google-app-auth": "~8.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-loader": "~5.1.0", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-intent-launcher": "~12.1.5", "expo-insights": "~0.9.3", "expo-keep-awake": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-local-authentication": "~16.0.5", "expo-localization": "~16.1.6", "expo-location": "~18.1.6", "expo-mail-composer": "~14.1.5", "expo-manifests": "~0.16.6", "expo-maps": "~0.11.0", "expo-media-library": "~17.1.7", "expo-mesh-gradient": "~0.3.4", "expo-module-template": "~10.16.7", "expo-modules-core": "~2.5.0", "expo-navigation-bar": "~4.2.7", "expo-network": "~7.1.5", "expo-notifications": "~0.31.4", "expo-print": "~14.1.4", "expo-live-photo": "~0.1.4", "expo-router": "~5.1.3", "expo-screen-capture": "~7.1.5", "expo-screen-orientation": "~8.1.7", "expo-secure-store": "~14.2.3", "expo-sensors": "~14.1.4", "expo-sharing": "~13.1.5", "expo-sms": "~13.1.4", "expo-speech": "~13.1.7", "expo-splash-screen": "~0.30.10", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-task-manager": "~13.1.6", "expo-tracking-transparency": "~5.2.4", "expo-updates": "~0.28.17", "expo-video-thumbnails": "~9.1.3", "expo-video": "~2.2.2", "expo-web-browser": "~14.2.0", "jest-expo": "~53.0.9", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-web": "~0.20.0", "react-native-edge-to-edge": "1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "~1.11.0", "react-native-maps": "1.20.1", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-screens": "~4.11.1", "react-native-safe-area-context": "5.4.0", "react-native-svg": "15.11.2", "react-native-view-shot": "4.0.3", "react-native-webview": "13.13.5", "sentry-expo": "~7.0.0", "unimodules-app-loader": "~5.1.3", "unimodules-image-loader-interface": "~6.1.0", "@shopify/react-native-skia": "v2.0.0-next.4", "@shopify/flash-list": "1.7.6", "@sentry/react-native": "~6.14.0", "react-native-bootsplash": "^6.3.7"}