import*as e from"../../legacy.js";import*as t from"../../../../third_party/codemirror.next/codemirror.next.js";import*as i from"../../../../core/platform/platform.js";import*as n from"../../../visual_logging/visual_logging.js";import*as s from"../../../../core/common/common.js";import*as o from"../../../lit-html/lit-html.js";import*as r from"../../../../core/host/host.js";import*as a from"../../../../core/i18n/i18n.js";import*as l from"../color_picker/color_picker.js";import*as d from"../../../../core/sdk/sdk.js";import*as h from"../../../components/icon_button/icon_button.js";import*as c from"../../../../panels/css_overview/css_overview.js";import*as u from"../../theme_support/theme_support.js";const p=t.css.cssLanguage.parser,m=new Intl.NumberFormat("en",{maximumFractionDigits:2});function g(e,t){for(let i=t;i<e.length;i++)if(!isNaN(e[i].input))return i;return-1}function v(e,t){const i=[];for(;","!==e.type.name&&")"!==e.type.name;){const n=t.substring(e.from,e.to);if("NumberLiteral"!==e.type.name)return null;i.push(n),e.next(!1)}if(i.length>3)return null;const n=i.filter((e=>e.includes("%")));if(n.length>2)return null;const s=i.filter((e=>!e.includes("%")));return 1!==s.length?null:{number:Number(s[0]),lengthA:n[0]?Number(n[0].substring(0,n[0].length-1)):void 0,lengthB:n[1]?Number(n[1].substring(0,n[1].length-1)):void 0}}const b={linear:"linear(0 0%, 1 100%)"};class x{#e;constructor(e){this.#e=e}static parse(e){if(b[e])return x.parse(b[e]);const t=function(e){const t=`*{--a: ${e}}`,i=p.parse(t).cursorAt(t.indexOf(":")+1);for(;"ArgList"!==i.name&&i.next(!0);)if("Callee"===i.name&&"linear"!==t.substring(i.from,i.to))return null;if("ArgList"!==i.name)return null;i.firstChild();const n=[];for(;")"!==i.type.name&&i.next(!1);){const e=v(i,t);if(!e)return null;n.push(e)}return n}(e);if(!t||t.length<2)return null;let i=-1/0;const n=[];for(let e=0;e<t.length;e++){const s=t[e],o={input:NaN,output:s.number};if(n.push(o),void 0!==s.lengthA){if(o.input=Math.max(s.lengthA,i),i=o.input,void 0!==s.lengthB){const e={input:NaN,output:o.output};n.push(e),e.input=Math.max(s.lengthB,i),i=e.input}}else 0===e?(o.input=0,i=0):e===t.length-1&&(o.input=Math.max(100,i))}let s=0;for(let e=1;e<n.length;e++)isNaN(n[e].input)&&(e>s&&(s=g(n,e)),n[e].input=n[e-1].input+(n[s].input-n[e-1].input)/(s-(e-1)));return new x(n)}addPoint(e,t){void 0===t?this.#e.push(e):this.#e.splice(t,0,e)}removePoint(e){this.#e.splice(e,1)}setPoint(e,t){this.#e[e]=t}points(){return this.#e}asCSSText(){const e=`linear(${this.#e.map((e=>`${m.format(e.output)} ${m.format(e.input)}%`)).join(", ")})`;for(const[t,i]of Object.entries(b))if(i===e)return t;return e}}var f=Object.freeze({__proto__:null,CSSLinearEasingModel:x});class w{static parse(t){const i=x.parse(t);return i||(e.Geometry.CubicBezier.parse(t)||null)}}const y=e.Geometry.LINEAR_BEZIER;var S=Object.freeze({__proto__:null,AnimationTimingModel:w,LINEAR_BEZIER:y});class C{width;height;marginTop;radius;shouldDrawLine;constructor({width:e,height:t,marginTop:i,controlPointRadius:n,shouldDrawLine:s}){this.width=e,this.height=t,this.marginTop=i,this.radius=n,this.shouldDrawLine=s}static drawVelocityChart(e,t,i){const n=k;let s=["M",0,n];const o=1/40;let r=e.evaluateAt(0);for(let t=o;t<1.025;t+=o){const o=e.evaluateAt(t);let a=(o.y-r.y)/(o.x-r.x);const l=r.x*(1-t)+o.x*t;a=Math.tanh(a/1.5),s=s.concat(["L",(l*i).toFixed(2),(n-a*n).toFixed(2)]),r=o}s=s.concat(["L",i.toFixed(2),n,"Z"]),t.setAttribute("d",s.join(" "))}curveWidth(){return this.width-2*this.radius}curveHeight(){return this.height-2*this.radius-2*this.marginTop}drawLine(t,i,n,s,o,r){const a=e.UIUtils.createSVGChild(t,"line",i);a.setAttribute("x1",String(n+this.radius)),a.setAttribute("y1",String(s+this.radius+this.marginTop)),a.setAttribute("x2",String(o+this.radius)),a.setAttribute("y2",String(r+this.radius+this.marginTop))}drawControlPoints(t,i,s,o,r){this.drawLine(t,"bezier-control-line",i,s,o,r);const a=e.UIUtils.createSVGChild(t,"circle","bezier-control-circle");a.setAttribute("jslog",`${n.controlPoint("bezier.control-circle").track({drag:!0})}`),a.setAttribute("cx",String(o+this.radius)),a.setAttribute("cy",String(r+this.radius+this.marginTop)),a.setAttribute("r",String(this.radius))}drawCurve(t,i){if(!t)return;const n=this.curveWidth(),s=this.curveHeight();i.setAttribute("width",String(this.width)),i.setAttribute("height",String(this.height)),i.removeChildren();const o=e.UIUtils.createSVGChild(i,"g");this.shouldDrawLine&&this.drawLine(o,"linear-line",0,s,n,0);const r=e.UIUtils.createSVGChild(o,"path","bezier-path"),a=[new e.Geometry.Point(t.controlPoints[0].x*n+this.radius,(1-t.controlPoints[0].y)*s+this.radius+this.marginTop),new e.Geometry.Point(t.controlPoints[1].x*n+this.radius,(1-t.controlPoints[1].y)*s+this.radius+this.marginTop),new e.Geometry.Point(n+this.radius,this.marginTop+this.radius)];r.setAttribute("d","M"+this.radius+","+(s+this.radius+this.marginTop)+" C"+a.join(" ")),this.drawControlPoints(o,0,s,t.controlPoints[0].x*n,(1-t.controlPoints[0].y)*s),this.drawControlPoints(o,n,0,t.controlPoints[1].x*n,(1-t.controlPoints[1].y)*s)}}const k=26;var I=Object.freeze({__proto__:null,BezierUI:C,Height:k});class E{#t;#i;#n;#s;#o;#r;#a;constructor({bezier:t,container:i,onBezierChange:n}){this.#i=t,this.#t=new C({width:150,height:250,marginTop:50,controlPointRadius:7,shouldDrawLine:!0}),this.#n=e.UIUtils.createSVGChild(i,"svg","bezier-curve"),this.#a=n,e.UIUtils.installDragHandle(this.#n,this.dragStart.bind(this),this.dragMove.bind(this),this.dragEnd.bind(this),"default")}dragStart(t){this.#s=new e.Geometry.Point(t.x,t.y);const n=this.#t;this.#o=new e.Geometry.Point(i.NumberUtilities.clamp((t.offsetX-n.radius)/n.curveWidth(),0,1),(n.curveHeight()+n.marginTop+n.radius-t.offsetY)/n.curveHeight());const s=this.#o.distanceTo(this.#i.controlPoints[0])<this.#o.distanceTo(this.#i.controlPoints[1]);return this.#r=s?0:1,this.#i.controlPoints[this.#r]=this.#o,this.#a(this.#i),t.consume(!0),!0}updateControlPosition(t,n){if(void 0===this.#s||void 0===this.#o||void 0===this.#r)return;const s=(t-this.#s.x)/this.#t.curveWidth(),o=(n-this.#s.y)/this.#t.curveHeight(),r=new e.Geometry.Point(i.NumberUtilities.clamp(this.#o.x+s,0,1),this.#o.y-o);this.#i.controlPoints[this.#r]=r}dragMove(e){this.updateControlPosition(e.x,e.y),this.#a(this.#i)}dragEnd(e){this.updateControlPosition(e.x,e.y),this.#a(this.#i)}setBezier(e){this.#i=e,this.draw()}draw(){this.#t.drawCurve(this.#i,this.#n)}}class T{params;renderedPositions;constructor(e){this.params=e}#l(){return this.params.width-2*this.params.pointRadius}#d(){return this.params.height-2*this.params.pointRadius-2*this.params.marginTop}#h(t,i,s,o){const r=e.UIUtils.createSVGChild(t,"circle","bezier-control-circle");r.setAttribute("jslog",`${n.controlPoint("bezier.linear-control-circle").track({drag:!0,dblclick:!0})}`),r.setAttribute("data-point-index",String(o)),r.setAttribute("cx",String(i)),r.setAttribute("cy",String(s)),r.setAttribute("r",String(this.params.pointRadius))}timingPointToPosition(e){return{x:e.input/100*this.#l()+this.params.pointRadius,y:(1-e.output)*this.#d()+this.params.pointRadius}}positionToTimingPoint(e){return{input:(e.x-this.params.pointRadius)/this.#l()*100,output:1-(e.y-this.params.pointRadius)/this.#d()}}draw(t,i){i.setAttribute("width",String(this.#l())),i.setAttribute("height",String(this.#d())),i.removeChildren();const n=e.UIUtils.createSVGChild(i,"g"),s=t.points().map((e=>this.timingPointToPosition(e)));this.renderedPositions=s;let o=s[0];for(let t=1;t<s.length;t++){const i=s[t],r=e.UIUtils.createSVGChild(n,"path","bezier-path linear-path");r.setAttribute("d",`M ${o.x} ${o.y} L ${i.x} ${i.y}`),r.setAttribute("data-line-index",String(t)),o=i}for(let e=0;e<s.length;e++){const t=s[e];this.#h(n,t.x,t.y,e)}}}class P{#c;#u;#p;#m;#g;#v;#s;#b;constructor({model:t,container:i,onChange:n}){this.#c=t,this.#u=n,this.#p=new T({width:150,height:250,pointRadius:7,marginTop:50}),this.#b=e.UIUtils.createSVGChild(i,"svg","bezier-curve linear"),e.UIUtils.installDragHandle(this.#b,this.#x.bind(this),this.#f.bind(this),this.#w.bind(this),"default")}#y(e,t){const i=this.#p.positionToTimingPoint({x:e.offsetX,y:e.offsetY});this.#c.addPoint(i,t),this.#m=void 0,this.#s=void 0}#S(e,t){if(this.#m=t,this.#s={x:e.x,y:e.y},clearTimeout(this.#g),this.#v===this.#m)return this.#c.removePoint(this.#m),this.#v=void 0,this.#m=void 0,void(this.#s=void 0);this.#v=this.#m,this.#g=window.setTimeout((()=>{this.#v=void 0}),500)}#x(e){return e.target instanceof SVGElement&&(void 0!==e.target.dataset.lineIndex?(this.#y(e,Number(e.target.dataset.lineIndex)),e.consume(!0),!0):void 0!==e.target.dataset.pointIndex&&(this.#S(e,Number(e.target.dataset.pointIndex)),e.consume(!0),!0))}#C(e,t){if(void 0===this.#m||void 0===this.#s)return;const i=this.#p.renderedPositions?.[this.#m];if(!i)return;const n=e-this.#s.x,s=t-this.#s.y;this.#s={x:e,y:t};const o={x:i.x+n,y:i.y+s};this.#c.setPoint(this.#m,this.#p.positionToTimingPoint(o))}#f(e){this.#C(e.x,e.y),this.#u(this.#c)}#w(e){this.#C(e.x,e.y),this.#u(this.#c)}setCSSLinearEasingModel(e){this.#c=e,this.draw()}draw(){this.#p.draw(this.#c,this.#b)}}class z{#k;#I;constructor(){this.#k=new T({width:40,height:40,marginTop:0,pointRadius:2}),this.#I=new C({width:40,height:40,marginTop:0,controlPointRadius:2,shouldDrawLine:!1})}draw(t,i){t instanceof x?this.#k.draw(t,i):t instanceof e.Geometry.CubicBezier&&this.#I.drawCurve(t,i)}}class M{#E;#T;#P;#c;#u;#z;#M;constructor({model:t,onChange:i}){this.#E=document.createElement("div"),this.#E.className="animation-timing-ui",this.#E.style.width="150px",this.#E.style.height="250px",this.#T=document.createElement("div"),this.#T.classList.add("bezier-ui-container"),this.#P=document.createElement("div"),this.#P.classList.add("linear-easing-ui-container"),this.#E.appendChild(this.#T),this.#E.appendChild(this.#P),this.#c=t,this.#u=i,this.#c instanceof e.Geometry.CubicBezier?this.#z=new E({bezier:this.#c,container:this.#T,onBezierChange:this.#u}):this.#c instanceof x&&(this.#M=new P({model:this.#c,container:this.#P,onChange:this.#u}))}element(){return this.#E}setModel(t){this.#c=t,this.#c instanceof e.Geometry.CubicBezier?this.#z?this.#z.setBezier(this.#c):this.#z=new E({bezier:this.#c,container:this.#T,onBezierChange:this.#u}):this.#c instanceof x&&(this.#M?this.#M.setCSSLinearEasingModel(this.#c):this.#M=new P({model:this.#c,container:this.#P,onChange:this.#u})),this.draw()}draw(){this.#P.classList.toggle("hidden",!(this.#c instanceof x)),this.#T.classList.toggle("hidden",!(this.#c instanceof e.Geometry.CubicBezier)),this.#z&&this.#z.draw(),this.#M&&this.#M.draw()}}var L=Object.freeze({__proto__:null,PresetUI:z,AnimationTimingUI:M});const A=new CSSStyleSheet;A.replaceSync(":host{width:270px;height:350px;user-select:none;padding:16px;overflow:hidden}.bezier-preset-selected > svg{background-color:var(--sys-color-tonal-container)}.bezier-container{--override-bezier-control-color:var(--sys-color-purple-bright);display:flex;margin-top:38px;flex-shrink:0;z-index:2;background-image:radial-gradient(circle,var(--sys-color-surface-variant) 1px,var(--color-background-inverted-opacity-0) 1px);background-size:17px 17px;background-position:-5px -10px}.bezier-preset{width:50px;height:50px;padding:5px;margin:auto;background-color:var(--sys-color-surface1);border-radius:3px}.bezier-preset line.bezier-control-line{stroke:var(--sys-color-token-subtle);stroke-width:1;stroke-linecap:round;fill:none}.bezier-preset circle.bezier-control-circle{fill:var(--sys-color-token-subtle)}.bezier-preset path.bezier-path{stroke:var(--sys-color-inverse-surface);stroke-width:2;stroke-linecap:round;fill:none}.bezier-preset-selected path.bezier-path,\n.bezier-preset-selected line.bezier-control-line{stroke:var(--sys-color-on-tonal-container)}.bezier-preset-selected circle.bezier-control-circle{fill:var(--sys-color-on-tonal-container)}.bezier-curve line.linear-line{stroke:var(--sys-color-neutral-outline);stroke-width:2;stroke-linecap:round;fill:none}.bezier-curve line.bezier-control-line{stroke:var(--override-bezier-control-color);stroke-width:2;stroke-linecap:round;fill:none;opacity:60%}.bezier-curve circle.bezier-control-circle{fill:var(--override-bezier-control-color);cursor:pointer}.bezier-curve path.bezier-path{stroke:var(--sys-color-inverse-surface);stroke-width:3;stroke-linecap:round;fill:none}.bezier-curve path.bezier-path.linear-path{cursor:pointer}.bezier-preview-container{position:relative;background-color:var(--sys-color-cdt-base-container);overflow:hidden;border-radius:20px;width:200%;height:20px;z-index:2;flex-shrink:0;opacity:0%}.bezier-preview-animation{background-color:var(--sys-color-purple-bright);width:20px;height:20px;border-radius:20px;position:absolute}.bezier-preview-onion{margin-top:-20px;position:relative;z-index:1}.bezier-preview-onion > .bezier-preview-animation{opacity:10%}svg.bezier-preset-modify{background-color:var(--sys-color-cdt-base-container);border-radius:35px;display:inline-block;visibility:hidden;transition:transform 100ms cubic-bezier(0.4,0,0.2,1);cursor:pointer;position:absolute}svg.bezier-preset-modify:hover,\n.bezier-preset:hover{background-color:var(--sys-color-state-hover-on-subtle)}.bezier-preset-selected .bezier-preset:hover{background-color:var(--sys-color-tonal-container)}.bezier-preset-modify path{stroke-width:2;stroke:var(--sys-color-on-surface-subtle);fill:none}.bezier-presets{display:flex;flex-direction:column;justify-content:space-between}.bezier-preset-selected .bezier-preset-modify{opacity:100%}.bezier-preset-category{width:50px;cursor:pointer;transition:transform 100ms cubic-bezier(0.4,0,0.2,1)}span.bezier-display-value{width:100%;user-select:text;display:block;text-align:center;line-height:20px;min-height:20px;cursor:text}svg.bezier-curve{margin-left:32px;margin-top:-8px}svg.bezier-curve.linear{margin-top:42px;overflow:visible}svg.bezier-preset-modify.bezier-preset-plus{right:0}.bezier-header{margin-top:16px;z-index:1}svg.bezier-preset-modify:active{transform:scale(1.1);background-color:var(--sys-color-state-ripple-neutral-on-subtle)}.bezier-preset-category:active{transform:scale(1.05)}.bezier-header-active > svg.bezier-preset-modify{visibility:visible}.bezier-preset-modify:active path{stroke:var(--sys-color-on-surface-subtle)}\n/*# sourceURL=bezierEditor.css */\n");class U extends(s.ObjectWrapper.eventMixin(e.Widget.VBox)){model;previewElement;previewOnion;outerContainer;selectedCategory;presetsContainer;presetUI;presetCategories;animationTimingUI;header;label;previewAnimation;debouncedStartPreviewAnimation;constructor(e){super(!0),this.model=e,this.contentElement.tabIndex=0,this.contentElement.setAttribute("jslog",`${n.dialog("bezierEditor").parent("mapped").track({keydown:"Enter|Escape"})}`),this.setDefaultFocusedElement(this.contentElement),this.element.style.overflowY="auto",this.previewElement=this.contentElement.createChild("div","bezier-preview-container"),this.previewElement.setAttribute("jslog",`${n.preview().track({click:!0})}`),this.previewElement.createChild("div","bezier-preview-animation"),this.previewElement.addEventListener("click",this.startPreviewAnimation.bind(this)),this.previewOnion=this.contentElement.createChild("div","bezier-preview-onion"),this.previewOnion.setAttribute("jslog",`${n.preview().track({click:!0})}`),this.previewOnion.addEventListener("click",this.startPreviewAnimation.bind(this)),this.outerContainer=this.contentElement.createChild("div","bezier-container"),this.selectedCategory=null,this.presetsContainer=this.outerContainer.createChild("div","bezier-presets"),this.presetUI=new z,this.presetCategories=[];for(let e=0;e<F.length;e++){const t=this.createCategory(F[e]);t&&(this.presetCategories[e]=t,this.presetsContainer.appendChild(this.presetCategories[e].icon))}this.debouncedStartPreviewAnimation=s.Debouncer.debounce(this.startPreviewAnimation.bind(this),300),this.animationTimingUI=new M({model:this.model,onChange:e=>{this.setModel(e),this.onchange(),this.unselectPresets(),this.debouncedStartPreviewAnimation()}}),this.animationTimingUI.element().setAttribute("jslog",`${n.bezierCurveEditor().track({click:!0,drag:!0})}`),this.outerContainer.appendChild(this.animationTimingUI.element()),this.header=this.contentElement.createChild("div","bezier-header");const t=this.createPresetModifyIcon(this.header,"bezier-preset-minus","M 12 6 L 8 10 L 12 14");t.addEventListener("click",this.presetModifyClicked.bind(this,!1)),t.setAttribute("jslog",`${n.action("bezier.prev-preset").track({click:!0})}`);const i=this.createPresetModifyIcon(this.header,"bezier-preset-plus","M 8 6 L 12 10 L 8 14");i.addEventListener("click",this.presetModifyClicked.bind(this,!0)),i.setAttribute("jslog",`${n.action("bezier.next-preset").track({click:!0})}`),this.label=this.header.createChild("span","source-code bezier-display-value")}setModel(e){this.model=e,this.animationTimingUI?.setModel(this.model),this.updateUI()}wasShown(){this.registerCSSFiles([A]),this.unselectPresets();for(const e of this.presetCategories)for(let t=0;t<e.presets.length;t++)this.model.asCSSText()===e.presets[t].value&&(e.presetIndex=t,this.presetCategorySelected(e));this.updateUI(),this.startPreviewAnimation()}onchange(){this.updateUI(),this.dispatchEventToListeners("BezierChanged",this.model.asCSSText())}updateUI(){const e=this.selectedCategory?this.selectedCategory.presets[this.selectedCategory.presetIndex].name:this.model.asCSSText().replace(/\s(-\d\.\d)/g,"$1");this.label.textContent=e,this.animationTimingUI?.draw()}createCategory(t){const i=w.parse(t[0].value);if(!i)return null;const s=document.createElement("div");s.classList.add("bezier-preset-category"),s.setAttribute("jslog",`${n.bezierPresetCategory().track({click:!0}).context(t[0].name)}`);const o=e.UIUtils.createSVGChild(s,"svg","bezier-preset monospace"),r={presets:t,presetIndex:0,icon:s};return this.presetUI.draw(i,o),o.addEventListener("click",this.presetCategorySelected.bind(this,r)),r}createPresetModifyIcon(t,i,n){const s=e.UIUtils.createSVGChild(t,"svg","bezier-preset-modify "+i);s.setAttribute("width","20"),s.setAttribute("height","20");return e.UIUtils.createSVGChild(s,"path").setAttribute("d",n),s}unselectPresets(){for(const e of this.presetCategories)e.icon.classList.remove("bezier-preset-selected");this.selectedCategory=null,this.header.classList.remove("bezier-header-active")}presetCategorySelected(e,t){if(this.selectedCategory===e)return;this.unselectPresets(),this.header.classList.add("bezier-header-active"),this.selectedCategory=e,this.selectedCategory.icon.classList.add("bezier-preset-selected");const i=w.parse(e.presets[e.presetIndex].value);i&&(this.setModel(i),this.onchange(),this.startPreviewAnimation()),t&&t.consume(!0)}presetModifyClicked(e,t){if(null===this.selectedCategory)return;const i=this.selectedCategory.presets.length;this.selectedCategory.presetIndex=(this.selectedCategory.presetIndex+(e?1:-1)+i)%i;const n=this.selectedCategory.presets[this.selectedCategory.presetIndex].value,s=w.parse(n);s&&(this.setModel(s),this.onchange(),this.startPreviewAnimation())}startPreviewAnimation(){this.previewOnion.removeChildren(),this.previewAnimation&&this.previewAnimation.cancel();const e=1600;this.previewAnimation=this.previewElement.animate([{offset:0,transform:"translateX(0px)",opacity:1},{offset:1,transform:"translateX(218px)",opacity:1}],{easing:this.model.asCSSText(),duration:e}),this.previewOnion.removeChildren();for(let t=0;t<=20;t++){const i=this.previewOnion.createChild("div","bezier-preview-animation").animate([{transform:"translateX(0px)",easing:this.model.asCSSText()},{transform:"translateX(218px)"}],{duration:e,fill:"forwards"});i.pause(),i.currentTime=e*t/20}}}const F=[[{name:"linear",value:"linear"},{name:"elastic",value:"linear(0 0%, 0.22 2.1%, 0.86 6.5%, 1.11 8.6%, 1.3 10.7%, 1.35 11.8%, 1.37 12.9%, 1.37 13.7%, 1.36 14.5%, 1.32 16.2%, 1.03 21.8%, 0.94 24%, 0.89 25.9%, 0.88 26.85%, 0.87 27.8%, 0.87 29.25%, 0.88 30.7%, 0.91 32.4%, 0.98 36.4%, 1.01 38.3%, 1.04 40.5%, 1.05 42.7%, 1.05 44.1%, 1.04 45.7%, 1 53.3%, 0.99 55.4%, 0.98 57.5%, 0.99 60.7%, 1 68.1%, 1.01 72.2%, 1 86.7%, 1 100%)"},{name:"bounce",value:"linear(0 0%, 0 2.27%, 0.02 4.53%, 0.04 6.8%, 0.06 9.07%, 0.1 11.33%, 0.14 13.6%, 0.25 18.15%, 0.39 22.7%, 0.56 27.25%, 0.77 31.8%, 1 36.35%, 0.89 40.9%, 0.85 43.18%, 0.81 45.45%, 0.79 47.72%, 0.77 50%, 0.75 52.27%, 0.75 54.55%, 0.75 56.82%, 0.77 59.1%, 0.79 61.38%, 0.81 63.65%, 0.85 65.93%, 0.89 68.2%, 1 72.7%, 0.97 74.98%, 0.95 77.25%, 0.94 79.53%, 0.94 81.8%, 0.94 84.08%, 0.95 86.35%, 0.97 88.63%, 1 90.9%, 0.99 93.18%, 0.98 95.45%, 0.99 97.73%, 1 100%)"},{name:"emphasized",value:"linear(0 0%, 0 1.8%, 0.01 3.6%, 0.03 6.35%, 0.07 9.1%, 0.13 11.4%, 0.19 13.4%, 0.27 15%, 0.34 16.1%, 0.54 18.35%, 0.66 20.6%, 0.72 22.4%, 0.77 24.6%, 0.81 27.3%, 0.85 30.4%, 0.88 35.1%, 0.92 40.6%, 0.94 47.2%, 0.96 55%, 0.98 64%, 0.99 74.4%, 1 86.4%, 1 100%)"}],[{name:"ease-in-out",value:"ease-in-out"},{name:"In Out · Sine",value:"cubic-bezier(0.45, 0.05, 0.55, 0.95)"},{name:"In Out · Quadratic",value:"cubic-bezier(0.46, 0.03, 0.52, 0.96)"},{name:"In Out · Cubic",value:"cubic-bezier(0.65, 0.05, 0.36, 1)"},{name:"Fast Out, Slow In",value:"cubic-bezier(0.4, 0, 0.2, 1)"},{name:"In Out · Back",value:"cubic-bezier(0.68, -0.55, 0.27, 1.55)"}],[{name:"Fast Out, Linear In",value:"cubic-bezier(0.4, 0, 1, 1)"},{name:"ease-in",value:"ease-in"},{name:"In · Sine",value:"cubic-bezier(0.47, 0, 0.75, 0.72)"},{name:"In · Quadratic",value:"cubic-bezier(0.55, 0.09, 0.68, 0.53)"},{name:"In · Cubic",value:"cubic-bezier(0.55, 0.06, 0.68, 0.19)"},{name:"In · Back",value:"cubic-bezier(0.6, -0.28, 0.74, 0.05)"}],[{name:"ease-out",value:"ease-out"},{name:"Out · Sine",value:"cubic-bezier(0.39, 0.58, 0.57, 1)"},{name:"Out · Quadratic",value:"cubic-bezier(0.25, 0.46, 0.45, 0.94)"},{name:"Out · Cubic",value:"cubic-bezier(0.22, 0.61, 0.36, 1)"},{name:"Linear Out, Slow In",value:"cubic-bezier(0, 0, 0.2, 1)"},{name:"Out · Back",value:"cubic-bezier(0.18, 0.89, 0.32, 1.28)"}]];var B=Object.freeze({__proto__:null,BezierEditor:U,Presets:F});const N=new CSSStyleSheet;N.replaceSync(".swatch-icon{display:inline-grid;inline-size:15px;grid:[stack] 1fr/[stack] 1fr;margin-left:1px;margin-right:2px;color:var(--color)}.swatch{aspect-ratio:1/1;display:inline-block;width:10px;border-radius:1e5px;background:linear-gradient(var(--color),var(--color)),var(--image-file-checker);box-shadow:inset 0 0 0 .5px rgb(128 128 128/60%);grid-area:stack}.swatch-right{justify-self:end}.swatch-mix{box-shadow:none;justify-self:end;mask:radial-gradient(circle at 0% center,rgb(0 0 0) 50%,rgb(0 0 0/0%) calc(50% + 0.5px))}\n/*# sourceURL=colorMixSwatch.css */\n");class $ extends(s.ObjectWrapper.eventMixin(HTMLElement)){static litTagName=o.literal`devtools-color-mix-swatch`;shadow=this.attachShadow({mode:"open"});colorMixText="";firstColorText="";secondColorText="";#L;constructor(){super(),this.shadow.adoptedStyleSheets=[N]}get icon(){return this.shadow.firstElementChild}mixedColor(){const e=this.icon?.computedStyleMap().get("color")?.toString()??null;return e?s.Color.parse(e):null}setFirstColor(e){this.firstColorText&&(this.colorMixText=this.colorMixText.replace(this.firstColorText,e)),this.firstColorText=e,this.dispatchEventToListeners("colorChanged",{text:this.colorMixText}),this.#A()}setSecondColor(e){this.secondColorText&&(this.colorMixText=i.StringUtilities.replaceLast(this.colorMixText,this.secondColorText,e)),this.secondColorText=e,this.dispatchEventToListeners("colorChanged",{text:this.colorMixText}),this.#A()}setColorMixText(e){this.colorMixText=e,this.dispatchEventToListeners("colorChanged",{text:this.colorMixText}),this.#A()}setRegisterPopoverCallback(e){this.#L=e,e(this)}getText(){return this.colorMixText}#A(){this.colorMixText&&this.firstColorText&&this.secondColorText?(o.render(o.html`<div class="swatch-icon" jslog=${n.cssColorMix()} style="--color: ${this.colorMixText}">
        <span class="swatch swatch-left" id="swatch-1" style="--color: ${this.firstColorText}"></span>
        <span class="swatch swatch-right" id="swatch-2" style="--color: ${this.secondColorText}"></span>
        <span class="swatch swatch-mix" id="mix-result" style="--color: ${this.colorMixText}"></span>
      </div><slot>${this.colorMixText}</slot>`,this.shadow,{host:this}),this.#L&&this.#L(this)):o.render(this.colorMixText,this.shadow,{host:this})}}customElements.define("devtools-color-mix-swatch",$);var R=Object.freeze({__proto__:null,ColorMixSwatch:$});const D=new CSSStyleSheet;D.replaceSync(":host{white-space:nowrap}.color-swatch{position:relative;margin-left:1px;margin-right:2px;width:12px;height:12px;transform:scale(0.8);vertical-align:-2px;display:inline-block;user-select:none;background-image:var(--image-file-checker);line-height:10px}.color-swatch-inner{width:100%;height:100%;display:inline-block;border:1px solid var(--sys-color-neutral-outline);box-sizing:border-box;cursor:pointer}.color-swatch.readonly .color-swatch-inner{cursor:unset}.color-swatch:not(.readonly) .color-swatch-inner:hover{border:1px solid var(--sys-color-outline)}@media (forced-colors: active){.color-swatch{forced-color-adjust:none}}\n/*# sourceURL=colorSwatch.css */\n");const O={shiftclickToChangeColorFormat:"Shift-click to change color format"},_=a.i18n.registerUIStrings("ui/legacy/components/inline_editor/ColorSwatch.ts",O),H=a.i18n.getLocalizedString.bind(void 0,_);class j extends Event{static eventName="colorchanged";data;constructor(e){super(j.eventName,{}),this.data={color:e}}}class V extends Event{static eventName="swatchclick";constructor(){super(V.eventName,{})}}class G extends HTMLElement{static litTagName=o.literal`devtools-color-swatch`;shadow=this.attachShadow({mode:"open"});tooltip=H(O.shiftclickToChangeColorFormat);color=null;readonly=!1;constructor(e){super(),this.shadow.adoptedStyleSheets=[D],e&&(this.tooltip=e)}static isColorSwatch(e){return"devtools-color-swatch"===e.localName}getReadonly(){return this.readonly}setReadonly(e){this.readonly!==e&&(this.readonly=e,this.color&&this.renderColor(this.color))}getColor(){return this.color}get anchorBox(){const e=this.shadow.querySelector(".color-swatch");return e?e.boxInWindow():null}getText(){return this.color?.getAuthoredText()??this.color?.asString()}renderColor(e){this.color=e;const t=o.Directives.classMap({"color-swatch":!0,readonly:this.readonly});o.render(o.html`<span class=${t} title=${this.tooltip}><span class="color-swatch-inner"
        style="background-color: ${this.getText()};"
        jslog=${n.showStyleEditor("color").track({click:!0})}
        @click=${this.onClick}
        @mousedown=${this.consume}
        @dblclick=${this.consume}></span></span><slot><span>${this.getText()}</span></slot>`,this.shadow,{host:this})}onClick(e){if(!this.readonly)return e.shiftKey?(e.stopPropagation(),void this.showFormatPicker(e)):void this.dispatchEvent(new V)}consume(e){e.stopPropagation()}setColor(e){this.renderColor(e),this.dispatchEvent(new j(e))}setColorText(e){this.firstElementChild?.remove(),this.renderColor(e);this.appendChild(document.createElement("span")).appendChild(document.createTextNode(e.getAuthoredText()??e.asString())),this.dispatchEvent(new j(e))}showFormatPicker(e){if(!this.color)return;new l.FormatPickerContextMenu.FormatPickerContextMenu(this.color).show(e,(e=>{this.setColorText(e),r.userMetrics.colorConvertedFrom(0)}))}}customElements.define("devtools-color-swatch",G);var X=Object.freeze({__proto__:null,ColorChangedEvent:j,ClickEvent:V,ColorSwatch:G});const W=new CSSStyleSheet;W.replaceSync(".css-angle{display:inline-block;position:relative;outline:none}devtools-css-angle-swatch{display:inline-block;margin-right:2px;user-select:none}devtools-css-angle-editor{--override-dial-color:var(--sys-color-neutral-outline);position:fixed;z-index:2}.preview{display:inline-block}\n/*# sourceURL=cssAngle.css */\n");const K=new CSSStyleSheet;K.replaceSync('.editor.interacting::before{content:"";position:fixed;inset:0}.clock,\n.pointer,\n.center,\n.hand,\n.dial{position:absolute}.clock{top:6px;width:6em;height:6em;background-color:var(--sys-color-cdt-base-container);border:0.5em solid var(--sys-color-neutral-outline);border-radius:9em;box-shadow:var(--drop-shadow),inset 0 0 15px var(--box-shadow-outline-color);transform:translateX(-3em)}.center,\n.hand{box-shadow:0 0 2px var(--box-shadow-outline-color)}.pointer{margin:auto;top:0;left:-0.4em;right:0;width:0;height:0;border-style:solid;border-width:0 0.9em 0.9em;border-color:transparent transparent var(--sys-color-neutral-outline) transparent}.center,\n.hand,\n.dial{margin:auto;top:0;left:0;right:0;bottom:0}.center{width:0.7em;height:0.7em;border-radius:10px}.dial{width:2px;height:var(--clock-dial-length);background-color:var(--override-dial-color);border-radius:1px}.hand{height:50%;width:0.3em;background:var(--sys-color-tonal-container)}.hand::before{content:"";display:inline-block;position:absolute;top:-0.6em;left:-0.35em;width:1em;height:1em;border-radius:50%;cursor:pointer;box-shadow:0 0 5px var(--box-shadow-outline-color)}.hand::before,\n.center{background-color:var(--sys-color-tonal-container)}:host-context(.theme-with-dark-background) .hand::before{box-shadow:0 0 5px hsl(0deg 0% 0%/80%)}:host-context(.theme-with-dark-background) .center,\n:host-context(.theme-with-dark-background) .hand{box-shadow:0 0 2px hsl(0deg 0% 0%/60%)}:host-context(.theme-with-dark-background) .clock{background-color:hsl(225deg 5% 27%)}\n/*# sourceURL=cssAngleEditor.css */\n');const Y=/(?<value>[+-]?\d*\.?\d+)(?<unit>deg|grad|rad|turn)/,q=e=>{const t=e.match(Y);return t&&t.groups?{value:Number(t.groups.value),unit:t.groups.unit}:null},Z=(t,i)=>{let n=t;switch(i){case"grad":n=e.Geometry.radiansToGradians(t);break;case"deg":n=e.Geometry.radiansToDegrees(t);break;case"turn":n=e.Geometry.radiansToTurns(t)}return{value:n,unit:i}},Q=t=>{switch(t.unit){case"deg":return e.Geometry.degreesToRadians(t.value);case"grad":return e.Geometry.gradiansToRadians(t.value);case"turn":return e.Geometry.turnsToRadians(t.value)}return t.value},J=(e,t)=>{const i=Q(e);return{translateX:Math.sin(i)*t,translateY:-Math.cos(i)*t}},ee=e=>{let t=e.value;switch(e.unit){case"deg":case"grad":t=Math.round(e.value);break;case"rad":t=Math.round(1e4*e.value)/1e4;break;case"turn":t=Math.round(100*e.value)/100;break;default:i.assertNever(e.unit,`Unknown angle unit: ${e.unit}`)}return{value:t,unit:e.unit}},te=e=>{switch(e){case"deg":return"grad";case"grad":return"rad";case"rad":return"turn";default:return"deg"}},ie=(e,t)=>{if(e.unit===t)return e;const i=Q(e);return Z(i,t)},ne=(t,i)=>{const n=e.UIUtils.getValueModificationDirection(i);if(null===n)return;let s="Up"===n?Math.PI/180:-Math.PI/180;i.shiftKey&&(s*=10);const o=Q(t);return Z(o+s,t.unit)};var se=Object.freeze({__proto__:null,CSSAngleRegex:Y,parseText:q,getAngleFromRadians:Z,getRadiansFromAngle:Q,get2DTranslationsForAngle:J,roundAngleByUnit:ee,getNextUnit:te,convertAngleUnit:ie,getNewAngleFromEvent:ne});const{render:oe,html:re}=o,ae=o.Directives.styleMap;class le extends HTMLElement{static litTagName=o.literal`devtools-css-angle-editor`;shadow=this.attachShadow({mode:"open"});angle={value:0,unit:"rad"};onAngleUpdate;background="";clockRadius=38.5;dialTemplates;mousemoveThrottler=new s.Throttler.Throttler(16.67);mousemoveListener=this.onMousemove.bind(this);connectedCallback(){this.shadow.adoptedStyleSheets=[K],this.style.setProperty("--clock-dial-length","6px")}set data(e){this.angle=e.angle,this.onAngleUpdate=e.onAngleUpdate,this.background=e.background,this.render()}updateAngleFromMousePosition(e,t,i){const n=this.shadow.querySelector(".clock");if(!n||!this.onAngleUpdate)return;const{top:s,right:o,bottom:r,left:a}=n.getBoundingClientRect();this.clockRadius=(o-a)/2;const l=(a+o)/2,d=(r+s)/2,h=-Math.atan2(e-l,t-d)+Math.PI;if(i){const e=Q({value:15,unit:"deg"}),t=Math.round(h/e)*e;this.onAngleUpdate(Z(t,this.angle.unit))}else this.onAngleUpdate(Z(h,this.angle.unit))}onEditorMousedown(e){e.stopPropagation(),this.updateAngleFromMousePosition(e.pageX,e.pageY,e.shiftKey);const t=e.target instanceof Node&&e.target.ownerDocument,i=this.shadow.querySelector(".editor");t&&i&&(t.addEventListener("mousemove",this.mousemoveListener,{capture:!0}),i.classList.add("interacting"),t.addEventListener("mouseup",(()=>{t.removeEventListener("mousemove",this.mousemoveListener,{capture:!0}),i.classList.remove("interacting")}),{once:!0}))}onMousemove(e){1===e.buttons&&(e.preventDefault(),this.mousemoveThrottler.schedule((()=>(this.updateAngleFromMousePosition(e.pageX,e.pageY,e.shiftKey),Promise.resolve()))))}onEditorWheel(e){if(!this.onAngleUpdate)return;const t=ne(this.angle,e);t&&this.onAngleUpdate(t),e.preventDefault()}render(){const e={background:this.background},{translateX:t,translateY:i}=J(this.angle,this.clockRadius/2),s={transform:`translate(${t}px, ${i}px) rotate(${this.angle.value}${this.angle.unit})`};oe(re`
      <div class="editor" jslog=${n.dialog("cssAngleEditor").track({click:!0,drag:!0,resize:!0,keydown:"Enter|Escape"})}>
        <span class="pointer"></span>
        <div
          class="clock"
          style=${ae(e)}
          @mousedown=${this.onEditorMousedown}
          @wheel=${this.onEditorWheel}>
          ${this.renderDials()}
          <div class="hand" style=${ae(s)}></div>
          <span class="center"></span>
        </div>
      </div>
    `,this.shadow,{host:this})}renderDials(){return this.dialTemplates||(this.dialTemplates=[0,45,90,135,180,225,270,315].map((e=>{const t=this.clockRadius-6-3,{translateX:i,translateY:n}=J({value:e,unit:"deg"},t);return re`<span class="dial" style=${ae({transform:`translate(${i}px, ${n}px) rotate(${e}deg)`})}></span>`}))),this.dialTemplates}}customElements.define("devtools-css-angle-editor",le);const de=new CSSStyleSheet;de.replaceSync(".swatch{position:relative;display:inline-block;margin-bottom:-2px;width:1em;height:1em;border:1px solid var(--sys-color-neutral-outline);border-radius:50%;overflow:hidden;cursor:pointer;background-color:var(--sys-color-neutral-container)}.mini-hand{position:absolute;margin:auto;top:0;left:0;right:0;bottom:0;height:55%;width:2px;background-color:var(--sys-color-tonal-container);border-radius:5px}\n/*# sourceURL=cssAngleSwatch.css */\n");const{render:he,html:ce}=o,ue=o.Directives.styleMap;class pe extends HTMLElement{static litTagName=o.literal`devtools-css-angle-swatch`;shadow=this.attachShadow({mode:"open"});angle={value:0,unit:"rad"};connectedCallback(){this.shadow.adoptedStyleSheets=[de]}set data(e){this.angle=e.angle,this.render()}render(){const{translateX:e,translateY:t}=J(this.angle,2.75),i={transform:`translate(${e}px, ${t}px) rotate(${this.angle.value}${this.angle.unit})`};he(ce`
      <div class="swatch">
        <span class="mini-hand" style=${ue(i)}></span>
      </div>
    `,this.shadow,{host:this})}}customElements.define("devtools-css-angle-swatch",pe);class me extends Event{static eventName="valuechanged";data;constructor(e){super(me.eventName,{}),this.data={value:e}}}var ge=Object.freeze({__proto__:null,ValueChangedEvent:me});const{render:ve,html:be}=o,xe=o.Directives.styleMap;class fe extends Event{static eventName="popovertoggled";data;constructor(e){super(fe.eventName,{}),this.data={open:e}}}class we extends Event{static eventName="unitchanged";data;constructor(e){super(we.eventName,{}),this.data={value:e}}}const ye={value:0,unit:"rad"};class Se extends HTMLElement{static litTagName=o.literal`devtools-css-angle`;shadow=this.attachShadow({mode:"open"});angle=ye;displayedAngle=ye;propertyValue="";containingPane;angleElement=null;swatchElement=null;popoverOpen=!1;popoverStyleTop="";popoverStyleLeft="";onMinifyingAction=this.minify.bind(this);connectedCallback(){this.shadow.adoptedStyleSheets=[W]}set data(e){const t=q(e.angleText);t&&(this.angle=t,this.displayedAngle={...t},this.containingPane=e.containingPane,this.render())}disconnectedCallback(){this.unbindMinifyingAction()}popOver(){if(!this.containingPane)return;if(this.angleElement||(this.angleElement=this.shadow.querySelector(".css-angle")),this.swatchElement||(this.swatchElement=this.shadow.querySelector("devtools-css-angle-swatch")),!this.angleElement||!this.swatchElement)return;this.dispatchEvent(new fe(!0)),this.bindMinifyingAction();const e=this.swatchElement.getBoundingClientRect().bottom,t=this.swatchElement.getBoundingClientRect().left;if(e&&t){const i=this.containingPane.getBoundingClientRect().top,n=this.containingPane.getBoundingClientRect().left;this.popoverStyleTop=e-i+"px",this.popoverStyleLeft=t-n+"px"}this.popoverOpen=!0,this.render(),this.angleElement.focus()}addEventListener(e,t,i){super.addEventListener(e,t,i)}minify(){!1!==this.popoverOpen&&(this.popoverOpen=!1,this.dispatchEvent(new fe(!1)),this.unbindMinifyingAction(),this.render())}updateProperty(e){this.propertyValue=e,this.render()}updateAngle(e){this.displayedAngle=ee(ie(e,this.displayedAngle.unit)),this.angle=this.displayedAngle,this.dispatchEvent(new me(`${this.angle.value}${this.angle.unit}`))}displayNextUnit(){const e=te(this.displayedAngle.unit);this.displayedAngle=ee(ie(this.angle,e)),this.dispatchEvent(new we(`${this.displayedAngle.value}${this.displayedAngle.unit}`))}bindMinifyingAction(){document.addEventListener("mousedown",this.onMinifyingAction),this.containingPane&&this.containingPane.addEventListener("scroll",this.onMinifyingAction)}unbindMinifyingAction(){document.removeEventListener("mousedown",this.onMinifyingAction),this.containingPane&&this.containingPane.removeEventListener("scroll",this.onMinifyingAction)}onMiniIconClick(e){e.stopPropagation(),!e.shiftKey||this.popoverOpen?this.popoverOpen?this.minify():this.popOver():this.displayNextUnit()}consume(e){e.stopPropagation()}onKeydown(e){if(this.popoverOpen)switch(e.key){case"Escape":e.stopPropagation(),this.minify(),this.blur();break;case"ArrowUp":case"ArrowDown":{const t=ne(this.angle,e);t&&this.updateAngle(t),e.preventDefault();break}}}render(){ve(be`
      <div class="css-angle" @focusout=${this.minify} @keydown=${this.onKeydown} tabindex="-1">
        <div class="preview">
          <${pe.litTagName}
            @click=${this.onMiniIconClick}
            @mousedown=${this.consume}
            @dblclick=${this.consume}
            .data=${{angle:this.angle}}>
          </${pe.litTagName}><slot></slot></div>
        ${this.popoverOpen?this.renderPopover():null}
      </div>
    `,this.shadow,{host:this})}renderPopover(){let e="";return this.propertyValue&&!this.propertyValue.match(/url\(.*\)/i)&&(e=this.propertyValue),be`
    <${le.litTagName}
      class="popover popover-css-angle"
      style=${xe({top:this.popoverStyleTop,left:this.popoverStyleLeft})}
      .data=${{angle:this.angle,onAngleUpdate:e=>{this.updateAngle(e)},background:e}}
    ></${le.litTagName}>
    `}}customElements.define("devtools-css-angle",Se);var Ce=Object.freeze({__proto__:null,PopoverToggledEvent:fe,UnitChangedEvent:we,CSSAngle:Se});const ke=new CSSStyleSheet;ke.replaceSync('.css-length{display:inline-block;position:relative;outline:none}.css-length.overloaded{text-decoration:line-through}.value{cursor:ew-resize}.icon{position:absolute;display:inline-block;mask-image:var(--image-file-chevron-down);mask-repeat:no-repeat;background-color:var(--icon-default);transform:scale(0.7);margin-left:-7px;margin-top:-3px;content:"";height:2em;width:2em}:host(:not(:last-child)){margin-right:0.1em}\n/*# sourceURL=cssLength.css */\n');const Ie=["px","cm","mm","Q","in","pc","pt","cap","ch","em","ex","ic","lh","rcap","rch","rem","rex","ric","rlh","vb","vh","vi","vw","vmin","vmax"],Ee=new RegExp(`(?<value>[+-]?\\d*\\.?\\d+)(?<unit>${Ie.join("|")})`),Te=e=>{const t=e.match(Ee);return t&&t.groups?{value:Number(t.groups.value),unit:t.groups.unit}:null};var Pe=Object.freeze({__proto__:null,LENGTH_UNITS:Ie,CSSLengthRegex:Ee,parseText:Te});const{render:ze,html:Me,Directives:{classMap:Le}}=o;class Ae extends Event{static eventName="draggingfinished";constructor(){super(Ae.eventName,{})}}const Ue={value:0,unit:"px"};class Fe extends HTMLElement{static litTagName=o.literal`devtools-css-length`;shadow=this.attachShadow({mode:"open"});onDraggingValue=this.dragValue.bind(this);length=Ue;overloaded=!1;isEditingSlot=!1;isDraggingValue=!1;currentMouseClientX=0;#U=0;set data(e){const t=Te(e.lengthText);t&&(this.length=t,this.overloaded=e.overloaded,this.render())}connectedCallback(){this.shadow.adoptedStyleSheets=[ke]}dragValue(e){if(e.preventDefault(),e.stopPropagation(),Date.now()-this.#U<=300)return;this.isDraggingValue=!0;let t=e.clientX-this.currentMouseClientX;this.currentMouseClientX=e.clientX,e.shiftKey&&(t*=10),e.altKey&&(t*=.1),this.length.value=this.length.value+t,this.dispatchEvent(new me(`${this.length.value}${this.length.unit}`)),r.userMetrics.swatchActivated(8),this.render()}onValueMousedown(e){if(0!==e.button)return;this.#U=Date.now(),this.currentMouseClientX=e.clientX;const t=e.target instanceof Node&&e.target.ownerDocument;t&&(t.addEventListener("mousemove",this.onDraggingValue,{capture:!0}),t.addEventListener("mouseup",(e=>{t.removeEventListener("mousemove",this.onDraggingValue,{capture:!0}),this.isDraggingValue&&(e.preventDefault(),e.stopPropagation(),this.isDraggingValue=!1,this.dispatchEvent(new Ae))}),{once:!0,capture:!0}))}onValueMouseup(){this.isDraggingValue||(this.isEditingSlot=!0,this.render())}render(){const e={"css-length":!0,overloaded:this.overloaded};ze(Me`
      <div class=${Le(e)}>
        ${this.renderContent()}
      </div>
    `,this.shadow,{host:this})}renderContent(){return this.isEditingSlot?Me`<slot></slot>`:Me`
        <span class="value"
          @mousedown=${this.onValueMousedown}
          @mouseup=${this.onValueMouseup}
        >${this.length.value}</span>${this.length.unit}
      `}}customElements.define("devtools-css-length",Fe);var Be=Object.freeze({__proto__:null,DraggingFinishedEvent:Ae,CSSLength:Fe});const Ne=new CSSStyleSheet;Ne.replaceSync(":host{user-select:none;padding:4px 12px 12px;border:1px solid transparent}.shadow-editor-field{height:24px;margin-top:8px;font-size:12px;flex-shrink:0}.shadow-editor-field:last-of-type{margin-bottom:8px}.shadow-editor-flex-field{display:flex;align-items:center;flex-direction:row}.shadow-editor-field.shadow-editor-blur-field{margin-top:40px}.shadow-editor-2D-slider{position:absolute;height:88px;width:88px;border:1px solid var(--divider-line);border-radius:2px}.shadow-editor-label{display:inline-block;width:52px;height:24px;line-height:24px;margin-right:8px;text-align:right}.shadow-editor-button-left,\n.shadow-editor-button-right{width:74px;height:24px;padding:3px 7px;line-height:16px;border:1px solid var(--divider-line);color:var(--sys-color-on-surface);background-color:var(--sys-color-cdt-base-container);text-align:center;font-weight:500}.shadow-editor-button-left{border-radius:2px 0 0 2px}.shadow-editor-button-right{border-radius:0 2px 2px 0;border-left-width:0}.shadow-editor-button-left:hover,\n.shadow-editor-button-right:hover{box-shadow:0 1px 1px var(--color-background-elevation-1)}.shadow-editor-button-left:focus,\n.shadow-editor-button-right:focus{background-color:var(--color-background-elevation-1)}.shadow-editor-button-left.enabled,\n.shadow-editor-button-right.enabled{--override-button-text-color:#fff;background-color:var(--color-primary-old);color:var(--override-button-text-color)}.shadow-editor-button-left.enabled:focus,\n.shadow-editor-button-right.enabled:focus{background-color:var(--color-primary-variant)}.shadow-editor-text-input{width:52px;margin-right:8px;text-align:right;box-shadow:var(--legacy-focus-ring-inactive-shadow)}@media (forced-colors: active){.shadow-editor-button-left:hover,\n  .shadow-editor-button-left.enabled:focus,\n  .shadow-editor-button-right:hover .shadow-editor-button-left.enabled,\n  .shadow-editor-button-right.enabled,\n  .shadow-editor-button-right.enabled:focus{forced-color-adjust:none;background-color:Highlight;color:HighlightText}}\n/*# sourceURL=cssShadowEditor.css */\n");const $e={type:"Type",xOffset:"X offset",yOffset:"Y offset",blur:"Blur",spread:"Spread"},Re=a.i18n.registerUIStrings("ui/legacy/components/inline_editor/CSSShadowEditor.ts",$e),De=a.i18n.getLocalizedString.bind(void 0,Re),Oe=20,_e="px",He=new RegExp("([+-]?(?:[0-9]*[.])?[0-9]+(?:[eE][+-]?[0-9]+)?)(ch|cm|em|ex|in|mm|pc|pt|px|rem|vh|vmax|vmin|vw)|[+-]?(?:0*[.])?0+(?:[eE][+-]?[0-9]+)?","gi").source;class je{amount;unit;constructor(e,t){this.amount=e,this.unit=t}static parse(e){const t=new RegExp("^(?:"+He+")$","i"),i=e.match(t);return i?i.length>2&&i[2]?new je(parseFloat(i[1]),i[2]):je.zero():null}static zero(){return new je(0,"")}asCSSText(){return this.amount+this.unit}}class Ve extends(s.ObjectWrapper.eventMixin(e.Widget.VBox)){typeField;outsetButton;insetButton;xInput;yInput;xySlider;halfCanvasSize;innerCanvasSize;blurInput;blurSlider;spreadField;spreadInput;spreadSlider;model;canvasOrigin;changedElement;constructor(){super(!0),this.contentElement.tabIndex=0,this.contentElement.setAttribute("jslog",`${n.dialog("cssShadowEditor").parent("mapped").track({keydown:"Enter|Escape"})}`),this.setDefaultFocusedElement(this.contentElement),this.typeField=this.contentElement.createChild("div","shadow-editor-field shadow-editor-flex-field"),this.typeField.createChild("label","shadow-editor-label").textContent=De($e.type),this.outsetButton=this.typeField.createChild("button","shadow-editor-button-left"),this.outsetButton.textContent=a.i18n.lockedString("Outset"),this.outsetButton.addEventListener("click",this.onButtonClick.bind(this),!1),this.insetButton=this.typeField.createChild("button","shadow-editor-button-right"),this.insetButton.textContent=a.i18n.lockedString("Inset"),this.insetButton.addEventListener("click",this.onButtonClick.bind(this),!1);const t=this.contentElement.createChild("div","shadow-editor-field");this.xInput=this.createTextInput(t,De($e.xOffset),"x-offset");const i=this.contentElement.createChild("div","shadow-editor-field");this.yInput=this.createTextInput(i,De($e.yOffset),"y-offset"),this.xySlider=t.createChild("canvas","shadow-editor-2D-slider"),this.xySlider.setAttribute("jslog",`${n.slider("xy").track({click:!0,drag:!0,keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight"})}`),this.xySlider.width=88,this.xySlider.height=88,this.xySlider.tabIndex=-1,this.halfCanvasSize=44,this.innerCanvasSize=this.halfCanvasSize-6,e.UIUtils.installDragHandle(this.xySlider,this.dragStart.bind(this),this.dragMove.bind(this),null,"default"),this.xySlider.addEventListener("keydown",this.onCanvasArrowKey.bind(this),!1),this.xySlider.addEventListener("blur",this.onCanvasBlur.bind(this),!1);const s=this.contentElement.createChild("div","shadow-editor-field shadow-editor-flex-field shadow-editor-blur-field");this.blurInput=this.createTextInput(s,De($e.blur),"blur"),this.blurSlider=this.createSlider(s,"blur"),this.spreadField=this.contentElement.createChild("div","shadow-editor-field shadow-editor-flex-field"),this.spreadInput=this.createTextInput(this.spreadField,De($e.spread),"spread"),this.spreadSlider=this.createSlider(this.spreadField,"spread")}createTextInput(t,i,s){const o=t.createChild("label","shadow-editor-label");o.textContent=i,o.setAttribute("for",i);const r=e.UIUtils.createInput("shadow-editor-text-input","text");return t.appendChild(r),r.id=i,r.addEventListener("keydown",this.handleValueModification.bind(this),!1),r.addEventListener("wheel",this.handleValueModification.bind(this),!1),r.addEventListener("input",this.onTextInput.bind(this),!1),r.addEventListener("blur",this.onTextBlur.bind(this),!1),r.setAttribute("jslog",`${n.value().track({change:!0,keydown:"ArrowUp|ArrowDown"}).context(s)}`),r}createSlider(t,i){const s=e.UIUtils.createSlider(0,Oe,-1);return s.addEventListener("input",this.onSliderInput.bind(this),!1),s.setAttribute("jslog",`${n.slider().track({click:!0,drag:!0}).context(i)}`),t.appendChild(s),s}wasShown(){this.registerCSSFiles([Ne]),this.updateUI()}setModel(e){this.model=e,this.typeField.classList.toggle("hidden",!e.isBoxShadow()),this.spreadField.classList.toggle("hidden",!e.isBoxShadow()),this.updateUI()}updateUI(){this.updateButtons(),this.xInput.value=this.model.offsetX().asCSSText(),this.yInput.value=this.model.offsetY().asCSSText(),this.blurInput.value=this.model.blurRadius().asCSSText(),this.spreadInput.value=this.model.spreadRadius().asCSSText(),this.blurSlider.value=this.model.blurRadius().amount.toString(),this.spreadSlider.value=this.model.spreadRadius().amount.toString(),this.updateCanvas(!1)}updateButtons(){this.insetButton.classList.toggle("enabled",this.model.inset()),this.outsetButton.classList.toggle("enabled",!this.model.inset())}updateCanvas(e){const t=this.xySlider.getContext("2d");if(!t)throw new Error("Unable to obtain canvas context");t.clearRect(0,0,this.xySlider.width,this.xySlider.height),t.save(),t.setLineDash([1,1]),t.strokeStyle="rgba(210, 210, 210, 0.8)",t.beginPath(),t.moveTo(this.halfCanvasSize,0),t.lineTo(this.halfCanvasSize,88),t.moveTo(0,this.halfCanvasSize),t.lineTo(88,this.halfCanvasSize),t.stroke(),t.restore();const i=this.sliderThumbPosition();t.save(),t.translate(this.halfCanvasSize,this.halfCanvasSize),t.lineWidth=2,t.strokeStyle="rgba(130, 130, 130, 0.75)",t.beginPath(),t.moveTo(0,0),t.lineTo(i.x,i.y),t.stroke(),e&&(t.beginPath(),t.fillStyle="rgba(66, 133, 244, 0.4)",t.arc(i.x,i.y,8,0,2*Math.PI),t.fill()),t.beginPath(),t.fillStyle="#4285F4",t.arc(i.x,i.y,6,0,2*Math.PI),t.fill(),t.restore()}onButtonClick(e){const t=e.currentTarget===this.insetButton;t&&this.model.inset()||!t&&!this.model.inset()||(this.model.setInset(t),this.updateButtons(),this.dispatchEventToListeners("ShadowChanged",this.model))}handleValueModification(t){const i=t.currentTarget,n=e.UIUtils.createReplacementString(i.value,t,(function(e,t,i){i.length||(i=_e);return e+t+i}));if(!n)return;const s=je.parse(n);s&&(t.currentTarget===this.blurInput&&s.amount<0&&(s.amount=0),i.value=s.asCSSText(),i.selectionStart=0,i.selectionEnd=i.value.length,this.onTextInput(t),t.consume(!0))}onTextInput(e){const t=e.currentTarget;this.changedElement=t,this.changedElement.classList.remove("invalid");const i=je.parse(t.value);!i||t===this.blurInput&&i.amount<0||(t===this.xInput?(this.model.setOffsetX(i),this.updateCanvas(!1)):t===this.yInput?(this.model.setOffsetY(i),this.updateCanvas(!1)):t===this.blurInput?(this.model.setBlurRadius(i),this.blurSlider.value=i.amount.toString()):t===this.spreadInput&&(this.model.setSpreadRadius(i),this.spreadSlider.value=i.amount.toString()),this.dispatchEventToListeners("ShadowChanged",this.model))}onTextBlur(){if(!this.changedElement)return;let e=this.changedElement.value.trim()?je.parse(this.changedElement.value):je.zero();if(e||(e=je.parse(this.changedElement.value+_e)),!e)return this.changedElement.classList.add("invalid"),void(this.changedElement=null);this.changedElement===this.xInput?(this.model.setOffsetX(e),this.xInput.value=e.asCSSText(),this.updateCanvas(!1)):this.changedElement===this.yInput?(this.model.setOffsetY(e),this.yInput.value=e.asCSSText(),this.updateCanvas(!1)):this.changedElement===this.blurInput?(e.amount<0&&(e=je.zero()),this.model.setBlurRadius(e),this.blurInput.value=e.asCSSText(),this.blurSlider.value=e.amount.toString()):this.changedElement===this.spreadInput&&(this.model.setSpreadRadius(e),this.spreadInput.value=e.asCSSText(),this.spreadSlider.value=e.amount.toString()),this.changedElement=null,this.dispatchEventToListeners("ShadowChanged",this.model)}onSliderInput(e){e.currentTarget===this.blurSlider?(this.model.setBlurRadius(new je(Number(this.blurSlider.value),this.model.blurRadius().unit||_e)),this.blurInput.value=this.model.blurRadius().asCSSText(),this.blurInput.classList.remove("invalid")):e.currentTarget===this.spreadSlider&&(this.model.setSpreadRadius(new je(Number(this.spreadSlider.value),this.model.spreadRadius().unit||_e)),this.spreadInput.value=this.model.spreadRadius().asCSSText(),this.spreadInput.classList.remove("invalid")),this.dispatchEventToListeners("ShadowChanged",this.model)}dragStart(t){this.xySlider.focus(),this.updateCanvas(!0),this.canvasOrigin=new e.Geometry.Point(this.xySlider.getBoundingClientRect().left+this.halfCanvasSize,this.xySlider.getBoundingClientRect().top+this.halfCanvasSize);const i=new e.Geometry.Point(t.x-this.canvasOrigin.x,t.y-this.canvasOrigin.y),n=this.sliderThumbPosition();return i.distanceTo(n)>=6&&this.dragMove(t),!0}dragMove(t){let i=new e.Geometry.Point(t.x-this.canvasOrigin.x,t.y-this.canvasOrigin.y);t.shiftKey&&(i=this.snapToClosestDirection(i));const n=this.constrainPoint(i,this.innerCanvasSize),s=Math.round(n.x/this.innerCanvasSize*Oe),o=Math.round(n.y/this.innerCanvasSize*Oe);t.shiftKey?(this.model.setOffsetX(new je(s,this.model.offsetX().unit||_e)),this.model.setOffsetY(new je(o,this.model.offsetY().unit||_e))):(t.altKey||this.model.setOffsetX(new je(s,this.model.offsetX().unit||_e)),e.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(t)||this.model.setOffsetY(new je(o,this.model.offsetY().unit||_e))),this.xInput.value=this.model.offsetX().asCSSText(),this.yInput.value=this.model.offsetY().asCSSText(),this.xInput.classList.remove("invalid"),this.yInput.classList.remove("invalid"),this.updateCanvas(!0),this.dispatchEventToListeners("ShadowChanged",this.model)}onCanvasBlur(){this.updateCanvas(!1)}onCanvasArrowKey(e){const t=e;let n=0,s=0;if("ArrowRight"===t.key?n=1:"ArrowLeft"===t.key?n=-1:"ArrowUp"===t.key?s=-1:"ArrowDown"===t.key&&(s=1),n||s){if(e.consume(!0),n){const e=this.model.offsetX(),t=i.NumberUtilities.clamp(e.amount+n,-20,Oe);if(t===e.amount)return;this.model.setOffsetX(new je(t,e.unit||_e)),this.xInput.value=this.model.offsetX().asCSSText(),this.xInput.classList.remove("invalid")}if(s){const e=this.model.offsetY(),t=i.NumberUtilities.clamp(e.amount+s,-20,Oe);if(t===e.amount)return;this.model.setOffsetY(new je(t,e.unit||_e)),this.yInput.value=this.model.offsetY().asCSSText(),this.yInput.classList.remove("invalid")}this.updateCanvas(!0),this.dispatchEventToListeners("ShadowChanged",this.model)}}constrainPoint(t,i){return Math.abs(t.x)<=i&&Math.abs(t.y)<=i?new e.Geometry.Point(t.x,t.y):t.scale(i/Math.max(Math.abs(t.x),Math.abs(t.y)))}snapToClosestDirection(t){let i=Number.MAX_VALUE,n=t;const s=[new e.Geometry.Point(0,-1),new e.Geometry.Point(1,-1),new e.Geometry.Point(1,0),new e.Geometry.Point(1,1)];for(const e of s){const s=t.projectOn(e),o=t.distanceTo(s);o<i&&(i=o,n=s)}return n}sliderThumbPosition(){const t=this.model.offsetX().amount/Oe*this.innerCanvasSize,i=this.model.offsetY().amount/Oe*this.innerCanvasSize;return this.constrainPoint(new e.Geometry.Point(t,i),this.innerCanvasSize)}}var Ge=Object.freeze({__proto__:null,CSSLength:je,CSSShadowEditor:Ve});const Xe=new CSSStyleSheet;Xe.replaceSync(":host{user-select:none;padding:4px 12px 12px}.error-input{box-shadow:0 0 0 1px var(--sys-color-error)}.error-text{color:var(--sys-color-error);padding:6px 0}.warning-input{--override-warning-input-color:#ffdd9e;box-shadow:0 0 0 1px var(--override-warning-input-color)}.theme-with-dark-background .warning-input,\n:host-context(.theme-with-dark-background) .warning-input{--override-warning-input-color:rgb(97 63 0)}.hide-warning{display:none}.font-section-header{font-weight:normal;font-size:17px;text-align:left}.font-section-subheader{font-size:12px;text-align:left;font-weight:bold}.font-selector-section{overflow-y:auto;padding-bottom:10px}.font-selector-input{width:204px;text-align-last:center}.font-reset-button{width:100%;margin-top:10px}.font-section{border-top:1px solid var(--sys-color-divider)}.chrome-select.font-editor-select{min-width:50px;min-height:27px}input::-webkit-outer-spin-button,\ninput::-webkit-inner-spin-button{display:none;margin:0}.preview-text{max-width:300px;word-break:break-word;display:block}.rendered-font-list-label{font-weight:bold;font-size:12px}.rendered-font-list{padding:5px 0}.shadow-editor-field{height:24px;margin-top:8px;font-size:12px;flex-shrink:0}.shadow-editor-field:last-of-type{margin-bottom:8px}.shadow-editor-flex-field{display:flex;align-items:center;flex-direction:row}.shadow-editor-field.shadow-editor-blur-field{margin-top:40px}.shadow-editor-2D-slider{position:absolute;height:88px;width:88px;border:1px solid var(--divider-line);border-radius:2px}.shadow-editor-label{display:inline-block;width:70px;height:24px;line-height:24px;margin-right:8px;text-align:left}.shadow-editor-button-left,\n.shadow-editor-button-right{width:74px;height:24px;padding:3px 7px;line-height:16px;border:1px solid var(--divider-line);color:var(--sys-color-on-surface);background-color:var(--sys-color-cdt-base-container);text-align:center;font-weight:500}.shadow-editor-button-left{border-radius:2px 0 0 2px}.shadow-editor-button-right{border-radius:0 2px 2px 0;border-left-width:0}.shadow-editor-button-left:hover,\n.shadow-editor-button-right:hover{box-shadow:0 1px 1px var(--divider-line)}.shadow-editor-button-left:focus,\n.shadow-editor-button-right:focus{background-color:var(--sys-color-state-focus-highlight)}.shadow-editor-text-input{width:50px;margin:8px;text-align:center;box-shadow:var(--legacy-focus-ring-inactive-shadow)}.spectrum-switcher{border-radius:2px;height:20px;width:20px;padding:2px;margin-left:5px}.spectrum-switcher:hover{background-color:var(--sys-color-state-hover-on-subtle)}.spectrum-switcher:focus-visible{background-color:var(--sys-color-state-focus-highlight)}\n/*# sourceURL=fontEditor.css */\n");const We=6;async function Ke(t){const i=e.Context.Context.instance().flavor(d.DOMModel.DOMNode);let n;if(i&&i.parentNode&&"HTML"!==i.nodeName()){const[e]=d.TargetManager.TargetManager.instance().models(c.CSSOverviewModel.CSSOverviewModel),s=t?i.parentNode.id:i.id,o=(await e.getComputedStyleForNode(s).then(Ye)).replace(/[a-z]/g,"");n=parseFloat(o)}else n=16;return n}function Ye(e){const t=e.computedStyle;let i=We;if(t[i].name&&"font-size"!==t[i].name)for(let e=0;e<t.length;e++)if("font-size"===t[e].name){i=e;break}return t[i].value}const qe={expression:"window.innerWidth",includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:!1,userGesture:!1,awaitPromise:!0,throwOnSideEffect:!1,disableBreaks:!0,replMode:!1,allowUnsafeEvalBlockedByCSP:!1},Ze={expression:"window.innerHeight",includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:!1,userGesture:!1,awaitPromise:!0,throwOnSideEffect:!1,disableBreaks:!0,replMode:!1,allowUnsafeEvalBlockedByCSP:!1};async function Qe(){const t=e.Context.Context.instance().flavor(d.RuntimeModel.ExecutionContext);let i,n;if(t){const e=await t.evaluate(qe,!1,!1),s=await t.evaluate(Ze,!1,!1);if("error"in e||"error"in s)return null;e.object&&(i=e.object.value),s.object&&(n=s.object.value)}if(void 0===i||void 0===n){const t=e.Context.Context.instance().flavor(d.DOMModel.DOMNode);if(!t)return null;const s=await t.domModel().target().pageAgent().invoke_getLayoutMetrics(),o=s.visualViewport.zoom?s.visualViewport.zoom:1;n=s.visualViewport.clientHeight/o,i=s.visualViewport.clientWidth/o}return{width:i,height:n}}const Je=new Map;async function et(e,t,i){let n,s;""===e&&(e="em"),""===t&&(t="em");const o=Je.get(e),r=Je.get(t);return o&&r?(n="em"===e||"%"===e?await o(i):await o(),s="em"===t||"%"===t?await r(i):await r(),n/s):1}Je.set("px",(function(){return 1})),Je.set("em",Ke),Je.set("rem",(async function(){const t=function(e){let t=e;for(;t&&"HTML"!==t.nodeName()&&t.parentNode;)t=t.parentNode;return t}(e.Context.Context.instance().flavor(d.DOMModel.DOMNode));if(!t||!t.id)return 16;const[i]=d.TargetManager.TargetManager.instance().models(c.CSSOverviewModel.CSSOverviewModel),n=(await i.getComputedStyleForNode(t.id).then(Ye)).replace(/[a-z]/g,"");return parseFloat(n)})),Je.set("%",(async function(e){return await Ke(e)/100})),Je.set("vh",(async function(){const e=await Qe();return e?e.height/100:1})),Je.set("vw",(async function(){const e=await Qe();return e?e.width/100:1})),Je.set("vmin",(async function(){const e=await Qe();if(!e)return 1;const t=e.width,i=e.height;return Math.min(t,i)/100})),Je.set("vmax",(async function(){const e=await Qe();if(!e)return 1;const t=e.width,i=e.height;return Math.max(t,i)/100})),Je.set("cm",(function(){return 37.795})),Je.set("mm",(function(){return 3.7795})),Je.set("in",(function(){return 96})),Je.set("pt",(function(){return 4/3})),Je.set("pc",(function(){return 16}));var tt=Object.freeze({__proto__:null,getUnitConversionMultiplier:et});const it=new Set(["px","em","rem","%","vh","vw"]),nt=new Set(["","px","em","%"]),st=new Set(["em","rem","px"]),ot=["","xx-small","x-small","smaller","small","medium","large","larger","x-large","xx-large"],rt=["","normal"],at=["","lighter","normal","bold","bolder"],lt=["","normal"],dt=["inherit","initial","unset"];ot.push(...dt),rt.push(...dt),at.push(...dt),lt.push(...dt);const ht=new Set(ot),ct=new Set(rt),ut=new Set(at),pt=new Set(lt),mt=new Map([["px",{min:0,max:72,step:1}],["em",{min:0,max:4.5,step:.1}],["rem",{min:0,max:4.5,step:.1}],["%",{min:0,max:450,step:1}],["vh",{min:0,max:10,step:.1}],["vw",{min:0,max:10,step:.1}],["vmin",{min:0,max:10,step:.1}],["vmax",{min:0,max:10,step:.1}],["cm",{min:0,max:2,step:.1}],["mm",{min:0,max:20,step:.1}],["in",{min:0,max:1,step:.01}],["pt",{min:0,max:54,step:1}],["pc",{min:0,max:4.5,step:.1}]]),gt=new Map([["",{min:0,max:2,step:.1}],["em",{min:0,max:2,step:.1}],["%",{min:0,max:200,step:1}],["px",{min:0,max:32,step:1}],["rem",{min:0,max:2,step:.1}],["vh",{min:0,max:4.5,step:.1}],["vw",{min:0,max:4.5,step:.1}],["vmin",{min:0,max:4.5,step:.1}],["vmax",{min:0,max:4.5,step:.1}],["cm",{min:0,max:1,step:.1}],["mm",{min:0,max:8.5,step:.1}],["in",{min:0,max:.5,step:.1}],["pt",{min:0,max:24,step:1}],["pc",{min:0,max:2,step:.1}]]),vt=new Map([["",{min:100,max:700,step:100}]]),bt=new Map([["px",{min:-10,max:10,step:.01}],["em",{min:-.625,max:.625,step:.001}],["rem",{min:-.625,max:.625,step:.001}],["%",{min:-62.5,max:62.5,step:.1}],["vh",{min:-1.5,max:1.5,step:.01}],["vw",{min:-1.5,max:1.5,step:.01}],["vmin",{min:-1.5,max:1.5,step:.01}],["vmax",{min:-1.5,max:1.5,step:.01}],["cm",{min:-.25,max:.025,step:.001}],["mm",{min:-2.5,max:2.5,step:.01}],["in",{min:-.1,max:.1,step:.001}],["pt",{min:-7.5,max:7.5,step:.01}],["pc",{min:-.625,max:.625,step:.001}]]),xt={regex:/(^[\+\d\.]+)([a-zA-Z%]+)/,units:it,keyValues:ht,rangeMap:mt,defaultUnit:"px"},ft={regex:/(^[\+\d\.]+)([a-zA-Z%]*)/,units:nt,keyValues:ct,rangeMap:gt,defaultUnit:""},wt={regex:/(^[\+\d\.]+)/,units:null,keyValues:ut,rangeMap:vt,defaultUnit:null},yt={regex:/([\+-0-9\.]+)([a-zA-Z%]+)/,units:st,keyValues:pt,rangeMap:bt,defaultUnit:"em"},St=["Arial","Bookman","Candara","Comic Sans MS","Courier New","Garamond","Georgia","Helvetica","Impact","Palatino","Roboto","Times New Roman","Verdana"],Ct=["serif","sans-serif","monspace","cursive","fantasy","system-ui","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","emoji","math","fangsong"];async function kt(){const e=d.TargetManager.TargetManager.instance().models(c.CSSOverviewModel.CSSOverviewModel);if(e){const t=e[0];if(t){const{fontInfo:e}=await t.getNodeStyleStats();return Array.from(e.keys())}}return[]}function It(e){switch(e){case 1:default:return 0;case.1:return 1;case.01:return 2;case.001:return 3}}var Et=Object.freeze({__proto__:null,FontPropertiesRegex:/^[^- ][a-zA-Z-]+$|^-?\+?(?:[0-9]+\.[0-9]+|\.[0-9]+|[0-9]+)[a-zA-Z%]{0,4}$/,FontFamilyRegex:/^"[\w \,-]+"$|^'[\w \,-]+'$|^[\w \-]+$/,GlobalValues:dt,FontSizeStaticParams:xt,LineHeightStaticParams:ft,FontWeightStaticParams:wt,LetterSpacingStaticParams:yt,SystemFonts:St,GenericFonts:Ct,generateComputedFontArray:kt,getRoundingPrecision:It});const Tt={fontFamily:"Font Family",cssProperties:"CSS Properties",fontSize:"Font Size",lineHeight:"Line Height",fontWeight:"Font Weight",spacing:"Spacing",fallbackS:"Fallback {PH1}",thereIsNoValueToDeleteAtIndexS:"There is no value to delete at index: {PH1}",fontSelectorDeletedAtIndexS:"Font Selector deleted at index: {PH1}",deleteS:"Delete {PH1}",PleaseEnterAValidValueForSText:"* Please enter a valid value for {PH1} text input",thisPropertyIsSetToContainUnits:"This property is set to contain units but does not have a defined corresponding unitsArray: {PH1}",sSliderInput:"{PH1} Slider Input",sTextInput:"{PH1} Text Input",units:"Units",sUnitInput:"{PH1} Unit Input",sKeyValueSelector:"{PH1} Key Value Selector",sToggleInputType:"{PH1} toggle input type",selectorInputMode:"Selector Input Mode",sliderInputMode:"Slider Input Mode"},Pt=a.i18n.registerUIStrings("ui/legacy/components/inline_editor/FontEditor.ts",Tt),zt=a.i18n.getLocalizedString.bind(void 0,Pt);class Mt extends(s.ObjectWrapper.eventMixin(e.Widget.VBox)){selectedNode;propertyMap;fontSelectorSection;fontSelectors;fontsList;constructor(t){super(!0),this.selectedNode=e.Context.Context.instance().flavor(d.DOMModel.DOMNode),this.propertyMap=t,this.contentElement.tabIndex=0,this.contentElement.setAttribute("jslog",`${n.dialog("font-editor").parent("mapped").track({keydown:"Enter|Escape"})}`),this.setDefaultFocusedElement(this.contentElement),this.fontSelectorSection=this.contentElement.createChild("div","font-selector-section"),this.fontSelectorSection.createChild("h2","font-section-header").textContent=zt(Tt.fontFamily),this.fontSelectors=[],this.fontsList=null;const i=this.propertyMap.get("font-family");this.createFontSelectorSection(i);const s=this.contentElement.createChild("div","font-section");s.createChild("h2","font-section-header").textContent=zt(Tt.cssProperties);const o=this.getPropertyInfo("font-size",xt.regex),r=this.getPropertyInfo("line-height",ft.regex),a=this.getPropertyInfo("font-weight",wt.regex),l=this.getPropertyInfo("letter-spacing",yt.regex);new Lt("font-size",zt(Tt.fontSize),s,o,xt,this.updatePropertyValue.bind(this),this.resizePopout.bind(this),!0),new Lt("line-height",zt(Tt.lineHeight),s,r,ft,this.updatePropertyValue.bind(this),this.resizePopout.bind(this),!0),new Lt("font-weight",zt(Tt.fontWeight),s,a,wt,this.updatePropertyValue.bind(this),this.resizePopout.bind(this),!1),new Lt("letter-spacing",zt(Tt.spacing),s,l,yt,this.updatePropertyValue.bind(this),this.resizePopout.bind(this),!0)}wasShown(){this.registerCSSFiles([Xe])}async createFontSelectorSection(e){if(e){const t=e.split(",");if(await this.createFontSelector(t[0],!0),!dt.includes(t[0]))for(let e=1;e<t.length+1;e++)this.createFontSelector(t[e])}else this.createFontSelector("",!0);this.resizePopout()}async createFontsList(){const e=await kt(),t=new Map,i=this.splitComputedFontArray(e);t.set("Computed Fonts",i);const n=new Map;n.set("System Fonts",St),n.set("Generic Families",Ct);const s=[];return s.push(t),s.push(n),s}splitComputedFontArray(e){const t=[];for(const i of e)if(i.includes(",")){i.split(",").forEach((e=>{-1===t.findIndex((t=>t.toLowerCase()===e.trim().toLowerCase().replace(/"/g,"'")))&&t.push(e.trim().replace(/"/g,""))}))}else-1===t.findIndex((e=>e.toLowerCase()===i.toLowerCase().replace('"',"'")))&&t.push(i.replace(/"/g,""));return t}async createFontSelector(e,t){if(e=e?e.trim():""){const t=e.charAt(0);"'"===t?e=e.replace(/'/g,""):'"'===t&&(e=e.replace(/"/g,""))}const i=this.fontSelectorSection.createChild("div","shadow-editor-field shadow-editor-flex-field");let n;if(this.fontsList||(this.fontsList=await this.createFontsList()),t){n=zt(Tt.fontFamily);const t=new Map([["Global Values",dt]]),s=[...this.fontsList];s.push(t),this.createSelector(i,n,s,e.trim(),"primary-font-family")}else n=zt(Tt.fallbackS,{PH1:this.fontSelectors.length}),this.createSelector(i,n,this.fontsList,e.trim(),"fallback-font-family")}deleteFontSelector(t,i){let n=this.fontSelectors[t];const s=0===t;if(""===n.input.value&&!i)return void e.ARIAUtils.alert(zt(Tt.thereIsNoValueToDeleteAtIndexS,{PH1:t}));if(s){const e=this.fontSelectors[1];let i="";e&&(i=e.input.value,n=e);this.fontSelectors[0].input.value=i,t=1}if(n.input.parentNode){const i=this.fontSelectors.length>1;if(!s||i){const e=n.input.parentElement;e&&(e.remove(),this.fontSelectors.splice(t,1),this.updateFontSelectorList())}e.ARIAUtils.alert(zt(Tt.fontSelectorDeletedAtIndexS,{PH1:t}))}this.onFontSelectorChanged(),this.resizePopout();const o=s?0:t-1;this.fontSelectors[o].input.focus()}updateFontSelectorList(){for(let t=0;t<this.fontSelectors.length;t++){const i=this.fontSelectors[t];let n;n=0===t?zt(Tt.fontFamily):zt(Tt.fallbackS,{PH1:t}),i.label.textContent=n,e.ARIAUtils.setLabel(i.input,n),i.deleteButton.setTitle(zt(Tt.deleteS,{PH1:n})),i.index=t}}getPropertyInfo(e,t){const i=this.propertyMap.get(e);if(i){const e=i,n=e.match(t);if(n){return{value:"+"===n[1].charAt(0)?n[1].substr(1):n[1],units:n[2]?n[2]:""}}return{value:e,units:null}}return{value:null,units:null}}createSelector(t,s,o,r,a){const l=this.fontSelectors.length,d=e.UIUtils.createSelect(s,o);d.value=r,d.setAttribute("jslog",`${n.dropDown(a).track({click:!0,change:!0})}`);const h=e.UIUtils.createLabel(s,"shadow-editor-label",d);d.addEventListener("input",this.onFontSelectorChanged.bind(this),!1),d.addEventListener("keydown",(e=>{"Enter"===e.key&&e.consume()}),!1),t.appendChild(h),t.appendChild(d);const c=new e.Toolbar.Toolbar("",t),u=new e.Toolbar.ToolbarButton(zt(Tt.deleteS,{PH1:s}),"bin",void 0,"delete");c.appendToolbarItem(u);const p={label:h,input:d,deleteButton:u,index:l};u.addEventListener("Click",(()=>{this.deleteFontSelector(p.index)})),u.element.addEventListener("keydown",(e=>{i.KeyboardUtilities.isEnterOrSpaceKey(e)&&(this.deleteFontSelector(p.index),e.consume())}),!1),this.fontSelectors.push(p)}onFontSelectorChanged(){let e="";const t=dt.includes(this.fontSelectors[0].input.value);if(t)for(let e=1;e<this.fontSelectors.length;e++)this.deleteFontSelector(e,!0);for(const t of this.fontSelectors){const i=t.input;""!==i.value&&(""===e?e=this.fontSelectors[0].input.value:e+=", "+i.value)}""!==this.fontSelectors[this.fontSelectors.length-1].input.value&&!t&&this.fontSelectors.length<10&&(this.createFontSelector(""),this.resizePopout()),this.updatePropertyValue("font-family",e)}updatePropertyValue(e,t){this.dispatchEventToListeners("FontChanged",{propertyName:e,value:t})}resizePopout(){this.dispatchEventToListeners("FontEditorResized")}}class Lt{showSliderMode;errorText;propertyInfo;propertyName;staticParams;hasUnits;units;addedUnit;initialRange;boundUpdateCallback;boundResizeCallback;selectedNode;sliderInput;textBoxInput;unitInput;selectorInput;applyNextInput;constructor(t,i,n,s,o,r,a,l){this.showSliderMode=!0;const h=n.createChild("div","shadow-editor-field shadow-editor-flex-field");if(this.errorText=n.createChild("div","error-text"),this.errorText.textContent=zt(Tt.PleaseEnterAValidValueForSText,{PH1:t}),this.errorText.hidden=!0,e.ARIAUtils.markAsAlert(this.errorText),this.propertyInfo=s,this.propertyName=t,this.staticParams=o,this.hasUnits=l,this.hasUnits&&this.staticParams.units&&null!==this.staticParams.defaultUnit){const e=this.staticParams.defaultUnit;this.units=null!==s.units?s.units:e,this.addedUnit=!this.staticParams.units.has(this.units)}else{if(this.hasUnits)throw new Error(zt(Tt.thisPropertyIsSetToContainUnits,{PH1:t}));this.units=""}this.initialRange=this.getUnitRange(),this.boundUpdateCallback=r,this.boundResizeCallback=a,this.selectedNode=e.Context.Context.instance().flavor(d.DOMModel.DOMNode);const c=e.UIUtils.createLabel(i,"shadow-editor-label");h.append(c),this.sliderInput=this.createSliderInput(h,t),this.textBoxInput=this.createTextBoxInput(h,t),e.ARIAUtils.bindLabelToControl(c,this.textBoxInput),this.unitInput=this.createUnitInput(h,`${t}-unit`),this.selectorInput=this.createSelectorInput(h,t),this.createTypeToggle(h,`${t}-value-type`),this.checkSelectorValueAndToggle(),this.applyNextInput=!1}setInvalidTextBoxInput(e){e?this.errorText.hidden&&(this.errorText.hidden=!1,this.textBoxInput.classList.add("error-input"),this.boundResizeCallback()):this.errorText.hidden||(this.errorText.hidden=!0,this.textBoxInput.classList.remove("error-input"),this.boundResizeCallback())}checkSelectorValueAndToggle(){return!(!this.staticParams.keyValues||null===this.propertyInfo.value||!this.staticParams.keyValues.has(this.propertyInfo.value))&&(this.toggleInputType(),!0)}getUnitRange(){let e=0,t=100,i=1;if(null!==this.propertyInfo.value&&/\d/.test(this.propertyInfo.value))if(this.staticParams.rangeMap.get(this.units)){const n=this.staticParams.rangeMap.get(this.units);n&&(e=Math.min(n.min,parseFloat(this.propertyInfo.value)),t=Math.max(n.max,parseFloat(this.propertyInfo.value)),i=n.step)}else{const n=this.staticParams.rangeMap.get("px");n&&(e=Math.min(n.min,parseFloat(this.propertyInfo.value)),t=Math.max(n.max,parseFloat(this.propertyInfo.value)),i=n.step)}else{const n=this.staticParams.rangeMap.get(this.units);n&&(e=n.min,t=n.max,i=n.step)}return{min:e,max:t,step:i}}createSliderInput(t,i){const s=this.initialRange.min,o=this.initialRange.max,r=this.initialRange.step,a=e.UIUtils.createSlider(s,o,-1);if(a.sliderElement.step=r.toString(),a.sliderElement.tabIndex=0,this.propertyInfo.value)a.value=parseFloat(this.propertyInfo.value);else{const e=(s+o)/2;a.value=e}return a.addEventListener("input",(e=>{this.onSliderInput(e,!1)})),a.addEventListener("mouseup",(e=>{this.onSliderInput(e,!0)})),a.addEventListener("keydown",(e=>{"ArrowUp"!==e.key&&"ArrowDown"!==e.key&&"ArrowLeft"!==e.key&&"ArrowRight"!==e.key||(this.applyNextInput=!0)})),t.appendChild(a),e.ARIAUtils.setLabel(a.sliderElement,zt(Tt.sSliderInput,{PH1:this.propertyName})),a.sliderElement.setAttribute("jslog",`${n.slider(i).track({change:!0})}`),a}createTextBoxInput(t,i){const n=e.UIUtils.createInput("shadow-editor-text-input","number",i);return n.step=this.initialRange.step.toString(),n.classList.add("font-editor-text-input"),null!==this.propertyInfo.value&&("+"===this.propertyInfo.value.charAt(0)&&(this.propertyInfo.value=this.propertyInfo.value.substr(1)),n.value=this.propertyInfo.value),n.step="any",n.addEventListener("input",this.onTextBoxInput.bind(this),!1),t.appendChild(n),e.ARIAUtils.setLabel(n,zt(Tt.sTextInput,{PH1:this.propertyName})),n}createUnitInput(t,i){let s;if(this.hasUnits&&this.staticParams.units){const t=this.propertyInfo.units,i=this.staticParams.units;s=e.UIUtils.createSelect(zt(Tt.units),i),s.classList.add("font-editor-select"),this.addedUnit&&t&&s.add(new Option(t,t)),t&&(s.value=t),s.addEventListener("change",this.onUnitInput.bind(this),!1)}else s=e.UIUtils.createSelect(zt(Tt.units),[]),s.classList.add("font-editor-select"),s.disabled=!0;return s.setAttribute("jslog",`${n.dropDown(i).track({click:!0,change:!0})}`),s.addEventListener("keydown",(e=>{"Enter"===e.key&&e.consume()}),!1),t.appendChild(s),e.ARIAUtils.setLabel(s,zt(Tt.sUnitInput,{PH1:this.propertyName})),s}createSelectorInput(t,i){const s=e.UIUtils.createSelect(zt(Tt.sKeyValueSelector,{PH1:this.propertyName}),this.staticParams.keyValues);return s.classList.add("font-selector-input"),this.propertyInfo.value&&(s.value=this.propertyInfo.value),s.addEventListener("input",this.onSelectorInput.bind(this),!1),s.addEventListener("keydown",(e=>{"Enter"===e.key&&e.consume()}),!1),t.appendChild(s),s.hidden=!0,s.setAttribute("jslog",`${n.dropDown(i).track({click:!0,change:!0})}`),s}onSelectorInput(e){if(e.currentTarget){const t=e.currentTarget.value;this.textBoxInput.value="";const i=(parseFloat(this.sliderInput.sliderElement.min)+parseFloat(this.sliderInput.sliderElement.max))/2;this.sliderInput.value=i,this.setInvalidTextBoxInput(!1),this.boundUpdateCallback(this.propertyName,t)}}onSliderInput(e,t){const i=e.currentTarget;if(i){const e=i.value;this.textBoxInput.value=e,this.selectorInput.value="";const n=this.hasUnits?e+this.unitInput.value:e.toString();this.setInvalidTextBoxInput(!1),(t||this.applyNextInput)&&(this.boundUpdateCallback(this.propertyName,n),this.applyNextInput=!1)}}onTextBoxInput(e){const t=e.currentTarget;if(t){const e=t.value,i=e+(""===e?"":this.unitInput.value);this.staticParams.regex.test(i)||""===e&&!t.validationMessage.length?(parseFloat(e)>parseFloat(this.sliderInput.sliderElement.max)?this.sliderInput.sliderElement.max=e:parseFloat(e)<parseFloat(this.sliderInput.sliderElement.min)&&(this.sliderInput.sliderElement.min=e),this.sliderInput.value=parseFloat(e),this.selectorInput.value="",this.setInvalidTextBoxInput(!1),this.boundUpdateCallback(this.propertyName,i)):this.setInvalidTextBoxInput(!0)}}async onUnitInput(e){const t=e.currentTarget,i=t.hasFocus(),n=t.value;t.disabled=!0;const s=this.units,o=await et(s,n,"font-size"===this.propertyName);this.setInputUnits(o,n),this.textBoxInput.value&&this.boundUpdateCallback(this.propertyName,this.textBoxInput.value+n),this.units=n,t.disabled=!1,i&&t.focus()}createTypeToggle(t,i){const s=t.createChild("div","spectrum-switcher"),o=new h.Icon.Icon;o.data={iconName:"fold-more",color:"var(--icon-default)",width:"16px",height:"16px"},s.appendChild(o),e.UIUtils.setTitle(s,zt(Tt.sToggleInputType,{PH1:this.propertyName})),s.tabIndex=0,self.onInvokeElement(s,this.toggleInputType.bind(this)),e.ARIAUtils.markAsButton(s),s.setAttribute("jslog",`${n.toggle(i).track({click:!0})}`)}toggleInputType(t){t&&"Enter"===t.key&&t.consume(),this.showSliderMode?(this.sliderInput.hidden=!0,this.textBoxInput.hidden=!0,this.unitInput.hidden=!0,this.selectorInput.hidden=!1,this.showSliderMode=!1,e.ARIAUtils.alert(zt(Tt.selectorInputMode))):(this.sliderInput.hidden=!1,this.textBoxInput.hidden=!1,this.unitInput.hidden=!1,this.selectorInput.hidden=!0,this.showSliderMode=!0,e.ARIAUtils.alert(zt(Tt.sliderInputMode)))}setInputUnits(e,t){const i=this.staticParams.rangeMap.get(t);let n,s,o;i?(n=i.min,s=i.max,o=i.step):(n=0,s=100,o=1);let r=!1;const a=It(o);let l=(n+s)/2;this.textBoxInput.value&&(r=!0,l=parseFloat((parseFloat(this.textBoxInput.value)*e).toFixed(a))),this.sliderInput.sliderElement.min=Math.min(l,n).toString(),this.sliderInput.sliderElement.max=Math.max(l,s).toString(),this.sliderInput.sliderElement.step=o.toString(),this.textBoxInput.step=o.toString(),r&&(this.textBoxInput.value=l.toString()),this.sliderInput.value=l}}var At=Object.freeze({__proto__:null,FontEditor:Mt}),Ut={cssContent:".text-button{margin:2px;height:24px;font-size:12px;border:1px solid var(--sys-color-tonal-outline);border-radius:12px;padding:0 12px;font-weight:500;color:var(--sys-color-primary);background-color:var(--sys-color-cdt-base-container);flex:none;white-space:nowrap}.text-button:disabled{opacity:38%}.text-button:not(:disabled):focus,\n.text-button:not(:disabled):hover,\n.text-button:not(:disabled):active{background-color:var(--sys-color-state-hover-on-subtle)}.text-button:not(:disabled):not(.primary-button):focus-visible{outline:2px solid var(--sys-color-state-focus-ring);color:var(--sys-color-on-primary);background-color:var(--sys-color-cdt-base-container)}.text-button:not(:disabled):not(.running):focus,\n.text-button:not(:disabled):not(.running):hover,\n.text-button:not(:disabled):not(.running):active{color:var(--sys-color-primary)}.text-button.link-style,\n.text-button.link-style:hover,\n.text-button.link-style:active{background:none;border:none;outline:none;border-radius:2px;margin:0;padding:0!important;font:inherit;cursor:pointer;height:unset}.text-button.primary-button,\n.text-button.primary-button:not(:disabled):focus{background-color:var(--sys-color-primary);border:none;color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):active{background-color:color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-ripple-primary) 32%);color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):hover{background-color:color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-hover-on-prominent) 6%);color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):focus-visible{background-color:var(--sys-color-primary);outline-offset:2px;outline:2px solid var(--sys-color-state-focus-ring);color:var(--sys-color-on-primary)}@media (forced-colors: active){.text-button{background-color:ButtonFace;color:ButtonText;border-color:ButtonText}.text-button:disabled{forced-color-adjust:none;opacity:100%;background:ButtonFace;border-color:GrayText;color:GrayText}.text-button:not(:disabled):focus-visible{forced-color-adjust:none;background-color:ButtonFace;color:Highlight!important;border-color:Highlight;outline:2px solid ButtonText;box-shadow:var(--legacy-focus-ring-active-shadow)}.text-button:not(:disabled):hover,\n  .text-button:not(:disabled):active{forced-color-adjust:none;background-color:Highlight;color:HighlightText!important;box-shadow:var(--sys-color-primary)}.text-button.primary-button{forced-color-adjust:none;background-color:Highlight;color:HighlightText;border:1px solid Highlight}.text-button.primary-button:not(:disabled):focus-visible{background-color:Highlight;color:HighlightText!important;border-color:ButtonText}.text-button.primary-button:not(:disabled):hover,\n  .text-button.primary-button:not(:disabled):active{background-color:HighlightText;color:Highlight!important;border-color:Highlight}}\n/*# sourceURL=textButton.css */\n"};const Ft=new CSSStyleSheet;Ft.replaceSync(".link-swatch-link{display:inline}.link-swatch-link:not(.undefined){cursor:pointer;text-underline-offset:2px;color:var(--text-link)}.link-swatch-link:hover:not(.undefined){text-decoration:underline}.link-swatch-link:focus:not(:focus-visible){outline:none}.link-swatch-link.undefined{opacity:100%;color:var(--text-disabled)}\n/*# sourceURL=linkSwatch.css */\n");const Bt={sIsNotDefined:"{PH1} is not defined"},Nt=a.i18n.registerUIStrings("ui/legacy/components/inline_editor/LinkSwatch.ts",Bt),$t=a.i18n.getLocalizedString.bind(void 0,Nt),{render:Rt,html:Dt,Directives:Ot}=o;class _t extends HTMLElement{static litTagName=o.literal`devtools-base-link-swatch`;shadow=this.attachShadow({mode:"open"});onLinkActivate=()=>{};#F;connectedCallback(){this.shadow.adoptedStyleSheets=[Ft],u.ThemeSupport.instance().appendStyle(this.shadow,Ut)}set data(e){this.onLinkActivate=(t,i)=>{i instanceof MouseEvent&&0!==i.button||(e.onLinkActivate(t),i.consume(!0))},e.showTitle=void 0===e.showTitle||e.showTitle,this.render(e)}get linkElement(){return this.#F}render(e){const{isDefined:t,text:i,title:n}=e,s=Ot.classMap({"link-style":!0,"text-button":!0,"link-swatch-link":!0,undefined:!t}),r=t?this.onLinkActivate.bind(this,i.trim()):null,{startNode:a}=Rt(Dt`<button .disabled=${!t} class=${s} title=${o.Directives.ifDefined(e.showTitle?n:null)} data-title=${o.Directives.ifDefined(e.showTitle?null:n)} @click=${r} role="link" tabindex="-1">${i}</button>`,this.shadow,{host:this});a?.nextSibling instanceof HTMLButtonElement&&(this.#F=a?.nextSibling)}}class Ht extends HTMLElement{static litTagName=o.literal`devtools-css-var-swatch`;shadow=this.attachShadow({mode:"open"});#B;constructor(){super(),this.tabIndex=-1,this.addEventListener("focus",(()=>{const e=this.shadow.querySelector('[role="link"]');e&&e.focus()}))}set data(e){this.render(e)}get link(){return this.#B}render(e){const{variableName:t,fromFallback:i,computedValue:s,onLinkActivate:o}=e,r=Boolean(s)&&!i,a=r?s??"":$t(Bt.sIsNotDefined,{PH1:t});this.#B=new _t,this.#B.data={title:a,showTitle:!1,text:t,isDefined:r,onLinkActivate:o},this.#B.classList.add("css-var-link"),Rt(Dt`<span data-title=${e.computedValue||""}
          jslog=${n.link("css-variable").track({click:!0,hover:!0})}
        >var(${this.#B}<slot name="fallback">${e.fallbackText?`, ${e.fallbackText}`:""}</slot>)</span>`,this.shadow,{host:this})}}class jt extends HTMLElement{static litTagName=o.literal`devtools-link-swatch`;shadow=this.attachShadow({mode:"open"});set data(e){this.render(e)}render(e){const{text:t,isDefined:i,onLinkActivate:s,jslogContext:o}=e,r=i?t:$t(Bt.sIsNotDefined,{PH1:t});Rt(Dt`<span title=${e.text} jslog=${n.link().track({click:!0}).context(o)}><${_t.litTagName} .data=${{text:t,isDefined:i,title:r,onLinkActivate:s}}></${_t.litTagName}></span>`,this.shadow,{host:this})}}customElements.define("devtools-base-link-swatch",_t),customElements.define("devtools-link-swatch",jt),customElements.define("devtools-css-var-swatch",Ht);var Vt=Object.freeze({__proto__:null,CSSVarSwatch:Ht,LinkSwatch:jt});const Gt=new CSSStyleSheet;Gt.replaceSync(":host{white-space:nowrap}devtools-icon.bezier-swatch-icon{position:relative;transform:scale(0.7);margin:-5px -2px -3px -4px;user-select:none;color:var(--icon-css);cursor:default}devtools-icon.bezier-swatch-icon:hover{color:var(--icon-css-hover)}\n/*# sourceURL=bezierSwatch.css */\n");const Xt=new CSSStyleSheet;Xt.replaceSync(":host{white-space:nowrap}devtools-icon.shadow-swatch-icon{color:var(--icon-css);transform:scale(0.7);position:relative;margin:-5px -2px -5px -4px;user-select:none;vertical-align:baseline;cursor:pointer;&:hover{color:var(--icon-css-hover)}}\n/*# sourceURL=cssShadowSwatch.css */\n");class Wt extends HTMLSpanElement{iconElementInternal;textElement;constructor(){super();const t=e.UIUtils.createShadowRootWithCoreStyles(this,{cssFile:[Gt],delegatesFocus:void 0});this.iconElementInternal=h.Icon.create("bezier-curve-filled","bezier-swatch-icon"),this.iconElementInternal.setAttribute("jslog",`${n.showStyleEditor("bezier")}`),t.appendChild(this.iconElementInternal),this.textElement=this.createChild("span"),t.createChild("slot")}static create(){let t=Wt.constructorInternal;return t||(t=e.UIUtils.registerCustomElement("span","bezier-swatch",Wt),Wt.constructorInternal=t),t()}bezierText(){return this.textElement.textContent||""}setBezierText(e){this.textElement.textContent=e}hideText(e){this.textElement.hidden=e}iconElement(){return this.iconElementInternal}static constructorInternal=null}class Kt extends HTMLElement{static litTagName=o.literal`css-shadow-swatch`;#N=this.attachShadow({mode:"open"});#$;#c;constructor(e){super(),this.#c=e,this.#N.adoptedStyleSheets=[Xt],o.render(o.html`<${h.Icon.Icon.litTagName} name="shadow" class="shadow-swatch-icon"></${h.Icon.Icon.litTagName}><slot></slot>`,this.#N,{host:this}),this.#$=this.#N.querySelector(h.Icon.Icon.litTagName.value)}model(){return this.#c}iconElement(){return this.#$}}customElements.define("css-shadow-swatch",Kt);var Yt=Object.freeze({__proto__:null,BezierSwatch:Wt,CSSShadowSwatch:Kt});const qt=new CSSStyleSheet;qt.replaceSync(".widget{display:flex;background:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow);border-radius:2px;overflow:auto;user-select:text;line-height:11px}\n/*# sourceURL=swatchPopover.css */\n");class Zt extends s.ObjectWrapper.ObjectWrapper{popover;hideProxy;boundOnKeyDown;boundFocusOut;isHidden;anchorElement;view;hiddenCallback;focusRestorer;constructor(){super(),this.popover=new e.GlassPane.GlassPane,this.popover.setSizeBehavior("MeasureContent"),this.popover.setMarginBehavior("Arrow"),this.popover.element.addEventListener("mousedown",(e=>e.consume()),!1),this.hideProxy=this.hide.bind(this,!0),this.boundOnKeyDown=this.onKeyDown.bind(this),this.boundFocusOut=this.onFocusOut.bind(this),this.isHidden=!0,this.anchorElement=null}onFocusOut(e){const t=e.relatedTarget;!this.isHidden&&t&&this.view&&!t.isSelfOrDescendant(this.view.contentElement)&&this.hideProxy()}setAnchorElement(e){this.anchorElement=e}isShowing(e){return this.popover.isShowing()&&(e&&this.view===e||!e)}show(e,t,i){if(this.popover.isShowing()){if(this.anchorElement===t)return;this.hide(!0)}n.setMappedParent(e.contentElement,t),this.popover.registerCSSFiles([qt]),this.dispatchEventToListeners("WillShowPopover"),this.isHidden=!1,this.anchorElement=t,this.view=e,this.hiddenCallback=i,this.reposition(),e.focus();const s=this.popover.element.ownerDocument;s.addEventListener("mousedown",this.hideProxy,!1),s.defaultView&&s.defaultView.addEventListener("resize",this.hideProxy,!1),this.view.contentElement.addEventListener("keydown",this.boundOnKeyDown,!1)}reposition(){if(!this.isHidden&&this.view){if(this.view.contentElement.removeEventListener("focusout",this.boundFocusOut,!1),this.view.show(this.popover.contentElement),this.anchorElement){let e=this.anchorElement.boxInWindow();if(G.isColorSwatch(this.anchorElement)){const t=this.anchorElement;if(!t.anchorBox)return;e=t.anchorBox}this.popover.setContentAnchorBox(e),this.popover.show(this.anchorElement.ownerDocument)}this.view.contentElement.addEventListener("focusout",this.boundFocusOut,!1),this.focusRestorer||(this.focusRestorer=new e.Widget.WidgetFocusRestorer(this.view))}}hide(e){if(this.isHidden)return;const t=this.popover.element.ownerDocument;this.isHidden=!0,this.popover.hide(),t.removeEventListener("mousedown",this.hideProxy,!1),t.defaultView&&t.defaultView.removeEventListener("resize",this.hideProxy,!1),this.hiddenCallback&&this.hiddenCallback.call(null,Boolean(e)),this.focusRestorer&&this.focusRestorer.restore(),this.anchorElement=null,this.view&&(this.view.detach(),this.view.contentElement.removeEventListener("keydown",this.boundOnKeyDown,!1),this.view.contentElement.removeEventListener("focusout",this.boundFocusOut,!1),delete this.view)}onKeyDown(e){if("Enter"===e.key)return this.hide(!0),void e.consume(!0);e.key===i.KeyboardUtilities.ESCAPE_KEY&&(this.hide(!1),e.consume(!0))}}var Qt=Object.freeze({__proto__:null,SwatchPopoverHelper:Zt});export{S as AnimationTimingModel,L as AnimationTimingUI,B as BezierEditor,I as BezierUI,Ce as CSSAngle,se as CSSAngleUtils,Be as CSSLength,Pe as CSSLengthUtils,f as CSSLinearEasingModel,Ge as CSSShadowEditor,R as ColorMixSwatch,X as ColorSwatch,At as FontEditor,tt as FontEditorUnitConverter,Et as FontEditorUtils,ge as InlineEditorUtils,Vt as LinkSwatch,Qt as SwatchPopoverHelper,Yt as Swatches};
