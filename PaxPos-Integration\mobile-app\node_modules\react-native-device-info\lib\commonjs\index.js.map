{"version": 3, "sources": ["index.ts"], "names": ["getUniqueId", "getUniqueIdSync", "memoKey", "supportedPlatforms", "getter", "RNDeviceInfo", "syncGetter", "defaultValue", "uniqueId", "syncUniqueId", "Platform", "OS", "getInstanceId", "getInstanceIdSync", "getSerialNumber", "getSerialNumberSync", "getAndroidId", "getAndroidIdSync", "getIpAddress", "getIpAddressSync", "isCameraPresent", "isCameraPresentSync", "getMacAddress", "getMacAddressSync", "getDeviceId", "deviceId", "getManufacturer", "getManufacturerSync", "Promise", "resolve", "getSystemManufacturer", "getSystemManufacturerSync", "getModel", "model", "get<PERSON>rand", "brand", "getSystemName", "select", "ios", "systemName", "android", "windows", "default", "getSystemVersion", "systemVersion", "getBuildId", "getBuildIdSync", "getApiLevel", "getApiLevelSync", "getBundleId", "bundleId", "getInstallerPackageName", "getInstallerPackageNameSync", "getApplicationName", "appName", "getBuildNumber", "buildNumber", "getVersion", "appVersion", "getReadableVersion", "getDeviceName", "getDeviceNameSync", "getUsed<PERSON><PERSON><PERSON>", "getUsedMemorySync", "getUserAgent", "getUserAgentSync", "getFontScale", "getFontScaleSync", "getBootloader", "getBootloaderSync", "getDevice", "getDeviceSync", "getDisplay", "getDisplaySync", "getFingerprint", "getFingerprintSync", "getHardware", "getHardwareSync", "getHost", "getHostSync", "getHostNames", "getHostNamesSync", "getProduct", "getProductSync", "getTags", "getTagsSync", "getType", "getTypeSync", "getBaseOs", "getBaseOsSync", "getPreviewSdkInt", "getPreviewSdkIntSync", "getSecurityPatch", "getSecurityPatchSync", "getCodename", "getCodenameSync", "getIncremental", "getIncrementalSync", "isEmulator", "isEmulatorSync", "isTablet", "isLowRamDevice", "isDisplayZoomed", "isPinOrFingerprintSet", "isPinOrFingerprintSetSync", "notch", "hasNotch", "undefined", "_brand", "_model", "devicesWithNotch", "findIndex", "item", "toLowerCase", "dynamicIsland", "hasDynamicIsland", "devicesWithDynamicIsland", "hasGms", "hasGmsSync", "hasHms", "hasHmsSync", "getFirstInstallTime", "getFirstInstallTimeSync", "getInstallReferrer", "getInstallReferrerSync", "getLastUpdateTime", "getLastUpdateTimeSync", "getPhoneNumber", "getPhoneNumberSync", "get<PERSON>arrier", "getCarrierSync", "getTotalMemory", "getTotalMemorySync", "getMaxMemory", "getMaxMemorySync", "getTotalDiskCapacity", "getTotalDiskCapacitySync", "getTotalDiskCapacityOld", "getTotalDiskCapacityOldSync", "getFreeDiskStorage", "getFreeDiskStorageSync", "getFreeDiskStorageOld", "getFreeDiskStorageOldSync", "getBatteryLevel", "getBatteryLevelSync", "getPowerState", "getPowerStateSync", "isBatteryCharging", "isBatteryChargingSync", "isLandscape", "isLandscapeSync", "height", "width", "Dimensions", "get", "isAirplaneMode", "isAirplaneModeSync", "getDeviceType", "deviceType", "getDeviceTypeSync", "supportedAbis", "supportedAbisSync", "getSupportedAbis", "getSupportedAbisSync", "supported32BitAbis", "supported32BitAbisSync", "getSupported32BitAbis", "getSupported32BitAbisSync", "supported64BitAbis", "supported64BitAbisSync", "getSupported64BitAbis", "getSupported64BitAbisSync", "hasSystemFeature", "feature", "hasSystemFeatureSync", "isLowBatteryLevel", "level", "getSystemAvailableFeatures", "getSystemAvailableFeaturesSync", "isLocationEnabled", "isLocationEnabledSync", "isHeadphonesConnected", "isHeadphonesConnectedSync", "isWiredHeadphonesConnected", "isWiredHeadphonesConnectedSync", "isBluetoothHeadphonesConnected", "isBluetoothHeadphonesConnectedSync", "isMouseConnected", "isMouseConnectedSync", "isKeyboardConnected", "isKeyboardConnectedSync", "getSupportedMediaTypeList", "getSupportedMediaTypeListSync", "isTabletMode", "getAvailableLocationProviders", "getAvailableLocationProvidersSync", "getBrightness", "getBrightnessSync", "getDeviceToken", "deviceInfoEmitter", "NativeEventEmitter", "NativeModules", "useBatteryLevel", "batteryLevel", "setBatteryLevel", "setInitialValue", "initialValue", "onChange", "subscription", "addListener", "remove", "useBatteryLevelIsLow", "batteryLevelIsLow", "setBatteryLevelIsLow", "usePowerState", "powerState", "setPowerState", "state", "useIsHeadphonesConnected", "useIsWiredHeadphonesConnected", "useIsBluetoothHeadphonesConnected", "useFirstInstallTime", "useDeviceName", "useHasSystemFeature", "asyncGetter", "useIsEmulator", "useManufacturer", "useBrightness", "brightness", "setBrightness", "value", "DeviceInfo"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;AAaO,MAAM,CAACA,WAAD,EAAcC,eAAd,IAAiC,8DAAkC;AAC9EC,EAAAA,OAAO,EAAE,UADqE;AAE9EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAF0D;AAG9EC,EAAAA,MAAM,EAAE,MAAMC,yBAAaL,WAAb,EAHgE;AAI9EM,EAAAA,UAAU,EAAE,MAAMD,yBAAaJ,eAAb,EAJ4D;AAK9EM,EAAAA,YAAY,EAAE;AALgE,CAAlC,CAAvC;;;AAQP,IAAIC,QAAJ;;AACO,eAAeC,YAAf,GAA8B;AACnC,MAAIC,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzBH,IAAAA,QAAQ,GAAG,MAAMH,yBAAaI,YAAb,EAAjB;AACD,GAFD,MAEO;AACLD,IAAAA,QAAQ,GAAG,MAAMR,WAAW,EAA5B;AACD;;AACD,SAAOQ,QAAP;AACD;;AAEM,MAAM,CAACI,aAAD,EAAgBC,iBAAhB,IAAqC,8DAAkC;AAClFX,EAAAA,OAAO,EAAE,YADyE;AAElFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF8D;AAGlFC,EAAAA,MAAM,EAAE,MAAMC,yBAAaO,aAAb,EAHoE;AAIlFN,EAAAA,UAAU,EAAE,MAAMD,yBAAaQ,iBAAb,EAJgE;AAKlFN,EAAAA,YAAY,EAAE;AALoE,CAAlC,CAA3C;;;AAQA,MAAM,CAACO,eAAD,EAAkBC,mBAAlB,IAAyC,8DAAkC;AACtFb,EAAAA,OAAO,EAAE,cAD6E;AAEtFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,CAFkE;AAGtFC,EAAAA,MAAM,EAAE,MAAMC,yBAAaS,eAAb,EAHwE;AAItFR,EAAAA,UAAU,EAAE,MAAMD,yBAAaU,mBAAb,EAJoE;AAKtFR,EAAAA,YAAY,EAAE;AALwE,CAAlC,CAA/C;;;AAQA,MAAM,CAACS,YAAD,EAAeC,gBAAf,IAAmC,8DAAkC;AAChFf,EAAAA,OAAO,EAAE,WADuE;AAEhFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF4D;AAGhFC,EAAAA,MAAM,EAAE,MAAMC,yBAAaW,YAAb,EAHkE;AAIhFV,EAAAA,UAAU,EAAE,MAAMD,yBAAaY,gBAAb,EAJ8D;AAKhFV,EAAAA,YAAY,EAAE;AALkE,CAAlC,CAAzC;;;AAQA,MAAM,CAACW,YAAD,EAAeC,gBAAf,IAAmC,8DAAkC;AAChFhB,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAD4D;AAEhFC,EAAAA,MAAM,EAAE,MAAMC,yBAAaa,YAAb,EAFkE;AAGhFZ,EAAAA,UAAU,EAAE,MAAMD,yBAAac,gBAAb,EAH8D;AAIhFZ,EAAAA,YAAY,EAAE;AAJkE,CAAlC,CAAzC;;;AAOA,MAAM,CAACa,eAAD,EAAkBC,mBAAlB,IAAyC,8DAAkC;AACtFlB,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,KAAvB,CADkE;AAEtFC,EAAAA,MAAM,EAAE,MAAMC,yBAAae,eAAb,EAFwE;AAGtFd,EAAAA,UAAU,EAAE,MAAMD,yBAAagB,mBAAb,EAHoE;AAItFd,EAAAA,YAAY,EAAE;AAJwE,CAAlC,CAA/C;;;;AAOA,eAAee,aAAf,GAA+B;AACpC,MAAIZ,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAON,yBAAaiB,aAAb,EAAP;AACD,GAFD,MAEO,IAAIZ,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AAChC,WAAO,mBAAP;AACD;;AACD,SAAO,SAAP;AACD;;AAEM,SAASY,iBAAT,GAA6B;AAClC,MAAIb,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAON,yBAAakB,iBAAb,EAAP;AACD,GAFD,MAEO,IAAIb,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AAChC,WAAO,mBAAP;AACD;;AACD,SAAO,SAAP;AACD;;AAEM,MAAMa,WAAW,GAAG,MACzB,yDAA6B;AAC3BjB,EAAAA,YAAY,EAAE,SADa;AAE3BL,EAAAA,OAAO,EAAE,UAFkB;AAG3BE,EAAAA,MAAM,EAAE,MAAMC,yBAAaoB,QAHA;AAI3BtB,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB;AAJO,CAA7B,CADK;;;AAQA,MAAM,CAACuB,eAAD,EAAkBC,mBAAlB,IAAyC,8DAAkC;AACtFzB,EAAAA,OAAO,EAAE,cAD6E;AAEtFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFkE;AAGtFC,EAAAA,MAAM,EAAE,MACNM,sBAASC,EAAT,IAAe,KAAf,GAAuBiB,OAAO,CAACC,OAAR,CAAgB,OAAhB,CAAvB,GAAkDxB,yBAAayB,qBAAb,EAJkC;AAKtFxB,EAAAA,UAAU,EAAE,MAAOI,sBAASC,EAAT,IAAe,KAAf,GAAuB,OAAvB,GAAiCN,yBAAa0B,yBAAb,EALkC;AAMtFxB,EAAAA,YAAY,EAAE;AANwE,CAAlC,CAA/C;;;;AASA,MAAMyB,QAAQ,GAAG,MACtB,yDAA6B;AAC3B9B,EAAAA,OAAO,EAAE,OADkB;AAE3BK,EAAAA,YAAY,EAAE,SAFa;AAG3BJ,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,SAAR,EAAmB,SAAnB,CAHO;AAI3BC,EAAAA,MAAM,EAAE,MAAMC,yBAAa4B;AAJA,CAA7B,CADK;;;;AAQA,MAAMC,QAAQ,GAAG,MACtB,yDAA6B;AAC3BhC,EAAAA,OAAO,EAAE,OADkB;AAE3BC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFO;AAG3BI,EAAAA,YAAY,EAAE,SAHa;AAI3BH,EAAAA,MAAM,EAAE,MAAMC,yBAAa8B;AAJA,CAA7B,CADK;;;;AAQA,MAAMC,aAAa,GAAG,MAC3B,yDAA6B;AAC3B7B,EAAAA,YAAY,EAAE,SADa;AAE3BJ,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,SAAR,EAAmB,SAAnB,CAFO;AAG3BD,EAAAA,OAAO,EAAE,YAHkB;AAI3BE,EAAAA,MAAM,EAAE,MACNM,sBAAS2B,MAAT,CAAgB;AACdC,IAAAA,GAAG,EAAEjC,yBAAakC,UADJ;AAEdC,IAAAA,OAAO,EAAE,SAFK;AAGdC,IAAAA,OAAO,EAAE,SAHK;AAIdC,IAAAA,OAAO,EAAE;AAJK,GAAhB;AALyB,CAA7B,CADK;;;;AAcA,MAAMC,gBAAgB,GAAG,MAC9B,yDAA6B;AAC3BpC,EAAAA,YAAY,EAAE,SADa;AAE3BH,EAAAA,MAAM,EAAE,MAAMC,yBAAauC,aAFA;AAG3BzC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAHO;AAI3BD,EAAAA,OAAO,EAAE;AAJkB,CAA7B,CADK;;;AAQA,MAAM,CAAC2C,UAAD,EAAaC,cAAb,IAA+B,8DAAkC;AAC5E5C,EAAAA,OAAO,EAAE,SADmE;AAE5EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFwD;AAG5EC,EAAAA,MAAM,EAAE,MAAMC,yBAAawC,UAAb,EAH8D;AAI5EvC,EAAAA,UAAU,EAAE,MAAMD,yBAAayC,cAAb,EAJ0D;AAK5EvC,EAAAA,YAAY,EAAE;AAL8D,CAAlC,CAArC;;;AAQA,MAAM,CAACwC,WAAD,EAAcC,eAAd,IAAiC,8DAAkC;AAC9E9C,EAAAA,OAAO,EAAE,UADqE;AAE9EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF0D;AAG9EC,EAAAA,MAAM,EAAE,MAAMC,yBAAa0C,WAAb,EAHgE;AAI9EzC,EAAAA,UAAU,EAAE,MAAMD,yBAAa2C,eAAb,EAJ4D;AAK9EzC,EAAAA,YAAY,EAAE,CAAC;AAL+D,CAAlC,CAAvC;;;;AAQA,MAAM0C,WAAW,GAAG,MACzB,yDAA6B;AAC3B/C,EAAAA,OAAO,EAAE,UADkB;AAE3BC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFO;AAG3BI,EAAAA,YAAY,EAAE,SAHa;AAI3BH,EAAAA,MAAM,EAAE,MAAMC,yBAAa6C;AAJA,CAA7B,CADK;;;AAQA,MAAM,CACXC,uBADW,EAEXC,2BAFW,IAGT,8DAAkC;AACpClD,EAAAA,OAAO,EAAE,sBAD2B;AAEpCC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,KAAvB,CAFgB;AAGpCC,EAAAA,MAAM,EAAE,MAAMC,yBAAa8C,uBAAb,EAHsB;AAIpC7C,EAAAA,UAAU,EAAE,MAAMD,yBAAa+C,2BAAb,EAJkB;AAKpC7C,EAAAA,YAAY,EAAE;AALsB,CAAlC,CAHG;;;;AAWA,MAAM8C,kBAAkB,GAAG,MAChC,yDAA6B;AAC3BnD,EAAAA,OAAO,EAAE,SADkB;AAE3BK,EAAAA,YAAY,EAAE,SAFa;AAG3BH,EAAAA,MAAM,EAAE,MAAMC,yBAAaiD,OAHA;AAI3BnD,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB;AAJO,CAA7B,CADK;;;;AAQA,MAAMoD,cAAc,GAAG,MAC5B,yDAA6B;AAC3BrD,EAAAA,OAAO,EAAE,aADkB;AAE3BC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFO;AAG3BC,EAAAA,MAAM,EAAE,MAAMC,yBAAamD,WAHA;AAI3BjD,EAAAA,YAAY,EAAE;AAJa,CAA7B,CADK;;;;AAQA,MAAMkD,UAAU,GAAG,MACxB,yDAA6B;AAC3BvD,EAAAA,OAAO,EAAE,SADkB;AAE3BK,EAAAA,YAAY,EAAE,SAFa;AAG3BJ,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAHO;AAI3BC,EAAAA,MAAM,EAAE,MAAMC,yBAAaqD;AAJA,CAA7B,CADK;;;;AAQA,SAASC,kBAAT,GAA8B;AACnC,SAAOF,UAAU,KAAK,GAAf,GAAqBF,cAAc,EAA1C;AACD;;AAEM,MAAM,CAACK,aAAD,EAAgBC,iBAAhB,IAAqC,8DAAkC;AAClF1D,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAD8D;AAElFC,EAAAA,MAAM,EAAE,MAAMC,yBAAauD,aAAb,EAFoE;AAGlFtD,EAAAA,UAAU,EAAE,MAAMD,yBAAawD,iBAAb,EAHgE;AAIlFtD,EAAAA,YAAY,EAAE;AAJoE,CAAlC,CAA3C;;;AAOA,MAAM,CAACuD,aAAD,EAAgBC,iBAAhB,IAAqC,8DAAkC;AAClF5D,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CAD8D;AAElFC,EAAAA,MAAM,EAAE,MAAMC,yBAAayD,aAAb,EAFoE;AAGlFxD,EAAAA,UAAU,EAAE,MAAMD,yBAAa0D,iBAAb,EAHgE;AAIlFxD,EAAAA,YAAY,EAAE,CAAC;AAJmE,CAAlC,CAA3C;;;;AAOA,MAAMyD,YAAY,GAAG,MAC1B,0DAA8B;AAC5B9D,EAAAA,OAAO,EAAE,WADmB;AAE5BK,EAAAA,YAAY,EAAE,SAFc;AAG5BJ,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,KAAnB,CAHQ;AAI5BC,EAAAA,MAAM,EAAE,MAAMC,yBAAa2D,YAAb;AAJc,CAA9B,CADK;;;;AAQA,MAAMC,gBAAgB,GAAG,MAC9B,yDAA6B;AAC3B/D,EAAAA,OAAO,EAAE,eADkB;AAE3BK,EAAAA,YAAY,EAAE,SAFa;AAG3BJ,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CAHO;AAI3BC,EAAAA,MAAM,EAAE,MAAMC,yBAAa4D,gBAAb;AAJa,CAA7B,CADK;;;AAQA,MAAM,CAACC,YAAD,EAAeC,gBAAf,IAAmC,8DAAkC;AAChFhE,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAD4D;AAEhFC,EAAAA,MAAM,EAAE,MAAMC,yBAAa6D,YAAb,EAFkE;AAGhF5D,EAAAA,UAAU,EAAE,MAAMD,yBAAa8D,gBAAb,EAH8D;AAIhF5D,EAAAA,YAAY,EAAE,CAAC;AAJiE,CAAlC,CAAzC;;;AAOA,MAAM,CAAC6D,aAAD,EAAgBC,iBAAhB,IAAqC,8DAAkC;AAClFnE,EAAAA,OAAO,EAAE,YADyE;AAElFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF8D;AAGlFC,EAAAA,MAAM,EAAE,MAAMC,yBAAa+D,aAAb,EAHoE;AAIlF9D,EAAAA,UAAU,EAAE,MAAMD,yBAAagE,iBAAb,EAJgE;AAKlF9D,EAAAA,YAAY,EAAE;AALoE,CAAlC,CAA3C;;;AAQA,MAAM,CAAC+D,SAAD,EAAYC,aAAZ,IAA6B,8DAAkC;AAC1EnE,EAAAA,MAAM,EAAE,MAAMC,yBAAaiE,SAAb,EAD4D;AAE1EhE,EAAAA,UAAU,EAAE,MAAMD,yBAAakE,aAAb,EAFwD;AAG1EhE,EAAAA,YAAY,EAAE,SAH4D;AAI1EL,EAAAA,OAAO,EAAE,QAJiE;AAK1EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD;AALsD,CAAlC,CAAnC;;;AAQA,MAAM,CAACqE,UAAD,EAAaC,cAAb,IAA+B,8DAAkC;AAC5EvE,EAAAA,OAAO,EAAE,SADmE;AAE5EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFwD;AAG5EC,EAAAA,MAAM,EAAE,MAAMC,yBAAamE,UAAb,EAH8D;AAI5ElE,EAAAA,UAAU,EAAE,MAAMD,yBAAaoE,cAAb,EAJ0D;AAK5ElE,EAAAA,YAAY,EAAE;AAL8D,CAAlC,CAArC;;;AAQA,MAAM,CAACmE,cAAD,EAAiBC,kBAAjB,IAAuC,8DAAkC;AACpFzE,EAAAA,OAAO,EAAE,aAD2E;AAEpFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFgE;AAGpFC,EAAAA,MAAM,EAAE,MAAMC,yBAAaqE,cAAb,EAHsE;AAIpFpE,EAAAA,UAAU,EAAE,MAAMD,yBAAasE,kBAAb,EAJkE;AAKpFpE,EAAAA,YAAY,EAAE;AALsE,CAAlC,CAA7C;;;AAQA,MAAM,CAACqE,WAAD,EAAcC,eAAd,IAAiC,8DAAkC;AAC9E3E,EAAAA,OAAO,EAAE,UADqE;AAE9EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF0D;AAG9EC,EAAAA,MAAM,EAAE,MAAMC,yBAAauE,WAAb,EAHgE;AAI9EtE,EAAAA,UAAU,EAAE,MAAMD,yBAAawE,eAAb,EAJ4D;AAK9EtE,EAAAA,YAAY,EAAE;AALgE,CAAlC,CAAvC;;;AAQA,MAAM,CAACuE,OAAD,EAAUC,WAAV,IAAyB,8DAAkC;AACtE7E,EAAAA,OAAO,EAAE,MAD6D;AAEtEC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,CAFkD;AAGtEC,EAAAA,MAAM,EAAE,MAAMC,yBAAayE,OAAb,EAHwD;AAItExE,EAAAA,UAAU,EAAE,MAAMD,yBAAa0E,WAAb,EAJoD;AAKtExE,EAAAA,YAAY,EAAE;AALwD,CAAlC,CAA/B;;;AAQA,MAAM,CAACyE,YAAD,EAAeC,gBAAf,IAAmC,8DAAkC;AAChF/E,EAAAA,OAAO,EAAE,WADuE;AAEhFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF4D;AAGhFC,EAAAA,MAAM,EAAE,MAAMC,yBAAa2E,YAAb,EAHkE;AAIhF1E,EAAAA,UAAU,EAAE,MAAMD,yBAAa4E,gBAAb,EAJ8D;AAKhF1E,EAAAA,YAAY,EAAE;AALkE,CAAlC,CAAzC;;;AAQA,MAAM,CAAC2E,UAAD,EAAaC,cAAb,IAA+B,8DAAkC;AAC5EjF,EAAAA,OAAO,EAAE,SADmE;AAE5EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFwD;AAG5EC,EAAAA,MAAM,EAAE,MAAMC,yBAAa6E,UAAb,EAH8D;AAI5E5E,EAAAA,UAAU,EAAE,MAAMD,yBAAa8E,cAAb,EAJ0D;AAK5E5E,EAAAA,YAAY,EAAE;AAL8D,CAAlC,CAArC;;;AAQA,MAAM,CAAC6E,OAAD,EAAUC,WAAV,IAAyB,8DAAkC;AACtEnF,EAAAA,OAAO,EAAE,MAD6D;AAEtEC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFkD;AAGtEC,EAAAA,MAAM,EAAE,MAAMC,yBAAa+E,OAAb,EAHwD;AAItE9E,EAAAA,UAAU,EAAE,MAAMD,yBAAagF,WAAb,EAJoD;AAKtE9E,EAAAA,YAAY,EAAE;AALwD,CAAlC,CAA/B;;;AAQA,MAAM,CAAC+E,OAAD,EAAUC,WAAV,IAAyB,8DAAkC;AACtErF,EAAAA,OAAO,EAAE,MAD6D;AAEtEC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFkD;AAGtEC,EAAAA,MAAM,EAAE,MAAMC,yBAAaiF,OAAb,EAHwD;AAItEhF,EAAAA,UAAU,EAAE,MAAMD,yBAAakF,WAAb,EAJoD;AAKtEhF,EAAAA,YAAY,EAAE;AALwD,CAAlC,CAA/B;;;AAQA,MAAM,CAACiF,SAAD,EAAYC,aAAZ,IAA6B,8DAAkC;AAC1EvF,EAAAA,OAAO,EAAE,QADiE;AAE1EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFsD;AAG1EC,EAAAA,MAAM,EAAE,MAAMC,yBAAamF,SAAb,EAH4D;AAI1ElF,EAAAA,UAAU,EAAE,MAAMD,yBAAaoF,aAAb,EAJwD;AAK1ElF,EAAAA,YAAY,EAAE;AAL4D,CAAlC,CAAnC;;;AAQA,MAAM,CAACmF,gBAAD,EAAmBC,oBAAnB,IAA2C,8DAAkC;AACxFzF,EAAAA,OAAO,EAAE,eAD+E;AAExFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFoE;AAGxFC,EAAAA,MAAM,EAAE,MAAMC,yBAAaqF,gBAAb,EAH0E;AAIxFpF,EAAAA,UAAU,EAAE,MAAMD,yBAAasF,oBAAb,EAJsE;AAKxFpF,EAAAA,YAAY,EAAE,CAAC;AALyE,CAAlC,CAAjD;;;AAQA,MAAM,CAACqF,gBAAD,EAAmBC,oBAAnB,IAA2C,8DAAkC;AACxF3F,EAAAA,OAAO,EAAE,eAD+E;AAExFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFoE;AAGxFC,EAAAA,MAAM,EAAE,MAAMC,yBAAauF,gBAAb,EAH0E;AAIxFtF,EAAAA,UAAU,EAAE,MAAMD,yBAAawF,oBAAb,EAJsE;AAKxFtF,EAAAA,YAAY,EAAE;AAL0E,CAAlC,CAAjD;;;AAQA,MAAM,CAACuF,WAAD,EAAcC,eAAd,IAAiC,8DAAkC;AAC9E7F,EAAAA,OAAO,EAAE,UADqE;AAE9EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAF0D;AAG9EC,EAAAA,MAAM,EAAE,MAAMC,yBAAayF,WAAb,EAHgE;AAI9ExF,EAAAA,UAAU,EAAE,MAAMD,yBAAa0F,eAAb,EAJ4D;AAK9ExF,EAAAA,YAAY,EAAE;AALgE,CAAlC,CAAvC;;;AAQA,MAAM,CAACyF,cAAD,EAAiBC,kBAAjB,IAAuC,8DAAkC;AACpF/F,EAAAA,OAAO,EAAE,aAD2E;AAEpFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFgE;AAGpFC,EAAAA,MAAM,EAAE,MAAMC,yBAAa2F,cAAb,EAHsE;AAIpF1F,EAAAA,UAAU,EAAE,MAAMD,yBAAa4F,kBAAb,EAJkE;AAKpF1F,EAAAA,YAAY,EAAE;AALsE,CAAlC,CAA7C;;;AAQA,MAAM,CAAC2F,UAAD,EAAaC,cAAb,IAA+B,8DAAkC;AAC5EjG,EAAAA,OAAO,EAAE,UADmE;AAE5EC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFwD;AAG5EC,EAAAA,MAAM,EAAE,MAAMC,yBAAa6F,UAAb,EAH8D;AAI5E5F,EAAAA,UAAU,EAAE,MAAMD,yBAAa8F,cAAb,EAJ0D;AAK5E5F,EAAAA,YAAY,EAAE;AAL8D,CAAlC,CAArC;;;;AAQA,MAAM6F,QAAQ,GAAG,MACtB,yDAA6B;AAC3B7F,EAAAA,YAAY,EAAE,KADa;AAE3BJ,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFO;AAG3BD,EAAAA,OAAO,EAAE,QAHkB;AAI3BE,EAAAA,MAAM,EAAE,MAAMC,yBAAa+F;AAJA,CAA7B,CADK;;;;AAQA,MAAMC,cAAc,GAAG,MAC5B,yDAA6B;AAC3B9F,EAAAA,YAAY,EAAE,KADa;AAE3BJ,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFO;AAG3BD,EAAAA,OAAO,EAAE,QAHkB;AAI3BE,EAAAA,MAAM,EAAE,MAAMC,yBAAagG;AAJA,CAA7B,CADK;;;;AAQA,MAAMC,eAAe,GAAG,MAC7B,yDAA6B;AAC3B/F,EAAAA,YAAY,EAAE,KADa;AAE3BJ,EAAAA,kBAAkB,EAAE,CAAC,KAAD,CAFO;AAG3BD,EAAAA,OAAO,EAAE,QAHkB;AAI3BE,EAAAA,MAAM,EAAE,MAAMC,yBAAaiG;AAJA,CAA7B,CADK;;;AAQA,MAAM,CAACC,qBAAD,EAAwBC,yBAAxB,IAAqD,8DAChE;AACErG,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMC,yBAAakG,qBAAb,EAFhB;AAGEjG,EAAAA,UAAU,EAAE,MAAMD,yBAAamG,yBAAb,EAHpB;AAIEjG,EAAAA,YAAY,EAAE;AAJhB,CADgE,CAA3D;;;AASP,IAAIkG,KAAJ;;AACO,SAASC,QAAT,GAAoB;AACzB,MAAID,KAAK,KAAKE,SAAd,EAAyB;AACvB,QAAIC,MAAM,GAAG1E,QAAQ,EAArB;;AACA,QAAI2E,MAAM,GAAG7E,QAAQ,EAArB;;AACAyE,IAAAA,KAAK,GACHK,0BAAiBC,SAAjB,CACGC,IAAD,IACEA,IAAI,CAAC7E,KAAL,CAAW8E,WAAX,OAA6BL,MAAM,CAACK,WAAP,EAA7B,IACAD,IAAI,CAAC/E,KAAL,CAAWgF,WAAX,OAA6BJ,MAAM,CAACI,WAAP,EAHjC,MAIM,CAAC,CALT;AAMD;;AACD,SAAOR,KAAP;AACD;;AAED,IAAIS,aAAJ;;AACO,SAASC,gBAAT,GAA4B;AACjC,MAAID,aAAa,KAAKP,SAAtB,EAAiC;AAC/B,QAAIC,MAAM,GAAG1E,QAAQ,EAArB;;AACA,QAAI2E,MAAM,GAAG7E,QAAQ,EAArB;;AACAkF,IAAAA,aAAa,GACXE,kCAAyBL,SAAzB,CACGC,IAAD,IACEA,IAAI,CAAC7E,KAAL,CAAW8E,WAAX,OAA6BL,MAAM,CAACK,WAAP,EAA7B,IACAD,IAAI,CAAC/E,KAAL,CAAWgF,WAAX,OAA6BJ,MAAM,CAACI,WAAP,EAHjC,MAIM,CAAC,CALT;AAMD;;AACD,SAAOC,aAAP;AACD;;AAEM,MAAM,CAACG,MAAD,EAASC,UAAT,IAAuB,8DAAkC;AACpEnH,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADgD;AAEpEC,EAAAA,MAAM,EAAE,MAAMC,yBAAagH,MAAb,EAFsD;AAGpE/G,EAAAA,UAAU,EAAE,MAAMD,yBAAaiH,UAAb,EAHkD;AAIpE/G,EAAAA,YAAY,EAAE;AAJsD,CAAlC,CAA7B;;;AAOA,MAAM,CAACgH,MAAD,EAASC,UAAT,IAAuB,8DAAkC;AACpErH,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADgD;AAEpEC,EAAAA,MAAM,EAAE,MAAMC,yBAAakH,MAAb,EAFsD;AAGpEjH,EAAAA,UAAU,EAAE,MAAMD,yBAAamH,UAAb,EAHkD;AAIpEjH,EAAAA,YAAY,EAAE;AAJsD,CAAlC,CAA7B;;;AAOA,MAAM,CAACkH,mBAAD,EAAsBC,uBAAtB,IAAiD,8DAAkC;AAC9FxH,EAAAA,OAAO,EAAE,kBADqF;AAE9FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAF0E;AAG9FC,EAAAA,MAAM,EAAE,MAAMC,yBAAaoH,mBAAb,EAHgF;AAI9FnH,EAAAA,UAAU,EAAE,MAAMD,yBAAaqH,uBAAb,EAJ4E;AAK9FnH,EAAAA,YAAY,EAAE,CAAC;AAL+E,CAAlC,CAAvD;;;AAQA,MAAM,CAACoH,kBAAD,EAAqBC,sBAArB,IAA+C,8DAAkC;AAC5F1H,EAAAA,OAAO,EAAE,iBADmF;AAE5FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,KAAvB,CAFwE;AAG5FC,EAAAA,MAAM,EAAE,MAAMC,yBAAasH,kBAAb,EAH8E;AAI5FrH,EAAAA,UAAU,EAAE,MAAMD,yBAAauH,sBAAb,EAJ0E;AAK5FrH,EAAAA,YAAY,EAAE;AAL8E,CAAlC,CAArD;;;AAQA,MAAM,CAACsH,iBAAD,EAAoBC,qBAApB,IAA6C,8DAAkC;AAC1F5H,EAAAA,OAAO,EAAE,gBADiF;AAE1FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFsE;AAG1FC,EAAAA,MAAM,EAAE,MAAMC,yBAAawH,iBAAb,EAH4E;AAI1FvH,EAAAA,UAAU,EAAE,MAAMD,yBAAayH,qBAAb,EAJwE;AAK1FvH,EAAAA,YAAY,EAAE,CAAC;AAL2E,CAAlC,CAAnD;;;AAQA,MAAM,CAACwH,cAAD,EAAiBC,kBAAjB,IAAuC,8DAAkC;AACpF7H,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADgE;AAEpFC,EAAAA,MAAM,EAAE,MAAMC,yBAAa0H,cAAb,EAFsE;AAGpFzH,EAAAA,UAAU,EAAE,MAAMD,yBAAa2H,kBAAb,EAHkE;AAIpFzH,EAAAA,YAAY,EAAE;AAJsE,CAAlC,CAA7C;;;AAOA,MAAM,CAAC0H,UAAD,EAAaC,cAAb,IAA+B,8DAAkC;AAC5E/H,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADwD;AAE5EC,EAAAA,MAAM,EAAE,MAAMC,yBAAa4H,UAAb,EAF8D;AAG5E3H,EAAAA,UAAU,EAAE,MAAMD,yBAAa6H,cAAb,EAH0D;AAI5E3H,EAAAA,YAAY,EAAE;AAJ8D,CAAlC,CAArC;;;AAOA,MAAM,CAAC4H,cAAD,EAAiBC,kBAAjB,IAAuC,8DAAkC;AACpFlI,EAAAA,OAAO,EAAE,aAD2E;AAEpFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CAFgE;AAGpFC,EAAAA,MAAM,EAAE,MAAMC,yBAAa8H,cAAb,EAHsE;AAIpF7H,EAAAA,UAAU,EAAE,MAAMD,yBAAa+H,kBAAb,EAJkE;AAKpF7H,EAAAA,YAAY,EAAE,CAAC;AALqE,CAAlC,CAA7C;;;AAQA,MAAM,CAAC8H,YAAD,EAAeC,gBAAf,IAAmC,8DAAkC;AAChFpI,EAAAA,OAAO,EAAE,WADuE;AAEhFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,KAAvB,CAF4D;AAGhFC,EAAAA,MAAM,EAAE,MAAMC,yBAAagI,YAAb,EAHkE;AAIhF/H,EAAAA,UAAU,EAAE,MAAMD,yBAAaiI,gBAAb,EAJ8D;AAKhF/H,EAAAA,YAAY,EAAE,CAAC;AALiE,CAAlC,CAAzC;;;AAQA,MAAM,CAACgI,oBAAD,EAAuBC,wBAAvB,IAAmD,8DAAkC;AAChGrI,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CAD4E;AAEhGC,EAAAA,MAAM,EAAE,MAAMC,yBAAakI,oBAAb,EAFkF;AAGhGjI,EAAAA,UAAU,EAAE,MAAMD,yBAAamI,wBAAb,EAH8E;AAIhGjI,EAAAA,YAAY,EAAE,CAAC;AAJiF,CAAlC,CAAzD;;;;AAOA,eAAekI,uBAAf,GAAyC;AAC9C,MAAI/H,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAON,yBAAaoI,uBAAb,EAAP;AACD;;AACD,MAAI/H,sBAASC,EAAT,KAAgB,KAAhB,IAAyBD,sBAASC,EAAT,KAAgB,SAAzC,IAAsDD,sBAASC,EAAT,KAAgB,KAA1E,EAAiF;AAC/E,WAAO4H,oBAAoB,EAA3B;AACD;;AAED,SAAO,CAAC,CAAR;AACD;;AAEM,SAASG,2BAAT,GAAuC;AAC5C,MAAIhI,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAON,yBAAaqI,2BAAb,EAAP;AACD;;AACD,MAAIhI,sBAASC,EAAT,KAAgB,KAAhB,IAAyBD,sBAASC,EAAT,KAAgB,SAAzC,IAAsDD,sBAASC,EAAT,KAAgB,KAA1E,EAAiF;AAC/E,WAAO6H,wBAAwB,EAA/B;AACD;;AAED,SAAO,CAAC,CAAR;AACD;;AAEM,MAAM,CAACG,kBAAD,EAAqBC,sBAArB,IAA+C,8DAAkC;AAC5FzI,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CADwE;AAE5FC,EAAAA,MAAM,EAAE,MAAMC,yBAAasI,kBAAb,EAF8E;AAG5FrI,EAAAA,UAAU,EAAE,MAAMD,yBAAauI,sBAAb,EAH0E;AAI5FrI,EAAAA,YAAY,EAAE,CAAC;AAJ6E,CAAlC,CAArD;;;;AAOA,eAAesI,qBAAf,GAAuC;AAC5C,MAAInI,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAON,yBAAawI,qBAAb,EAAP;AACD;;AACD,MAAInI,sBAASC,EAAT,KAAgB,KAAhB,IAAyBD,sBAASC,EAAT,KAAgB,SAAzC,IAAsDD,sBAASC,EAAT,KAAgB,KAA1E,EAAiF;AAC/E,WAAOgI,kBAAkB,EAAzB;AACD;;AAED,SAAO,CAAC,CAAR;AACD;;AAEM,SAASG,yBAAT,GAAqC;AAC1C,MAAIpI,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAON,yBAAayI,yBAAb,EAAP;AACD;;AACD,MAAIpI,sBAASC,EAAT,KAAgB,KAAhB,IAAyBD,sBAASC,EAAT,KAAgB,SAAzC,IAAsDD,sBAASC,EAAT,KAAgB,KAA1E,EAAiF;AAC/E,WAAOiI,sBAAsB,EAA7B;AACD;;AAED,SAAO,CAAC,CAAR;AACD;;AAEM,MAAM,CAACG,eAAD,EAAkBC,mBAAlB,IAAyC,8DAAkC;AACtF7I,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CADkE;AAEtFC,EAAAA,MAAM,EAAE,MAAMC,yBAAa0I,eAAb,EAFwE;AAGtFzI,EAAAA,UAAU,EAAE,MAAMD,yBAAa2I,mBAAb,EAHoE;AAItFzI,EAAAA,YAAY,EAAE,CAAC;AAJuE,CAAlC,CAA/C;;;AAOA,MAAM,CAAC0I,aAAD,EAAgBC,iBAAhB,IAAqC,8DAEhD;AACA/I,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,SAAR,EAAmB,SAAnB,EAA8B,KAA9B,CADpB;AAEAC,EAAAA,MAAM,EAAE,MAAMC,yBAAa4I,aAAb,EAFd;AAGA3I,EAAAA,UAAU,EAAE,MAAMD,yBAAa6I,iBAAb,EAHlB;AAIA3I,EAAAA,YAAY,EAAE;AAJd,CAFgD,CAA3C;;;AASA,MAAM,CAAC4I,iBAAD,EAAoBC,qBAApB,IAA6C,8DAAkC;AAC1FjJ,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,EAA8B,KAA9B,CADsE;AAE1FC,EAAAA,MAAM,EAAE,MAAMC,yBAAa8I,iBAAb,EAF4E;AAG1F7I,EAAAA,UAAU,EAAE,MAAMD,yBAAa+I,qBAAb,EAHwE;AAI1F7I,EAAAA,YAAY,EAAE;AAJ4E,CAAlC,CAAnD;;;;AAOA,eAAe8I,WAAf,GAA6B;AAClC,SAAOzH,OAAO,CAACC,OAAR,CAAgByH,eAAe,EAA/B,CAAP;AACD;;AAEM,SAASA,eAAT,GAA2B;AAChC,QAAM;AAAEC,IAAAA,MAAF;AAAUC,IAAAA;AAAV,MAAoBC,wBAAWC,GAAX,CAAe,QAAf,CAA1B;;AACA,SAAOF,KAAK,IAAID,MAAhB;AACD;;AAEM,MAAM,CAACI,cAAD,EAAiBC,kBAAjB,IAAuC,8DAAkC;AACpFzJ,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADgE;AAEpFC,EAAAA,MAAM,EAAE,MAAMC,yBAAasJ,cAAb,EAFsE;AAGpFrJ,EAAAA,UAAU,EAAE,MAAMD,yBAAauJ,kBAAb,EAHkE;AAIpFrJ,EAAAA,YAAY,EAAE;AAJsE,CAAlC,CAA7C;;;;AAOA,MAAMsJ,aAAa,GAAG,MAAM;AACjC,SAAO,yDAA6B;AAClC3J,IAAAA,OAAO,EAAE,YADyB;AAElCC,IAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFc;AAGlCI,IAAAA,YAAY,EAAE,SAHoB;AAIlCH,IAAAA,MAAM,EAAE,MAAMC,yBAAayJ;AAJO,GAA7B,CAAP;AAMD,CAPM;;;;AASA,MAAMC,iBAAiB,GAAG,MAAM;AACrC,SAAO,yDAA6B;AAClC7J,IAAAA,OAAO,EAAE,YADyB;AAElCC,IAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAFc;AAGlCI,IAAAA,YAAY,EAAE,SAHoB;AAIlCH,IAAAA,MAAM,EAAE,MAAMC,yBAAayJ;AAJO,GAA7B,CAAP;AAMD,CAPM;;;AASA,MAAM,CAACE,aAAD,EAAgBC,iBAAhB,IAAqC,8DAAkC;AAClF/J,EAAAA,OAAO,EAAE,gBADyE;AAElFC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,SAAnB,CAF8D;AAGlFC,EAAAA,MAAM,EAAE,MAAMC,yBAAa6J,gBAAb,EAHoE;AAIlF5J,EAAAA,UAAU,EAAE,MAAMD,yBAAa8J,oBAAb,EAJgE;AAKlF5J,EAAAA,YAAY,EAAE;AALoE,CAAlC,CAA3C;;;AAQA,MAAM,CAAC6J,kBAAD,EAAqBC,sBAArB,IAA+C,8DAAkC;AAC5FnK,EAAAA,OAAO,EAAE,qBADmF;AAE5FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFwE;AAG5FC,EAAAA,MAAM,EAAE,MAAMC,yBAAaiK,qBAAb,EAH8E;AAI5FhK,EAAAA,UAAU,EAAE,MAAMD,yBAAakK,yBAAb,EAJ0E;AAK5FhK,EAAAA,YAAY,EAAE;AAL8E,CAAlC,CAArD;;;AAQA,MAAM,CAACiK,kBAAD,EAAqBC,sBAArB,IAA+C,8DAAkC;AAC5FvK,EAAAA,OAAO,EAAE,qBADmF;AAE5FC,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAFwE;AAG5FC,EAAAA,MAAM,EAAE,MAAMC,yBAAaqK,qBAAb,EAH8E;AAI5FpK,EAAAA,UAAU,EAAE,MAAMD,yBAAasK,yBAAb,EAJ0E;AAK5FpK,EAAAA,YAAY,EAAE;AAL8E,CAAlC,CAArD;;;;AAQA,eAAeqK,gBAAf,CAAgCC,OAAhC,EAAiD;AACtD,MAAInK,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAON,yBAAauK,gBAAb,CAA8BC,OAA9B,CAAP;AACD;;AACD,SAAO,KAAP;AACD;;AAEM,SAASC,oBAAT,CAA8BD,OAA9B,EAA+C;AACpD,MAAInK,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAON,yBAAayK,oBAAb,CAAkCD,OAAlC,CAAP;AACD;;AACD,SAAO,KAAP;AACD;;AAEM,SAASE,iBAAT,CAA2BC,KAA3B,EAAmD;AACxD,MAAItK,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,WAAOqK,KAAK,GAAG,IAAf;AACD;;AACD,SAAOA,KAAK,GAAG,GAAf;AACD;;AAEM,MAAM,CACXC,0BADW,EAEXC,8BAFW,IAGT,8DAAkC;AACpC/K,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADgB;AAEpCC,EAAAA,MAAM,EAAE,MAAMC,yBAAa4K,0BAAb,EAFsB;AAGpC3K,EAAAA,UAAU,EAAE,MAAMD,yBAAa6K,8BAAb,EAHkB;AAIpC3K,EAAAA,YAAY,EAAE;AAJsB,CAAlC,CAHG;;;AAUA,MAAM,CAAC4K,iBAAD,EAAoBC,qBAApB,IAA6C,8DAAkC;AAC1FjL,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,KAAnB,CADsE;AAE1FC,EAAAA,MAAM,EAAE,MAAMC,yBAAa8K,iBAAb,EAF4E;AAG1F7K,EAAAA,UAAU,EAAE,MAAMD,yBAAa+K,qBAAb,EAHwE;AAI1F7K,EAAAA,YAAY,EAAE;AAJ4E,CAAlC,CAAnD;;;AAOA,MAAM,CAAC8K,qBAAD,EAAwBC,yBAAxB,IAAqD,8DAChE;AACEnL,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMC,yBAAagL,qBAAb,EAFhB;AAGE/K,EAAAA,UAAU,EAAE,MAAMD,yBAAaiL,yBAAb,EAHpB;AAIE/K,EAAAA,YAAY,EAAE;AAJhB,CADgE,CAA3D;;;AASA,MAAM,CAACgL,0BAAD,EAA6BC,8BAA7B,IAA+D,8DAC1E;AACErL,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMC,yBAAakL,0BAAb,EAFhB;AAGEjL,EAAAA,UAAU,EAAE,MAAMD,yBAAamL,8BAAb,EAHpB;AAIEjL,EAAAA,YAAY,EAAE;AAJhB,CAD0E,CAArE;;;AASA,MAAM,CAACkL,8BAAD,EAAiCC,kCAAjC,IAAuE,8DAClF;AACEvL,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMC,yBAAaoL,8BAAb,EAFhB;AAGEnL,EAAAA,UAAU,EAAE,MAAMD,yBAAaqL,kCAAb,EAHpB;AAIEnL,EAAAA,YAAY,EAAE;AAJhB,CADkF,CAA7E;;;AASA,MAAM,CAACoL,gBAAD,EAAmBC,oBAAnB,IAA2C,8DAAkC;AACxFzL,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADoE;AAExFC,EAAAA,MAAM,EAAE,MAAMC,yBAAasL,gBAAb,EAF0E;AAGxFrL,EAAAA,UAAU,EAAE,MAAMD,yBAAauL,oBAAb,EAHsE;AAIxFrL,EAAAA,YAAY,EAAE;AAJ0E,CAAlC,CAAjD;;;AAOA,MAAM,CAACsL,mBAAD,EAAsBC,uBAAtB,IAAiD,8DAAkC;AAC9F3L,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CAD0E;AAE9FC,EAAAA,MAAM,EAAE,MAAMC,yBAAawL,mBAAb,EAFgF;AAG9FvL,EAAAA,UAAU,EAAE,MAAMD,yBAAayL,uBAAb,EAH4E;AAI9FvL,EAAAA,YAAY,EAAE;AAJgF,CAAlC,CAAvD;;;AAOA,MAAM,CAACwL,yBAAD,EAA4BC,6BAA5B,IAA6D,8DACxE;AACE7L,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADtB;AAEEC,EAAAA,MAAM,EAAE,MAAMC,yBAAa0L,yBAAb,EAFhB;AAGEzL,EAAAA,UAAU,EAAE,MAAMD,yBAAa2L,6BAAb,EAHpB;AAIEzL,EAAAA,YAAY,EAAE;AAJhB,CADwE,CAAnE;;;;AAUA,MAAM0L,YAAY,GAAG,MAC1B,0DAA8B;AAC5B9L,EAAAA,kBAAkB,EAAE,CAAC,SAAD,CADQ;AAE5BC,EAAAA,MAAM,EAAE,MAAMC,yBAAa4L,YAAb,EAFc;AAG5B1L,EAAAA,YAAY,EAAE;AAHc,CAA9B,CADK;;;AAOA,MAAM,CACX2L,6BADW,EAEXC,iCAFW,IAGT,8DAAkC;AACpChM,EAAAA,kBAAkB,EAAE,CAAC,SAAD,EAAY,KAAZ,CADgB;AAEpCC,EAAAA,MAAM,EAAE,MAAMC,yBAAa6L,6BAAb,EAFsB;AAGpC5L,EAAAA,UAAU,EAAE,MAAMD,yBAAa8L,iCAAb,EAHkB;AAIpC5L,EAAAA,YAAY,EAAE;AAJsB,CAAlC,CAHG;;;AAUA,MAAM,CAAC6L,aAAD,EAAgBC,iBAAhB,IAAqC,8DAAkC;AAClFlM,EAAAA,kBAAkB,EAAE,CAAC,KAAD,CAD8D;AAElFC,EAAAA,MAAM,EAAE,MAAMC,yBAAa+L,aAAb,EAFoE;AAGlF9L,EAAAA,UAAU,EAAE,MAAMD,yBAAagM,iBAAb,EAHgE;AAIlF9L,EAAAA,YAAY,EAAE,CAAC;AAJmE,CAAlC,CAA3C;;;;AAOA,eAAe+L,cAAf,GAAgC;AACrC,MAAI5L,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB,WAAON,yBAAaiM,cAAb,EAAP;AACD;;AACD,SAAO,SAAP;AACD;;AAED,MAAMC,iBAAiB,GAAG,IAAIC,+BAAJ,CAAuBC,2BAAcpM,YAArC,CAA1B;;AACO,SAASqM,eAAT,GAA0C;AAC/C,QAAM,CAACC,YAAD,EAAeC,eAAf,IAAkC,qBAAwB,IAAxB,CAAxC;AAEA,wBAAU,MAAM;AACd,UAAMC,eAAe,GAAG,YAAY;AAClC,YAAMC,YAAoB,GAAG,MAAM/D,eAAe,EAAlD;AACA6D,MAAAA,eAAe,CAACE,YAAD,CAAf;AACD,KAHD;;AAKA,UAAMC,QAAQ,GAAI/B,KAAD,IAAmB;AAClC4B,MAAAA,eAAe,CAAC5B,KAAD,CAAf;AACD,KAFD;;AAIA6B,IAAAA,eAAe;AAEf,UAAMG,YAAY,GAAGT,iBAAiB,CAACU,WAAlB,CACnB,oCADmB,EAEnBF,QAFmB,CAArB;AAKA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAlBD,EAkBG,EAlBH;AAoBA,SAAOP,YAAP;AACD;;AAEM,SAASQ,oBAAT,GAA+C;AACpD,QAAM,CAACC,iBAAD,EAAoBC,oBAApB,IAA4C,qBAAwB,IAAxB,CAAlD;AAEA,wBAAU,MAAM;AACd,UAAMR,eAAe,GAAG,YAAY;AAClC,YAAMC,YAAoB,GAAG,MAAM/D,eAAe,EAAlD;AACAgC,MAAAA,iBAAiB,CAAC+B,YAAD,CAAjB,IAAmCO,oBAAoB,CAACP,YAAD,CAAvD;AACD,KAHD;;AAKAD,IAAAA,eAAe;;AAEf,UAAME,QAAQ,GAAI/B,KAAD,IAAmB;AAClCqC,MAAAA,oBAAoB,CAACrC,KAAD,CAApB;AACD,KAFD;;AAIA,UAAMgC,YAAY,GAAGT,iBAAiB,CAACU,WAAlB,CAA8B,gCAA9B,EAAgEF,QAAhE,CAArB;AAEA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAfD,EAeG,EAfH;AAiBA,SAAOE,iBAAP;AACD;;AAEM,SAASE,aAAT,GAA8C;AACnD,QAAM,CAACC,UAAD,EAAaC,aAAb,IAA8B,qBAA8B,EAA9B,CAApC;AAEA,wBAAU,MAAM;AACd,UAAMX,eAAe,GAAG,YAAY;AAClC,YAAMC,YAAiC,GAAG,MAAM7D,aAAa,EAA7D;AACAuE,MAAAA,aAAa,CAACV,YAAD,CAAb;AACD,KAHD;;AAKA,UAAMC,QAAQ,GAAIU,KAAD,IAAuB;AACtCD,MAAAA,aAAa,CAACC,KAAD,CAAb;AACD,KAFD;;AAIAZ,IAAAA,eAAe;AAEf,UAAMG,YAAY,GAAGT,iBAAiB,CAACU,WAAlB,CACnB,kCADmB,EAEnBF,QAFmB,CAArB;AAKA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAlBD,EAkBG,EAlBH;AAoBA,SAAOK,UAAP;AACD;;AAEM,SAASG,wBAAT,GAA8D;AACnE,SAAO,mCAAW,2CAAX,EAAwDrC,qBAAxD,EAA+E,KAA/E,CAAP;AACD;;AAEM,SAASsC,6BAAT,GAAmE;AACxE,SAAO,mCAAW,gDAAX,EAA6DpC,0BAA7D,EAAyF,KAAzF,CAAP;AACD;;AAEM,SAASqC,iCAAT,GAAuE;AAC5E,SAAO,mCAAW,oDAAX,EAAiEnC,8BAAjE,EAAiG,KAAjG,CAAP;AACD;;AAEM,SAASoC,mBAAT,GAAwD;AAC7D,SAAO,mCAAWpG,mBAAX,EAAgC,CAAC,CAAjC,CAAP;AACD;;AAEM,SAASqG,aAAT,GAAkD;AACvD,SAAO,mCAAWlK,aAAX,EAA0B,SAA1B,CAAP;AACD;;AAEM,SAASmK,mBAAT,CAA6BlD,OAA7B,EAAwE;AAC7E,QAAMmD,WAAW,GAAG,wBAAY,MAAMpD,gBAAgB,CAACC,OAAD,CAAlC,EAA6C,CAACA,OAAD,CAA7C,CAApB;AACA,SAAO,mCAAWmD,WAAX,EAAwB,KAAxB,CAAP;AACD;;AAEM,SAASC,aAAT,GAAmD;AACxD,SAAO,mCAAW/H,UAAX,EAAuB,KAAvB,CAAP;AACD;;AAEM,SAASgI,eAAT,GAAoD;AACzD,SAAO,mCAAWxM,eAAX,EAA4B,SAA5B,CAAP;AACD;;AAEM,SAASyM,aAAT,GAAwC;AAC7C,QAAM,CAACC,UAAD,EAAaC,aAAb,IAA8B,qBAAwB,IAAxB,CAApC;AAEA,wBAAU,MAAM;AACd,UAAMxB,eAAe,GAAG,YAAY;AAClC,YAAMC,YAAoB,GAAG,MAAMV,aAAa,EAAhD;AACAiC,MAAAA,aAAa,CAACvB,YAAD,CAAb;AACD,KAHD;;AAKA,UAAMC,QAAQ,GAAIuB,KAAD,IAAmB;AAClCD,MAAAA,aAAa,CAACC,KAAD,CAAb;AACD,KAFD;;AAIAzB,IAAAA,eAAe;AAEf,UAAMG,YAAY,GAAGT,iBAAiB,CAACU,WAAlB,CACnB,kCADmB,EAEnBF,QAFmB,CAArB;AAKA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAlBD,EAkBG,EAlBH;AAoBA,SAAOkB,UAAP;AACD;;AAID,MAAMG,UAA4B,GAAG;AACnCvN,EAAAA,YADmC;AAEnCC,EAAAA,gBAFmC;AAGnC8B,EAAAA,WAHmC;AAInCC,EAAAA,eAJmC;AAKnCK,EAAAA,kBALmC;AAMnC6I,EAAAA,6BANmC;AAOnCC,EAAAA,iCAPmC;AAQnC3G,EAAAA,SARmC;AASnCC,EAAAA,aATmC;AAUnCsD,EAAAA,eAVmC;AAWnCC,EAAAA,mBAXmC;AAYnC5E,EAAAA,aAZmC;AAanCC,EAAAA,iBAbmC;AAcnCnC,EAAAA,QAdmC;AAenCW,EAAAA,UAfmC;AAgBnCC,EAAAA,cAhBmC;AAiBnCS,EAAAA,cAjBmC;AAkBnCN,EAAAA,WAlBmC;AAmBnCgF,EAAAA,UAnBmC;AAoBnCC,EAAAA,cApBmC;AAqBnCpC,EAAAA,WArBmC;AAsBnCC,EAAAA,eAtBmC;AAuBnCzB,EAAAA,SAvBmC;AAwBnC9C,EAAAA,WAxBmC;AAyBnCoC,EAAAA,aAzBmC;AA0BnCC,EAAAA,iBA1BmC;AA2BnCU,EAAAA,aA3BmC;AA4BnC+H,EAAAA,cA5BmC;AA6BnCzC,EAAAA,aA7BmC;AA8BnCrF,EAAAA,UA9BmC;AA+BnCC,EAAAA,cA/BmC;AAgCnCC,EAAAA,cAhCmC;AAiCnCC,EAAAA,kBAjCmC;AAkCnC8C,EAAAA,mBAlCmC;AAmCnCC,EAAAA,uBAnCmC;AAoCnCxD,EAAAA,YApCmC;AAqCnCC,EAAAA,gBArCmC;AAsCnCwE,EAAAA,kBAtCmC;AAuCnCE,EAAAA,qBAvCmC;AAwCnCD,EAAAA,sBAxCmC;AAyCnCE,EAAAA,yBAzCmC;AA0CnClE,EAAAA,WA1CmC;AA2CnCC,EAAAA,eA3CmC;AA4CnCC,EAAAA,OA5CmC;AA6CnCC,EAAAA,WA7CmC;AA8CnCC,EAAAA,YA9CmC;AA+CnCC,EAAAA,gBA/CmC;AAgDnCe,EAAAA,cAhDmC;AAiDnCC,EAAAA,kBAjDmC;AAkDnC9C,EAAAA,uBAlDmC;AAmDnCC,EAAAA,2BAnDmC;AAoDnCuE,EAAAA,kBApDmC;AAqDnCC,EAAAA,sBArDmC;AAsDnChH,EAAAA,aAtDmC;AAuDnCC,EAAAA,iBAvDmC;AAwDnCK,EAAAA,YAxDmC;AAyDnCC,EAAAA,gBAzDmC;AA0DnC0G,EAAAA,iBA1DmC;AA2DnCC,EAAAA,qBA3DmC;AA4DnCxG,EAAAA,aA5DmC;AA6DnCC,EAAAA,iBA7DmC;AA8DnCG,EAAAA,eA9DmC;AA+DnCC,EAAAA,mBA/DmC;AAgEnC0G,EAAAA,YAhEmC;AAiEnCC,EAAAA,gBAjEmC;AAkEnCtG,EAAAA,QAlEmC;AAmEnC+F,EAAAA,cAnEmC;AAoEnCC,EAAAA,kBApEmC;AAqEnCiB,EAAAA,aArEmC;AAsEnCC,EAAAA,iBAtEmC;AAuEnCxD,EAAAA,gBAvEmC;AAwEnCC,EAAAA,oBAxEmC;AAyEnCT,EAAAA,UAzEmC;AA0EnCC,EAAAA,cA1EmC;AA2EnCxB,EAAAA,kBA3EmC;AA4EnCiC,EAAAA,gBA5EmC;AA6EnCC,EAAAA,oBA7EmC;AA8EnC/E,EAAAA,eA9EmC;AA+EnCC,EAAAA,mBA/EmC;AAgFnCkK,EAAAA,0BAhFmC;AAiFnCC,EAAAA,8BAjFmC;AAkFnC9I,EAAAA,aAlFmC;AAmFnCO,EAAAA,gBAnFmC;AAoFnCyC,EAAAA,OApFmC;AAqFnCC,EAAAA,WArFmC;AAsFnCkD,EAAAA,oBAtFmC;AAuFnCE,EAAAA,uBAvFmC;AAwFnCD,EAAAA,wBAxFmC;AAyFnCE,EAAAA,2BAzFmC;AA0FnCP,EAAAA,cA1FmC;AA2FnCC,EAAAA,kBA3FmC;AA4FnC9C,EAAAA,OA5FmC;AA6FnCC,EAAAA,WA7FmC;AA8FnCvF,EAAAA,WA9FmC;AA+FnCC,EAAAA,eA/FmC;AAgGnC6D,EAAAA,aAhGmC;AAiGnCC,EAAAA,iBAjGmC;AAkGnCC,EAAAA,YAlGmC;AAmGnCC,EAAAA,gBAnGmC;AAoGnCR,EAAAA,UApGmC;AAqGnC2I,EAAAA,aArGmC;AAsGnCC,EAAAA,iBAtGmC;AAuGnChF,EAAAA,MAvGmC;AAwGnCC,EAAAA,UAxGmC;AAyGnCC,EAAAA,MAzGmC;AA0GnCC,EAAAA,UA1GmC;AA2GnCd,EAAAA,QA3GmC;AA4GnCS,EAAAA,gBA5GmC;AA6GnCyD,EAAAA,gBA7GmC;AA8GnCE,EAAAA,oBA9GmC;AA+GnCnB,EAAAA,cA/GmC;AAgHnCC,EAAAA,kBAhHmC;AAiHnCT,EAAAA,iBAjHmC;AAkHnCC,EAAAA,qBAlHmC;AAmHnChI,EAAAA,eAnHmC;AAoHnCC,EAAAA,mBApHmC;AAqHnC6E,EAAAA,UArHmC;AAsHnCC,EAAAA,cAtHmC;AAuHnCkF,EAAAA,qBAvHmC;AAwHnCC,EAAAA,yBAxHmC;AAyHnCC,EAAAA,0BAzHmC;AA0HnCC,EAAAA,8BA1HmC;AA2HnCC,EAAAA,8BA3HmC;AA4HnCC,EAAAA,kCA5HmC;AA6HnCrC,EAAAA,WA7HmC;AA8HnCC,EAAAA,eA9HmC;AA+HnC6B,EAAAA,iBA/HmC;AAgInCC,EAAAA,qBAhImC;AAiInC7E,EAAAA,qBAjImC;AAkInCC,EAAAA,yBAlImC;AAmInCmF,EAAAA,gBAnImC;AAoInCC,EAAAA,oBApImC;AAqInCC,EAAAA,mBArImC;AAsInCC,EAAAA,uBAtImC;AAuInCG,EAAAA,YAvImC;AAwInC7F,EAAAA,QAxImC;AAyInCC,EAAAA,cAzImC;AA0InCC,EAAAA,eA1ImC;AA2InC8D,EAAAA,kBA3ImC;AA4InCC,EAAAA,sBA5ImC;AA6InCG,EAAAA,kBA7ImC;AA8InCC,EAAAA,sBA9ImC;AA+InCT,EAAAA,aA/ImC;AAgJnCC,EAAAA,iBAhJmC;AAiJnCxJ,EAAAA,YAjJmC;AAkJnCiM,EAAAA,eAlJmC;AAmJnCS,EAAAA,oBAnJmC;AAoJnCW,EAAAA,aApJmC;AAqJnCD,EAAAA,mBArJmC;AAsJnCE,EAAAA,mBAtJmC;AAuJnCE,EAAAA,aAvJmC;AAwJnCX,EAAAA,aAxJmC;AAyJnCY,EAAAA,eAzJmC;AA0JnCR,EAAAA,wBA1JmC;AA2JnCC,EAAAA,6BA3JmC;AA4JnCC,EAAAA,iCA5JmC;AA6JnCO,EAAAA,aA7JmC;AA8JnCpC,EAAAA,yBA9JmC;AA+JnCC,EAAAA;AA/JmC,CAArC;eAkKeuC,U", "sourcesContent": ["import { useCallback, useEffect, useState } from 'react';\nimport { Dimensions, NativeEventEmitter, NativeModules, Platform } from 'react-native';\nimport { useOnEvent, useOnMount } from './internal/asyncHookWrappers';\nimport devicesWithDynamicIsland from \"./internal/devicesWithDynamicIsland\";\nimport devicesWithNotch from './internal/devicesWithNotch';\nimport RNDeviceInfo from './internal/nativeInterface';\nimport {\n  getSupportedPlatformInfoAsync,\n  getSupportedPlatformInfoFunctions,\n  getSupportedPlatformInfoSync,\n} from './internal/supported-platform-info';\nimport { DeviceInfoModule } from './internal/privateTypes';\nimport type {\n  AsyncHookResult,\n  DeviceType,\n  LocationProviderInfo,\n  PowerState,\n} from './internal/types';\n\nexport const [getUniqueId, getUniqueIdSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'uniqueId',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getUniqueId(),\n  syncGetter: () => RNDeviceInfo.getUniqueIdSync(),\n  defaultValue: 'unknown',\n});\n\nlet uniqueId: string;\nexport async function syncUniqueId() {\n  if (Platform.OS === 'ios') {\n    uniqueId = await RNDeviceInfo.syncUniqueId();\n  } else {\n    uniqueId = await getUniqueId();\n  }\n  return uniqueId;\n}\n\nexport const [getInstanceId, getInstanceIdSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'instanceId',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getInstanceId(),\n  syncGetter: () => RNDeviceInfo.getInstanceIdSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getSerialNumber, getSerialNumberSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'serialNumber',\n  supportedPlatforms: ['android', 'windows'],\n  getter: () => RNDeviceInfo.getSerialNumber(),\n  syncGetter: () => RNDeviceInfo.getSerialNumberSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getAndroidId, getAndroidIdSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'androidId',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getAndroidId(),\n  syncGetter: () => RNDeviceInfo.getAndroidIdSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getIpAddress, getIpAddressSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getIpAddress(),\n  syncGetter: () => RNDeviceInfo.getIpAddressSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [isCameraPresent, isCameraPresentSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'windows', 'web'],\n  getter: () => RNDeviceInfo.isCameraPresent(),\n  syncGetter: () => RNDeviceInfo.isCameraPresentSync(),\n  defaultValue: false,\n});\n\nexport async function getMacAddress() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getMacAddress();\n  } else if (Platform.OS === 'ios') {\n    return '02:00:00:00:00:00';\n  }\n  return 'unknown';\n}\n\nexport function getMacAddressSync() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getMacAddressSync();\n  } else if (Platform.OS === 'ios') {\n    return '02:00:00:00:00:00';\n  }\n  return 'unknown';\n}\n\nexport const getDeviceId = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: 'unknown',\n    memoKey: 'deviceId',\n    getter: () => RNDeviceInfo.deviceId,\n    supportedPlatforms: ['android', 'ios', 'windows'],\n  });\n\nexport const [getManufacturer, getManufacturerSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'manufacturer',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () =>\n    Platform.OS == 'ios' ? Promise.resolve('Apple') : RNDeviceInfo.getSystemManufacturer(),\n  syncGetter: () => (Platform.OS == 'ios' ? 'Apple' : RNDeviceInfo.getSystemManufacturerSync()),\n  defaultValue: 'unknown',\n});\n\nexport const getModel = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'model',\n    defaultValue: 'unknown',\n    supportedPlatforms: ['ios', 'android', 'windows'],\n    getter: () => RNDeviceInfo.model,\n  });\n\nexport const getBrand = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'brand',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.brand,\n  });\n\nexport const getSystemName = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: 'unknown',\n    supportedPlatforms: ['ios', 'android', 'windows'],\n    memoKey: 'systemName',\n    getter: () =>\n      Platform.select({\n        ios: RNDeviceInfo.systemName,\n        android: 'Android',\n        windows: 'Windows',\n        default: 'unknown',\n      }),\n  });\n\nexport const getSystemVersion = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.systemVersion,\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    memoKey: 'systemVersion',\n  });\n\nexport const [getBuildId, getBuildIdSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'buildId',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getBuildId(),\n  syncGetter: () => RNDeviceInfo.getBuildIdSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getApiLevel, getApiLevelSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'apiLevel',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getApiLevel(),\n  syncGetter: () => RNDeviceInfo.getApiLevelSync(),\n  defaultValue: -1,\n});\n\nexport const getBundleId = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'bundleId',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.bundleId,\n  });\n\nexport const [\n  getInstallerPackageName,\n  getInstallerPackageNameSync,\n] = getSupportedPlatformInfoFunctions({\n  memoKey: 'installerPackageName',\n  supportedPlatforms: ['android', 'windows', 'ios'],\n  getter: () => RNDeviceInfo.getInstallerPackageName(),\n  syncGetter: () => RNDeviceInfo.getInstallerPackageNameSync(),\n  defaultValue: 'unknown',\n});\n\nexport const getApplicationName = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'appName',\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.appName,\n    supportedPlatforms: ['android', 'ios', 'windows'],\n  });\n\nexport const getBuildNumber = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'buildNumber',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    getter: () => RNDeviceInfo.buildNumber,\n    defaultValue: 'unknown',\n  });\n\nexport const getVersion = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'version',\n    defaultValue: 'unknown',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    getter: () => RNDeviceInfo.appVersion,\n  });\n\nexport function getReadableVersion() {\n  return getVersion() + '.' + getBuildNumber();\n}\n\nexport const [getDeviceName, getDeviceNameSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getDeviceName(),\n  syncGetter: () => RNDeviceInfo.getDeviceNameSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getUsedMemory, getUsedMemorySync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getUsedMemory(),\n  syncGetter: () => RNDeviceInfo.getUsedMemorySync(),\n  defaultValue: -1,\n});\n\nexport const getUserAgent = () =>\n  getSupportedPlatformInfoAsync({\n    memoKey: 'userAgent',\n    defaultValue: 'unknown',\n    supportedPlatforms: ['android', 'ios', 'web'],\n    getter: () => RNDeviceInfo.getUserAgent(),\n  });\n\nexport const getUserAgentSync = () =>\n  getSupportedPlatformInfoSync({\n    memoKey: 'userAgentSync',\n    defaultValue: 'unknown',\n    supportedPlatforms: ['android', 'web'],\n    getter: () => RNDeviceInfo.getUserAgentSync(),\n  });\n\nexport const [getFontScale, getFontScaleSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getFontScale(),\n  syncGetter: () => RNDeviceInfo.getFontScaleSync(),\n  defaultValue: -1,\n});\n\nexport const [getBootloader, getBootloaderSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'bootloader',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getBootloader(),\n  syncGetter: () => RNDeviceInfo.getBootloaderSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getDevice, getDeviceSync] = getSupportedPlatformInfoFunctions({\n  getter: () => RNDeviceInfo.getDevice(),\n  syncGetter: () => RNDeviceInfo.getDeviceSync(),\n  defaultValue: 'unknown',\n  memoKey: 'device',\n  supportedPlatforms: ['android'],\n});\n\nexport const [getDisplay, getDisplaySync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'display',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getDisplay(),\n  syncGetter: () => RNDeviceInfo.getDisplaySync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getFingerprint, getFingerprintSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'fingerprint',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getFingerprint(),\n  syncGetter: () => RNDeviceInfo.getFingerprintSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getHardware, getHardwareSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'hardware',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getHardware(),\n  syncGetter: () => RNDeviceInfo.getHardwareSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getHost, getHostSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'host',\n  supportedPlatforms: ['android', 'windows'],\n  getter: () => RNDeviceInfo.getHost(),\n  syncGetter: () => RNDeviceInfo.getHostSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getHostNames, getHostNamesSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'hostNames',\n  supportedPlatforms: ['windows'],\n  getter: () => RNDeviceInfo.getHostNames(),\n  syncGetter: () => RNDeviceInfo.getHostNamesSync(),\n  defaultValue: [] as string[],\n});\n\nexport const [getProduct, getProductSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'product',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getProduct(),\n  syncGetter: () => RNDeviceInfo.getProductSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getTags, getTagsSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'tags',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getTags(),\n  syncGetter: () => RNDeviceInfo.getTagsSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getType, getTypeSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'type',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getType(),\n  syncGetter: () => RNDeviceInfo.getTypeSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getBaseOs, getBaseOsSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'baseOs',\n  supportedPlatforms: ['android', 'web', 'windows'],\n  getter: () => RNDeviceInfo.getBaseOs(),\n  syncGetter: () => RNDeviceInfo.getBaseOsSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getPreviewSdkInt, getPreviewSdkIntSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'previewSdkInt',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getPreviewSdkInt(),\n  syncGetter: () => RNDeviceInfo.getPreviewSdkIntSync(),\n  defaultValue: -1,\n});\n\nexport const [getSecurityPatch, getSecurityPatchSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'securityPatch',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getSecurityPatch(),\n  syncGetter: () => RNDeviceInfo.getSecurityPatchSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getCodename, getCodenameSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'codeName',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getCodename(),\n  syncGetter: () => RNDeviceInfo.getCodenameSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getIncremental, getIncrementalSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'incremental',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getIncremental(),\n  syncGetter: () => RNDeviceInfo.getIncrementalSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [isEmulator, isEmulatorSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'emulator',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.isEmulator(),\n  syncGetter: () => RNDeviceInfo.isEmulatorSync(),\n  defaultValue: false,\n});\n\nexport const isTablet = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: false,\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    memoKey: 'tablet',\n    getter: () => RNDeviceInfo.isTablet,\n  });\n\nexport const isLowRamDevice = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: false,\n    supportedPlatforms: ['android'],\n    memoKey: 'lowRam',\n    getter: () => RNDeviceInfo.isLowRamDevice,\n  });\n\nexport const isDisplayZoomed = () =>\n  getSupportedPlatformInfoSync({\n    defaultValue: false,\n    supportedPlatforms: ['ios'],\n    memoKey: 'zoomed',\n    getter: () => RNDeviceInfo.isDisplayZoomed,\n  });\n\nexport const [isPinOrFingerprintSet, isPinOrFingerprintSetSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    getter: () => RNDeviceInfo.isPinOrFingerprintSet(),\n    syncGetter: () => RNDeviceInfo.isPinOrFingerprintSetSync(),\n    defaultValue: false,\n  }\n);\n\nlet notch: boolean;\nexport function hasNotch() {\n  if (notch === undefined) {\n    let _brand = getBrand();\n    let _model = getModel();\n    notch =\n      devicesWithNotch.findIndex(\n        (item) =>\n          item.brand.toLowerCase() === _brand.toLowerCase() &&\n          item.model.toLowerCase() === _model.toLowerCase()\n      ) !== -1;\n  }\n  return notch;\n}\n\nlet dynamicIsland: boolean;\nexport function hasDynamicIsland() {\n  if (dynamicIsland === undefined) {\n    let _brand = getBrand();\n    let _model = getModel();\n    dynamicIsland =\n      devicesWithDynamicIsland.findIndex(\n        (item) =>\n          item.brand.toLowerCase() === _brand.toLowerCase() &&\n          item.model.toLowerCase() === _model.toLowerCase()\n      ) !== -1;\n  }\n  return dynamicIsland;\n}\n\nexport const [hasGms, hasGmsSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.hasGms(),\n  syncGetter: () => RNDeviceInfo.hasGmsSync(),\n  defaultValue: false,\n});\n\nexport const [hasHms, hasHmsSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.hasHms(),\n  syncGetter: () => RNDeviceInfo.hasHmsSync(),\n  defaultValue: false,\n});\n\nexport const [getFirstInstallTime, getFirstInstallTimeSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'firstInstallTime',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getFirstInstallTime(),\n  syncGetter: () => RNDeviceInfo.getFirstInstallTimeSync(),\n  defaultValue: -1,\n});\n\nexport const [getInstallReferrer, getInstallReferrerSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'installReferrer',\n  supportedPlatforms: ['android', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getInstallReferrer(),\n  syncGetter: () => RNDeviceInfo.getInstallReferrerSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getLastUpdateTime, getLastUpdateTimeSync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'lastUpdateTime',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getLastUpdateTime(),\n  syncGetter: () => RNDeviceInfo.getLastUpdateTimeSync(),\n  defaultValue: -1,\n});\n\nexport const [getPhoneNumber, getPhoneNumberSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getPhoneNumber(),\n  syncGetter: () => RNDeviceInfo.getPhoneNumberSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getCarrier, getCarrierSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios'],\n  getter: () => RNDeviceInfo.getCarrier(),\n  syncGetter: () => RNDeviceInfo.getCarrierSync(),\n  defaultValue: 'unknown',\n});\n\nexport const [getTotalMemory, getTotalMemorySync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'totalMemory',\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getTotalMemory(),\n  syncGetter: () => RNDeviceInfo.getTotalMemorySync(),\n  defaultValue: -1,\n});\n\nexport const [getMaxMemory, getMaxMemorySync] = getSupportedPlatformInfoFunctions({\n  memoKey: 'maxMemory',\n  supportedPlatforms: ['android', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getMaxMemory(),\n  syncGetter: () => RNDeviceInfo.getMaxMemorySync(),\n  defaultValue: -1,\n});\n\nexport const [getTotalDiskCapacity, getTotalDiskCapacitySync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getTotalDiskCapacity(),\n  syncGetter: () => RNDeviceInfo.getTotalDiskCapacitySync(),\n  defaultValue: -1,\n});\n\nexport async function getTotalDiskCapacityOld() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getTotalDiskCapacityOld();\n  }\n  if (Platform.OS === 'ios' || Platform.OS === 'windows' || Platform.OS === 'web') {\n    return getTotalDiskCapacity();\n  }\n\n  return -1;\n}\n\nexport function getTotalDiskCapacityOldSync() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getTotalDiskCapacityOldSync();\n  }\n  if (Platform.OS === 'ios' || Platform.OS === 'windows' || Platform.OS === 'web') {\n    return getTotalDiskCapacitySync();\n  }\n\n  return -1;\n}\n\nexport const [getFreeDiskStorage, getFreeDiskStorageSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getFreeDiskStorage(),\n  syncGetter: () => RNDeviceInfo.getFreeDiskStorageSync(),\n  defaultValue: -1,\n});\n\nexport async function getFreeDiskStorageOld() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getFreeDiskStorageOld();\n  }\n  if (Platform.OS === 'ios' || Platform.OS === 'windows' || Platform.OS === 'web') {\n    return getFreeDiskStorage();\n  }\n\n  return -1;\n}\n\nexport function getFreeDiskStorageOldSync() {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.getFreeDiskStorageOldSync();\n  }\n  if (Platform.OS === 'ios' || Platform.OS === 'windows' || Platform.OS === 'web') {\n    return getFreeDiskStorageSync();\n  }\n\n  return -1;\n}\n\nexport const [getBatteryLevel, getBatteryLevelSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getBatteryLevel(),\n  syncGetter: () => RNDeviceInfo.getBatteryLevelSync(),\n  defaultValue: -1,\n});\n\nexport const [getPowerState, getPowerStateSync] = getSupportedPlatformInfoFunctions<\n  Partial<PowerState>\n>({\n  supportedPlatforms: ['ios', 'android', 'windows', 'web'],\n  getter: () => RNDeviceInfo.getPowerState(),\n  syncGetter: () => RNDeviceInfo.getPowerStateSync(),\n  defaultValue: {},\n});\n\nexport const [isBatteryCharging, isBatteryChargingSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'windows', 'web'],\n  getter: () => RNDeviceInfo.isBatteryCharging(),\n  syncGetter: () => RNDeviceInfo.isBatteryChargingSync(),\n  defaultValue: false,\n});\n\nexport async function isLandscape() {\n  return Promise.resolve(isLandscapeSync());\n}\n\nexport function isLandscapeSync() {\n  const { height, width } = Dimensions.get('window');\n  return width >= height;\n}\n\nexport const [isAirplaneMode, isAirplaneModeSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'web'],\n  getter: () => RNDeviceInfo.isAirplaneMode(),\n  syncGetter: () => RNDeviceInfo.isAirplaneModeSync(),\n  defaultValue: false,\n});\n\nexport const getDeviceType = () => {\n  return getSupportedPlatformInfoSync({\n    memoKey: 'deviceType',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.deviceType,\n  });\n};\n\nexport const getDeviceTypeSync = () => {\n  return getSupportedPlatformInfoSync({\n    memoKey: 'deviceType',\n    supportedPlatforms: ['android', 'ios', 'windows'],\n    defaultValue: 'unknown',\n    getter: () => RNDeviceInfo.deviceType,\n  });\n};\n\nexport const [supportedAbis, supportedAbisSync] = getSupportedPlatformInfoFunctions({\n  memoKey: '_supportedAbis',\n  supportedPlatforms: ['android', 'ios', 'windows'],\n  getter: () => RNDeviceInfo.getSupportedAbis(),\n  syncGetter: () => RNDeviceInfo.getSupportedAbisSync(),\n  defaultValue: [] as string[],\n});\n\nexport const [supported32BitAbis, supported32BitAbisSync] = getSupportedPlatformInfoFunctions({\n  memoKey: '_supported32BitAbis',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getSupported32BitAbis(),\n  syncGetter: () => RNDeviceInfo.getSupported32BitAbisSync(),\n  defaultValue: [] as string[],\n});\n\nexport const [supported64BitAbis, supported64BitAbisSync] = getSupportedPlatformInfoFunctions({\n  memoKey: '_supported64BitAbis',\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getSupported64BitAbis(),\n  syncGetter: () => RNDeviceInfo.getSupported64BitAbisSync(),\n  defaultValue: [],\n});\n\nexport async function hasSystemFeature(feature: string) {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.hasSystemFeature(feature);\n  }\n  return false;\n}\n\nexport function hasSystemFeatureSync(feature: string) {\n  if (Platform.OS === 'android') {\n    return RNDeviceInfo.hasSystemFeatureSync(feature);\n  }\n  return false;\n}\n\nexport function isLowBatteryLevel(level: number): boolean {\n  if (Platform.OS === 'android') {\n    return level < 0.15;\n  }\n  return level < 0.2;\n}\n\nexport const [\n  getSystemAvailableFeatures,\n  getSystemAvailableFeaturesSync,\n] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android'],\n  getter: () => RNDeviceInfo.getSystemAvailableFeatures(),\n  syncGetter: () => RNDeviceInfo.getSystemAvailableFeaturesSync(),\n  defaultValue: [] as string[],\n});\n\nexport const [isLocationEnabled, isLocationEnabledSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios', 'web'],\n  getter: () => RNDeviceInfo.isLocationEnabled(),\n  syncGetter: () => RNDeviceInfo.isLocationEnabledSync(),\n  defaultValue: false,\n});\n\nexport const [isHeadphonesConnected, isHeadphonesConnectedSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android', 'ios'],\n    getter: () => RNDeviceInfo.isHeadphonesConnected(),\n    syncGetter: () => RNDeviceInfo.isHeadphonesConnectedSync(),\n    defaultValue: false,\n  }\n);\n\nexport const [isWiredHeadphonesConnected, isWiredHeadphonesConnectedSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android', 'ios'],\n    getter: () => RNDeviceInfo.isWiredHeadphonesConnected(),\n    syncGetter: () => RNDeviceInfo.isWiredHeadphonesConnectedSync(),\n    defaultValue: false,\n  }\n);\n\nexport const [isBluetoothHeadphonesConnected, isBluetoothHeadphonesConnectedSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android', 'ios'],\n    getter: () => RNDeviceInfo.isBluetoothHeadphonesConnected(),\n    syncGetter: () => RNDeviceInfo.isBluetoothHeadphonesConnectedSync(),\n    defaultValue: false,\n  }\n);\n\nexport const [isMouseConnected, isMouseConnectedSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['windows'],\n  getter: () => RNDeviceInfo.isMouseConnected(),\n  syncGetter: () => RNDeviceInfo.isMouseConnectedSync(),\n  defaultValue: false,\n});\n\nexport const [isKeyboardConnected, isKeyboardConnectedSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['windows'],\n  getter: () => RNDeviceInfo.isKeyboardConnected(),\n  syncGetter: () => RNDeviceInfo.isKeyboardConnectedSync(),\n  defaultValue: false,\n});\n\nexport const [getSupportedMediaTypeList, getSupportedMediaTypeListSync] = getSupportedPlatformInfoFunctions(\n  {\n    supportedPlatforms: ['android'],\n    getter: () => RNDeviceInfo.getSupportedMediaTypeList(),\n    syncGetter: () => RNDeviceInfo.getSupportedMediaTypeListSync(),\n    defaultValue: []\n  }\n)\n\n\nexport const isTabletMode = () =>\n  getSupportedPlatformInfoAsync({\n    supportedPlatforms: ['windows'],\n    getter: () => RNDeviceInfo.isTabletMode(),\n    defaultValue: false,\n  });\n\nexport const [\n  getAvailableLocationProviders,\n  getAvailableLocationProvidersSync,\n] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['android', 'ios'],\n  getter: () => RNDeviceInfo.getAvailableLocationProviders(),\n  syncGetter: () => RNDeviceInfo.getAvailableLocationProvidersSync(),\n  defaultValue: {},\n});\n\nexport const [getBrightness, getBrightnessSync] = getSupportedPlatformInfoFunctions({\n  supportedPlatforms: ['ios'],\n  getter: () => RNDeviceInfo.getBrightness(),\n  syncGetter: () => RNDeviceInfo.getBrightnessSync(),\n  defaultValue: -1,\n});\n\nexport async function getDeviceToken() {\n  if (Platform.OS === 'ios') {\n    return RNDeviceInfo.getDeviceToken();\n  }\n  return 'unknown';\n}\n\nconst deviceInfoEmitter = new NativeEventEmitter(NativeModules.RNDeviceInfo);\nexport function useBatteryLevel(): number | null {\n  const [batteryLevel, setBatteryLevel] = useState<number | null>(null);\n\n  useEffect(() => {\n    const setInitialValue = async () => {\n      const initialValue: number = await getBatteryLevel();\n      setBatteryLevel(initialValue);\n    };\n\n    const onChange = (level: number) => {\n      setBatteryLevel(level);\n    };\n\n    setInitialValue();\n\n    const subscription = deviceInfoEmitter.addListener(\n      'RNDeviceInfo_batteryLevelDidChange',\n      onChange\n    );\n\n    return () => subscription.remove();\n  }, []);\n\n  return batteryLevel;\n}\n\nexport function useBatteryLevelIsLow(): number | null {\n  const [batteryLevelIsLow, setBatteryLevelIsLow] = useState<number | null>(null);\n\n  useEffect(() => {\n    const setInitialValue = async () => {\n      const initialValue: number = await getBatteryLevel();\n      isLowBatteryLevel(initialValue) && setBatteryLevelIsLow(initialValue);\n    };\n\n    setInitialValue();\n\n    const onChange = (level: number) => {\n      setBatteryLevelIsLow(level);\n    };\n\n    const subscription = deviceInfoEmitter.addListener('RNDeviceInfo_batteryLevelIsLow', onChange);\n\n    return () => subscription.remove();\n  }, []);\n\n  return batteryLevelIsLow;\n}\n\nexport function usePowerState(): Partial<PowerState> {\n  const [powerState, setPowerState] = useState<Partial<PowerState>>({});\n\n  useEffect(() => {\n    const setInitialValue = async () => {\n      const initialValue: Partial<PowerState> = await getPowerState();\n      setPowerState(initialValue);\n    };\n\n    const onChange = (state: PowerState) => {\n      setPowerState(state);\n    };\n\n    setInitialValue();\n\n    const subscription = deviceInfoEmitter.addListener(\n      'RNDeviceInfo_powerStateDidChange',\n      onChange\n    );\n\n    return () => subscription.remove();\n  }, []);\n\n  return powerState;\n}\n\nexport function useIsHeadphonesConnected(): AsyncHookResult<boolean> {\n  return useOnEvent('RNDeviceInfo_headphoneConnectionDidChange', isHeadphonesConnected, false);\n}\n\nexport function useIsWiredHeadphonesConnected(): AsyncHookResult<boolean> {\n  return useOnEvent('RNDeviceInfo_headphoneWiredConnectionDidChange', isWiredHeadphonesConnected, false);\n}\n\nexport function useIsBluetoothHeadphonesConnected(): AsyncHookResult<boolean> {\n  return useOnEvent('RNDeviceInfo_headphoneBluetoothConnectionDidChange', isBluetoothHeadphonesConnected, false);\n}\n\nexport function useFirstInstallTime(): AsyncHookResult<number> {\n  return useOnMount(getFirstInstallTime, -1);\n}\n\nexport function useDeviceName(): AsyncHookResult<string> {\n  return useOnMount(getDeviceName, 'unknown');\n}\n\nexport function useHasSystemFeature(feature: string): AsyncHookResult<boolean> {\n  const asyncGetter = useCallback(() => hasSystemFeature(feature), [feature]);\n  return useOnMount(asyncGetter, false);\n}\n\nexport function useIsEmulator(): AsyncHookResult<boolean> {\n  return useOnMount(isEmulator, false);\n}\n\nexport function useManufacturer(): AsyncHookResult<string> {\n  return useOnMount(getManufacturer, 'unknown');\n}\n\nexport function useBrightness(): number | null {\n  const [brightness, setBrightness] = useState<number | null>(null);\n\n  useEffect(() => {\n    const setInitialValue = async () => {\n      const initialValue: number = await getBrightness();\n      setBrightness(initialValue);\n    };\n\n    const onChange = (value: number) => {\n      setBrightness(value);\n    };\n\n    setInitialValue();\n\n    const subscription = deviceInfoEmitter.addListener(\n      'RNDeviceInfo_brightnessDidChange',\n      onChange\n    );\n\n    return () => subscription.remove();\n  }, []);\n\n  return brightness;\n}\n\nexport type { AsyncHookResult, DeviceType, LocationProviderInfo, PowerState };\n\nconst DeviceInfo: DeviceInfoModule = {\n  getAndroidId,\n  getAndroidIdSync,\n  getApiLevel,\n  getApiLevelSync,\n  getApplicationName,\n  getAvailableLocationProviders,\n  getAvailableLocationProvidersSync,\n  getBaseOs,\n  getBaseOsSync,\n  getBatteryLevel,\n  getBatteryLevelSync,\n  getBootloader,\n  getBootloaderSync,\n  getBrand,\n  getBuildId,\n  getBuildIdSync,\n  getBuildNumber,\n  getBundleId,\n  getCarrier,\n  getCarrierSync,\n  getCodename,\n  getCodenameSync,\n  getDevice,\n  getDeviceId,\n  getDeviceName,\n  getDeviceNameSync,\n  getDeviceSync,\n  getDeviceToken,\n  getDeviceType,\n  getDisplay,\n  getDisplaySync,\n  getFingerprint,\n  getFingerprintSync,\n  getFirstInstallTime,\n  getFirstInstallTimeSync,\n  getFontScale,\n  getFontScaleSync,\n  getFreeDiskStorage,\n  getFreeDiskStorageOld,\n  getFreeDiskStorageSync,\n  getFreeDiskStorageOldSync,\n  getHardware,\n  getHardwareSync,\n  getHost,\n  getHostSync,\n  getHostNames,\n  getHostNamesSync,\n  getIncremental,\n  getIncrementalSync,\n  getInstallerPackageName,\n  getInstallerPackageNameSync,\n  getInstallReferrer,\n  getInstallReferrerSync,\n  getInstanceId,\n  getInstanceIdSync,\n  getIpAddress,\n  getIpAddressSync,\n  getLastUpdateTime,\n  getLastUpdateTimeSync,\n  getMacAddress,\n  getMacAddressSync,\n  getManufacturer,\n  getManufacturerSync,\n  getMaxMemory,\n  getMaxMemorySync,\n  getModel,\n  getPhoneNumber,\n  getPhoneNumberSync,\n  getPowerState,\n  getPowerStateSync,\n  getPreviewSdkInt,\n  getPreviewSdkIntSync,\n  getProduct,\n  getProductSync,\n  getReadableVersion,\n  getSecurityPatch,\n  getSecurityPatchSync,\n  getSerialNumber,\n  getSerialNumberSync,\n  getSystemAvailableFeatures,\n  getSystemAvailableFeaturesSync,\n  getSystemName,\n  getSystemVersion,\n  getTags,\n  getTagsSync,\n  getTotalDiskCapacity,\n  getTotalDiskCapacityOld,\n  getTotalDiskCapacitySync,\n  getTotalDiskCapacityOldSync,\n  getTotalMemory,\n  getTotalMemorySync,\n  getType,\n  getTypeSync,\n  getUniqueId,\n  getUniqueIdSync,\n  getUsedMemory,\n  getUsedMemorySync,\n  getUserAgent,\n  getUserAgentSync,\n  getVersion,\n  getBrightness,\n  getBrightnessSync,\n  hasGms,\n  hasGmsSync,\n  hasHms,\n  hasHmsSync,\n  hasNotch,\n  hasDynamicIsland,\n  hasSystemFeature,\n  hasSystemFeatureSync,\n  isAirplaneMode,\n  isAirplaneModeSync,\n  isBatteryCharging,\n  isBatteryChargingSync,\n  isCameraPresent,\n  isCameraPresentSync,\n  isEmulator,\n  isEmulatorSync,\n  isHeadphonesConnected,\n  isHeadphonesConnectedSync,\n  isWiredHeadphonesConnected,\n  isWiredHeadphonesConnectedSync,\n  isBluetoothHeadphonesConnected,\n  isBluetoothHeadphonesConnectedSync,\n  isLandscape,\n  isLandscapeSync,\n  isLocationEnabled,\n  isLocationEnabledSync,\n  isPinOrFingerprintSet,\n  isPinOrFingerprintSetSync,\n  isMouseConnected,\n  isMouseConnectedSync,\n  isKeyboardConnected,\n  isKeyboardConnectedSync,\n  isTabletMode,\n  isTablet,\n  isLowRamDevice,\n  isDisplayZoomed,\n  supported32BitAbis,\n  supported32BitAbisSync,\n  supported64BitAbis,\n  supported64BitAbisSync,\n  supportedAbis,\n  supportedAbisSync,\n  syncUniqueId,\n  useBatteryLevel,\n  useBatteryLevelIsLow,\n  useDeviceName,\n  useFirstInstallTime,\n  useHasSystemFeature,\n  useIsEmulator,\n  usePowerState,\n  useManufacturer,\n  useIsHeadphonesConnected,\n  useIsWiredHeadphonesConnected,\n  useIsBluetoothHeadphonesConnected,\n  useBrightness,\n  getSupportedMediaTypeList,\n  getSupportedMediaTypeListSync\n};\n\nexport default DeviceInfo;\n"]}