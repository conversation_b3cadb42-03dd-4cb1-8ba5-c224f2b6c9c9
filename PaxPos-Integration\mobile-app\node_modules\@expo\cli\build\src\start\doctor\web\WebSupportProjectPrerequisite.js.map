{"version": 3, "sources": ["../../../../../src/start/doctor/web/WebSupportProjectPrerequisite.ts"], "sourcesContent": ["import {\n  AppJSONConfig,\n  ExpoConfig,\n  getConfig,\n  getProjectConfigDescriptionWithPaths,\n  ProjectConfig,\n} from '@expo/config';\nimport chalk from 'chalk';\n\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { getPlatformBundlers } from '../../server/platformBundlers';\nimport { PrerequisiteCommandError, ProjectPrerequisite } from '../Prerequisite';\nimport { ensureDependenciesAsync } from '../dependencies/ensureDependenciesAsync';\nimport { ResolvedPackage } from '../dependencies/getMissingPackages';\n\nconst debug = require('debug')('expo:doctor:webSupport') as typeof console.log;\n\n/** Ensure the project has the required web support settings. */\nexport class WebSupportProjectPrerequisite extends ProjectPrerequisite {\n  /** Ensure a project that hasn't explicitly disabled web support has all the required packages for running in the browser. */\n  async assertImplementation(): Promise<void> {\n    if (env.EXPO_NO_WEB_SETUP) {\n      Log.warn('Skipping web setup: EXPO_NO_WEB_SETUP is enabled.');\n      return;\n    }\n    debug('Ensuring web support is setup');\n\n    const result = await this._shouldSetupWebSupportAsync();\n\n    // Ensure web packages are installed\n    await this._ensureWebDependenciesInstalledAsync({ exp: result.exp });\n  }\n\n  /** Exposed for testing. */\n  async _shouldSetupWebSupportAsync(): Promise<ProjectConfig> {\n    const config = getConfig(this.projectRoot);\n\n    // Detect if the 'web' string is purposefully missing from the platforms array.\n    if (isWebPlatformExcluded(config.rootConfig)) {\n      // Get exact config description with paths.\n      const configName = getProjectConfigDescriptionWithPaths(this.projectRoot, config);\n      throw new PrerequisiteCommandError(\n        'WEB_SUPPORT',\n        chalk`Skipping web setup: {bold \"web\"} is not included in the project ${configName} {bold \"platforms\"} array.`\n      );\n    }\n\n    return config;\n  }\n\n  /** Exposed for testing. */\n  async _ensureWebDependenciesInstalledAsync({ exp }: { exp: ExpoConfig }): Promise<boolean> {\n    const requiredPackages: ResolvedPackage[] = [\n      { file: 'react-dom/package.json', pkg: 'react-dom' },\n    ];\n    if (!env.EXPO_NO_REACT_NATIVE_WEB) {\n      // use react-native-web/package.json to skip node module cache issues when the user installs\n      // the package and attempts to resolve the module in the same process.\n      requiredPackages.push({ file: 'react-native-web/package.json', pkg: 'react-native-web' });\n    }\n\n    const bundler = getPlatformBundlers(this.projectRoot, exp).web;\n    // Only include webpack-config if bundler is webpack.\n    if (bundler === 'webpack') {\n      requiredPackages.push(\n        // `webpack` and `webpack-dev-server` should be installed in the `@expo/webpack-config`\n        {\n          file: '@expo/webpack-config/package.json',\n          pkg: '@expo/webpack-config',\n          dev: true,\n        }\n      );\n    } else if (bundler === 'metro') {\n      requiredPackages.push({\n        file: '@expo/metro-runtime/package.json',\n        pkg: '@expo/metro-runtime',\n      });\n    }\n\n    try {\n      return await ensureDependenciesAsync(this.projectRoot, {\n        // This never seems to work when prompting, installing, and running -- instead just inform the user to run the install command and try again.\n        skipPrompt: true,\n        isProjectMutable: false,\n        exp,\n        installMessage: `It looks like you're trying to use web support but don't have the required dependencies installed.`,\n        warningMessage: chalk`If you're not using web, please ensure you remove the {bold \"web\"} string from the platforms array in the project Expo config.`,\n        requiredPackages,\n      });\n    } catch (error) {\n      // Reset the cached check so we can re-run the check if the user re-runs the command by pressing 'w' in the Terminal UI.\n      this.resetAssertion();\n      throw error;\n    }\n  }\n}\n\n/** Return `true` if the `web` platform is purposefully excluded from the project Expo config. */\nexport function isWebPlatformExcluded(rootConfig: AppJSONConfig): boolean {\n  // Detect if the 'web' string is purposefully missing from the platforms array.\n  const isWebExcluded =\n    Array.isArray(rootConfig?.expo?.platforms) &&\n    !!rootConfig.expo?.platforms.length &&\n    !rootConfig.expo?.platforms.includes('web');\n  return isWebExcluded;\n}\n"], "names": ["WebSupportProjectPrerequisite", "isWebPlatformExcluded", "debug", "require", "ProjectPrerequisite", "assertImplementation", "env", "EXPO_NO_WEB_SETUP", "Log", "warn", "result", "_shouldSetupWebSupportAsync", "_ensureWebDependenciesInstalledAsync", "exp", "config", "getConfig", "projectRoot", "rootConfig", "config<PERSON><PERSON>", "getProjectConfigDescriptionWithPaths", "PrerequisiteCommandError", "chalk", "requiredPackages", "file", "pkg", "EXPO_NO_REACT_NATIVE_WEB", "push", "bundler", "getPlatformBundlers", "web", "dev", "ensureDependenciesAsync", "skip<PERSON>rompt", "isProjectMutable", "installMessage", "warningMessage", "error", "resetAssertion", "isWebExcluded", "Array", "isArray", "expo", "platforms", "length", "includes"], "mappings": ";;;;;;;;;;;IAmBaA,6BAA6B;eAA7BA;;IAgFGC,qBAAqB;eAArBA;;;;yBA7FT;;;;;;;gEACW;;;;;;6DAEG;qBACD;kCACgB;8BAC0B;yCACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGxC,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,MAAMH,sCAAsCI,iCAAmB;IACpE,2HAA2H,GAC3H,MAAMC,uBAAsC;QAC1C,IAAIC,QAAG,CAACC,iBAAiB,EAAE;YACzBC,KAAIC,IAAI,CAAC;YACT;QACF;QACAP,MAAM;QAEN,MAAMQ,SAAS,MAAM,IAAI,CAACC,2BAA2B;QAErD,oCAAoC;QACpC,MAAM,IAAI,CAACC,oCAAoC,CAAC;YAAEC,KAAKH,OAAOG,GAAG;QAAC;IACpE;IAEA,yBAAyB,GACzB,MAAMF,8BAAsD;QAC1D,MAAMG,SAASC,IAAAA,mBAAS,EAAC,IAAI,CAACC,WAAW;QAEzC,+EAA+E;QAC/E,IAAIf,sBAAsBa,OAAOG,UAAU,GAAG;YAC5C,2CAA2C;YAC3C,MAAMC,aAAaC,IAAAA,8CAAoC,EAAC,IAAI,CAACH,WAAW,EAAEF;YAC1E,MAAM,IAAIM,sCAAwB,CAChC,eACAC,IAAAA,gBAAK,CAAA,CAAC,gEAAgE,EAAEH,WAAW,0BAA0B,CAAC;QAElH;QAEA,OAAOJ;IACT;IAEA,yBAAyB,GACzB,MAAMF,qCAAqC,EAAEC,GAAG,EAAuB,EAAoB;QACzF,MAAMS,mBAAsC;YAC1C;gBAAEC,MAAM;gBAA0BC,KAAK;YAAY;SACpD;QACD,IAAI,CAAClB,QAAG,CAACmB,wBAAwB,EAAE;YACjC,4FAA4F;YAC5F,sEAAsE;YACtEH,iBAAiBI,IAAI,CAAC;gBAAEH,MAAM;gBAAiCC,KAAK;YAAmB;QACzF;QAEA,MAAMG,UAAUC,IAAAA,qCAAmB,EAAC,IAAI,CAACZ,WAAW,EAAEH,KAAKgB,GAAG;QAC9D,qDAAqD;QACrD,IAAIF,YAAY,WAAW;YACzBL,iBAAiBI,IAAI,CACnB,uFAAuF;YACvF;gBACEH,MAAM;gBACNC,KAAK;gBACLM,KAAK;YACP;QAEJ,OAAO,IAAIH,YAAY,SAAS;YAC9BL,iBAAiBI,IAAI,CAAC;gBACpBH,MAAM;gBACNC,KAAK;YACP;QACF;QAEA,IAAI;YACF,OAAO,MAAMO,IAAAA,gDAAuB,EAAC,IAAI,CAACf,WAAW,EAAE;gBACrD,6IAA6I;gBAC7IgB,YAAY;gBACZC,kBAAkB;gBAClBpB;gBACAqB,gBAAgB,CAAC,kGAAkG,CAAC;gBACpHC,gBAAgBd,IAAAA,gBAAK,CAAA,CAAC,8HAA8H,CAAC;gBACrJC;YACF;QACF,EAAE,OAAOc,OAAO;YACd,wHAAwH;YACxH,IAAI,CAACC,cAAc;YACnB,MAAMD;QACR;IACF;AACF;AAGO,SAASnC,sBAAsBgB,UAAyB;QAG7CA,kBACZA,mBACDA;IAJH,+EAA+E;IAC/E,MAAMqB,gBACJC,MAAMC,OAAO,CAACvB,+BAAAA,mBAAAA,WAAYwB,IAAI,qBAAhBxB,iBAAkByB,SAAS,KACzC,CAAC,GAACzB,oBAAAA,WAAWwB,IAAI,qBAAfxB,kBAAiByB,SAAS,CAACC,MAAM,KACnC,GAAC1B,oBAAAA,WAAWwB,IAAI,qBAAfxB,kBAAiByB,SAAS,CAACE,QAAQ,CAAC;IACvC,OAAON;AACT"}