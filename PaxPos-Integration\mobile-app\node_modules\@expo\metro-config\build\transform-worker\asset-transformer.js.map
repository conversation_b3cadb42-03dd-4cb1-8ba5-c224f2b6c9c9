{"version": 3, "file": "asset-transformer.js", "sourceRoot": "", "sources": ["../../src/transform-worker/asset-transformer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,8BAuHC;AAvJD;;;;;;;;;GASG;AACH,+DAAuC;AACvC,gDAAkC;AAClC,iDAAkE;AAElE,0DAA6B;AAC7B,wDAA2B;AAE3B,2CAAoD;AACpD,gDAAgD;AAEhD,0EAA0E;AAC1E,MAAM,2BAA2B,GAAG,kBAAQ,CAAC,SAAS,CACpD,iGAAiG,CAClG,CAAC;AAEF,MAAM,cAAc,GAAG,kBAAQ,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;AAEzE,MAAM,oBAAoB,GAAG,kBAAQ,CAAC,SAAS;AAC7C,8FAA8F;AAC9F,oEAAoE,CACrE,CAAC;AAEK,KAAK,UAAU,SAAS,CAC7B,EACE,QAAQ,EACR,OAAO,GAOR,EACD,iBAAyB,EACzB,gBAAmC;IAKnC,OAAO,KAAK,OAAO,IAAI;QACrB,QAAQ,EAAE,EAAE;QACZ,WAAW,EAAE,EAAE;KAChB,CAAC;IAEF,2BAA2B;IAC3B,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,sBAAsB,EAAE,GAAG,CAAC;IACzF,MAAM,cAAc,GAAG,OAAO,CAAC,sBAAsB,EAAE,cAAc,CAAC;IACtE,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;IAC9D,MAAM,aAAa,GAAG,OAAO,CAAC,sBAAsB,EAAE,WAAW,KAAK,cAAc,CAAC;IACrF,MAAM,WAAW,GAAG,aAAa,IAAI,OAAO,CAAC,sBAAsB,EAAE,WAAW,KAAK,MAAM,CAAC;IAE5F,MAAM,YAAY,GAAG,mBAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAEjE,MAAM,kBAAkB,GAAG,GAAG,EAAE,CAC9B,aAAa,CAAC,CAAC,CAAC,kBAAG,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;IAEnE,IACE,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK;QACzB,iGAAiG;QACjG,cAAc,CAAC;QACjB,iFAAiF;QACjF,iHAAiH;QACjH,aAAa,EACb,CAAC;QACD,OAAO;YACL,GAAG,EAAE;gBACH,GAAG,CAAC,CAAC,IAAI,CACP,CAAC,CAAC,OAAO,CAAC;oBACR,2BAA2B,CAAC;wBAC1B,SAAS,EAAE,IAAI,CAAC,SAAS,CACvB,KAAK,IAAA,sBAAW,EAAC,mBAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,EAAE,CACrE;qBACF,CAAC;iBACH,CAAC,CACH;gBACD,MAAM,EAAE,EAAE;aACX;YACD,oBAAoB,EAAE,kBAAkB,EAAG;SAC5C,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,GAAG,MAAM,IAAA,iCAAqB,EACtC,YAAY,EACZ,QAAQ,EACR,gBAAgB,EAChB,OAAO,CAAC,QAAQ,EAChB,cAAc,IAAI,QAAQ;QACxB,CAAC,CAAC,0HAA0H;YAC1H,+CAA+C;YAC/C,4BAA4B;QAC9B,CAAC,CAAC,OAAO,CAAC,UAAU,CACvB,CAAC;IAEF,IAAI,WAAW,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;QAC9C,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/C,IAAI,SAAiB,CAAC;QACtB,IAAI,cAAc,EAAE,CAAC;YACnB,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAC/B,CAAC;aAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YACrB,SAAS,GAAG,IAAI,CAAC,kBAAkB,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACvF,CAAC;QAED,iGAAiG;QACjG,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YAC9C,OAAO;gBACL,GAAG,EAAE;oBACH,GAAG,CAAC,CAAC,IAAI,CACP,CAAC,CAAC,OAAO,CAAC;wBACR,oBAAoB,CAAC;4BACnB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;4BACpC,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,EAAE;4BACjF,MAAM,EACJ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,EAAE;yBAC/E,CAAC;qBACH,CAAC,CACH;oBACD,MAAM,EAAE,EAAE;iBACX;gBACD,oBAAoB,EAAE,kBAAkB,EAAE;aAC3C,CAAC;QACJ,CAAC;QAED,oEAAoE;QACpE,mCAAmC;QACnC,OAAO;YACL,GAAG,EAAE;gBACH,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAChF,MAAM,EAAE,EAAE;aACX;YACD,oBAAoB,EAAE,kBAAkB,EAAE;SAC3C,CAAC;IACJ,CAAC;IAED,OAAO;QACL,GAAG,EAAE;YACH,GAAG,IAAA,+BAAwB,EAAC,iBAAiB,EAAE,IAAI,CAAC;YACpD,MAAM,EAAE,EAAE;SACX;KACF,CAAC;AACJ,CAAC"}