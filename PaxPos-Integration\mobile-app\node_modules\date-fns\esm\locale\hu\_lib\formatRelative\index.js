var accusativeWeekdays = ['vas<PERSON>rnap', 'hétfőn', 'kedden', 'szerd<PERSON>', 'cs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'p<PERSON><PERSON><PERSON>', 'szombaton'];
function week(isFuture) {
  return function (date) {
    var weekday = accusativeWeekdays[date.getUTCDay()];
    var prefix = isFuture ? '' : "'múlt' ";
    return "".concat(prefix, "'").concat(weekday, "' p'-kor'");
  };
}
var formatRelativeLocale = {
  lastWeek: week(false),
  yesterday: "'tegnap' p'-kor'",
  today: "'ma' p'-kor'",
  tomorrow: "'holnap' p'-kor'",
  nextWeek: week(true),
  other: 'P'
};
var formatRelative = function formatRelative(token, date) {
  var format = formatRelativeLocale[token];
  if (typeof format === 'function') {
    return format(date);
  }
  return format;
};
export default formatRelative;