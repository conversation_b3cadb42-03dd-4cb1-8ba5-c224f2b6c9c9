# PAX Hardware Integration

This document explains how the PAX A920PFG terminal integration works with the React frontend deployed on Vercel.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Vercel Cloud  │    │  Local Machine   │    │  PAX Terminal   │
│                 │    │                  │    │                 │
│  React App      │◄──►│  Frontend        │◄──►│  A920PFG        │
│  (UI/UX)        │    │  (PAX Service)   │    │  ************** │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Key Features

### 🌐 Cloud-Local Hybrid
- **Frontend**: Deployed on Vercel (cloud)
- **Hardware Communication**: Runs locally on the same machine as PAX terminal
- **Seamless Integration**: Cloud UI triggers local hardware actions

### 🖥️ Terminal Specifications
- **Model**: PAX A920PFG
- **IP Address**: **************
- **Port**: 10009
- **Software Version**: 1.00.42.11303

### 🔧 Technical Implementation

#### 1. Integrated PAX Service (`integratedPaxService.ts`)
```typescript
// Direct hardware communication without separate server
class IntegratedPaxService {
  // Simulates PAX SDK integration
  // In production: loads actual PAX SDK
  // Handles: payments, receipts, status checks
}
```

#### 2. React Hook (`usePaxHardware.ts`)
```typescript
// Provides React components with PAX functionality
const { processPayment, printReceipt, isReady } = usePaxHardware();
```

#### 3. Payment Terminal Component (`PaxPaymentTerminal.tsx`)
```typescript
// Complete UI for PAX terminal operations
<PaxPaymentTerminal 
  onPaymentComplete={handlePayment}
  onReceiptPrinted={handleReceipt}
/>
```

## Usage Examples

### Process Payment
```typescript
const paymentRequest = {
  amount: 25.99,
  transactionId: 'TXN_123456'
};

const result = await processPayment(paymentRequest);
if (result.success) {
  console.log('Payment successful:', result.transactionId);
}
```

### Print Receipt
```typescript
const receiptData = {
  transactionId: 'TXN_123456',
  amount: 25.99,
  cardInfo: { last4: '1234', brand: 'VISA' },
  timestamp: new Date().toISOString(),
  merchantInfo: {
    name: 'Your Store',
    address: '123 Store Street'
  }
};

const success = await printReceipt(receiptData);
```

## Deployment Strategy

### Development
1. Run React app locally: `npm run dev`
2. PAX service auto-initializes
3. Test with simulated hardware

### Production
1. Deploy React app to Vercel
2. Local machine runs the same frontend code
3. PAX service detects local hardware
4. Cloud UI triggers local hardware actions

## File Structure

```
frontend/
├── src/
│   ├── services/
│   │   ├── integratedPaxService.ts    # Core PAX integration
│   │   └── paxHardwareService.ts      # Legacy service (reference)
│   ├── hooks/
│   │   └── usePaxHardware.ts          # React hook for PAX
│   ├── components/
│   │   └── PaxPaymentTerminal.tsx     # Terminal UI component
│   └── pages/
│       └── PaxDemo.tsx                # Demo page
└── PAX_INTEGRATION.md                 # This documentation
```

## Benefits

### ✅ Advantages
- **No separate server needed**: Everything runs in the frontend
- **Cloud deployment**: UI can be hosted on Vercel
- **Local hardware access**: Direct communication with PAX terminal
- **Real-time operations**: Instant payment processing and receipt printing
- **Unified codebase**: Same code works locally and in cloud

### 🔄 How It Works
1. **Cloud UI**: User interacts with Vercel-deployed interface
2. **Local Execution**: Frontend code runs locally on machine with PAX terminal
3. **Hardware Communication**: Direct SDK calls to PAX A920PFG
4. **Receipt Printing**: Immediate local printing triggered from cloud UI

## Next Steps

### For Production Implementation
1. **Install PAX SDK**: Add actual PAX SDK files
2. **Hardware Testing**: Test with real PAX A920PFG terminal
3. **Error Handling**: Implement robust error handling
4. **Security**: Add payment security measures
5. **Logging**: Implement transaction logging

### For Development
1. **Run Demo**: `npm run dev` and visit `/pax-demo`
2. **Test Payments**: Use the payment terminal component
3. **Check Logs**: Monitor console for PAX service messages
4. **Customize**: Modify components for your specific needs

## Support

For issues with PAX integration:
1. Check terminal IP connectivity (**************)
2. Verify PAX SDK installation
3. Review browser console logs
4. Test with PAX demo page

---

*This integration enables a React app deployed on Vercel to seamlessly communicate with local PAX hardware for payment processing and receipt printing.*
