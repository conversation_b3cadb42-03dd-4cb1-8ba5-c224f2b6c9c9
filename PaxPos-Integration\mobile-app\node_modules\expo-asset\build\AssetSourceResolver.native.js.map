{"version": 3, "file": "AssetSourceResolver.native.js", "sourceRoot": "", "sources": ["../src/AssetSourceResolver.native.ts"], "names": [], "mappings": "AAAA,OAAO,mBAAmB,MAAM,kDAAkD,CAAC;AACnF,eAAe,mBAAmB,CAAC;AACnC,cAAc,kDAAkD,CAAC", "sourcesContent": ["import AssetSourceResolver from 'react-native/Libraries/Image/AssetSourceResolver';\nexport default AssetSourceResolver;\nexport * from 'react-native/Libraries/Image/AssetSourceResolver';\n"]}