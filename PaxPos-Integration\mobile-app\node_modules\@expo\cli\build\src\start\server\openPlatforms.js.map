{"version": 3, "sources": ["../../../../src/start/server/openPlatforms.ts"], "sourcesContent": ["import { DevServerManager } from './DevServerManager';\nimport { AbortCommandError } from '../../utils/errors';\nimport { Options } from '../resolveOptions';\n\n/** Launch the app on various platforms in parallel. */\nexport async function openPlatformsAsync(\n  devServerManager: DevServerManager,\n  options: Pick<Options, 'ios' | 'android' | 'web'>\n) {\n  const results = await Promise.allSettled([\n    options.android ? devServerManager.getDefaultDevServer().openPlatformAsync('emulator') : null,\n    options.ios ? devServerManager.getDefaultDevServer().openPlatformAsync('simulator') : null,\n    options.web\n      ? devServerManager\n          .ensureWebDevServerRunningAsync()\n          .then(() => devServerManager.getWebDevServer()?.openPlatformAsync('desktop'))\n      : null,\n  ]);\n\n  const errors = results\n    .map((result) => (result.status === 'rejected' ? result.reason : null))\n    .filter(Boolean);\n\n  if (errors.length) {\n    // ctrl+c\n    const isEscapedError = errors.some((error: any) => error.code === 'ABORTED');\n    if (isEscapedError) {\n      throw new AbortCommandError();\n    }\n    throw errors[0];\n  }\n\n  return !!options.android || !!options.ios;\n}\n"], "names": ["openPlatformsAsync", "devServerManager", "options", "results", "Promise", "allSettled", "android", "getDefaultDevServer", "openPlatformAsync", "ios", "web", "ensureWebDevServerRunningAsync", "then", "getWebDevServer", "errors", "map", "result", "status", "reason", "filter", "Boolean", "length", "isEscapedError", "some", "error", "code", "AbortCommandError"], "mappings": ";;;;+BAKsBA;;;eAAAA;;;wBAJY;AAI3B,eAAeA,mBACpBC,gBAAkC,EAClCC,OAAiD;IAEjD,MAAMC,UAAU,MAAMC,QAAQC,UAAU,CAAC;QACvCH,QAAQI,OAAO,GAAGL,iBAAiBM,mBAAmB,GAAGC,iBAAiB,CAAC,cAAc;QACzFN,QAAQO,GAAG,GAAGR,iBAAiBM,mBAAmB,GAAGC,iBAAiB,CAAC,eAAe;QACtFN,QAAQQ,GAAG,GACPT,iBACGU,8BAA8B,GAC9BC,IAAI,CAAC;gBAAMX;oBAAAA,oCAAAA,iBAAiBY,eAAe,uBAAhCZ,kCAAoCO,iBAAiB,CAAC;aACpE;KACL;IAED,MAAMM,SAASX,QACZY,GAAG,CAAC,CAACC,SAAYA,OAAOC,MAAM,KAAK,aAAaD,OAAOE,MAAM,GAAG,MAChEC,MAAM,CAACC;IAEV,IAAIN,OAAOO,MAAM,EAAE;QACjB,SAAS;QACT,MAAMC,iBAAiBR,OAAOS,IAAI,CAAC,CAACC,QAAeA,MAAMC,IAAI,KAAK;QAClE,IAAIH,gBAAgB;YAClB,MAAM,IAAII,yBAAiB;QAC7B;QACA,MAAMZ,MAAM,CAAC,EAAE;IACjB;IAEA,OAAO,CAAC,CAACZ,QAAQI,OAAO,IAAI,CAAC,CAACJ,QAAQO,GAAG;AAC3C"}