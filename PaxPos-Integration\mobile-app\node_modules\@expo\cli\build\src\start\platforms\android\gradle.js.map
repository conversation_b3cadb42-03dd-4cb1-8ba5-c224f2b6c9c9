{"version": 3, "sources": ["../../../../../src/start/platforms/android/gradle.ts"], "sourcesContent": ["import spawnAsync, { SpawnResult } from '@expo/spawn-async';\nimport path from 'path';\n\nimport { env } from '../../../utils/env';\nimport { AbortCommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:platforms:android:gradle') as typeof console.log;\n\nfunction upperFirst(name: string) {\n  return name.charAt(0).toUpperCase() + name.slice(1);\n}\n\n/** Format gradle assemble arguments. Exposed for testing.  */\nexport function formatGradleArguments(\n  cmd: 'assemble' | 'install',\n  {\n    appName,\n    variant,\n    tasks = [cmd + upperFirst(variant)],\n  }: { tasks?: string[]; variant: string; appName: string }\n): string[] {\n  return appName ? tasks.map((task) => `${appName}:${task}`) : tasks;\n}\n\nfunction resolveGradleWPath(androidProjectPath: string): string {\n  return path.join(androidProjectPath, process.platform === 'win32' ? 'gradlew.bat' : 'gradlew');\n}\n\nfunction getPortArg(port: number): string {\n  return `-PreactNativeDevServerPort=${port}`;\n}\n\nfunction getActiveArchArg(architectures: string): string {\n  return `-PreactNativeArchitectures=${architectures}`;\n}\n\n/**\n * Build the Android project using Gradle.\n *\n * @param androidProjectPath - Path to the Android project like `projectRoot/android`.\n * @param props.variant - Variant to install.\n * @param props.appName - Name of the 'app' folder, this appears to always be `app`.\n * @param props.port - Dev server port to pass to the install command.\n * @param props.buildCache - Should use the `--build-cache` flag, enabling the [Gradle build cache](https://docs.gradle.org/current/userguide/build_cache.html).\n * @param props.architectures - Architectures to build for.\n * @returns - A promise resolving to spawn results.\n */\nexport async function assembleAsync(\n  androidProjectPath: string,\n  {\n    variant,\n    port,\n    appName,\n    buildCache,\n    architectures,\n    eagerBundleOptions,\n  }: {\n    variant: string;\n    port?: number;\n    appName: string;\n    buildCache?: boolean;\n    architectures?: string;\n    eagerBundleOptions?: string;\n  }\n): Promise<SpawnResult> {\n  const task = formatGradleArguments('assemble', { variant, appName });\n  const args = [\n    ...task,\n    // ignore linting errors\n    '-x',\n    'lint',\n    // ignore tests\n    '-x',\n    'test',\n    '--configure-on-demand',\n  ];\n\n  if (buildCache) args.push('--build-cache');\n\n  // Generate a profile under `/android/app/build/reports/profile`\n  if (env.EXPO_PROFILE) args.push('--profile');\n\n  return await spawnGradleAsync(androidProjectPath, {\n    port,\n    architectures,\n    args,\n    env: eagerBundleOptions\n      ? {\n          __EXPO_EAGER_BUNDLE_OPTIONS: eagerBundleOptions,\n        }\n      : {},\n  });\n}\n\n/**\n * Install an app on device or emulator using `gradlew install`.\n *\n * @param androidProjectPath - Path to the Android project like `projectRoot/android`.\n * @param props.variant - Variant to install.\n * @param props.appName - Name of the 'app' folder, this appears to always be `app`.\n * @param props.port - Dev server port to pass to the install command.\n * @returns - A promise resolving to spawn results.\n */\nexport async function installAsync(\n  androidProjectPath: string,\n  {\n    variant,\n    appName,\n    port,\n  }: {\n    variant: string;\n    appName: string;\n    port?: number;\n  }\n): Promise<SpawnResult> {\n  const args = formatGradleArguments('install', { variant, appName });\n  return await spawnGradleAsync(androidProjectPath, { port, args });\n}\n\nexport async function spawnGradleAsync(\n  projectRoot: string,\n  {\n    port,\n    architectures,\n    args,\n    env,\n  }: { port?: number; architectures?: string; args: string[]; env?: Record<string, string> }\n): Promise<SpawnResult> {\n  const gradlew = resolveGradleWPath(projectRoot);\n  if (port != null) args.push(getPortArg(port));\n  if (architectures) args.push(getActiveArchArg(architectures));\n  debug(`  ${gradlew} ${args.join(' ')}`);\n  try {\n    return await spawnAsync(gradlew, args, {\n      cwd: projectRoot,\n      stdio: 'inherit',\n      env: {\n        ...process.env,\n        ...(env ?? {}),\n      },\n    });\n  } catch (error: any) {\n    // User aborted the command with ctrl-c\n    if (error.status === 130) {\n      // Fail silently\n      throw new AbortCommandError();\n    }\n    throw error;\n  }\n}\n"], "names": ["assembleAsync", "formatGradleArguments", "installAsync", "spawnGradleAsync", "debug", "require", "upperFirst", "name", "char<PERSON>t", "toUpperCase", "slice", "cmd", "appName", "variant", "tasks", "map", "task", "resolveGradleWPath", "androidProjectPath", "path", "join", "process", "platform", "getPortArg", "port", "getActiveArchArg", "architectures", "buildCache", "eagerBundleOptions", "args", "push", "env", "EXPO_PROFILE", "__EXPO_EAGER_BUNDLE_OPTIONS", "projectRoot", "gradlew", "spawnAsync", "cwd", "stdio", "error", "status", "AbortCommandError"], "mappings": ";;;;;;;;;;;IA+CsBA,aAAa;eAAbA;;IAlCNC,qBAAqB;eAArBA;;IA0FMC,YAAY;eAAZA;;IAgBAC,gBAAgB;eAAhBA;;;;gEAvHkB;;;;;;;gEACvB;;;;;;qBAEG;wBACc;;;;;;AAElC,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,SAASC,WAAWC,IAAY;IAC9B,OAAOA,KAAKC,MAAM,CAAC,GAAGC,WAAW,KAAKF,KAAKG,KAAK,CAAC;AACnD;AAGO,SAAST,sBACdU,GAA2B,EAC3B,EACEC,OAAO,EACPC,OAAO,EACPC,QAAQ;IAACH,MAAML,WAAWO;CAAS,EACoB;IAEzD,OAAOD,UAAUE,MAAMC,GAAG,CAAC,CAACC,OAAS,GAAGJ,QAAQ,CAAC,EAAEI,MAAM,IAAIF;AAC/D;AAEA,SAASG,mBAAmBC,kBAA0B;IACpD,OAAOC,eAAI,CAACC,IAAI,CAACF,oBAAoBG,QAAQC,QAAQ,KAAK,UAAU,gBAAgB;AACtF;AAEA,SAASC,WAAWC,IAAY;IAC9B,OAAO,CAAC,2BAA2B,EAAEA,MAAM;AAC7C;AAEA,SAASC,iBAAiBC,aAAqB;IAC7C,OAAO,CAAC,2BAA2B,EAAEA,eAAe;AACtD;AAaO,eAAe1B,cACpBkB,kBAA0B,EAC1B,EACEL,OAAO,EACPW,IAAI,EACJZ,OAAO,EACPe,UAAU,EACVD,aAAa,EACbE,kBAAkB,EAQnB;IAED,MAAMZ,OAAOf,sBAAsB,YAAY;QAAEY;QAASD;IAAQ;IAClE,MAAMiB,OAAO;WACRb;QACH,wBAAwB;QACxB;QACA;QACA,eAAe;QACf;QACA;QACA;KACD;IAED,IAAIW,YAAYE,KAAKC,IAAI,CAAC;IAE1B,gEAAgE;IAChE,IAAIC,QAAG,CAACC,YAAY,EAAEH,KAAKC,IAAI,CAAC;IAEhC,OAAO,MAAM3B,iBAAiBe,oBAAoB;QAChDM;QACAE;QACAG;QACAE,KAAKH,qBACD;YACEK,6BAA6BL;QAC/B,IACA,CAAC;IACP;AACF;AAWO,eAAe1B,aACpBgB,kBAA0B,EAC1B,EACEL,OAAO,EACPD,OAAO,EACPY,IAAI,EAKL;IAED,MAAMK,OAAO5B,sBAAsB,WAAW;QAAEY;QAASD;IAAQ;IACjE,OAAO,MAAMT,iBAAiBe,oBAAoB;QAAEM;QAAMK;IAAK;AACjE;AAEO,eAAe1B,iBACpB+B,WAAmB,EACnB,EACEV,IAAI,EACJE,aAAa,EACbG,IAAI,EACJE,GAAG,EACqF;IAE1F,MAAMI,UAAUlB,mBAAmBiB;IACnC,IAAIV,QAAQ,MAAMK,KAAKC,IAAI,CAACP,WAAWC;IACvC,IAAIE,eAAeG,KAAKC,IAAI,CAACL,iBAAiBC;IAC9CtB,MAAM,CAAC,EAAE,EAAE+B,QAAQ,CAAC,EAAEN,KAAKT,IAAI,CAAC,MAAM;IACtC,IAAI;QACF,OAAO,MAAMgB,IAAAA,qBAAU,EAACD,SAASN,MAAM;YACrCQ,KAAKH;YACLI,OAAO;YACPP,KAAK;gBACH,GAAGV,QAAQU,GAAG;gBACd,GAAIA,OAAO,CAAC,CAAC;YACf;QACF;IACF,EAAE,OAAOQ,OAAY;QACnB,uCAAuC;QACvC,IAAIA,MAAMC,MAAM,KAAK,KAAK;YACxB,gBAAgB;YAChB,MAAM,IAAIC,yBAAiB;QAC7B;QACA,MAAMF;IACR;AACF"}