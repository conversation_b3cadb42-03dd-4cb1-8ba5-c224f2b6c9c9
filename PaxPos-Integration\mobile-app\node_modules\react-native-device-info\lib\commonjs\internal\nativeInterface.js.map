{"version": 3, "sources": ["nativeInterface.ts"], "names": ["RNDeviceInfo", "NativeModules", "Platform", "OS", "require", "Error"], "mappings": ";;;;;;;AAAA;;AAGA,IAAIA,YAAgD,GAAGC,2BAAcD,YAArE,C,CAEA;;AACA,IAAIE,sBAASC,EAAT,KAAgB,KAAhB,IAAyBD,sBAASC,EAAT,KAAgB,KAA7C,EAAoD;AAClDH,EAAAA,YAAY,GAAGI,OAAO,CAAC,QAAD,CAAtB;AACD;;AAED,IAAI,CAACJ,YAAL,EAAmB;AACjB;AACA,MACEE,sBAASC,EAAT,KAAgB,SAAhB,IACAD,sBAASC,EAAT,KAAgB,KADhB,IAEAD,sBAASC,EAAT,KAAgB,KAFhB,IAGA;AACAD,wBAASC,EAAT,KAAgB,KALlB,EAME;AACA,UAAM,IAAIE,KAAJ,CAAW;AACrB;AACA;AACA;AACA,sJAJU,CAAN;AAKD;AACF;;eAEcL,Y", "sourcesContent": ["import { Platform, NativeModules } from 'react-native';\nimport { DeviceInfoNativeModule } from './privateTypes';\n\nlet RNDeviceInfo: DeviceInfoNativeModule | undefined = NativeModules.RNDeviceInfo;\n\n// @ts-ignore\nif (Platform.OS === 'web' || Platform.OS === 'dom') {\n  RNDeviceInfo = require('../web');\n}\n\nif (!RNDeviceInfo) {\n  // Produce an error if we don't have the native module\n  if (\n    Platform.OS === 'android' ||\n    Platform.OS === 'ios' ||\n    Platform.OS === 'web' ||\n    // @ts-ignore\n    Platform.OS === 'dom'\n  ) {\n    throw new Error(`react-native-device-info: NativeModule.RNDeviceInfo is null. To fix this issue try these steps:\n  • For react-native <= 0.59: Run \\`react-native link react-native-device-info\\` in the project root.\n  • Rebuild and re-run the app.\n  • If you are using CocoaPods on iOS, run \\`pod install\\` in the \\`ios\\` directory and then rebuild and re-run the app. You may also need to re-open Xcode to get the new pods.\n  If none of these fix the issue, please open an issue on the Github repository: https://github.com/react-native-device-info/react-native-device-info`);\n  }\n}\n\nexport default RNDeviceInfo as DeviceInfoNativeModule;\n"]}