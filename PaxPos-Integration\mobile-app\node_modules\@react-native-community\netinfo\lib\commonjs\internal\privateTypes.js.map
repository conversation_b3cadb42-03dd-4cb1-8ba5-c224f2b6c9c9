{"version": 3, "sources": ["privateTypes.ts"], "names": ["DEVICE_CONNECTIVITY_EVENT"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIO,MAAMA,yBAAyB,GAAG,gCAAlC,C,CAEP", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {NetInfoConfiguration, NetInfoState} from './types';\n\nexport const DEVICE_CONNECTIVITY_EVENT = 'netInfo.networkStatusDidChange';\n\n// Certain properties are optional when sent by the native module and are handled by the JS code\nexport type NetInfoNativeModuleState = Pick<\n  NetInfoState,\n  Exclude<keyof NetInfoState, 'isInternetReachable'>\n> & {isInternetReachable?: boolean};\n\nexport interface Events {\n  [DEVICE_CONNECTIVITY_EVENT]: NetInfoNativeModuleState;\n}\n\nexport interface NetInfoNativeModule {\n  configure: (config: Partial<NetInfoConfiguration>) => void;\n  getCurrentState: (\n    requestedInterface?: string,\n  ) => Promise<NetInfoNativeModuleState>;\n  addListener<K extends keyof Events>(\n    type: K,\n    listener: (event: Events[K]) => void,\n  ): void;\n  removeListeners<K extends keyof Events>(\n    type: K,\n    listener: (event: Events[K]) => void,\n  ): void;\n}\n\nexport type NetInfoInternetReachabilityChangeListener = (\n  isInternetReachable: boolean | null | undefined,\n) => void;\n"]}