{"version": 3, "file": "FontAwesome6.js", "sourceRoot": "", "sources": ["../src/FontAwesome6.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,QAAQ,MAAM,oEAAoE,CAAC;AAC1F,OAAO,QAAQ,MAAM,yEAAyE,CAAC;AAE/F,MAAM,OAAO,GAAG;IACd,OAAO,EAAE,OAAO,CAAC,mEAAmE,CAAC;IACrF,KAAK,EAAE,OAAO,CAAC,mEAAmE,CAAC;IACnF,KAAK,EAAE,OAAO,CAAC,iEAAiE,CAAC;IACjF,MAAM,EAAE,OAAO,CAAC,kEAAkE,CAAC;IACnF,aAAa,EAAE,OAAO,CAAC,mEAAmE,CAAC;IAC3F,WAAW,EAAE,OAAO,CAAC,mEAAmE,CAAC;IACzF,WAAW,EAAE,OAAO,CAAC,iEAAiE,CAAC;IACvF,OAAO,EAAE,OAAO,CAAC,iEAAiE,CAAC;IACnF,IAAI,EAAE,OAAO,CAAC,mEAAmE,CAAC;CACnF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,UAAU,EAAE,YAAY;IACxB,UAAU,EAAE,YAAY;IACxB,OAAO,EAAE,SAAS;IAClB,IAAI,EAAE,MAAM;CACb,CAAC;AAEF,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAErE,eAAe,OAAO,CAAC", "sourcesContent": ["\"use client\";\n\nimport { createFA6iconSet } from './createIconSetFromFontAwesome6';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/FontAwesome6Free.json';\nimport metadata from './vendor/react-native-vector-icons/glyphmaps/FontAwesome6Free_meta.json';\n\nconst fontMap = {\n  Regular: require('./vendor/react-native-vector-icons/Fonts/FontAwesome6_Regular.ttf'),\n  Light: require('./vendor/react-native-vector-icons/Fonts/FontAwesome6_Regular.ttf'),\n  Solid: require('./vendor/react-native-vector-icons/Fonts/FontAwesome6_Solid.ttf'),\n  Brands: require('./vendor/react-native-vector-icons/Fonts/FontAwesome6_Brands.ttf'),\n  Sharp_Regular: require('./vendor/react-native-vector-icons/Fonts/FontAwesome6_Regular.ttf'),\n  Sharp_Light: require('./vendor/react-native-vector-icons/Fonts/FontAwesome6_Regular.ttf'),\n  Sharp_Solid: require('./vendor/react-native-vector-icons/Fonts/FontAwesome6_Solid.ttf'),\n  Duotone: require('./vendor/react-native-vector-icons/Fonts/FontAwesome6_Solid.ttf'),\n  Thin: require('./vendor/react-native-vector-icons/Fonts/FontAwesome6_Regular.ttf'),\n};\n\nexport const FA6Style = {\n  regular: 'regular',\n  light: 'light',\n  solid: 'solid',\n  brand: 'brand',\n  sharp: 'sharp',\n  sharpLight: 'sharpLight',\n  sharpSolid: 'sharpSolid',\n  duotone: 'duotone',\n  thin: 'thin',\n};\n\nconst iconSet = createFA6iconSet(glyphMap, metadata, fontMap, false);\n\nexport default iconSet;\n"]}