import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/legacy.js";const o={coverage:"Coverage",showCoverage:"Show Coverage",instrumentCoverage:"Instrument coverage",stopInstrumentingCoverageAndShow:"Stop instrumenting coverage and show results",startInstrumentingCoverageAnd:"Start instrumenting coverage and reload page",clearCoverage:"Clear coverage",exportCoverage:"Export coverage"},a=e.i18n.registerUIStrings("panels/coverage/coverage-meta.ts",o),r=e.i18n.getLazilyComputedLocalizedString.bind(void 0,a);let n;async function i(){return n||(n=await import("./coverage.js")),n}function g(e){return void 0===n?[]:e(n)}t.ViewManager.registerViewExtension({location:"drawer-view",id:"coverage",title:r(o.coverage),commandPrompt:r(o.showCoverage),persistence:"closeable",order:100,loadView:async()=>(await i()).CoverageView.CoverageView.instance()}),t.ActionRegistration.registerActionExtension({actionId:"coverage.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,loadActionDelegate:async()=>new((await i()).CoverageView.ActionDelegate),category:"PERFORMANCE",options:[{value:!0,title:r(o.instrumentCoverage)},{value:!1,title:r(o.stopInstrumentingCoverageAndShow)}]}),t.ActionRegistration.registerActionExtension({actionId:"coverage.start-with-reload",iconClass:"refresh",loadActionDelegate:async()=>new((await i()).CoverageView.ActionDelegate),category:"PERFORMANCE",title:r(o.startInstrumentingCoverageAnd)}),t.ActionRegistration.registerActionExtension({actionId:"coverage.clear",iconClass:"clear",category:"PERFORMANCE",title:r(o.clearCoverage),loadActionDelegate:async()=>new((await i()).CoverageView.ActionDelegate),contextTypes:()=>g((e=>[e.CoverageView.CoverageView]))}),t.ActionRegistration.registerActionExtension({actionId:"coverage.export",iconClass:"download",category:"PERFORMANCE",title:r(o.exportCoverage),loadActionDelegate:async()=>new((await i()).CoverageView.ActionDelegate),contextTypes:()=>g((e=>[e.CoverageView.CoverageView]))});
