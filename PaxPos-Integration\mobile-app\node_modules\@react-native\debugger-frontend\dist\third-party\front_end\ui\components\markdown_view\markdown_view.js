import*as e from"../../legacy/legacy.js";import*as t from"../../../core/host/host.js";import*as o from"../../../core/i18n/i18n.js";import*as r from"../../../third_party/codemirror.next/codemirror.next.js";import*as s from"../text_editor/text_editor.js";import*as i from"../icon_button/icon_button.js";import*as n from"../../lit-html/lit-html.js";import*as a from"../../visual_logging/visual_logging.js";const c=new CSSStyleSheet;c.replaceSync("*{margin:0;padding:0;box-sizing:border-box}code{color:var(--sys-color-on-surface);font-size:11px;user-select:text;cursor:text;background:var(--sys-color-surface1)}.codeblock{margin-bottom:8px;box-sizing:border-box;border-radius:4px;background-color:var(--sys-color-surface5);color:var(--sys-color-on-surface)}.codeblock .toolbar{box-sizing:border-box;display:flex;height:28px;flex-direction:row;padding:0 11px;font-size:11px;font-style:normal;font-weight:400;line-height:16px}.codeblock .editor-wrapper{color:var(--sys-color-on-surface);background:var(--sys-color-surface1);padding:10px 5px}.codeblock .lang{padding:6px 0;flex:1}.codeblock .copy{padding:4px 0;align-items:center;display:flex}.codeblock .copy-button{margin:0;padding:0;background:transparent;border:none;font-size:11px;line-height:16px;display:flex;flex-direction:row;align-items:center;gap:4px}.codeblock .copy-button.copied{color:var(--sys-color-primary);--copy-icon-color:var(--sys-color-primary)}.notice{margin-top:3px;margin-left:3px;.link{color:var(--sys-color-primary);text-decoration-line:underline}}\n/*# sourceURL=codeBlock.css */\n");const l={copy:"Copy code",copied:"Copied to clipboard",disclaimer:"Use code snippets with caution"},d=o.i18n.registerUIStrings("ui/components/markdown_view/CodeBlock.ts",l),h=o.i18n.getLocalizedString.bind(void 0,d);class p extends HTMLElement{static litTagName=n.literal`devtools-code-block`;#e=this.attachShadow({mode:"open"});#t="";#o="";#r=1e3;#s;#i=!1;#n;#a=new r.Compartment;#c=!1;#l=!0;connectedCallback(){this.#e.adoptedStyleSheets=[c],this.#d()}set code(e){this.#t=e,this.#n=r.EditorState.create({doc:this.#t,extensions:[s.Config.baseConfiguration(this.#t),r.EditorState.readOnly.of(!0),r.EditorView.lineWrapping,this.#a.of(r.javascript.javascript())]}),this.#d()}get code(){return this.#t}set codeLang(e){this.#o=e,this.#d()}set timeout(e){this.#r=e,this.#d()}set displayNotice(e){this.#c=e,this.#d()}set displayToolbar(e){this.#l=e,this.#d()}#h(){t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.#t),this.#i=!0,this.#d(),clearTimeout(this.#s),this.#s=setTimeout((()=>{this.#i=!1,this.#d()}),this.#r)}#d(){const e=n.Directives.classMap({copied:this.#i,"copy-button":!0});n.render(n.html`<div class="codeblock" jslog=${a.section("code")}>
      ${this.#l?n.html`<div class="toolbar" jslog=${a.toolbar()}>
        <div class="lang">${this.#o}</div>
        <div class="copy">
          <button class=${e}
            title=${h(l.copy)}
            jslog=${a.action("copy").track({click:!0})}
            @click=${this.#h}>
            <${i.Icon.Icon.litTagName}
              .data=${{iconName:"copy",width:"16px",height:"16px",color:"var(--copy-icon-color, var(--icon-default))"}}
            >
            </${i.Icon.Icon.litTagName}>
            <span>${this.#i?h(l.copied):h(l.copy)}</span>
          </button>
        </div>
      </div>`:""}
      <div class="editor-wrapper">
        <${s.TextEditor.TextEditor.litTagName} .state=${this.#n}></${s.TextEditor.TextEditor.litTagName}>
        ${this.#c?n.html`<p class="notice">
          <x-link class="link" href="https://support.google.com/legal/answer/13505487" jslog=${a.link("code-disclaimer").track({click:!0})}>
            ${h(l.disclaimer)}
         </x-link>
        </p>`:n.nothing}
      </div>
    </div>`,this.#e,{host:this});const t=this.#e?.querySelector("devtools-text-editor")?.editor;if(!t)return;let o=r.html.html({autoCloseTags:!1,selfClosingTags:!0});switch(this.#o){case"js":o=r.javascript.javascript();break;case"ts":o=r.javascript.javascript({typescript:!0});break;case"jsx":o=r.javascript.javascript({jsx:!0});break;case"css":o=r.css.css()}t.dispatch({effects:this.#a.reconfigure(o)})}}customElements.define("devtools-code-block",p);var m=Object.freeze({__proto__:null,CodeBlock:p});const g=new CSSStyleSheet;g.replaceSync(".markdown-image{display:block}\n/*# sourceURL=markdownImage.css */\n");const u=new Map([]),k=e=>{const t=u.get(e);if(!t)throw new Error(`Markdown image with key '${e}' is not available, please check MarkdownImagesMap.ts`);return t};var w=Object.freeze({__proto__:null,markdownImages:u,getMarkdownImage:k});class x extends HTMLElement{static litTagName=n.literal`devtools-markdown-image`;#e=this.attachShadow({mode:"open"});#p;#m;connectedCallback(){this.#e.adoptedStyleSheets=[g]}set data(e){const{key:t,title:o}=e,r=k(t);this.#p=r,this.#m=o,this.#d()}#g(){if(!this.#p)return n.html``;const{src:e,color:t,width:o="100%",height:r="100%"}=this.#p;return n.html`
      <${i.Icon.Icon.litTagName} .data=${{iconPath:e,color:t,width:o,height:r}}></${i.Icon.Icon.litTagName}>
    `}#u(){if(!this.#p)return n.html``;const{src:e,width:t="100%",height:o="100%"}=this.#p;return n.html`
      <img class="markdown-image" src=${e} alt=${this.#m} width=${t} height=${o}/>
    `}#d(){if(!this.#p)return;const{isIcon:e}=this.#p,t=e?this.#g():this.#u();n.render(t,this.#e,{host:this})}}customElements.define("devtools-markdown-image",x);var y=Object.freeze({__proto__:null,MarkdownImage:x});const b=new CSSStyleSheet;b.replaceSync(".devtools-link{color:var(--sys-color-primary);outline-offset:2px;text-decoration:none}.devtools-link:hover{text-decoration:underline}\n/*# sourceURL=markdownLink.css */\n");const v=new Map([["issuesContrastWCAG21AA","https://www.w3.org/TR/WCAG21/#contrast-minimum"],["issuesContrastWCAG21AAA","https://www.w3.org/TR/WCAG21/#contrast-enhanced"],["issuesContrastSuggestColor","https://developers.google.com/web/updates/2020/08/devtools#accessible-color"],["issuesCSPSetStrict","https://web.dev/strict-csp"],["issuesCSPWhyStrictOverAllowlist","https://web.dev/strict-csp/#why-a-strict-csp-is-recommended-over-allowlist-csps"],["issueCorsPreflightRequest","https://web.dev/cross-origin-resource-sharing/#preflight-requests-for-complex-http-calls"],["issueQuirksModeDoctype","https://web.dev/doctype/"],["sameSiteAndSameOrigin","https://web.dev/same-site-same-origin/"],["punycodeReference","https://wikipedia.org/wiki/Punycode"],["https://xhr.spec.whatwg.org/","https://xhr.spec.whatwg.org/"],["https://goo.gle/chrome-insecure-origins","https://goo.gle/chrome-insecure-origins"],["https://webrtc.org/web-apis/chrome/unified-plan/","https://webrtc.org/web-apis/chrome/unified-plan/"],["https://developer.chrome.com/blog/enabling-shared-array-buffer/","https://developer.chrome.com/blog/enabling-shared-array-buffer/"],["https://developer.chrome.com/docs/extensions/mv3/","https://developer.chrome.com/docs/extensions/mv3/"],["https://developer.chrome.com/blog/immutable-document-domain/","https://developer.chrome.com/blog/immutable-document-domain/"],["https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md","https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md"],["https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled","https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled"],["PNASecureContextRestrictionFeatureStatus","https://chromestatus.com/feature/5954091755241472"],["https://w3c.github.io/uievents/#legacy-event-types","https://w3c.github.io/uievents/#legacy-event-types"],["manageCookiesHelpPage","https://support.google.com/chrome/answer/95647"],["gracePeriodStagedControlExplainer","https://developers.google.com/privacy-sandbox/blog/grace-period-opt-out"]]),f=e=>{if(/^https:\/\/www.chromestatus.com\/feature\/\d+$/.test(e))return e;const t=v.get(e);if(!t)throw new Error(`Markdown link with key '${e}' is not available, please check MarkdownLinksMap.ts`);return t};var T=Object.freeze({__proto__:null,markdownLinks:v,getMarkdownLink:f});class $ extends HTMLElement{static litTagName=n.literal`devtools-markdown-link`;#e=this.attachShadow({mode:"open"});#k="";#w="";connectedCallback(){this.#e.adoptedStyleSheets=[b]}set data(e){const{key:t,title:o}=e,r=f(t);this.#k=o,this.#w=r,this.#d()}#d(){const e=n.html`<x-link class="devtools-link" href=${this.#w} jslog=${a.link().track({click:!0})}
    >${this.#k}</x-link>`;n.render(e,this.#e,{host:this})}}customElements.define("devtools-markdown-link",$);var S=Object.freeze({__proto__:null,MarkdownLink:$});const C=new CSSStyleSheet;C.replaceSync(".message{line-height:18px;font-size:12px;color:var(--override-markdown-view-message-color,--sys-color-token-subtle);margin-bottom:4px;user-select:text}.message p{margin-bottom:10px;margin-block-start:2px}.message ul{list-style-type:none;padding-inline-start:1em}.message ul ul{padding-inline-start:19px}.message li{margin-top:8px;display:list-item;list-style-type:disc}.message code{color:var(--sys-color-on-surface);font-size:11px;user-select:text;cursor:text;background:var(--sys-color-surface1)}.devtools-link{color:var(--sys-color-primary);outline-offset:2px;text-decoration:none}.devtools-link:hover{text-decoration:underline}\n/*# sourceURL=markdownView.css */\n");const _=n.html,j=n.render;class I extends HTMLElement{static litTagName=n.literal`devtools-markdown-view`;#e=this.attachShadow({mode:"open"});#x=[];#y=new M;connectedCallback(){this.#e.adoptedStyleSheets=[C]}set data(e){this.#x=e.tokens,e.renderer&&(this.#y=e.renderer),this.#b()}#b(){this.#d()}#d(){j(_`
      <div class='message'>
        ${this.#x.map((e=>this.#y.renderToken(e)))}
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-markdown-view",I);class M{renderChildTokens(e){if("tokens"in e&&e.tokens)return e.tokens.map((e=>this.renderToken(e)));throw new Error("Tokens not found")}unescape(e){const t=new Map([["&amp;","&"],["&lt;","<"],["&gt;",">"],["&quot;",'"'],["&#39;","'"]]);return e.replace(/&(amp|lt|gt|quot|#39);/g,(e=>{const o=t.get(e);return o||e}))}renderText(e){return"tokens"in e&&e.tokens?_`${this.renderChildTokens(e)}`:_`${this.unescape("text"in e?e.text:"")}`}renderHeading(e){switch(e.depth){case 1:return _`<h1>${this.renderText(e)}</h1>`;case 2:return _`<h2>${this.renderText(e)}</h2>`;case 3:return _`<h3>${this.renderText(e)}</h3>`;case 4:return _`<h4>${this.renderText(e)}</h4>`;case 5:return _`<h5>${this.renderText(e)}</h5>`;default:return _`<h6>${this.renderText(e)}</h6>`}}renderCodeBlock(e){return _`<${p.litTagName}
      .code=${this.unescape(e.text)}
      .codeLang=${e.lang}>
    </${p.litTagName}>`}templateForToken(e){switch(e.type){case"paragraph":return _`<p>${this.renderChildTokens(e)}`;case"list":return _`<ul>${e.items.map((e=>this.renderToken(e)))}</ul>`;case"list_item":return _`<li>${this.renderChildTokens(e)}`;case"text":return this.renderText(e);case"codespan":return _`<code>${this.unescape(e.text)}</code>`;case"code":return this.renderCodeBlock(e);case"space":return _``;case"link":return _`<${$.litTagName} .data=${{key:e.href,title:e.text}}></${$.litTagName}>`;case"image":return _`<${x.litTagName} .data=${{key:e.href,title:e.text}}></${x.litTagName}>`;case"heading":return this.renderHeading(e);case"strong":return _`<strong>${this.renderText(e)}</strong>`;case"em":return _`<em>${this.renderText(e)}</em>`;default:return null}}renderToken(e){const t=this.templateForToken(e);if(null===t)throw new Error(`Markdown token type '${e.type}' not supported.`);return t}}var L=Object.freeze({__proto__:null,MarkdownView:I,MarkdownLitRenderer:M,MarkdownInsightRenderer:class extends M{renderToken(e){const t=this.templateForToken(e);return null===t?n.html`${e.raw}`:t}templateForToken(t){switch(t.type){case"heading":return _`<strong>${this.renderText(t)}</strong>`;case"link":case"image":return n.html`${e.XLink.XLink.create(t.href,t.text,void 0,void 0,"link-in-explanation")}`;case"code":return n.html`<${p.litTagName}
          .code=${this.unescape(t.text)}
          .codeLang=${t.lang}
          .displayNotice=${!0}>
        </${p.litTagName}>`}return super.templateForToken(t)}}});export{m as CodeBlock,y as MarkdownImage,w as MarkdownImagesMap,S as MarkdownLink,T as MarkdownLinksMap,L as MarkdownView};
