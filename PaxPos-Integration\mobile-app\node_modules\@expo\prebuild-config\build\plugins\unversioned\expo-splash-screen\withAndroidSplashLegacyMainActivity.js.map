{"version": 3, "file": "withAndroidSplashLegacyMainActivity.js", "names": ["_configPlugins", "data", "require", "_codeMod", "_generateCode", "_debug", "_interopRequireDefault", "_getAndroidSplashConfig", "e", "__esModule", "default", "debug", "Debug", "SHOW_SPLASH_ID", "withAndroidSplashLegacyMainActivity", "config", "props", "withMainActivity", "modResults", "contents", "setSplashScreenLegacyMainActivity", "language", "exports", "mainActivity", "splashConfig", "getAndroidSplashConfig", "mod", "removeContents", "src", "tag", "<PERSON><PERSON><PERSON><PERSON>", "statusBarTranslucent", "androidStatusBar", "translucent", "resizeMode", "isJava", "LE", "addImports", "match", "onCreateBlock", "mergeContents", "anchor", "offset", "comment", "newSrc", "join", "split", "filter", "line", "test", "toUpperCase"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withAndroidSplashLegacyMainActivity.ts"], "sourcesContent": ["import { ConfigPlugin, withMainActivity } from '@expo/config-plugins';\nimport { addImports } from '@expo/config-plugins/build/android/codeMod';\nimport { mergeContents, removeContents } from '@expo/config-plugins/build/utils/generateCode';\nimport { ExpoConfig } from '@expo/config-types';\nimport Debug from 'debug';\n\nimport { AndroidSplashConfig, getAndroidSplashConfig } from './getAndroidSplashConfig';\n\nconst debug = Debug('expo:prebuild-config:expo-splash-screen:android:mainActivity');\n\n// DO NOT CHANGE\nconst SHOW_SPLASH_ID = 'expo-splash-screen-mainActivity-onCreate-show-splash';\n\nexport const withAndroidSplashLegacyMainActivity: ConfigPlugin<AndroidSplashConfig> = (\n  config,\n  props\n) => {\n  return withMainActivity(config, (config) => {\n    config.modResults.contents = setSplashScreenLegacyMainActivity(\n      config,\n      props,\n      config.modResults.contents,\n      config.modResults.language\n    );\n    return config;\n  });\n};\n\nexport function setSplashScreenLegacyMainActivity(\n  config: Pick<ExpoConfig, 'android' | 'androidStatusBar' | 'userInterfaceStyle'>,\n  props: AndroidSplashConfig,\n  mainActivity: string,\n  language: 'java' | 'kt'\n): string {\n  debug(`Modify with language: \"${language}\"`);\n  const splashConfig = getAndroidSplashConfig(config, props);\n\n  if (!splashConfig) {\n    // Remove our generated code safely...\n    const mod = removeContents({\n      src: mainActivity,\n      tag: SHOW_SPLASH_ID,\n    });\n\n    mainActivity = mod.contents;\n    if (mod.didClear) {\n      debug('Removed SplashScreen.show()');\n    }\n    return mainActivity;\n  }\n  // TODO: Translucent is weird\n  const statusBarTranslucent = !!config.androidStatusBar?.translucent;\n\n  const { resizeMode } = splashConfig;\n  const isJava = language === 'java';\n  const LE = isJava ? ';' : '';\n\n  mainActivity = addImports(\n    mainActivity,\n    [\n      'host.exp.exponent.experience.splashscreen.legacy.singletons.SplashScreen',\n      'host.exp.exponent.experience.splashscreen.legacy.SplashScreenImageResizeMode',\n      'com.facebook.react.ReactRootView',\n      'android.os.Bundle',\n    ],\n    isJava\n  );\n\n  if (!mainActivity.match(/(?<=^.*super\\.onCreate.*$)/m)) {\n    const onCreateBlock = isJava\n      ? [\n          '    @Override',\n          '    protected void onCreate(Bundle savedInstanceState) {',\n          '      super.onCreate(savedInstanceState);',\n          '    }',\n        ]\n      : [\n          '    override fun onCreate(savedInstanceState: Bundle?) {',\n          '      super.onCreate(savedInstanceState)',\n          '    }',\n        ];\n\n    mainActivity = mergeContents({\n      src: mainActivity,\n      // insert just below super.onCreate\n      anchor: isJava\n        ? /(?<=public\\s+class\\s+.*\\s+extends\\s+.*\\s+{.*$)/m\n        : /(?<=class\\s+.*\\s+:\\s+.*\\s+{.*$)/m,\n      offset: 1,\n      comment: '//',\n      tag: 'expo-splash-screen-mainActivity-onCreate',\n      newSrc: onCreateBlock.join('\\n'),\n    }).contents;\n  }\n\n  // Remove our generated code safely...\n  mainActivity = removeContents({\n    src: mainActivity,\n    tag: SHOW_SPLASH_ID,\n  }).contents;\n\n  // Remove code from `@expo/configure-splash-screen`\n  mainActivity = mainActivity\n    .split('\\n')\n    .filter((line) => {\n      return !/SplashScreen\\.show\\(this,\\s?SplashScreenImageResizeMode\\./.test(line);\n    })\n    .join('\\n');\n\n  // Reapply generated code.\n  mainActivity = mergeContents({\n    src: mainActivity,\n    // insert just below super.onCreate\n    anchor: /(?<=^.*super\\.onCreate.*$)/m,\n    offset: 1,\n    comment: '//',\n    tag: SHOW_SPLASH_ID,\n    newSrc: `    SplashScreen.show(this, SplashScreenImageResizeMode.${resizeMode.toUpperCase()}, ReactRootView${\n      isJava ? '.class' : '::class.java'\n    }, ${statusBarTranslucent})${LE}`,\n  }).contents;\n\n  // TODO: Remove old `SplashScreen.show`\n\n  return mainActivity;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,SAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,QAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,cAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,aAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,OAAA;EAAA,MAAAJ,IAAA,GAAAK,sBAAA,CAAAJ,OAAA;EAAAG,MAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,wBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,uBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuF,SAAAK,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEvF,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,8DAA8D,CAAC;;AAEnF;AACA,MAAMC,cAAc,GAAG,sDAAsD;AAEtE,MAAMC,mCAAsE,GAAGA,CACpFC,MAAM,EACNC,KAAK,KACF;EACH,OAAO,IAAAC,iCAAgB,EAACF,MAAM,EAAGA,MAAM,IAAK;IAC1CA,MAAM,CAACG,UAAU,CAACC,QAAQ,GAAGC,iCAAiC,CAC5DL,MAAM,EACNC,KAAK,EACLD,MAAM,CAACG,UAAU,CAACC,QAAQ,EAC1BJ,MAAM,CAACG,UAAU,CAACG,QACpB,CAAC;IACD,OAAON,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAR,mCAAA,GAAAA,mCAAA;AAEK,SAASM,iCAAiCA,CAC/CL,MAA+E,EAC/EC,KAA0B,EAC1BO,YAAoB,EACpBF,QAAuB,EACf;EACRV,KAAK,CAAC,0BAA0BU,QAAQ,GAAG,CAAC;EAC5C,MAAMG,YAAY,GAAG,IAAAC,gDAAsB,EAACV,MAAM,EAAEC,KAAK,CAAC;EAE1D,IAAI,CAACQ,YAAY,EAAE;IACjB;IACA,MAAME,GAAG,GAAG,IAAAC,8BAAc,EAAC;MACzBC,GAAG,EAAEL,YAAY;MACjBM,GAAG,EAAEhB;IACP,CAAC,CAAC;IAEFU,YAAY,GAAGG,GAAG,CAACP,QAAQ;IAC3B,IAAIO,GAAG,CAACI,QAAQ,EAAE;MAChBnB,KAAK,CAAC,6BAA6B,CAAC;IACtC;IACA,OAAOY,YAAY;EACrB;EACA;EACA,MAAMQ,oBAAoB,GAAG,CAAC,CAAChB,MAAM,CAACiB,gBAAgB,EAAEC,WAAW;EAEnE,MAAM;IAAEC;EAAW,CAAC,GAAGV,YAAY;EACnC,MAAMW,MAAM,GAAGd,QAAQ,KAAK,MAAM;EAClC,MAAMe,EAAE,GAAGD,MAAM,GAAG,GAAG,GAAG,EAAE;EAE5BZ,YAAY,GAAG,IAAAc,qBAAU,EACvBd,YAAY,EACZ,CACE,0EAA0E,EAC1E,8EAA8E,EAC9E,kCAAkC,EAClC,mBAAmB,CACpB,EACDY,MACF,CAAC;EAED,IAAI,CAACZ,YAAY,CAACe,KAAK,CAAC,6BAA6B,CAAC,EAAE;IACtD,MAAMC,aAAa,GAAGJ,MAAM,GACxB,CACE,eAAe,EACf,0DAA0D,EAC1D,2CAA2C,EAC3C,OAAO,CACR,GACD,CACE,0DAA0D,EAC1D,0CAA0C,EAC1C,OAAO,CACR;IAELZ,YAAY,GAAG,IAAAiB,6BAAa,EAAC;MAC3BZ,GAAG,EAAEL,YAAY;MACjB;MACAkB,MAAM,EAAEN,MAAM,GACV,iDAAiD,GACjD,kCAAkC;MACtCO,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,IAAI;MACbd,GAAG,EAAE,0CAA0C;MAC/Ce,MAAM,EAAEL,aAAa,CAACM,IAAI,CAAC,IAAI;IACjC,CAAC,CAAC,CAAC1B,QAAQ;EACb;;EAEA;EACAI,YAAY,GAAG,IAAAI,8BAAc,EAAC;IAC5BC,GAAG,EAAEL,YAAY;IACjBM,GAAG,EAAEhB;EACP,CAAC,CAAC,CAACM,QAAQ;;EAEX;EACAI,YAAY,GAAGA,YAAY,CACxBuB,KAAK,CAAC,IAAI,CAAC,CACXC,MAAM,CAAEC,IAAI,IAAK;IAChB,OAAO,CAAC,2DAA2D,CAACC,IAAI,CAACD,IAAI,CAAC;EAChF,CAAC,CAAC,CACDH,IAAI,CAAC,IAAI,CAAC;;EAEb;EACAtB,YAAY,GAAG,IAAAiB,6BAAa,EAAC;IAC3BZ,GAAG,EAAEL,YAAY;IACjB;IACAkB,MAAM,EAAE,6BAA6B;IACrCC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,IAAI;IACbd,GAAG,EAAEhB,cAAc;IACnB+B,MAAM,EAAE,2DAA2DV,UAAU,CAACgB,WAAW,CAAC,CAAC,kBACzFf,MAAM,GAAG,QAAQ,GAAG,cAAc,KAC/BJ,oBAAoB,IAAIK,EAAE;EACjC,CAAC,CAAC,CAACjB,QAAQ;;EAEX;;EAEA,OAAOI,YAAY;AACrB", "ignoreList": []}