{"version": 3, "file": "withIosUserInterfaceStyle.js", "names": ["_iosPlugins", "data", "require", "withIosUserInterfaceStyle", "exports", "createInfoPlistPluginWithPropertyGuard", "setUserInterfaceStyle", "infoPlistProperty", "expoConfigProperty", "expoPropertyGetter", "getUserInterfaceStyle", "config", "ios", "userInterfaceStyle", "UIUserInterfaceStyle", "infoPlist", "style", "mapUserInterfaceStyleForInfoPlist"], "sources": ["../../../../src/plugins/unversioned/expo-system-ui/withIosUserInterfaceStyle.ts"], "sourcesContent": ["import { InfoPlist } from '@expo/config-plugins';\nimport { createInfoPlistPluginWithPropertyGuard } from '@expo/config-plugins/build/plugins/ios-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\nexport const withIosUserInterfaceStyle = createInfoPlistPluginWithPropertyGuard(\n  setUserInterfaceStyle,\n  {\n    infoPlistProperty: 'UIUserInterfaceStyle',\n    expoConfigProperty: 'userInterfaceStyle | ios.userInterfaceStyle',\n    expoPropertyGetter: getUserInterfaceStyle,\n  },\n  'withIosUserInterfaceStyle'\n);\n\nexport function getUserInterfaceStyle(\n  config: Pick<ExpoConfig, 'ios' | 'userInterfaceStyle'>\n): string {\n  return config.ios?.userInterfaceStyle ?? config.userInterfaceStyle ?? 'light';\n}\n\nexport function setUserInterfaceStyle(\n  config: Pick<ExpoConfig, 'ios' | 'userInterfaceStyle'>,\n  { UIUserInterfaceStyle, ...infoPlist }: InfoPlist\n): InfoPlist {\n  const userInterfaceStyle = getUserInterfaceStyle(config);\n  const style = mapUserInterfaceStyleForInfoPlist(userInterfaceStyle);\n\n  if (!style) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    UIUserInterfaceStyle: style,\n  };\n}\n\nfunction mapUserInterfaceStyleForInfoPlist(\n  userInterfaceStyle: string\n): NonNullable<InfoPlist['UIUserInterfaceStyle']> | null {\n  switch (userInterfaceStyle) {\n    case 'light':\n      return 'Light';\n    case 'dark':\n      return 'Dark';\n    case 'automatic':\n      return 'Automatic';\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;;AACA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,MAAME,yBAAyB,GAAAC,OAAA,CAAAD,yBAAA,GAAG,IAAAE,oDAAsC,EAC7EC,qBAAqB,EACrB;EACEC,iBAAiB,EAAE,sBAAsB;EACzCC,kBAAkB,EAAE,6CAA6C;EACjEC,kBAAkB,EAAEC;AACtB,CAAC,EACD,2BACF,CAAC;AAEM,SAASA,qBAAqBA,CACnCC,MAAsD,EAC9C;EACR,OAAOA,MAAM,CAACC,GAAG,EAAEC,kBAAkB,IAAIF,MAAM,CAACE,kBAAkB,IAAI,OAAO;AAC/E;AAEO,SAASP,qBAAqBA,CACnCK,MAAsD,EACtD;EAAEG,oBAAoB;EAAE,GAAGC;AAAqB,CAAC,EACtC;EACX,MAAMF,kBAAkB,GAAGH,qBAAqB,CAACC,MAAM,CAAC;EACxD,MAAMK,KAAK,GAAGC,iCAAiC,CAACJ,kBAAkB,CAAC;EAEnE,IAAI,CAACG,KAAK,EAAE;IACV,OAAOD,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZD,oBAAoB,EAAEE;EACxB,CAAC;AACH;AAEA,SAASC,iCAAiCA,CACxCJ,kBAA0B,EAC6B;EACvD,QAAQA,kBAAkB;IACxB,KAAK,OAAO;MACV,OAAO,OAAO;IAChB,KAAK,MAAM;MACT,OAAO,MAAM;IACf,KAAK,WAAW;MACd,OAAO,WAAW;EACtB;EAEA,OAAO,IAAI;AACb", "ignoreList": []}