{"version": 3, "file": "urql-core-chunk.mjs", "sources": ["../src/utils/error.ts", "../src/utils/hash.ts", "../src/utils/variables.ts", "../src/utils/request.ts", "../src/utils/result.ts", "../src/internal/fetchOptions.ts", "../src/internal/fetchSource.ts"], "sourcesContent": null, "names": ["rehydrateGraphQlError", "error", "message", "extensions", "name", "GraphQLError", "nodes", "source", "positions", "path", "CombinedError", "Error", "constructor", "input", "normalizedGraphQLErrors", "graphQLErrors", "map", "generateErrorMessage", "networkErr", "graphQlErrs", "i", "l", "length", "networkError", "super", "this", "response", "toString", "phash", "x", "seed", "h", "charCodeAt", "seen", "Set", "cache", "WeakMap", "stringify", "includeFiles", "has", "JSON", "toJSON", "Array", "isArray", "out", "FileConstructor", "NoopConstructor", "BlobConstructor", "keys", "Object", "sort", "getPrototypeOf", "prototype", "key", "get", "Math", "random", "slice", "set", "__key", "add", "value", "delete", "extract", "stringifyVariables", "clear", "File", "Blob", "GRAPHQL_STRING_RE", "REPLACE_CHAR_RE", "replaceOutsideStrings", "str", "idx", "replace", "sanitizeDocument", "node", "split", "join", "trim", "prints", "Map", "docs", "stringifyDocument", "printed", "loc", "body", "print", "start", "end", "locationOffset", "line", "column", "hashDocument", "documentId", "definitions", "operationName", "getOperationName", "keyDocument", "query", "parse", "noLocation", "createRequest", "_query", "_variables", "variables", "printedVars", "kind", "Kind", "OPERATION_DEFINITION", "undefined", "getOperationType", "operation", "makeResult", "result", "errors", "defaultHasNext", "data", "hasNext", "stale", "deepMerge", "target", "mergeResultPatch", "prevResult", "nextResult", "pending", "hasExtensions", "payload", "incremental", "withData", "_loop", "patch", "push", "assign", "prop", "part", "res", "find", "pendingRes", "id", "subPath", "items", "startIndex", "makeErrorResult", "makeFetchBody", "request", "<PERSON><PERSON><PERSON><PERSON>", "miss", "makeFetchURL", "useGETMethod", "context", "preferGetMethod", "url", "urlParts", "splitOutSearchParams", "finalUrl", "indexOf", "URLSearchParams", "serializeBody", "json", "files", "extractFiles", "size", "form", "FormData", "append", "index", "file", "values", "makeFetchOptions", "headers", "accept", "extraOptions", "fetchOptions", "isHeaders", "for<PERSON>ach", "toLowerCase", "serializedBody", "method", "boundaryHeaderRe", "eventStreamRe", "async", "streamBody", "Symbol", "asyncIterator", "chunk", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "cancel", "streamToBoundedChunks", "chunks", "boundary", "decoder", "TextDecoder", "buffer", "boundaryIndex", "decode", "stream", "fetchOperation", "networkMode", "Promise", "resolve", "contentType", "fetch", "results", "test", "parseMultipartMixed", "<PERSON><PERSON><PERSON><PERSON>", "match", "isPreamble", "preambleIndex", "parseEventStream", "parseJSON", "text", "parseMaybeJSON", "process", "env", "NODE_ENV", "console", "warn", "e", "status", "statusText", "makeFetchSource", "abortController", "AbortController", "signal", "onEnd", "abort", "filter", "fromAsyncIterable"], "mappings": ";;;;AAkBA,IAAMA,wBAAyBC;EAC7B,IACEA,KACyB,mBAAlBA,EAAMC,YACZD,EAAME,cAA6B,mBAAfF,EAAMG;IAE3B,OAAOH;SACF,IAAqB,mBAAVA,KAA+C,mBAAlBA,EAAMC;IACnD,OAAO,IAAIG,EACTJ,EAAMC,SACND,EAAMK,OACNL,EAAMM,QACNN,EAAMO,WACNP,EAAMQ,MACNR,GACAA,EAAME,cAAc,CAAA;;IAGtB,OAAO,IAAIE,EAAaJ;;AAC1B;;AAiBK,MAAMS,sBAAsBC;EAwCjCC,WAAAA,CAAYC;IAKV,IAAMC,KAA2BD,EAAME,iBAAiB,IAAIC,IAC1DhB;IAEF,IAAME,IAnGmBe,EAC3BC,GACAC;MAEA,IAAIlB,IAAQ;MACZ,IAAIiB;QAAY,OAAO,aAAaA,EAAWhB;;MAC/C,IAAIiB;QACF,KAAK,IAAIC,IAAI,GAAGC,IAAIF,EAAYG,QAAQF,IAAIC,GAAGD,KAAK;UAClD,IAAInB;YAAOA,KAAS;;UACpBA,KAAS,aAAakB,EAAYC,GAAGlB;AACvC;;MAEF,OAAOD;AAAK,MAuFMgB,CACdJ,EAAMU,cACNT;IAGFU,MAAMtB;IAENuB,KAAKrB,OAAO;IACZqB,KAAKvB,UAAUA;IACfuB,KAAKV,gBAAgBD;IACrBW,KAAKF,eAAeV,EAAMU;IAC1BE,KAAKC,WAAWb,EAAMa;AACxB;EAEAC,QAAAA;IACE,OAAOF,KAAKvB;AACd;;;ACvFK,IAAM0B,QAAQA,CAACC,GAAWC;EAC/B,IAAIC,IAAqB,KAAhBD,KAAQ;EACjB,KAAK,IAAIV,IAAI,GAAGC,IAAe,IAAXQ,EAAEP,QAAYF,IAAIC,GAAGD;IACvCW,KAAKA,KAAK,KAAKA,IAAIF,EAAEG,WAAWZ;;EAClC,OAAOW;AAAC;;ACjCV,IAAME,IAAiB,IAAIC;;AAC3B,IAAMC,IAA2B,IAAIC;;AAErC,IAAMC,YAAYA,CAACR,GAAQS;EACzB,IAAU,SAANT,KAAcI,EAAKM,IAAIV;IACzB,OAAO;SACF,IAAiB,mBAANA;IAChB,OAAOW,KAAKH,UAAUR,MAAM;SACvB,IAAIA,EAAEY;IACX,OAAOJ,UAAUR,EAAEY,UAAUH;SACxB,IAAII,MAAMC,QAAQd,IAAI;IAC3B,IAAIe,IAAM;IACV,KAAK,IAAIxB,IAAI,GAAGC,IAAIQ,EAAEP,QAAQF,IAAIC,GAAGD,KAAK;MACxC,IAAIwB,EAAItB,SAAS;QAAGsB,KAAO;;MAC3BA,KAAOP,UAAUR,EAAET,IAAIkB,MAAiB;AAC1C;IAEA,OADAM,KAAO;AAER,SAAM,KACJN,MACCO,MAAoBC,mBAAmBjB,aAAagB,KACnDE,MAAoBD,mBAAmBjB,aAAakB;IAEvD,OAAO;;EAGT,IAAMC,IAAOC,OAAOD,KAAKnB,GAAGqB;EAC5B,KACGF,EAAK1B,UACNO,EAAEjB,eACFqC,OAAOE,eAAetB,GAAGjB,gBAAgBqC,OAAOG,UAAUxC,aAC1D;IACA,IAAMyC,IAAMlB,EAAMmB,IAAIzB,MAAM0B,KAAKC,SAAS7B,SAAS,IAAI8B,MAAM;IAC7DtB,EAAMuB,IAAI7B,GAAGwB;IACb,OAAOhB,UAAU;MAAEsB,OAAON;OAAOf;AACnC;EAEAL,EAAK2B,IAAI/B;EACT,IAAIe,IAAM;EACV,KAAK,IAAIxB,IAAI,GAAGC,IAAI2B,EAAK1B,QAAQF,IAAIC,GAAGD,KAAK;IAC3C,IAAMyC,IAAQxB,UAAUR,EAAEmB,EAAK5B,KAAKkB;IACpC,IAAIuB,GAAO;MACT,IAAIjB,EAAItB,SAAS;QAAGsB,KAAO;;MAC3BA,KAAOP,UAAUW,EAAK5B,IAAIkB,KAAgB,MAAMuB;AAClD;AACF;EAEA5B,EAAK6B,OAAOjC;EAEZ,OADAe,KAAO;AACG;;AAGZ,IAAMmB,UAAUA,CAAC/C,GAAcP,GAAcoB;EAC3C,IAAS,QAALA,KAA0B,mBAANA,KAAkBA,EAAEY,UAAUR,EAAKM,IAAIV,YAExD,IAAIa,MAAMC,QAAQd;IACvB,KAAK,IAAIT,IAAI,GAAGC,IAAIQ,EAAEP,QAAQF,IAAIC,GAAGD;MACnC2C,QAAQ/C,GAAK,GAAGP,KAAQW,KAAKS,EAAET;;SAC5B,IAAIS,aAAagB,KAAmBhB,aAAakB;IACtD/B,EAAI0C,IAAIjD,GAAMoB;SACT;IACLI,EAAK2B,IAAI/B;IACT,KAAK,IAAMwB,KAAOxB;MAAGkC,QAAQ/C,GAAK,GAAGP,KAAQ4C,KAAOxB,EAAEwB;;AACxD;AAAA;;IAiBWW,qBAAqBA,CAACnC,GAAQS;EACzCL,EAAKgC;EACL,OAAO5B,UAAUR,GAAGS,MAAgB;AAAM;;AAG5C,MAAMQ;;AACN,IAAMD,IAAkC,sBAATqB,OAAuBA,OAAOpB;;AAC7D,IAAMC,IAAkC,sBAAToB,OAAuBA,OAAOrB;;AC/D7D,IAAMsB,IAAoB;;AAC1B,IAAMC,IAAkB;;AAExB,IAAMC,wBAAwBA,CAACC,GAAaC,MAC1CA,IAAM,KAAM,IAAID,EAAIE,QAAQJ,GAAiB,QAAQE;;AAGvD,IAAMG,mBAAoBC,KACxBA,EAAKC,MAAMR,GAAmBpD,IAAIsD,uBAAuBO,KAAK,IAAIC;;AAEpE,IAAMC,IAAqD,IAAIC;;AAI/D,IAAMC,IAA0C,IAAID;;AAmBvCE,IAAAA,oBACXP;EAEA,IAAIQ;EACJ,IAAoB,mBAATR;IACTQ,IAAUT,iBAAiBC;SACtB,IAAIA,EAAKS,OAAOH,EAAK3B,IAAKqB,EAA2BhB,WAAWgB;IACrEQ,IAAUR,EAAKS,IAAI7E,OAAO8E;SACrB;IACLF,IAAUJ,EAAOzB,IAAIqB,MAASD,iBAAiBY,EAAMX;IACrDI,EAAOrB,IAAIiB,GAAMQ;AACnB;EAEA,IAAoB,mBAATR,MAAsBA,EAAKS;IACnCT,EAAaS,MAAM;MAClBG,OAAO;MACPC,KAAKL,EAAQ7D;MACbf,QAAQ;QACN8E,MAAMF;QACN/E,MArDY;QAsDZqF,gBAAgB;UAAEC,MAAM;UAAGC,QAAQ;;;;;EAKzC,OAAOR;AAAO;;AAehB,IAAMS,eACJjB;EAEA,IAAItB;EACJ,IAAKsB,EAA+BkB;IAClCxC,IAAMzB,MAAO+C,EAA+BkB;SACvC;IACLxC,IAAMzB,MAAMsD,kBAAkBP;IAE9B,IAAKA,EAAsBmB,aAAa;MACtC,IAAMC,IAAgBC,iBAAiBrB;MACvC,IAAIoB;QAAe1C,IAAMzB,MAAM,OAAOmE,KAAiB1C;;AACzD;AACF;EACA,OAAOA;AAAG;;AAeC4C,IAAAA,cAAetB;EAC1B,IAAItB;EACJ,IAAI6C;EACJ,IAAoB,mBAATvB,GAAmB;IAC5BtB,IAAMuC,aAAajB;IACnBuB,IAAQjB,EAAK3B,IAAID,MAAQ8C,EAAMxB,GAAM;MAAEyB,aAAY;;AACrD,SAAO;IACL/C,IAAOsB,EAA2BhB,SAASiC,aAAajB;IACxDuB,IAAQjB,EAAK3B,IAAID,MAAQsB;AAC3B;EAGA,KAAKuB,EAAMd;IAAKF,kBAAkBgB;;EAEjCA,EAA4BvC,QAAQN;EACrC4B,EAAKvB,IAAIL,GAAK6C;EACd,OAAOA;AAAK;;AAiBP,IAAMG,gBAAgBA,CAI3BC,GACAC,GACApG;EAEA,IAAMqG,IAAYD,KAAe;EACjC,IAAML,IAAQD,YAAYK;EAC1B,IAAMG,IAAczC,mBAAmBwC,IAAW;EAClD,IAAInD,IAAM6C,EAAMvC;EAChB,IAAoB,SAAhB8C;IAAsBpD,IAAMzB,MAAM6E,GAAapD;;EACnD,OAAO;IAAEA;IAAK6C;IAAOM;IAAWrG;;AAAY;;AAOjC6F,IAAAA,mBAAoBE;EAC/B,KAAK,IAAI9E,IAAI,GAAGC,IAAI6E,EAAMJ,YAAYxE,QAAQF,IAAIC,GAAGD,KAAK;IACxD,IAAMuD,IAAOuB,EAAMJ,YAAY1E;IAC/B,IAAIuD,EAAK+B,SAASC,EAAKC;MACrB,OAAOjC,EAAKvE,OAAOuE,EAAKvE,KAAKyD,aAAQgD;;AAEzC;AAAA;;AAOWC,IAAAA,mBAAoBZ;EAC/B,KAAK,IAAI9E,IAAI,GAAGC,IAAI6E,EAAMJ,YAAYxE,QAAQF,IAAIC,GAAGD,KAAK;IACxD,IAAMuD,IAAOuB,EAAMJ,YAAY1E;IAC/B,IAAIuD,EAAK+B,SAASC,EAAKC;MACrB,OAAOjC,EAAKoC;;AAEhB;AAAA;;AC/KK,IAAMC,aAAaA,CACxBD,GACAE,GACAvF;EAEA,MACI,UAAUuF,KACT,YAAYA,KAAYvE,MAAMC,QAAQsE,EAAOC;IAEhD,MAAM,IAAIvG,MAAM;;EAGlB,IAAMwG,IAAoC,mBAAnBJ,EAAUL;EACjC,OAAO;IACLK;IACAK,MAAMH,EAAOG;IACbnH,OAAOyC,MAAMC,QAAQsE,EAAOC,UACxB,IAAIxG,cAAc;MAChBK,eAAekG,EAAOC;MACtBxF;cAEFmF;IACJ1G,YAAY8G,EAAO9G,aAAa;SAAK8G,EAAO9G;aAAe0G;IAC3DQ,SAA2B,QAAlBJ,EAAOI,UAAkBF,IAAiBF,EAAOI;IAC1DC,QAAO;;AACR;;AAGH,IAAMC,YAAYA,CAACC,GAAajH;EAC9B,IAAsB,mBAAXiH,KAAiC,QAAVA,GAAgB;IAChD,IAAI9E,MAAMC,QAAQ6E,IAAS;MACzBA,IAAS,KAAIA;MACb,KAAK,IAAIpG,IAAI,GAAGC,IAAId,EAAOe,QAAQF,IAAIC,GAAGD;QACxCoG,EAAOpG,KAAKmG,UAAUC,EAAOpG,IAAIb,EAAOa;;MAE1C,OAAOoG;AACT;IACA,KAAKA,EAAO5G,eAAe4G,EAAO5G,gBAAgBqC,QAAQ;MACxDuE,IAAS;WAAKA;;MACd,KAAK,IAAMnE,KAAO9C;QAChBiH,EAAOnE,KAAOkE,UAAUC,EAAOnE,IAAM9C,EAAO8C;;MAC9C,OAAOmE;AACT;AACF;EACA,OAAOjH;AAAM;;AAqBR,IAAMkH,mBAAmBA,CAC9BC,GACAC,GACAjG,GACAkG;EAEA,IAAIV,IAASQ,EAAWzH,QAAQyH,EAAWzH,MAAMc,gBAAgB;EACjE,IAAI8G,MACAH,EAAWvH,iBAAiBwH,EAAWG,WAAWH,GAAYxH;EAClE,IAAMA,IAAa;OACduH,EAAWvH;QACVwH,EAAWG,WAAWH,GAAYxH;;EAGxC,IAAI4H,IAAcJ,EAAWI;EAG7B,IAAI,UAAUJ;IACZI,IAAc,EAACJ;;EAGjB,IAAMK,IAAW;IAAEZ,MAAMM,EAAWN;;EACpC,IAAIW,GAAa;IAAA,IAAAE,QAAAA;MAEb,IAAMC,IAAQH,EAAY3G;MAC1B,IAAIsB,MAAMC,QAAQuF,EAAMhB;QACtBA,EAAOiB,QAASD,EAAMhB;;MAGxB,IAAIgB,EAAM/H,YAAY;QACpB8C,OAAOmF,OAAOjI,GAAY+H,EAAM/H;QAChC0H,KAAgB;AAClB;MAEA,IAAIQ,IAAwB;MAC5B,IAAIC,IAAyCN;MAC7C,IAAIvH,IAAqC;MACzC,IAAIyH,EAAMzH;QACRA,IAAOyH,EAAMzH;aACR,IAAImH,GAAS;QAClB,IAAMW,IAAMX,EAAQY,MAAKC,KAAcA,EAAWC,OAAOR,EAAMQ;QAC/D,IAAIR,EAAMS;UACRlI,IAAO,KAAI8H,EAAK9H,SAASyH,EAAMS;;UAE/BlI,IAAO8H,EAAK9H;;AAEhB;MAEA,KAAK,IAAIW,IAAI,GAAGC,IAAIZ,EAAKa,QAAQF,IAAIC,GAAGgH,IAAO5H,EAAKW;QAClDkH,IAAOA,EAAKD,KAAQ3F,MAAMC,QAAQ2F,EAAKD,MACnC,KAAIC,EAAKD,OACT;aAAKC,EAAKD;;;MAGhB,IAAIH,EAAMU,OAAO;QACf,IAAMC,KAAcR,KAAQ,IAAKA,IAAkB;QACnD,KAAK,IAAIjH,IAAI,GAAGC,IAAI6G,EAAMU,MAAMtH,QAAQF,IAAIC,GAAGD;UAC7CkH,EAAKO,IAAazH,KAAKmG,UACrBe,EAAKO,IAAazH,IAClB8G,EAAMU,MAAMxH;;AAElB,aAAO,SAAmByF,MAAfqB,EAAMd;QACfkB,EAAKD,KAAQd,UAAUe,EAAKD,IAAOH,EAAMd;;;IAvC7C,KAAK,IAAIhG,IAAI,GAAGC,IAAI0G,EAAYzG,QAAQF,IAAIC,GAAGD;MAAG6G;;AA0CpD,SAAO;IACLD,EAASZ,QAAQO,EAAWG,WAAWH,GAAYP,QAAQM,EAAWN;IACtEF,IACGS,EAAWT,UACXS,EAAWG,WAAWH,EAAWG,QAAQZ,UAC1CA;AACJ;EAEA,OAAO;IACLH,WAAWW,EAAWX;IACtBK,MAAMY,EAASZ;IACfnH,OAAOiH,EAAO5F,SACV,IAAIZ,cAAc;MAAEK,eAAemG;MAAQxF;cAC3CmF;IACJ1G,YAAY0H,IAAgB1H,SAAa0G;IACzCQ,SACwB,QAAtBM,EAAWN,UAAkBM,EAAWN,UAAUK,EAAWL;IAC/DC,QAAO;;AACR;;AAgBI,IAAMwB,kBAAkBA,CAC7B/B,GACA9G,GACAyB,OACqB;EACrBqF;EACAK,WAAMP;EACN5G,OAAO,IAAIS,cAAc;IACvBa,cAActB;IACdyB;;EAEFvB,iBAAY0G;EACZQ,UAAS;EACTC,QAAO;;;ACnLF,SAASyB,cAGdC;EACA,IAAM3D,IAAkB;IACtBa,YAAOW;IACPhB,iBAAYgB;IACZd,eAAeC,iBAAiBgD,EAAQ9C;IACxCM,WAAWwC,EAAQxC,kBAAaK;IAChC1G,YAAY6I,EAAQ7I;;EAGtB,IACE,gBAAgB6I,EAAQ9C,SACxB8C,EAAQ9C,MAAML,gBAGZmD,EAAQ9C,MAAMJ,gBAAgBkD,EAAQ9C,MAAMJ,YAAYxE;IAE1D+D,EAAKQ,aAAamD,EAAQ9C,MAAML;SAC3B,KACJmD,EAAQ7I,eACR6I,EAAQ7I,WAAW8I,kBAClBD,EAAQ7I,WAAW8I,eAAeC;IAEpC7D,EAAKa,QAAQhB,kBAAkB8D,EAAQ9C;;EAGzC,OAAOb;AACT;;IAaa8D,eAAeA,CAC1BpC,GACA1B;EAEA,IAAM+D,IACe,YAAnBrC,EAAUL,QAAoBK,EAAUsC,QAAQC;EAClD,KAAKF,MAAiB/D;IAAM,OAAO0B,EAAUsC,QAAQE;;EAErD,IAAMC,IAAWC,qBAAqB1C,EAAUsC,QAAQE;EACxD,KAAK,IAAMlG,KAAOgC,GAAM;IACtB,IAAMxB,IAAQwB,EAAKhC;IACnB,IAAIQ;MACF2F,EAAS,GAAG9F,IACVL,GACiB,mBAAVQ,IAAqBG,mBAAmBH,KAASA;;AAG9D;EACA,IAAM6F,IAAWF,EAAS3E,KAAK;EAC/B,IAAI6E,EAASpI,SAAS,QAAyB,YAAjB8H,GAA0B;IACtDrC,EAAUsC,QAAQC,mBAAkB;IACpC,OAAOvC,EAAUsC,QAAQE;AAC3B;EAEA,OAAOG;AAAQ;;AAGjB,IAAMD,uBACJF;EAEA,IAAMhE,IAAQgE,EAAII,QAAQ;EAC1B,OAAOpE,KAAS,IACZ,EAACgE,EAAI9F,MAAM,GAAG8B,IAAQ,IAAIqE,gBAAgBL,EAAI9F,MAAM8B,IAAQ,QAC5D,EAACgE,GAAK,IAAIK;AAAkB;;AAIlC,IAAMC,gBAAgBA,CACpB9C,GACA1B;EAIA,IAAIA,OADiB,YAAnB0B,EAAUL,UAAsBK,EAAUsC,QAAQC,kBAC7B;IACrB,IAAMQ,IAAO9F,mBAAmBqB;IAChC,IAAM0E,IHnBmBlI;MAC3B,IAAMb,IAAe,IAAIgE;MACzB,IACEnC,MAAoBC,mBACpBC,MAAoBD,iBACpB;QACAb,EAAKgC;QACLF,QAAQ/C,GAAK,aAAaa;AAC5B;MACA,OAAOb;AAAG,MGUMgJ,CAAa3E,EAAKmB;IAChC,IAAIuD,EAAME,MAAM;MACd,IAAMC,IAAO,IAAIC;MACjBD,EAAKE,OAAO,cAAcN;MAC1BI,EAAKE,OACH,OACApG,mBAAmB;WACd,KAAI+F,EAAM/G,SAAQhC,KAAI6C,KAAS,EAACA;;MAGvC,IAAIwG,IAAQ;MACZ,KAAK,IAAMC,KAAQP,EAAMQ;QAAUL,EAAKE,OAAO,KAAGC,KAAWC;;MAC7D,OAAOJ;AACT;IACA,OAAOJ;AACT;AAAA;;IAmBWU,mBAAmBA,CAC9BzD,GACA1B;EAEA,IAAMoF,IAAuB;IAC3BC,QACqB,mBAAnB3D,EAAUL,OACN,uCACA;;EAER,IAAMiE,KACuC,qBAAnC5D,EAAUsC,QAAQuB,eACtB7D,EAAUsC,QAAQuB,iBAClB7D,EAAUsC,QAAQuB,iBAAiB,CAAA;EACzC,IAAID,EAAaF;IACf,IA/BeA,MACjB,SAASA,MAAYxH,OAAOD,KAAKyH,GAASnJ,OA8BpCuJ,CAAUF,EAAaF;MACzBE,EAAaF,QAAQK,SAAQ,CAACjH,GAAOR;QACnCoH,EAAQpH,KAAOQ;AAAK;WAEjB,IAAInB,MAAMC,QAAQgI,EAAaF;MACnCE,EAAaF,QAAoCK,SAChD,CAACjH,GAAOR;QACN,IAAIX,MAAMC,QAAQkB;UAChB,IAAI4G,EAAQ5G,EAAM;YAChB4G,EAAQ5G,EAAM,MAAM,GAAG4G,EAAQ5G,EAAM,OAAOA,EAAM;;YAElD4G,EAAQ5G,EAAM,MAAMA,EAAM;;;UAG5B4G,EAAQpH,KAAOQ;;AACjB;;MAIJ,KAAK,IAAMR,KAAOsH,EAAaF;QAC7BA,EAAQpH,EAAI0H,iBAAiBJ,EAAaF,QAAQpH;;;;EAKxD,IAAM2H,IAAiBnB,cAAc9C,GAAW1B;EAChD,IAA8B,mBAAnB2F,MAAgCP,EAAQ;IACjDA,EAAQ,kBAAkB;;EAC5B,OAAO;OACFE;IACHM,QAAQD,IAAiB,SAAS;IAClC3F,MAAM2F;IACNP;;AACD;;AC/IH,IAAMS,IAAmB;;AACzB,IAAMC,IAAgB;;AAItBC,gBAAgBC,WACd3J;EAEA,IAAIA,EAAS2D,KAAMiG,OAAOC;IACxB,WAAW,IAAMC,KAAS9J,EAAS2D;YAAoBmG;;SAClD;IACL,IAAMC,IAAS/J,EAAS2D,KAAMqG;IAC9B,IAAIzE;IACJ;MACE,SAASA,UAAewE,EAAOE,QAAQC;cAAY3E,EAAOpD;;AAC5D,MAAU;MACR4H,EAAOI;AACT;AACF;AACF;;AAEAT,gBAAgBU,sBACdC,GACAC;EAEA,IAAMC,IAAiC,sBAAhBC,cAA8B,IAAIA,cAAgB;EACzE,IAAIC,IAAS;EACb,IAAIC;EACJ,WAAW,IAAMZ,KAASO,GAAQ;IAGhCI,KAC6B,aAA3BX,EAAM5K,YAAYR,OACboL,EAAiB7J,aAClBsK,EAASI,OAAOb,GAAsB;MAAEc,SAAQ;;IACtD,QAAQF,IAAgBD,EAAOxC,QAAQqC,OAAc,GAAG;YAChDG,EAAO1I,MAAM,GAAG2I;MACtBD,IAASA,EAAO1I,MAAM2I,IAAgBJ,EAAS1K;AACjD;AACF;AACF;;AAkFA8J,gBAAgBmB,eACdxF,GACAwC,GACAqB;EAEA,IAAI4B,KAAc;EAClB,IAAIvF,IAAiC;EACrC,IAAIvF;EAEJ;gBAGc+K,QAAQC;IAGpB,IAAMC,KADNjL,WAAkBqF,EAAUsC,QAAQuD,SAASA,OAAOrD,GAAKqB,IAC5BH,QAAQnH,IAAI,mBAAmB;IAE5D,IAAIuJ;IACJ,IAAI,oBAAoBC,KAAKH;MAC3BE,IArENzB,gBAAgB2B,oBACdJ,GACAjL;QAEA,IAAMsL,IAAiBL,EAAYM,MAAM/B;QACzC,IAAMc,IAAW,QAAQgB,IAAiBA,EAAe,KAAK;QAC9D,IAAIE,KAAa;QACjB,IAAIpF;QACJ,WAAW,IAAI0D,KAASM,sBACtBT,WAAW3J,IACX,SAASsK,IACR;UACD,IAAIkB,GAAY;YACdA,KAAa;YACb,IAAMC,IAAgB3B,EAAM7B,QAAQqC;YACpC,IAAImB,KAAiB;cACnB3B,IAAQA,EAAM/H,MAAM0J,IAAgBnB,EAAS1K;;cAE7C;;AAEJ;UACA;kBACSwG,IAAUtF,KAAK2D,MAAMqF,EAAM/H,MAAM+H,EAAM7B,QAAQ,cAAc;AACrE,YAAC,OAAO1J;YACP,KAAK6H;cAAS,MAAM7H;;AACtB;UACA,IAAI6H,MAA+B,MAApBA,EAAQT;YAAmB;;AAC5C;QACA,IAAIS,MAA+B,MAApBA,EAAQT;gBACf;YAAEA,UAAS;;;AAErB,OAsCgB0F,CAAoBJ,GAAajL;WACtC,IAAI,sBAAsBoL,KAAKH;MACpCE,IA/FNzB,gBAAgBgC,iBACd1L;QAEA,IAAIoG;QACJ,WAAW,IAAM0D,KAASM,sBACxBT,WAAW3J,IACX,SACC;UACD,IAAMuL,IAAQzB,EAAMyB,MAAM9B;UAC1B,IAAI8B,GAAO;YACT,IAAMzB,IAAQyB,EAAM;YACpB;oBACSnF,IAAUtF,KAAK2D,MAAMqF;AAC7B,cAAC,OAAOvL;cACP,KAAK6H;gBAAS,MAAM7H;;AACtB;YACA,IAAI6H,MAA+B,MAApBA,EAAQT;cAAmB;;AAC5C;AACF;QACA,IAAIS,MAA+B,MAApBA,EAAQT;gBACf;YAAEA,UAAS;;;AAErB,OAyEgB+F,CAAiB1L;WACtB,KAAK,UAAUoL,KAAKH;MACzBE,IAvGNzB,gBAAgBiC,UACd3L;cAEMc,KAAK2D,YAAYzE,EAAS4L;AAClC,OAmGgBD,CAAU3L;;MAEpBmL,IA1CNzB,gBAAgBmC,eACd7L;QAEA,IAAM4L,UAAa5L,EAAS4L;QAC5B;UACE,IAAMrG,IAASzE,KAAK2D,MAAMmH;UAC1B,IAA6B,iBAAzBE,QAAQC,IAAIC;YACdC,QAAQC,KACN;;gBAGE3G;AACP,UAAC,OAAO4G;UACP,MAAM,IAAIlN,MAAM2M;AAClB;AACF,OA2BgBC,CAAe7L;;IAG3B,IAAIkG;IACJ,WAAW,IAAME,KAAW+E,GAAS;MACnC,IAAI/E,EAAQF,YAAYX;QACtBW,IAAUE,EAAQF;aACb,IAAIE,EAAQF;QACjBA,IAAU,KAAIA,MAAaE,EAAQF;;MAErCX,IAASA,IACLQ,iBAAiBR,GAAQa,GAASpG,GAAUkG,KAC5CZ,WAAWD,GAAWe,GAASpG;MACnC8K,KAAc;YACRvF;MACNuF,KAAc;AAChB;IAEA,KAAKvF;YACIA,IAASD,WAAWD,GAAW,CAAE,GAAErF;;AAE7C,IAAC,OAAOzB;IACP,KAAKuM;MACH,MAAMvM;;UAGF6I,gBACJ/B,GACArF,MACGA,EAASoM,SAAS,OAAOpM,EAASoM,UAAU,QAC7CpM,EAASqM,aACP,IAAIpN,MAAMe,EAASqM,cACnB9N,GACJyB;AAEJ;AACF;;AA6BO,SAASsM,gBACdjH,GACAwC,GACAqB;EAEA,IAAIqD;EACJ,IAA+B,sBAApBC;IACTtD,EAAauD,UAAUF,IAAkB,IAAIC,iBAAmBC;;EAElE,OAGEC,GAAM;IACJ,IAAIH;MAAiBA,EAAgBI;;AAAO,KAD9CD,CADAE,GAAQrH,OAAwCA,GAAhDqH,CADAC,EAAkBhC,eAAexF,GAAWwC,GAAKqB;AAMrD;;"}