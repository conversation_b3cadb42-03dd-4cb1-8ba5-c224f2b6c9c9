/**
 * Automatically generated by expo-modules-test-core.
 *
 * This autogenerated file provides a mock for native Expo module,
 * and works out of the box with the expo jest preset.
 *  */

export type DigestAlgorithm = any;

export type DigestOptions = any;

export type TypedArray = any;

export function digestString(
  algorithm: DigestAlgorithm,
  str: string,
  options: DigestOptions
): string {
  return '';
}

export function getRandomBase64String(length: number): string {
  return '';
}

export function getRandomValues(array: TypedArray): TypedArray {
  return null; // TODO: Replace with mock for value of type TypedArray.
}

export function digest(algorithm: DigestAlgorithm, output: TypedArray, data: TypedArray): void {}

export function randomUUID(): any {}

export async function digestStringAsync(
  algorithm: DigestAlgorithm,
  str: string,
  options: DigestOptions
): Promise<string> {
  return '';
}

export async function getRandomBase64StringAsync(length: number): Promise<string> {
  return '';
}
