{"version": 3, "file": "FontAwesome.js", "sourceRoot": "", "sources": ["../src/FontAwesome.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,0DAA0D,CAAC;AAC5E,OAAO,QAAQ,MAAM,+DAA+D,CAAC;AAErF,eAAe,aAAa,CAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["\"use client\";\n\nimport createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/FontAwesome.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/FontAwesome.json';\n\nexport default createIconSet(glyphMap, 'FontAwesome', font);\n"]}