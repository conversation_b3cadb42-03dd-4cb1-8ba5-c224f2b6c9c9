{"version": 3, "file": "Feather.js", "sourceRoot": "", "sources": ["../src/Feather.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,sDAAsD,CAAC;AACxE,OAAO,QAAQ,MAAM,2DAA2D,CAAC;AAEjF,eAAe,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["\"use client\";\n\nimport createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Feather.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Feather.json';\n\nexport default createIconSet(glyphMap, 'feather', font);\n"]}