/**
 * Viva Webhook Monitor Component
 * 
 * Displays real-time webhook events from Viva Wallet
 * Shows payment status updates, transaction confirmations, and receipt generation
 */

import React, { useState, useEffect } from 'react';
import { useVivaWebhookEvents } from '../hooks/useWebSocket';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { VivaWebhookEventData, TransactionUpdateData, ReceiptEventData } from '../services/websocketService';

interface WebhookEvent {
  id: string;
  timestamp: string;
  type: 'viva_webhook' | 'transaction_update' | 'receipt_generated';
  data: VivaWebhookEventData | TransactionUpdateData | ReceiptEventData;
  status: 'success' | 'error' | 'info';
}

export function VivaWebhookMonitor() {
  const [events, setEvents] = useState<WebhookEvent[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  const { isConnected } = useVivaWebhookEvents(
    (vivaEvent) => {
      const event: WebhookEvent = {
        id: `viva_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        type: 'viva_webhook',
        data: vivaEvent,
        status: vivaEvent.processed ? 'success' : 'error'
      };
      
      setEvents(prev => [event, ...prev.slice(0, 49)]); // Keep last 50 events
      
      // Auto-show monitor when events are received
      setIsVisible(true);
    },
    (transactionUpdate) => {
      const event: WebhookEvent = {
        id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        type: 'transaction_update',
        data: transactionUpdate,
        status: transactionUpdate.status === 'success' ? 'success' : 
                transactionUpdate.status === 'failed' ? 'error' : 'info'
      };
      
      setEvents(prev => [event, ...prev.slice(0, 49)]);
    },
    (receiptEvent) => {
      const event: WebhookEvent = {
        id: `receipt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        type: 'receipt_generated',
        data: receiptEvent,
        status: 'success'
      };
      
      setEvents(prev => [event, ...prev.slice(0, 49)]);
    }
  );

  const clearEvents = () => {
    setEvents([]);
  };

  const getEventIcon = (event: WebhookEvent): string => {
    switch (event.type) {
      case 'viva_webhook':
        return '💳';
      case 'transaction_update':
        return '🔄';
      case 'receipt_generated':
        return '🧾';
      default:
        return '📡';
    }
  };

  const getEventTitle = (event: WebhookEvent): string => {
    switch (event.type) {
      case 'viva_webhook':
        const vivaData = event.data as VivaWebhookEventData;
        return `${vivaData.eventDescription} - Order ${vivaData.orderCode}`;
      case 'transaction_update':
        const txData = event.data as TransactionUpdateData;
        return `Transaction ${txData.status.toUpperCase()} - ${txData.transactionId.slice(-8)}`;
      case 'receipt_generated':
        const receiptData = event.data as ReceiptEventData;
        return `${receiptData.receiptType} Receipt Generated`;
      default:
        return 'Unknown Event';
    }
  };

  const getEventDescription = (event: WebhookEvent): string => {
    switch (event.type) {
      case 'viva_webhook':
        const vivaData = event.data as VivaWebhookEventData;
        return `Amount: €${(vivaData.amount / 100).toFixed(2)} | Status: ${vivaData.statusId}`;
      case 'transaction_update':
        const txData = event.data as TransactionUpdateData;
        return `${txData.paymentProvider} | ${txData.currency?.toUpperCase()} ${(txData.amount / 100).toFixed(2)}`;
      case 'receipt_generated':
        const receiptData = event.data as ReceiptEventData;
        return `Format: ${receiptData.format} | Transaction: ${receiptData.transactionId.slice(-8)}`;
      default:
        return '';
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'info':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg"
        >
          📡 Webhook Monitor {events.length > 0 && `(${events.length})`}
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-hidden">
      <Card className="bg-white shadow-xl border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-lg">📡</span>
              <h3 className="font-semibold text-gray-900">Viva Webhook Monitor</h3>
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={clearEvents}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                Clear
              </Button>
              <Button
                onClick={() => setIsVisible(false)}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                Hide
              </Button>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {isConnected ? 'Connected' : 'Disconnected'} • {events.length} events
          </p>
        </div>
        
        <div className="max-h-80 overflow-y-auto">
          {events.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <p className="text-sm">No webhook events yet</p>
              <p className="text-xs mt-1">Events will appear here in real-time</p>
            </div>
          ) : (
            <div className="space-y-2 p-2">
              {events.map((event) => (
                <div
                  key={event.id}
                  className={`p-3 rounded-lg border ${getStatusColor(event.status)}`}
                >
                  <div className="flex items-start space-x-2">
                    <span className="text-lg">{getEventIcon(event)}</span>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {getEventTitle(event)}
                      </p>
                      <p className="text-xs opacity-75 mt-1">
                        {getEventDescription(event)}
                      </p>
                      <p className="text-xs opacity-50 mt-1">
                        {new Date(event.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

/**
 * Compact version for embedding in other components
 */
export function VivaWebhookStatus() {
  const [lastEvent, setLastEvent] = useState<WebhookEvent | null>(null);
  
  const { isConnected } = useVivaWebhookEvents(
    (vivaEvent) => {
      setLastEvent({
        id: `viva_${Date.now()}`,
        timestamp: new Date().toISOString(),
        type: 'viva_webhook',
        data: vivaEvent,
        status: vivaEvent.processed ? 'success' : 'error'
      });
    }
  );

  return (
    <div className="flex items-center space-x-2 text-sm">
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
      <span className="text-gray-600">
        {isConnected ? 'Webhook Connected' : 'Webhook Disconnected'}
      </span>
      {lastEvent && (
        <span className="text-xs text-gray-500">
          Last: {new Date(lastEvent.timestamp).toLocaleTimeString()}
        </span>
      )}
    </div>
  );
}
