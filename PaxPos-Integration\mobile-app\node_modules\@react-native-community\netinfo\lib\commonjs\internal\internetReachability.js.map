{"version": 3, "sources": ["internetReachability.ts"], "names": ["InternetReachability", "constructor", "configuration", "listener", "undefined", "isInternetReachable", "_isInternetReachable", "_listener", "expectsConnection", "_currentInternetReachabilityCheckHandler", "cancel", "_currentTimeoutHandle", "clearTimeout", "_configuration", "reachabilityShouldRun", "_setIsInternetReachable", "_checkInternetReachability", "controller", "AbortController", "responsePromise", "fetch", "reachabilityUrl", "headers", "reachabilityHeaders", "method", "reachabilityMethod", "cache", "signal", "timeoutH<PERSON>le", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "reachabilityRequestTimeout", "cancelPromise", "promise", "race", "then", "response", "reachabilityTest", "result", "nextTimeoutInterval", "reachabilityLongTimeout", "reachabilityShortTimeout", "catch", "error", "abort", "state", "useNativeReachability", "_setExpectsConnection", "isConnected"], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAUe,MAAMA,oBAAN,CAA2B;AAOxCC,EAAAA,WAAW,CACTC,aADS,EAETC,QAFS,EAGT;AAAA;;AAAA;;AAAA,kDAPyDC,SAOzD;;AAAA,sEAN0F,IAM1F;;AAAA,mDALoE,IAKpE;;AAAA,qDAMAC,mBADgC,IAEvB;AACT,UAAI,KAAKC,oBAAL,KAA8BD,mBAAlC,EAAuD;AACrD;AACD;;AAED,WAAKC,oBAAL,GAA4BD,mBAA5B;;AACA,WAAKE,SAAL,CAAe,KAAKD,oBAApB;AACD,KAdC;;AAAA,mDAgB+BE,iBAAD,IAA6C;AAC3E;AACA,UAAI,KAAKC,wCAAL,KAAkD,IAAtD,EAA4D;AAC1D,aAAKA,wCAAL,CAA8CC,MAA9C;;AACA,aAAKD,wCAAL,GAAgD,IAAhD;AACD,OAL0E,CAM3E;;;AACA,UAAI,KAAKE,qBAAL,KAA+B,IAAnC,EAAyC;AACvCC,QAAAA,YAAY,CAAC,KAAKD,qBAAN,CAAZ;AACA,aAAKA,qBAAL,GAA6B,IAA7B;AACD;;AAED,UAAIH,iBAAiB,IAAI,KAAKK,cAAL,CAAoBC,qBAApB,EAAzB,EAAsE;AACpE;AACA;AACA,YAAI,CAAC,KAAKR,oBAAV,EAAgC;AAC9B,eAAKS,uBAAL,CAA6B,IAA7B;AACD,SALmE,CAMpE;;;AACA,aAAKN,wCAAL,GAAgD,KAAKO,0BAAL,EAAhD;AACD,OARD,MAQO;AACL;AACA,aAAKD,uBAAL,CAA6B,KAA7B;AACD;AACF,KAxCC;;AAAA,wDA0CmC,MAAwC;AAC3E,YAAME,UAAU,GAAG,IAAIC,eAAJ,EAAnB;AAEA,YAAMC,eAAe,GAAGC,KAAK,CAAC,KAAKP,cAAL,CAAoBQ,eAArB,EAAsC;AACjEC,QAAAA,OAAO,EAAE,KAAKT,cAAL,CAAoBU,mBADoC;AAEjEC,QAAAA,MAAM,EAAE,KAAKX,cAAL,CAAoBY,kBAFqC;AAGjEC,QAAAA,KAAK,EAAE,UAH0D;AAIjEC,QAAAA,MAAM,EAAEV,UAAU,CAACU;AAJ8C,OAAtC,CAA7B,CAH2E,CAU3E;;AACA,UAAIC,aAAJ;AACA,YAAMC,cAAc,GAAG,IAAIC,OAAJ,CAAsB,CAACC,CAAD,EAAIC,MAAJ,KAAqB;AAChEJ,QAAAA,aAAa,GAAGK,UAAU,CACxB,MAAYD,MAAM,CAAC,UAAD,CADM,EAExB,KAAKnB,cAAL,CAAoBqB,0BAFI,CAA1B;AAID,OALsB,CAAvB,CAZ2E,CAmB3E;AACA;;AACA,UAAIxB,MAAkB,GAAG,MAAY,CAAE,CAAvC;;AACA,YAAMyB,aAAa,GAAG,IAAIL,OAAJ,CAAsB,CAACC,CAAD,EAAIC,MAAJ,KAAqB;AAC/DtB,QAAAA,MAAM,GAAG,MAAYsB,MAAM,CAAC,UAAD,CAA3B;AACD,OAFqB,CAAtB;AAIA,YAAMI,OAAO,GAAGN,OAAO,CAACO,IAAR,CAAa,CAC3BlB,eAD2B,EAE3BU,cAF2B,EAG3BM,aAH2B,CAAb,EAKbG,IALa,CAMXC,QAAD,IAAgC;AAC9B,eAAO,KAAK1B,cAAL,CAAoB2B,gBAApB,CAAqCD,QAArC,CAAP;AACD,OARW,EAUbD,IAVa,CAWXG,MAAD,IAAkB;AAChB,aAAK1B,uBAAL,CAA6B0B,MAA7B;;AACA,cAAMC,mBAAmB,GAAG,KAAKpC,oBAAL,GACxB,KAAKO,cAAL,CAAoB8B,uBADI,GAExB,KAAK9B,cAAL,CAAoB+B,wBAFxB;AAGA,aAAKjC,qBAAL,GAA6BsB,UAAU,CACrC,KAAKjB,0BADgC,EAErC0B,mBAFqC,CAAvC;AAID,OApBW,EAsBbG,KAtBa,CAuBXC,KAAD,IAAkD;AAChD,YAAI,eAAeA,KAAnB,EAA0B;AACxB7B,UAAAA,UAAU,CAAC8B,KAAX;AACD,SAFD,MAEO;AACL,cAAI,eAAeD,KAAnB,EAA0B;AACxB7B,YAAAA,UAAU,CAAC8B,KAAX;AACD;;AAED,eAAKhC,uBAAL,CAA6B,KAA7B;;AACA,eAAKJ,qBAAL,GAA6BsB,UAAU,CACrC,KAAKjB,0BADgC,EAErC,KAAKH,cAAL,CAAoB+B,wBAFiB,CAAvC;AAID;AACF,OArCW,EAuCd;AAvCc,OAwCbN,IAxCa,CAyCZ,MAAY;AACV1B,QAAAA,YAAY,CAACgB,aAAD,CAAZ;AACD,OA3CW,EA4CXkB,KAAD,IAAwB;AACtBlC,QAAAA,YAAY,CAACgB,aAAD,CAAZ;AACA,cAAMkB,KAAN;AACD,OA/CW,CAAhB;AAkDA,aAAO;AACLV,QAAAA,OADK;AAEL1B,QAAAA;AAFK,OAAP;AAID,KA1HC;;AAAA,oCA4HesC,KAAD,IAAwD;AACtE,UACE,OAAOA,KAAK,CAAC3C,mBAAb,KAAqC,SAArC,IACA,KAAKQ,cAAL,CAAoBoC,qBAFtB,EAGE;AACA,aAAKlC,uBAAL,CAA6BiC,KAAK,CAAC3C,mBAAnC;AACD,OALD,MAKO;AACL,aAAK6C,qBAAL,CAA2BF,KAAK,CAACG,WAAjC;AACD;AACF,KArIC;;AAAA,0CAuIoB,MAAkC;AACtD,aAAO,KAAK7C,oBAAZ;AACD,KAzIC;;AAAA,sCA2IgB,MAAY;AAC5B;AACA,UAAI,KAAKG,wCAAL,KAAkD,IAAtD,EAA4D;AAC1D,aAAKA,wCAAL,CAA8CC,MAA9C;;AACA,aAAKD,wCAAL,GAAgD,IAAhD;AACD,OAL2B,CAO5B;;;AACA,UAAI,KAAKE,qBAAL,KAA+B,IAAnC,EAAyC;AACvCC,QAAAA,YAAY,CAAC,KAAKD,qBAAN,CAAZ;AACA,aAAKA,qBAAL,GAA6B,IAA7B;AACD;AACF,KAvJC;;AACA,SAAKE,cAAL,GAAsBX,aAAtB;AACA,SAAKK,SAAL,GAAiBJ,QAAjB;AACD;;AAbuC", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport * as PrivateTypes from './privateTypes';\nimport * as Types from './types';\n\ninterface InternetReachabilityCheckHandler {\n  promise: Promise<void>;\n  cancel: () => void;\n}\n\nexport default class InternetReachability {\n  private _configuration: Types.NetInfoConfiguration;\n  private _listener: PrivateTypes.NetInfoInternetReachabilityChangeListener;\n  private _isInternetReachable: boolean | null | undefined = undefined;\n  private _currentInternetReachabilityCheckHandler: InternetReachabilityCheckHandler | null = null;\n  private _currentTimeoutHandle: ReturnType<typeof setTimeout> | null = null;\n\n  constructor(\n    configuration: Types.NetInfoConfiguration,\n    listener: PrivateTypes.NetInfoInternetReachabilityChangeListener,\n  ) {\n    this._configuration = configuration;\n    this._listener = listener;\n  }\n\n  private _setIsInternetReachable = (\n    isInternetReachable: boolean | null,\n  ): void => {\n    if (this._isInternetReachable === isInternetReachable) {\n      return;\n    }\n\n    this._isInternetReachable = isInternetReachable;\n    this._listener(this._isInternetReachable);\n  };\n\n  private _setExpectsConnection = (expectsConnection: boolean | null): void => {\n    // Cancel any pending check\n    if (this._currentInternetReachabilityCheckHandler !== null) {\n      this._currentInternetReachabilityCheckHandler.cancel();\n      this._currentInternetReachabilityCheckHandler = null;\n    }\n    // Cancel any pending timeout\n    if (this._currentTimeoutHandle !== null) {\n      clearTimeout(this._currentTimeoutHandle);\n      this._currentTimeoutHandle = null;\n    }\n\n    if (expectsConnection && this._configuration.reachabilityShouldRun()) {\n      // If we expect a connection, start the process for finding if we have one\n      // Set the state to \"null\" if it was previously false\n      if (!this._isInternetReachable) {\n        this._setIsInternetReachable(null);\n      }\n      // Start a network request to check for internet\n      this._currentInternetReachabilityCheckHandler = this._checkInternetReachability();\n    } else {\n      // If we don't expect a connection or don't run reachability check, just change the state to \"false\"\n      this._setIsInternetReachable(false);\n    }\n  };\n\n  private _checkInternetReachability = (): InternetReachabilityCheckHandler => {\n    const controller = new AbortController();\n\n    const responsePromise = fetch(this._configuration.reachabilityUrl, {\n      headers: this._configuration.reachabilityHeaders,\n      method: this._configuration.reachabilityMethod,\n      cache: 'no-cache',\n      signal: controller.signal,\n    });\n\n    // Create promise that will reject after the request timeout has been reached\n    let timeoutHandle: ReturnType<typeof setTimeout>;\n    const timeoutPromise = new Promise<Response>((_, reject): void => {\n      timeoutHandle = setTimeout(\n        (): void => reject('timedout'),\n        this._configuration.reachabilityRequestTimeout,\n      );\n    });\n\n    // Create promise that makes it possible to cancel a pending request through a reject\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    let cancel: () => void = (): void => {};\n    const cancelPromise = new Promise<Response>((_, reject): void => {\n      cancel = (): void => reject('canceled');\n    });\n\n    const promise = Promise.race([\n      responsePromise,\n      timeoutPromise,\n      cancelPromise,\n    ])\n      .then(\n        (response): Promise<boolean> => {\n          return this._configuration.reachabilityTest(response);\n        },\n      )\n      .then(\n        (result): void => {\n          this._setIsInternetReachable(result);\n          const nextTimeoutInterval = this._isInternetReachable\n            ? this._configuration.reachabilityLongTimeout\n            : this._configuration.reachabilityShortTimeout;\n          this._currentTimeoutHandle = setTimeout(\n            this._checkInternetReachability,\n            nextTimeoutInterval,\n          );\n        },\n      )\n      .catch(\n        (error: Error | 'timedout' | 'canceled'): void => {\n          if ('canceled' === error) {\n            controller.abort();\n          } else {\n            if ('timedout' === error) {\n              controller.abort();\n            }\n            \n            this._setIsInternetReachable(false);\n            this._currentTimeoutHandle = setTimeout(\n              this._checkInternetReachability,\n              this._configuration.reachabilityShortTimeout,\n            );\n          }\n        },\n      )\n      // Clear request timeout and propagate any errors\n      .then(\n        (): void => {\n          clearTimeout(timeoutHandle);\n        },\n        (error: Error): void => {\n          clearTimeout(timeoutHandle);\n          throw error;\n        },\n      );\n\n    return {\n      promise,\n      cancel,\n    };\n  };\n\n  public update = (state: PrivateTypes.NetInfoNativeModuleState): void => {\n    if (\n      typeof state.isInternetReachable === 'boolean' &&\n      this._configuration.useNativeReachability\n    ) {\n      this._setIsInternetReachable(state.isInternetReachable);\n    } else {\n      this._setExpectsConnection(state.isConnected);\n    }\n  };\n\n  public currentState = (): boolean | null | undefined => {\n    return this._isInternetReachable;\n  };\n\n  public tearDown = (): void => {\n    // Cancel any pending check\n    if (this._currentInternetReachabilityCheckHandler !== null) {\n      this._currentInternetReachabilityCheckHandler.cancel();\n      this._currentInternetReachabilityCheckHandler = null;\n    }\n\n    // Cancel any pending timeout\n    if (this._currentTimeoutHandle !== null) {\n      clearTimeout(this._currentTimeoutHandle);\n      this._currentTimeoutHandle = null;\n    }\n  };\n}\n"]}