import*as e from"../../../../core/common/common.js";import*as t from"../../../../core/i18n/i18n.js";import*as i from"../../../../core/platform/platform.js";import*as o from"../../../../core/root/root.js";import*as s from"../../../../core/sdk/sdk.js";import*as a from"../../../../models/issues_manager/issues_manager.js";import*as r from"../../../../panels/network/forward/forward.js";import*as n from"../../../components/icon_button/icon_button.js";import*as l from"../../legacy.js";import*as d from"../data_grid/data_grid.js";const c=new CSSStyleSheet;c.replaceSync(".cookies-table devtools-icon{margin-right:4px}.cookies-table td.flagged-cookie-attribute-cell devtools-icon{filter:grayscale()}.cookies-table tr.revealed.data-grid-data-grid-node.flagged-cookie-attribute-row:not(.selected):nth-child(odd){background-color:var(--sys-color-surface-yellow-high)}.cookies-table tr.revealed.data-grid-data-grid-node.flagged-cookie-attribute-row:not(.selected):nth-child(even){background-color:var(--sys-color-surface-yellow)}\n/*# sourceURL=cookiesTable.css */\n");const h={session:"Session",name:"Name",value:"Value",size:"Size",editableCookies:"Editable Cookies",cookies:"Cookies",na:"N/A",showRequestsWithThisCookie:"Show Requests With This Cookie",showIssueAssociatedWithThis:"Show issue associated with this cookie",sourcePortTooltip:"Shows the source port (range 1-65535) the cookie was set on. If the port is unknown, this shows -1.",sourceSchemeTooltip:"Shows the source scheme (`Secure`, `NonSecure`) the cookie was set on. If the scheme is unknown, this shows `Unset`.",timeAfter:"after {date}",timeAfterTooltip:"The expiration timestamp is {seconds}, which corresponds to a date after {date}",opaquePartitionKey:"(opaque)"},u=t.i18n.registerUIStrings("ui/legacy/components/cookie_table/CookiesTable.ts",h),p=t.i18n.getLocalizedString.bind(void 0,u),m=t.i18n.getLazilyComputedLocalizedString.bind(void 0,u)(h.session);class k extends l.Widget.VBox{saveCallback;refreshCallback;deleteCallback;dataGrid;lastEditedColumnId;data;cookieDomain;cookieToBlockedReasons;cookieToExemptionReason;constructor(e,t,i,s,a){super(),this.element.classList.add("cookies-table"),this.saveCallback=t,this.refreshCallback=i,this.deleteCallback=a;const r=Boolean(t),n=[{id:"name",title:p(h.name),sortable:!0,disclosure:r,sort:d.DataGrid.Order.Ascending,longText:!0,weight:24,editable:r},{id:"value",title:p(h.value),sortable:!0,longText:!0,weight:34,editable:r},{id:"domain",title:"Domain",sortable:!0,weight:7,editable:r},{id:"path",title:"Path",sortable:!0,weight:7,editable:r},{id:"expires",title:"Expires / Max-Age",sortable:!0,weight:7,editable:r},{id:"size",title:p(h.size),sortable:!0,align:"right",weight:7},{id:"http-only",title:"HttpOnly",sortable:!0,align:"center",weight:7,dataType:"Boolean",editable:r},{id:"secure",title:"Secure",sortable:!0,align:"center",weight:7,dataType:"Boolean",editable:r},{id:"same-site",title:"SameSite",sortable:!0,weight:7,editable:r},{id:"partition-key-site",title:"Partition Key Site",sortable:!0,weight:7,editable:r},{id:"has-cross-site-ancestor",title:"Cross Site",sortable:!0,align:"center",weight:7,dataType:"Boolean",editable:r},{id:"priority",title:"Priority",sortable:!0,weight:7,editable:r}];if(o.Runtime.experiments.isEnabled("experimental-cookie-features")){const e=[{id:"source-scheme",title:"SourceScheme",sortable:!0,align:"center",weight:7,editable:r},{id:"source-port",title:"SourcePort",sortable:!0,align:"center",weight:7,editable:r}];n.push(...e)}this.dataGrid=r?new d.DataGrid.DataGridImpl({displayName:p(h.editableCookies),columns:n,editCallback:this.onUpdateCookie.bind(this),deleteCallback:this.onDeleteCookie.bind(this),refreshCallback:i}):new d.DataGrid.DataGridImpl({displayName:p(h.cookies),columns:n,editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.dataGrid.setStriped(!0),this.dataGrid.setName("cookies-table"),this.dataGrid.addEventListener("SortingChanged",this.rebuildTable,this),this.dataGrid.setRowContextMenuCallback(this.populateContextMenu.bind(this)),e&&this.dataGrid.renderInline(),s&&this.dataGrid.addEventListener("SelectedNode",s,this),this.lastEditedColumnId=null,this.dataGrid.asWidget().show(this.element),this.data=[],this.cookieDomain="",this.cookieToBlockedReasons=null,this.cookieToExemptionReason=null}wasShown(){this.registerCSSFiles([c])}setCookies(e,t,i){this.setCookieFolders([{cookies:e,folderName:null}],t,i)}setCookieFolders(e,t,i){this.data=e,this.cookieToBlockedReasons=t||null,this.cookieToExemptionReason=i||null,this.rebuildTable()}setCookieDomain(e){this.cookieDomain=e}selectedCookie(){const e=this.dataGrid.selectedNode;return e?e.cookie:null}getSelectionCookies(){const e=this.dataGrid.selectedNode,t=e&&e.traverseNextNode(!0),i=e&&e.traversePreviousNode(!0);return{current:e&&e.cookie,neighbor:t&&t.cookie||i&&i.cookie}}willHide(){this.lastEditedColumnId=null}findSelectedCookie(e,t){if(!t)return null;const i=e.current,o=t.find((e=>this.isSameCookie(e,i)));if(o)return o;const s=e.neighbor,a=t.find((e=>this.isSameCookie(e,s)));return a||null}isSameCookie(e,t){return null!=t&&t.name()===e.name()&&t.domain()===e.domain()&&t.path()===e.path()}rebuildTable(){const e=this.dataGrid.element?.contains(document.activeElement),t=this.getSelectionCookies(),i=this.lastEditedColumnId;this.lastEditedColumnId=null,this.dataGrid.rootNode().removeChildren();for(let e=0;e<this.data.length;++e){const o=this.data[e],s=this.findSelectedCookie(t,o.cookies);if(o.folderName){const e={};e.name=o.folderName,e.value="",e.size=this.totalSize(o.cookies),e.domain="",e.path="",e.expires="",e["http-only"]="",e.secure="",e["same-site"]="",e["source-port"]="",e["source-scheme"]="",e.priority="";const t=new d.DataGrid.DataGridNode(e);t.selectable=!0,this.dataGrid.rootNode().appendChild(t),t.element().classList.add("row-group"),this.populateNode(t,o.cookies,s,i),t.expand()}else this.populateNode(this.dataGrid.rootNode(),o.cookies,s,i)}t.current&&i&&!this.dataGrid.selectedNode&&this.addInactiveNode(this.dataGrid.rootNode(),t.current,i),this.saveCallback&&this.dataGrid.addCreationNode(!1),e&&this.dataGrid.element.focus()}populateNode(e,t,i,o){if(e.removeChildren(),t){this.sortCookies(t);for(let s=0;s<t.length;++s){const a=t[s],r=this.createGridNode(a);e.appendChild(r),this.isSameCookie(a,i)&&(r.select(),null!==o&&this.dataGrid.startEditingNextEditableColumnOfDataGridNode(r,o))}}}addInactiveNode(e,t,i){const o=this.createGridNode(t);e.appendChild(o),o.select(),o.setInactive(!0),null!==i&&this.dataGrid.startEditingNextEditableColumnOfDataGridNode(o,i)}totalSize(e){let t=0;for(let i=0;e&&i<e.length;++i)t+=e[i].size();return t}sortCookies(e){const t=this.dataGrid.isSortOrderAscending()?1:-1;function o(e,t){switch(t){case"name":default:return String(e.name());case"value":return String(e.value());case"domain":return String(e.domain());case"path":return String(e.path());case"http-only":return String(e.httpOnly());case"secure":return String(e.secure());case"same-site":return String(e.sameSite());case"partition-key-site":return e.partitionKeyOpaque()?p(h.opaquePartitionKey):String(e.topLevelSite());case"has-cross-site-ancestor":return String(!!e.partitioned()&&e.hasCrossSiteAncestor());case"source-scheme":return String(e.sourceScheme())}}function s(e,i,o){return t*(e(i)-e(o))}let a;const r=this.dataGrid.sortColumnId()||"name";a="expires"===r?function(e,i){return e.session()!==i.session()?t*(e.session()?1:-1):e.session()?0:e.maxAge()&&i.maxAge()?t*(e.maxAge()-i.maxAge()):e.expires()&&i.expires()?t*(e.expires()-i.expires()):t*(e.expires()?1:-1)}:"size"===r?s.bind(null,(e=>e.size())):"source-port"===r?s.bind(null,(e=>e.sourcePort())):"priority"===r?function(e,i){const o=["Low","Medium","High"],s=o.indexOf(e.priority()),a=o.indexOf(i.priority());return t*(s-a)}:function(e,s,a){return t*i.StringUtilities.compare(o(s,e),o(a,e))}.bind(null,r),e.sort(a)}createGridNode(e){const i={};let o;if(i.name=e.name(),i.value=e.value(),0===e.type()?(i.domain=e.domain()?e.domain():p(h.na),i.path=e.path()?e.path():p(h.na)):(i.domain=e.domain()||"",i.path=e.path()||""),e.maxAge())i.expires=t.TimeUtilities.secondsToString(Math.floor(e.maxAge()));else if(e.expires()){const t=e.expires();if(t<0)i.expires=m();else{const e=864e13;if(t>e){const s=new Date(e).toISOString();i.expires=p(h.timeAfter,{date:s}),o=p(h.timeAfterTooltip,{seconds:t,date:s})}else i.expires=new Date(t).toISOString()}}else i.expires=0===e.type()?p(h.na):m();i.size=e.size(),i["http-only"]=e.httpOnly(),i.secure=e.secure(),i["same-site"]=e.sameSite()||"",i["source-port"]=e.sourcePort(),i["source-scheme"]=e.sourceScheme(),i.priority=e.priority()||"",i["partition-key-site"]=e.topLevelSite(),i["has-cross-site-ancestor"]=e.hasCrossSiteAncestor()?"true":"";const s=this.cookieToBlockedReasons?.get(e),a=this.cookieToExemptionReason?.get(e),r=new g(i,e,s||null,a||null);return o&&r.setExpiresTooltip(o),r.selectable=!0,r}onDeleteCookie(e){e.cookie&&this.deleteCallback&&this.deleteCallback(e.cookie,(()=>this.refresh()))}onUpdateCookie(e,t,i,o){this.lastEditedColumnId=t,this.setDefaults(e),this.isValidCookieData(e.data)?this.saveNode(e):e.setDirty(!0)}setDefaults(e){null===e.data.name&&(e.data.name=""),null===e.data.value&&(e.data.value=""),null===e.data.domain&&(e.data.domain=this.cookieDomain),null===e.data.path&&(e.data.path="/"),null===e.data.expires&&(e.data.expires=m()),null===e.data["partition-key"]&&(e.data["partition-key"]="")}saveNode(e){const t=e.cookie,i=this.createCookieFromData(e.data);e.cookie=i,this.saveCallback&&this.saveCallback(i,t).then((t=>{t?this.refresh():e.setDirty(!0)}))}createCookieFromData(e){const t=new s.Cookie.Cookie(e.name,e.value,null,e.priority);return t.addAttribute("domain",e.domain),t.addAttribute("path",e.path),e.expires&&e.expires!==m()&&t.addAttribute("expires",new Date(e.expires).toUTCString()),e["http-only"]&&t.addAttribute("http-only"),e.secure&&t.addAttribute("secure"),e["same-site"]&&t.addAttribute("same-site",e["same-site"]),"source-scheme"in e&&t.addAttribute("source-scheme",e["source-scheme"]),"source-port"in e&&t.addAttribute("source-port",Number.parseInt(e["source-port"],10)||void 0),e["partition-key-site"]&&t.setPartitionKey(e["partition-key-site"],Boolean(!!e["has-cross-site-ancestor"]&&e["has-cross-site-ancestor"])),t.setSize(e.name.length+e.value.length),t}isValidCookieData(e){return(Boolean(e.name)||Boolean(e.value))&&this.isValidDomain(e.domain)&&this.isValidPath(e.path)&&this.isValidDate(e.expires)&&this.isValidPartitionKey(e.PartitionKeySite)}isValidDomain(t){if(!t)return!0;const i=e.ParsedURL.ParsedURL.fromString("http://"+t);return null!==i&&i.domain()===t}isValidPath(t){const i=e.ParsedURL.ParsedURL.fromString("http://example.com"+t);return null!==i&&i.path===t}isValidDate(e){return""===e||e===m()||!isNaN(Date.parse(e))}isValidPartitionKey(t){if(!t)return!0;return null!==e.ParsedURL.ParsedURL.fromString(t)}refresh(){this.refreshCallback&&this.refreshCallback()}populateContextMenu(t,i){const o=i.cookie;if(!o)return;const s=o;t.revealSection().appendItem(p(h.showRequestsWithThisCookie),(()=>{const t=r.UIFilter.UIRequestFilter.filters([{filterType:r.UIFilter.FilterType.CookieDomain,filterValue:s.domain()},{filterType:r.UIFilter.FilterType.CookieName,filterValue:s.name()}]);e.Revealer.reveal(t)}),{jslogContext:"show-requests-with-this-cookie"}),a.RelatedIssue.hasIssues(s)&&t.revealSection().appendItem(p(h.showIssueAssociatedWithThis),(()=>{a.RelatedIssue.reveal(s)}),{jslogContext:"show-issue-associated-with-this"})}}class g extends d.DataGrid.DataGridNode{cookie;blockedReasons;exemptionReason;expiresTooltip;constructor(e,t,i,o){super(e),this.cookie=t,this.blockedReasons=i,this.exemptionReason=o}createCells(e){super.createCells(e),this.blockedReasons&&this.blockedReasons.length&&e.classList.add("flagged-cookie-attribute-row")}setExpiresTooltip(e){this.expiresTooltip=e}createCell(e){const t=super.createCell(e);"source-port"===e?l.Tooltip.Tooltip.install(t,p(h.sourcePortTooltip)):"source-scheme"===e?l.Tooltip.Tooltip.install(t,p(h.sourceSchemeTooltip)):"expires"===e&&this.expiresTooltip?l.Tooltip.Tooltip.install(t,this.expiresTooltip):l.Tooltip.Tooltip.install(t,t.textContent||"");let i="";if(this.blockedReasons)for(const t of this.blockedReasons){const o=t.attribute===e,s=!t.attribute&&"name"===e;(o||s)&&(i&&(i+="\n"),i+=t.uiString)}if(i){const o=new n.Icon.Icon;"name"===e&&a.RelatedIssue.hasThirdPartyPhaseoutCookieIssue(this.cookie)?(o.data={iconName:"warning-filled",color:"var(--icon-warning)",width:"14px",height:"14px"},o.onclick=()=>a.RelatedIssue.reveal(this.cookie),o.style.cursor="pointer"):(o.data={iconName:"info",color:"var(--icon-info)",width:"14px",height:"14px"},t.classList.add("flagged-cookie-attribute-cell")),o.title=i,t.insertBefore(o,t.firstChild)}if(this.exemptionReason?.uiString&&"name"===e){const e=new n.Icon.Icon;e.data={iconName:"info",color:"var(--icon-info)",width:"14px",height:"14px"},t.classList.add("flagged-cookie-attribute-cell"),e.title=this.exemptionReason.uiString,t.insertBefore(e,t.firstChild)}return t}}var b=Object.freeze({__proto__:null,CookiesTable:k,DataGridNode:g});export{b as CookiesTable};
