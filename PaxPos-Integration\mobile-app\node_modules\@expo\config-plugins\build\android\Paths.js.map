{"version": 3, "file": "Paths.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_fs", "_glob", "path", "_interopRequireWildcard", "_errors", "_modules", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "getProjectFilePath", "projectRoot", "name", "filePath", "globSync", "cwd", "absolute", "assert", "getLanguage", "extension", "extname", "UnexpectedError", "getFileInfo", "normalize", "contents", "fs", "readFileSync", "language", "getMainApplicationAsync", "getMainActivityAsync", "getGradleFilePath", "gradleName", "groo<PERSON><PERSON><PERSON>", "resolve", "ktPath", "isGroovy", "existsSync", "isKotlin", "Error", "getProjectBuildGradleFilePath", "join", "getProjectBuildGradleAsync", "getSettingsGradleFilePath", "getSettingsGradleAsync", "getAppBuildGradleFilePath", "getAppBuildGradleAsync", "getProjectPathOrThrowAsync", "projectPath", "directoryExistsAsync", "getAndroidManifestAsync", "getResourceFolderAsync", "getResourceXMLPathAsync", "kind", "resourcePath"], "sources": ["../../src/android/Paths.ts"], "sourcesContent": ["import assert from 'assert';\nimport fs from 'fs';\nimport { globSync } from 'glob';\nimport * as path from 'path';\n\nimport { ResourceKind } from './Resources';\nimport { UnexpectedError } from '../utils/errors';\nimport { directoryExistsAsync } from '../utils/modules';\n\nexport interface ProjectFile<L extends string = string> {\n  path: string;\n  language: L;\n  contents: string;\n}\n\nexport type ApplicationProjectFile = ProjectFile<'java' | 'kt'>;\nexport type GradleProjectFile = ProjectFile<'groovy' | 'kt'>;\n\nexport function getProjectFilePath(projectRoot: string, name: string): string {\n  const filePath = globSync(`android/app/src/main/java/**/${name}.@(java|kt)`, {\n    cwd: projectRoot,\n    absolute: true,\n  })[0];\n  assert(\n    filePath,\n    `Project file \"${name}\" does not exist in android project for root \"${projectRoot}\"`\n  );\n\n  return filePath;\n}\n\nfunction getLanguage(filePath: string): 'java' | 'groovy' | 'kt' {\n  const extension = path.extname(filePath);\n  switch (extension) {\n    case '.java':\n      return 'java';\n    case '.kts':\n    case '.kt':\n      return 'kt';\n    case '.groovy':\n    case '.gradle':\n      return 'groovy';\n    default:\n      throw new UnexpectedError(`Unexpected Android file extension: ${extension}`);\n  }\n}\n\nexport function getFileInfo(filePath: string) {\n  return {\n    path: path.normalize(filePath),\n    contents: fs.readFileSync(filePath, 'utf8'),\n    language: getLanguage(filePath) as any,\n  };\n}\n\nexport async function getMainApplicationAsync(\n  projectRoot: string\n): Promise<ApplicationProjectFile> {\n  const filePath = getProjectFilePath(projectRoot, 'MainApplication');\n  return getFileInfo(filePath);\n}\n\nexport async function getMainActivityAsync(projectRoot: string): Promise<ApplicationProjectFile> {\n  const filePath = getProjectFilePath(projectRoot, 'MainActivity');\n  return getFileInfo(filePath);\n}\n\nexport function getGradleFilePath(projectRoot: string, gradleName: string): string {\n  const groovyPath = path.resolve(projectRoot, `${gradleName}.gradle`);\n  const ktPath = path.resolve(projectRoot, `${gradleName}.gradle.kts`);\n\n  const isGroovy = fs.existsSync(groovyPath);\n  const isKotlin = !isGroovy && fs.existsSync(ktPath);\n\n  if (!isGroovy && !isKotlin) {\n    throw new Error(`Failed to find '${gradleName}.gradle' file for project: ${projectRoot}.`);\n  }\n  const filePath = isGroovy ? groovyPath : ktPath;\n  return filePath;\n}\n\nexport function getProjectBuildGradleFilePath(projectRoot: string): string {\n  return getGradleFilePath(path.join(projectRoot, 'android'), 'build');\n}\n\nexport async function getProjectBuildGradleAsync(projectRoot: string): Promise<GradleProjectFile> {\n  return getFileInfo(getProjectBuildGradleFilePath(projectRoot));\n}\n\nexport function getSettingsGradleFilePath(projectRoot: string): string {\n  return getGradleFilePath(path.join(projectRoot, 'android'), 'settings');\n}\n\nexport async function getSettingsGradleAsync(projectRoot: string): Promise<GradleProjectFile> {\n  return getFileInfo(getSettingsGradleFilePath(projectRoot));\n}\n\nexport function getAppBuildGradleFilePath(projectRoot: string): string {\n  return getGradleFilePath(path.join(projectRoot, 'android', 'app'), 'build');\n}\n\nexport async function getAppBuildGradleAsync(projectRoot: string): Promise<GradleProjectFile> {\n  return getFileInfo(getAppBuildGradleFilePath(projectRoot));\n}\n\nexport async function getProjectPathOrThrowAsync(projectRoot: string): Promise<string> {\n  const projectPath = path.join(projectRoot, 'android');\n  if (await directoryExistsAsync(projectPath)) {\n    return projectPath;\n  }\n  throw new Error(`Android project folder is missing in project: ${projectRoot}`);\n}\n\nexport async function getAndroidManifestAsync(projectRoot: string): Promise<string> {\n  const projectPath = await getProjectPathOrThrowAsync(projectRoot);\n  const filePath = path.join(projectPath, 'app/src/main/AndroidManifest.xml');\n  return filePath;\n}\n\nexport async function getResourceFolderAsync(projectRoot: string): Promise<string> {\n  const projectPath = await getProjectPathOrThrowAsync(projectRoot);\n  return path.join(projectPath, `app/src/main/res`);\n}\n\nexport async function getResourceXMLPathAsync(\n  projectRoot: string,\n  { kind = 'values', name }: { kind?: ResourceKind; name: 'colors' | 'strings' | 'styles' | string }\n): Promise<string> {\n  const resourcePath = await getResourceFolderAsync(projectRoot);\n\n  const filePath = path.join(resourcePath, `${kind}/${name}.xml`);\n  return filePath;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,KAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,SAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,QAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAwD,SAAAS,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAjB,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAWjD,SAASmB,kBAAkBA,CAACC,WAAmB,EAAEC,IAAY,EAAU;EAC5E,MAAMC,QAAQ,GAAG,IAAAC,gBAAQ,EAAC,gCAAgCF,IAAI,aAAa,EAAE;IAC3EG,GAAG,EAAEJ,WAAW;IAChBK,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,IAAAC,iBAAM,EACJJ,QAAQ,EACR,iBAAiBD,IAAI,iDAAiDD,WAAW,GACnF,CAAC;EAED,OAAOE,QAAQ;AACjB;AAEA,SAASK,WAAWA,CAACL,QAAgB,EAA4B;EAC/D,MAAMM,SAAS,GAAGjC,IAAI,CAAD,CAAC,CAACkC,OAAO,CAACP,QAAQ,CAAC;EACxC,QAAQM,SAAS;IACf,KAAK,OAAO;MACV,OAAO,MAAM;IACf,KAAK,MAAM;IACX,KAAK,KAAK;MACR,OAAO,IAAI;IACb,KAAK,SAAS;IACd,KAAK,SAAS;MACZ,OAAO,QAAQ;IACjB;MACE,MAAM,KAAIE,yBAAe,EAAC,sCAAsCF,SAAS,EAAE,CAAC;EAChF;AACF;AAEO,SAASG,WAAWA,CAACT,QAAgB,EAAE;EAC5C,OAAO;IACL3B,IAAI,EAAEA,IAAI,CAAD,CAAC,CAACqC,SAAS,CAACV,QAAQ,CAAC;IAC9BW,QAAQ,EAAEC,aAAE,CAACC,YAAY,CAACb,QAAQ,EAAE,MAAM,CAAC;IAC3Cc,QAAQ,EAAET,WAAW,CAACL,QAAQ;EAChC,CAAC;AACH;AAEO,eAAee,uBAAuBA,CAC3CjB,WAAmB,EACc;EACjC,MAAME,QAAQ,GAAGH,kBAAkB,CAACC,WAAW,EAAE,iBAAiB,CAAC;EACnE,OAAOW,WAAW,CAACT,QAAQ,CAAC;AAC9B;AAEO,eAAegB,oBAAoBA,CAAClB,WAAmB,EAAmC;EAC/F,MAAME,QAAQ,GAAGH,kBAAkB,CAACC,WAAW,EAAE,cAAc,CAAC;EAChE,OAAOW,WAAW,CAACT,QAAQ,CAAC;AAC9B;AAEO,SAASiB,iBAAiBA,CAACnB,WAAmB,EAAEoB,UAAkB,EAAU;EACjF,MAAMC,UAAU,GAAG9C,IAAI,CAAD,CAAC,CAAC+C,OAAO,CAACtB,WAAW,EAAE,GAAGoB,UAAU,SAAS,CAAC;EACpE,MAAMG,MAAM,GAAGhD,IAAI,CAAD,CAAC,CAAC+C,OAAO,CAACtB,WAAW,EAAE,GAAGoB,UAAU,aAAa,CAAC;EAEpE,MAAMI,QAAQ,GAAGV,aAAE,CAACW,UAAU,CAACJ,UAAU,CAAC;EAC1C,MAAMK,QAAQ,GAAG,CAACF,QAAQ,IAAIV,aAAE,CAACW,UAAU,CAACF,MAAM,CAAC;EAEnD,IAAI,CAACC,QAAQ,IAAI,CAACE,QAAQ,EAAE;IAC1B,MAAM,IAAIC,KAAK,CAAC,mBAAmBP,UAAU,8BAA8BpB,WAAW,GAAG,CAAC;EAC5F;EACA,MAAME,QAAQ,GAAGsB,QAAQ,GAAGH,UAAU,GAAGE,MAAM;EAC/C,OAAOrB,QAAQ;AACjB;AAEO,SAAS0B,6BAA6BA,CAAC5B,WAAmB,EAAU;EACzE,OAAOmB,iBAAiB,CAAC5C,IAAI,CAAD,CAAC,CAACsD,IAAI,CAAC7B,WAAW,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;AACtE;AAEO,eAAe8B,0BAA0BA,CAAC9B,WAAmB,EAA8B;EAChG,OAAOW,WAAW,CAACiB,6BAA6B,CAAC5B,WAAW,CAAC,CAAC;AAChE;AAEO,SAAS+B,yBAAyBA,CAAC/B,WAAmB,EAAU;EACrE,OAAOmB,iBAAiB,CAAC5C,IAAI,CAAD,CAAC,CAACsD,IAAI,CAAC7B,WAAW,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC;AACzE;AAEO,eAAegC,sBAAsBA,CAAChC,WAAmB,EAA8B;EAC5F,OAAOW,WAAW,CAACoB,yBAAyB,CAAC/B,WAAW,CAAC,CAAC;AAC5D;AAEO,SAASiC,yBAAyBA,CAACjC,WAAmB,EAAU;EACrE,OAAOmB,iBAAiB,CAAC5C,IAAI,CAAD,CAAC,CAACsD,IAAI,CAAC7B,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC;AAC7E;AAEO,eAAekC,sBAAsBA,CAAClC,WAAmB,EAA8B;EAC5F,OAAOW,WAAW,CAACsB,yBAAyB,CAACjC,WAAW,CAAC,CAAC;AAC5D;AAEO,eAAemC,0BAA0BA,CAACnC,WAAmB,EAAmB;EACrF,MAAMoC,WAAW,GAAG7D,IAAI,CAAD,CAAC,CAACsD,IAAI,CAAC7B,WAAW,EAAE,SAAS,CAAC;EACrD,IAAI,MAAM,IAAAqC,+BAAoB,EAACD,WAAW,CAAC,EAAE;IAC3C,OAAOA,WAAW;EACpB;EACA,MAAM,IAAIT,KAAK,CAAC,iDAAiD3B,WAAW,EAAE,CAAC;AACjF;AAEO,eAAesC,uBAAuBA,CAACtC,WAAmB,EAAmB;EAClF,MAAMoC,WAAW,GAAG,MAAMD,0BAA0B,CAACnC,WAAW,CAAC;EACjE,MAAME,QAAQ,GAAG3B,IAAI,CAAD,CAAC,CAACsD,IAAI,CAACO,WAAW,EAAE,kCAAkC,CAAC;EAC3E,OAAOlC,QAAQ;AACjB;AAEO,eAAeqC,sBAAsBA,CAACvC,WAAmB,EAAmB;EACjF,MAAMoC,WAAW,GAAG,MAAMD,0BAA0B,CAACnC,WAAW,CAAC;EACjE,OAAOzB,IAAI,CAAD,CAAC,CAACsD,IAAI,CAACO,WAAW,EAAE,kBAAkB,CAAC;AACnD;AAEO,eAAeI,uBAAuBA,CAC3CxC,WAAmB,EACnB;EAAEyC,IAAI,GAAG,QAAQ;EAAExC;AAA8E,CAAC,EACjF;EACjB,MAAMyC,YAAY,GAAG,MAAMH,sBAAsB,CAACvC,WAAW,CAAC;EAE9D,MAAME,QAAQ,GAAG3B,IAAI,CAAD,CAAC,CAACsD,IAAI,CAACa,YAAY,EAAE,GAAGD,IAAI,IAAIxC,IAAI,MAAM,CAAC;EAC/D,OAAOC,QAAQ;AACjB", "ignoreList": []}