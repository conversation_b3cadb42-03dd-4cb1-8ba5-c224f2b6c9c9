# PaxSoft POS - Render Deployment Guide

This guide explains how to deploy the PaxSoft POS system to Render.com cloud hosting platform.

## 🚀 Quick Deployment

### Prerequisites
- Render.com account
- GitHub repository with your code
- Payment provider credentials (Stripe, Square, etc.)

### 1. Connect Repository
1. Log in to [Render.com](https://render.com)
2. Click "New +" → "Blueprint"
3. Connect your GitHub repository
4. Select the repository containing this code

### 2. Configure Environment Variables
In the Render dashboard, set these environment variables:

#### Required Secrets
```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Square Configuration  
SQUARE_APPLICATION_ID=your_square_app_id
SQUARE_ACCESS_TOKEN=your_square_access_token
SQUARE_LOCATION_ID=your_square_location_id

# Move Payment Configuration
MOVE_PAYMENT_API_KEY=your_move_payment_api_key
```

#### Optional Configuration
```bash
# PAX POS Settings
PAX_MERCHANT_NAME="Your Business Name"
PAX_SIMULATION_MODE=true
PAX_RECEIPT_ENABLED=true

# Logging
LOG_LEVEL=info
```

### 3. Deploy
1. Click "Apply" to start deployment
2. Wait for both services to build and deploy
3. Access your application at the provided URLs

## 🏗️ Architecture Overview

### Services Deployed
1. **Backend API** (`paxsoft-pos-backend`)
   - Node.js/Fastify server
   - Handles all payment processing
   - PAX POS integration (simulation mode in cloud)
   - Receipt generation

2. **Frontend Web App** (`paxsoft-pos-frontend`)
   - React/Vite application
   - Static site deployment
   - Responsive web interface

3. **Database** (`paxsoft-pos-db`)
   - PostgreSQL database
   - Stores transactions and logs

## 🔧 PAX POS Integration in Cloud

### Simulation Mode
When deployed to Render (or any cloud platform), the PAX POS integration automatically switches to **simulation mode**:

- ✅ All POS features work normally
- ✅ Receipt generation functions properly
- ✅ Transaction processing is simulated
- ✅ Web interface remains fully functional
- ⚠️ No actual hardware communication

### Hardware vs Cloud Comparison

| Feature | Local Hardware | Cloud Deployment |
|---------|---------------|------------------|
| Payment Processing | Real PAX terminal | Simulated |
| Receipt Generation | Thermal printer + Web | Web only |
| Transaction Storage | Database | Database |
| Web Interface | Full functionality | Full functionality |
| Card Reading | Physical cards | Simulated |
| Connectivity Test | Real terminal status | Simulated status |

## 🌐 Production Considerations

### 1. Environment Configuration
```bash
# Production settings
NODE_ENV=production
PAX_SIMULATION_MODE=true
CORS_ORIGIN=https://your-domain.com
```

### 2. Security Headers
The deployment includes security headers:
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Referrer-Policy: strict-origin-when-cross-origin

### 3. SSL/HTTPS
- Render provides automatic SSL certificates
- All traffic is encrypted
- Payment processing is secure

### 4. Database Backups
- Render provides automated backups
- Consider additional backup strategies for production

## 📱 Mobile Compatibility

The web interface is fully responsive and works on:
- Desktop browsers
- Tablet devices
- Mobile phones
- Touch-screen terminals

## 🔄 Hybrid Deployment Strategy

For businesses that need both cloud and local hardware:

### Option 1: Cloud-First with Local Fallback
1. Deploy to Render for primary operations
2. Keep local PAX integration for hardware-specific needs
3. Use cloud for remote locations, local for main terminal

### Option 2: Local-First with Cloud Backup
1. Run locally with PAX hardware
2. Use cloud deployment as backup/remote access
3. Sync data between environments

## 🛠️ Development Workflow

### Local Development
```bash
# Backend
cd backend
npm install
npm run dev

# Frontend  
cd frontend
npm install
npm run dev
```

### Testing Before Deployment
```bash
# Build and test locally
npm run build
npm run start:prod
```

### Deployment Pipeline
1. Push to GitHub
2. Render auto-deploys from main branch
3. Monitor deployment logs
4. Test deployed application

## 📊 Monitoring and Logs

### Application Logs
- Backend logs available in Render dashboard
- Frontend errors logged to browser console
- Transaction logs stored in database

### Health Checks
- Backend: `/health` endpoint
- Frontend: Automatic health monitoring
- Database: Connection monitoring

### Performance Monitoring
- Response time tracking
- Error rate monitoring
- Resource usage alerts

## 🔧 Troubleshooting

### Common Issues

#### 1. Environment Variables Not Set
**Problem**: Services fail to start
**Solution**: Verify all required environment variables are set in Render dashboard

#### 2. Database Connection Issues
**Problem**: Backend can't connect to database
**Solution**: Check database service status and connection string

#### 3. CORS Errors
**Problem**: Frontend can't communicate with backend
**Solution**: Verify CORS_ORIGIN is set correctly

#### 4. Payment Provider Issues
**Problem**: Payment processing fails
**Solution**: Verify API keys and webhook configurations

### Support Resources
- [Render Documentation](https://render.com/docs)
- [GitHub Issues](https://github.com/your-repo/issues)
- Application logs in Render dashboard

## 🚀 Next Steps

After successful deployment:

1. **Test All Features**
   - Payment processing
   - Receipt generation
   - Transaction history
   - User interface

2. **Configure Webhooks**
   - Set up payment provider webhooks
   - Point to your Render backend URL

3. **Monitor Performance**
   - Check response times
   - Monitor error rates
   - Review transaction logs

4. **Scale as Needed**
   - Upgrade Render plans for higher traffic
   - Add monitoring and alerting
   - Implement backup strategies

## 📞 Support

For deployment issues or questions:
- Check the troubleshooting section above
- Review Render documentation
- Contact support through appropriate channels
