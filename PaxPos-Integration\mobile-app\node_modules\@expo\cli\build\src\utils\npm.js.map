{"version": 3, "sources": ["../../../src/utils/npm.ts"], "sourcesContent": ["import { J<PERSON><PERSON>Value } from '@expo/json-file';\nimport spawnAsync from '@expo/spawn-async';\nimport assert from 'assert';\nimport crypto from 'crypto';\nimport fs from 'fs';\nimport slugify from 'slugify';\nimport { PassThrough, Readable, Stream } from 'stream';\nimport { extract as tarExtract, TarOptionsWithAliases } from 'tar';\nimport { promisify } from 'util';\n\nimport { createEntryResolver } from './createFileTransform';\nimport { ensureDirectoryAsync } from './dir';\nimport { CommandError } from './errors';\nimport { createCachedFetch } from '../api/rest/client';\n\nconst debug = require('debug')('expo:utils:npm') as typeof console.log;\n\nconst cachedFetch = createCachedFetch({\n  cacheDirectory: 'template-cache',\n  // Time to live. How long (in ms) responses remain cached before being automatically ejected. If undefined, responses are never automatically ejected from the cache.\n  // ttl: 1000,\n});\n\nexport function sanitizeNpmPackageName(name: string): string {\n  // https://github.com/npm/validate-npm-package-name/#naming-rules\n  return (\n    applyKnownNpmPackageNameRules(name) ||\n    applyKnownNpmPackageNameRules(slugify(name)) ||\n    // If nothing is left use 'app' like we do in Xcode projects.\n    'app'\n  );\n}\n\nfunction applyKnownNpmPackageNameRules(name: string): string | null {\n  // https://github.com/npm/validate-npm-package-name/#naming-rules\n\n  // package name cannot start with '.' or '_'.\n  while (/^(\\.|_)/.test(name)) {\n    name = name.substring(1);\n  }\n\n  name = name.toLowerCase().replace(/[^a-zA-Z._\\-/@]/g, '');\n\n  return (\n    name\n      // .replace(/![a-z0-9-._~]+/g, '')\n      // Remove special characters\n      .normalize('NFD')\n      .replace(/[\\u0300-\\u036f]/g, '') || null\n  );\n}\n\nexport async function npmViewAsync(...props: string[]): Promise<JSONValue> {\n  const cmd = ['view', ...props, '--json'];\n  const results = (await spawnAsync('npm', cmd)).stdout?.trim();\n  const cmdString = `npm ${cmd.join(' ')}`;\n  debug('Run:', cmdString);\n  if (!results) {\n    return null;\n  }\n  try {\n    return JSON.parse(results);\n  } catch (error: any) {\n    throw new Error(\n      `Could not parse JSON returned from \"${cmdString}\".\\n\\n${results}\\n\\nError: ${error.message}`\n    );\n  }\n}\n\n/** Given a package name like `expo` or `expo@beta`, return the registry URL if it exists. */\nexport async function getNpmUrlAsync(packageName: string): Promise<string> {\n  const results = await npmViewAsync(packageName, 'dist');\n\n  assert(results, `Could not get npm url for package \"${packageName}\"`);\n\n  // Fully qualified url returns an object.\n  // Example:\n  // 𝝠 npm view expo-template-bare-minimum@sdk-33 dist --json\n  if (typeof results === 'object' && !Array.isArray(results)) {\n    return results.tarball as string;\n  }\n\n  // When the tag is arbitrary, the tarball is an array, return the last value as it's the most recent.\n  // Example:\n  // 𝝠 npm view expo-template-bare-minimum@33 dist --json\n  if (Array.isArray(results)) {\n    const lastResult = results[results.length - 1];\n\n    if (lastResult && typeof lastResult === 'object' && !Array.isArray(lastResult)) {\n      return lastResult.tarball as string;\n    }\n  }\n\n  throw new CommandError(\n    'Expected results of `npm view ...` to be an array or string. Instead found: ' + results\n  );\n}\n\n// @ts-ignore\nconst pipeline = promisify(Stream.pipeline);\n\nexport async function downloadAndExtractNpmModuleAsync(\n  npmName: string,\n  props: ExtractProps\n): Promise<string> {\n  const url = await getNpmUrlAsync(npmName);\n\n  debug('Fetch from URL:', url);\n  return await extractNpmTarballFromUrlAsync(url, props);\n}\n\nexport async function extractLocalNpmTarballAsync(\n  tarFilePath: string,\n  props: ExtractProps\n): Promise<string> {\n  const readStream = fs.createReadStream(tarFilePath);\n  return await extractNpmTarballAsync(readStream, props);\n}\n\nexport type ExtractProps = {\n  name: string;\n  cwd: string;\n  strip?: number;\n  fileList?: string[];\n  /** The checksum algorithm to use when verifying the tarball. */\n  checksumAlgorithm?: string;\n  /** An optional filter to selectively extract specific paths */\n  filter?: TarOptionsWithAliases['filter'];\n};\n\nasync function createUrlStreamAsync(url: string) {\n  const response = await cachedFetch(url);\n  if (!response.ok || !response.body) {\n    throw new Error(`Unexpected response: ${response.statusText}. From url: ${url}`);\n  }\n\n  return Readable.fromWeb(response.body);\n}\n\nexport async function extractNpmTarballFromUrlAsync(\n  url: string,\n  props: ExtractProps\n): Promise<string> {\n  return await extractNpmTarballAsync(await createUrlStreamAsync(url), props);\n}\n\n/**\n * Extracts a tarball stream to a directory and returns the checksum of the tarball.\n */\nexport async function extractNpmTarballAsync(\n  stream: NodeJS.ReadableStream,\n  props: ExtractProps\n): Promise<string> {\n  const { cwd, strip, name, fileList = [], filter } = props;\n\n  await ensureDirectoryAsync(cwd);\n\n  const hash = crypto.createHash(props.checksumAlgorithm ?? 'md5');\n  const transformStream = new PassThrough();\n  transformStream.on('data', (chunk) => {\n    hash.update(chunk);\n  });\n\n  await pipeline(\n    stream,\n    transformStream,\n    tarExtract(\n      {\n        cwd,\n        filter,\n        onentry: createEntryResolver(name),\n        strip: strip ?? 1,\n      },\n      fileList\n    )\n  );\n\n  return hash.digest('hex');\n}\n"], "names": ["downloadAndExtractNpmModuleAsync", "extractLocalNpmTarballAsync", "extractNpmTarballAsync", "extractNpmTarballFromUrlAsync", "getNpmUrlAsync", "npmViewAsync", "sanitizeNpmPackageName", "debug", "require", "cachedFetch", "createCachedFetch", "cacheDirectory", "name", "applyKnownNpmPackageNameRules", "slugify", "test", "substring", "toLowerCase", "replace", "normalize", "props", "cmd", "results", "spawnAsync", "stdout", "trim", "cmdString", "join", "JSON", "parse", "error", "Error", "message", "packageName", "assert", "Array", "isArray", "tarball", "lastResult", "length", "CommandError", "pipeline", "promisify", "Stream", "npmName", "url", "tar<PERSON><PERSON><PERSON><PERSON>", "readStream", "fs", "createReadStream", "createUrlStreamAsync", "response", "ok", "body", "statusText", "Readable", "fromWeb", "stream", "cwd", "strip", "fileList", "filter", "ensureDirectoryAsync", "hash", "crypto", "createHash", "checksumAlgorithm", "transformStream", "PassThrough", "on", "chunk", "update", "tarExtract", "onentry", "createEntryResolver", "digest"], "mappings": ";;;;;;;;;;;IAqGsBA,gCAAgC;eAAhCA;;IAUAC,2BAA2B;eAA3BA;;IAsCAC,sBAAsB;eAAtBA;;IAVAC,6BAA6B;eAA7BA;;IArEAC,cAAc;eAAdA;;IAlBAC,YAAY;eAAZA;;IA7BNC,sBAAsB;eAAtBA;;;;gEAtBO;;;;;;;gEACJ;;;;;;;gEACA;;;;;;;gEACJ;;;;;;;gEACK;;;;;;;yBAC0B;;;;;;;yBACe;;;;;;;yBACnC;;;;;;qCAEU;qBACC;wBACR;wBACK;;;;;;AAElC,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,cAAcC,IAAAA,yBAAiB,EAAC;IACpCC,gBAAgB;AAGlB;AAEO,SAASL,uBAAuBM,IAAY;IACjD,iEAAiE;IACjE,OACEC,8BAA8BD,SAC9BC,8BAA8BC,IAAAA,kBAAO,EAACF,UACtC,6DAA6D;IAC7D;AAEJ;AAEA,SAASC,8BAA8BD,IAAY;IACjD,iEAAiE;IAEjE,6CAA6C;IAC7C,MAAO,UAAUG,IAAI,CAACH,MAAO;QAC3BA,OAAOA,KAAKI,SAAS,CAAC;IACxB;IAEAJ,OAAOA,KAAKK,WAAW,GAAGC,OAAO,CAAC,oBAAoB;IAEtD,OACEN,IACE,kCAAkC;IAClC,4BAA4B;KAC3BO,SAAS,CAAC,OACVD,OAAO,CAAC,oBAAoB,OAAO;AAE1C;AAEO,eAAeb,aAAa,GAAGe,KAAe;QAEnC;IADhB,MAAMC,MAAM;QAAC;WAAWD;QAAO;KAAS;IACxC,MAAME,WAAU,UAAA,AAAC,CAAA,MAAMC,IAAAA,qBAAU,EAAC,OAAOF,IAAG,EAAGG,MAAM,qBAArC,QAAuCC,IAAI;IAC3D,MAAMC,YAAY,CAAC,IAAI,EAAEL,IAAIM,IAAI,CAAC,MAAM;IACxCpB,MAAM,QAAQmB;IACd,IAAI,CAACJ,SAAS;QACZ,OAAO;IACT;IACA,IAAI;QACF,OAAOM,KAAKC,KAAK,CAACP;IACpB,EAAE,OAAOQ,OAAY;QACnB,MAAM,IAAIC,MACR,CAAC,oCAAoC,EAAEL,UAAU,MAAM,EAAEJ,QAAQ,WAAW,EAAEQ,MAAME,OAAO,EAAE;IAEjG;AACF;AAGO,eAAe5B,eAAe6B,WAAmB;IACtD,MAAMX,UAAU,MAAMjB,aAAa4B,aAAa;IAEhDC,IAAAA,iBAAM,EAACZ,SAAS,CAAC,mCAAmC,EAAEW,YAAY,CAAC,CAAC;IAEpE,yCAAyC;IACzC,WAAW;IACX,4DAA4D;IAC5D,IAAI,OAAOX,YAAY,YAAY,CAACa,MAAMC,OAAO,CAACd,UAAU;QAC1D,OAAOA,QAAQe,OAAO;IACxB;IAEA,qGAAqG;IACrG,WAAW;IACX,wDAAwD;IACxD,IAAIF,MAAMC,OAAO,CAACd,UAAU;QAC1B,MAAMgB,aAAahB,OAAO,CAACA,QAAQiB,MAAM,GAAG,EAAE;QAE9C,IAAID,cAAc,OAAOA,eAAe,YAAY,CAACH,MAAMC,OAAO,CAACE,aAAa;YAC9E,OAAOA,WAAWD,OAAO;QAC3B;IACF;IAEA,MAAM,IAAIG,oBAAY,CACpB,iFAAiFlB;AAErF;AAEA,aAAa;AACb,MAAMmB,WAAWC,IAAAA,iBAAS,EAACC,gBAAM,CAACF,QAAQ;AAEnC,eAAezC,iCACpB4C,OAAe,EACfxB,KAAmB;IAEnB,MAAMyB,MAAM,MAAMzC,eAAewC;IAEjCrC,MAAM,mBAAmBsC;IACzB,OAAO,MAAM1C,8BAA8B0C,KAAKzB;AAClD;AAEO,eAAenB,4BACpB6C,WAAmB,EACnB1B,KAAmB;IAEnB,MAAM2B,aAAaC,aAAE,CAACC,gBAAgB,CAACH;IACvC,OAAO,MAAM5C,uBAAuB6C,YAAY3B;AAClD;AAaA,eAAe8B,qBAAqBL,GAAW;IAC7C,MAAMM,WAAW,MAAM1C,YAAYoC;IACnC,IAAI,CAACM,SAASC,EAAE,IAAI,CAACD,SAASE,IAAI,EAAE;QAClC,MAAM,IAAItB,MAAM,CAAC,qBAAqB,EAAEoB,SAASG,UAAU,CAAC,YAAY,EAAET,KAAK;IACjF;IAEA,OAAOU,kBAAQ,CAACC,OAAO,CAACL,SAASE,IAAI;AACvC;AAEO,eAAelD,8BACpB0C,GAAW,EACXzB,KAAmB;IAEnB,OAAO,MAAMlB,uBAAuB,MAAMgD,qBAAqBL,MAAMzB;AACvE;AAKO,eAAelB,uBACpBuD,MAA6B,EAC7BrC,KAAmB;IAEnB,MAAM,EAAEsC,GAAG,EAAEC,KAAK,EAAE/C,IAAI,EAAEgD,WAAW,EAAE,EAAEC,MAAM,EAAE,GAAGzC;IAEpD,MAAM0C,IAAAA,yBAAoB,EAACJ;IAE3B,MAAMK,OAAOC,iBAAM,CAACC,UAAU,CAAC7C,MAAM8C,iBAAiB,IAAI;IAC1D,MAAMC,kBAAkB,IAAIC,CAAAA,SAAU,aAAC;IACvCD,gBAAgBE,EAAE,CAAC,QAAQ,CAACC;QAC1BP,KAAKQ,MAAM,CAACD;IACd;IAEA,MAAM7B,SACJgB,QACAU,iBACAK,IAAAA,cAAU,EACR;QACEd;QACAG;QACAY,SAASC,IAAAA,wCAAmB,EAAC9D;QAC7B+C,OAAOA,SAAS;IAClB,GACAC;IAIJ,OAAOG,KAAKY,MAAM,CAAC;AACrB"}