/**
 * PAX Receipt Viewer Component
 * Displays and manages PAX POS receipts with print functionality
 */

import { useState, useRef } from 'react';
import { PaxPaymentResponse } from '../services/paxPosService';

interface PaxReceiptViewerProps {
  transaction: PaxPaymentResponse;
  onClose?: () => void;
  showPrintOptions?: boolean;
}

export function PaxReceiptViewer({ 
  transaction, 
  onClose, 
  showPrintOptions = true 
}: PaxReceiptViewerProps) {
  const [selectedCopy, setSelectedCopy] = useState<'customer' | 'merchant'>('customer');
  const [isPrinting, setIsPrinting] = useState(false);
  const receiptRef = useRef<HTMLDivElement>(null);

  const handlePrint = async () => {
    if (!receiptRef.current) return;

    setIsPrinting(true);
    
    try {
      // Create a new window for printing
      const printWindow = window.open('', '_blank', 'width=400,height=600');
      if (!printWindow) {
        throw new Error('Unable to open print window. Please check popup blockers.');
      }

      // Get the receipt content
      const receiptContent = receiptRef.current.innerHTML;
      
      // Create print-optimized HTML
      const printHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>PAX Receipt - ${transaction.transactionId}</title>
          <style>
            body {
              font-family: 'Courier New', monospace;
              font-size: 12px;
              line-height: 1.2;
              margin: 0;
              padding: 10px;
              background: white;
              color: black;
            }
            .receipt-container {
              max-width: 300px;
              margin: 0 auto;
            }
            .receipt-header {
              text-align: center;
              border-bottom: 1px solid #000;
              padding-bottom: 5px;
              margin-bottom: 10px;
            }
            .receipt-line {
              display: flex;
              justify-content: space-between;
              margin: 2px 0;
            }
            .receipt-separator {
              border-top: 1px dashed #000;
              margin: 5px 0;
            }
            .receipt-footer {
              text-align: center;
              margin-top: 10px;
              padding-top: 5px;
              border-top: 1px solid #000;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="receipt-container">
            ${receiptContent}
          </div>
          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              };
            };
          </script>
        </body>
        </html>
      `;

      printWindow.document.write(printHTML);
      printWindow.document.close();

    } catch (error) {
      console.error('Print error:', error);
      alert('Failed to print receipt. Please try again.');
    } finally {
      setIsPrinting(false);
    }
  };

  const handleDownload = () => {
    if (!transaction.receiptData) return;

    const receiptText = transaction.receiptData[selectedCopy];
    const blob = new Blob([receiptText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `pax-receipt-${transaction.transactionId}-${selectedCopy}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  };

  const formatReceiptForDisplay = (receiptText: string) => {
    return receiptText.split('\n').map((line, index) => {
      if (line.includes('=====')) {
        return <div key={index} className="receipt-separator"></div>;
      }
      
      if (line.includes(':') && !line.includes('====')) {
        const [label, value] = line.split(':');
        return (
          <div key={index} className="receipt-line">
            <span>{label.trim()}:</span>
            <span>{value?.trim()}</span>
          </div>
        );
      }
      
      return (
        <div key={index} className={`text-center ${
          line.includes('COPY') || line.includes('PAXSOFT') ? 'font-bold' : ''
        }`}>
          {line}
        </div>
      );
    });
  };

  if (!transaction.receiptData) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-gray-500">
          <p>No receipt data available for this transaction.</p>
          {onClose && (
            <button
              onClick={onClose}
              className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Close
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Receipt Viewer</h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        )}
      </div>

      {/* Copy Selection */}
      <div className="flex space-x-2 mb-4">
        <button
          onClick={() => setSelectedCopy('customer')}
          className={`px-4 py-2 rounded-md transition-colors ${
            selectedCopy === 'customer'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Customer Copy
        </button>
        <button
          onClick={() => setSelectedCopy('merchant')}
          className={`px-4 py-2 rounded-md transition-colors ${
            selectedCopy === 'merchant'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Merchant Copy
        </button>
      </div>

      {/* Receipt Display */}
      <div className="border border-gray-300 rounded-md p-4 mb-4 bg-gray-50">
        <div 
          ref={receiptRef}
          className="font-mono text-sm leading-tight max-w-xs mx-auto"
          style={{ fontFamily: 'Courier New, monospace' }}
        >
          {formatReceiptForDisplay(transaction.receiptData[selectedCopy])}
        </div>
      </div>

      {/* Action Buttons */}
      {showPrintOptions && (
        <div className="flex flex-wrap gap-3">
          <button
            onClick={handlePrint}
            disabled={isPrinting}
            className="flex-1 min-w-[120px] bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
          >
            {isPrinting ? 'Printing...' : '🖨️ Print Receipt'}
          </button>
          
          <button
            onClick={handleDownload}
            className="flex-1 min-w-[120px] bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
          >
            📄 Download
          </button>
          
          <button
            onClick={() => {
              if (navigator.share && transaction.receiptData) {
                navigator.share({
                  title: `PAX Receipt - ${transaction.transactionId}`,
                  text: transaction.receiptData[selectedCopy],
                });
              } else {
                // Fallback: copy to clipboard
                if (transaction.receiptData) {
                  navigator.clipboard.writeText(transaction.receiptData[selectedCopy]);
                  alert('Receipt copied to clipboard');
                }
              }
            }}
            className="flex-1 min-w-[120px] bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors"
          >
            📤 Share
          </button>
        </div>
      )}

      {/* Transaction Info */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Transaction Details</h3>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <span className="text-gray-600">Transaction ID:</span>
            <span className="ml-2 font-medium">{transaction.transactionId}</span>
          </div>
          {transaction.authCode && (
            <div>
              <span className="text-gray-600">Auth Code:</span>
              <span className="ml-2 font-medium">{transaction.authCode}</span>
            </div>
          )}
          {transaction.cardInfo && (
            <>
              <div>
                <span className="text-gray-600">Card:</span>
                <span className="ml-2 font-medium">
                  {transaction.cardInfo.brand} ****{transaction.cardInfo.last4}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Entry:</span>
                <span className="ml-2 font-medium">{transaction.cardInfo.entryMethod}</span>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// Utility component for displaying receipts in a modal
interface PaxReceiptModalProps extends PaxReceiptViewerProps {
  isOpen: boolean;
}

export function PaxReceiptModal({ isOpen, ...props }: PaxReceiptModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="max-w-md w-full max-h-[90vh] overflow-y-auto">
        <PaxReceiptViewer {...props} />
      </div>
    </div>
  );
}
