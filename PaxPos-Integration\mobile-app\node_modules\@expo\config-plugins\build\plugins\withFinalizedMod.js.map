{"version": 3, "file": "withFinalizedMod.js", "names": ["_withMod", "data", "require", "withFinalizedMod", "config", "platform", "action", "with<PERSON><PERSON>", "mod", "exports"], "sources": ["../../src/plugins/withFinalizedMod.ts"], "sourcesContent": ["import { withMod } from './withMod';\nimport { ConfigPlugin, Mod, ModPlatform } from '../Plugin.types';\n\n/**\n * Mods that don't modify any data, all unresolved functionality is performed inside a finalized mod.\n * All finalized mods run after all the other mods.\n *\n * @param config\n * @param platform\n * @param action\n */\nexport const withFinalizedMod: ConfigPlugin<[ModPlatform, Mod<unknown>]> = (\n  config,\n  [platform, action]\n) => {\n  return withMod(config, {\n    platform,\n    mod: 'finalized',\n    action,\n  });\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAME,gBAA2D,GAAGA,CACzEC,MAAM,EACN,CAACC,QAAQ,EAAEC,MAAM,CAAC,KACf;EACH,OAAO,IAAAC,kBAAO,EAACH,MAAM,EAAE;IACrBC,QAAQ;IACRG,GAAG,EAAE,WAAW;IAChBF;EACF,CAAC,CAAC;AACJ,CAAC;AAACG,OAAA,CAAAN,gBAAA,GAAAA,gBAAA", "ignoreList": []}