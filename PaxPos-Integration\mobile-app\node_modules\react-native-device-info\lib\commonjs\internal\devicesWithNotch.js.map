{"version": 3, "sources": ["devicesWithNotch.ts"], "names": ["devicesWithNotch", "brand", "model"], "mappings": ";;;;;;AAEA,MAAMA,gBAA+B,GAAG,CACtC;AACEC,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CADsC,EAKtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CALsC,EAStC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CATsC,EAatC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAbsC,EAiBtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjBsC,EAqBtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArBsC,EAyBtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzBsC,EA6BtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7BsC,EAiCtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjCsC,EAqCtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArCsC,EAyCtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzCsC,EA6CtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7CsC,EAiDtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjDsC,EAqDtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArDsC,EAyDtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzDsC,EA6DtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7DsC,EAiEtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjEsC,EAqEtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArEsC,EAyEtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzEsC,EA6EtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7EsC,EAiFtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjFsC,EAqFtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArFsC,EAyFtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzFsC,EA6FtC;AACED,EAAAA,KAAK,EAAE,MADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7FsC,EAiGtC;AACED,EAAAA,KAAK,EAAE,MADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjGsC,EAqGtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArGsC,EAyGtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzGsC,EA6GtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7GsC,EAiHtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjHsC,EAqHtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArHsC,EAyHtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzHsC,EA6HtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7HsC,EAiItC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjIsC,EAqItC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArIsC,EAyItC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzIsC,EA6ItC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7IsC,EAiJtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjJsC,EAqJtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE,SAFT,CAEoB;;AAFpB,CArJsC,EAyJtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzJsC,EA6JtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7JsC,EAiKtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE,SAFT,CAEoB;;AAFpB,CAjKsC,EAqKtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArKsC,EAyKtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzKsC,EA6KtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7KsC,EAiLtC;AACED,EAAAA,KAAK,EAAE,IADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjLsC,EAqLtC;AACED,EAAAA,KAAK,EAAE,IADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArLsC,EAyLtC;AACED,EAAAA,KAAK,EAAE,IADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzLsC,EA6LtC;AACED,EAAAA,KAAK,EAAE,IADT;AAEEC,EAAAA,KAAK,EAAE,SAFT,CAEoB;;AAFpB,CA7LsC,EAiMtC;AACED,EAAAA,KAAK,EAAE,IADT;AAEEC,EAAAA,KAAK,EAAE,SAFT,CAEoB;;AAFpB,CAjMsC,EAqMtC;AACED,EAAAA,KAAK,EAAE,IADT;AAEEC,EAAAA,KAAK,EAAE,SAFT,CAEoB;;AAFpB,CArMsC,EAyMtC;AACED,EAAAA,KAAK,EAAE,UADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzMsC,EA6MtC;AACED,EAAAA,KAAK,EAAE,UADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7MsC,EAiNtC;AACED,EAAAA,KAAK,EAAE,UADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjNsC,EAqNtC;AACED,EAAAA,KAAK,EAAE,UADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArNsC,EAyNtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzNsC,EA6NtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7NsC,EAiOtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjOsC,EAqOtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArOsC,EAyOtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzOsC,EA6OtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7OsC,EAiPtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjPsC,EAqPtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArPsC,EAyPtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzPsC,EA6PtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7PsC,EAiQtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjQsC,EAqQtC;AACED,EAAAA,KAAK,EAAE,MADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArQsC,EAyQtC;AACED,EAAAA,KAAK,EAAE,MADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzQsC,EA6QtC;AACED,EAAAA,KAAK,EAAE,MADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7QsC,EAiRtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjRsC,EAqRtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArRsC,EAyRtC;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzRsC,EA6RtC;AACED,EAAAA,KAAK,EAAE,MADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7RsC,EAiStC;AACED,EAAAA,KAAK,EAAE,MADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjSsC,EAqStC;AACED,EAAAA,KAAK,EAAE,MADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArSsC,EAyStC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzSsC,EA6StC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7SsC,EAiTtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjTsC,EAqTtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArTsC,EAyTtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzTsC,EA6TtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7TsC,EAiUtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjUsC,EAqUtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArUsC,EAyUtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzUsC,EA6UtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7UsC,EAiVtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjVsC,EAqVtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArVsC,EAyVtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzVsC,EA6VtC;AACED,EAAAA,KAAK,EAAE,QADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7VsC,EAiWtC;AACED,EAAAA,KAAK,EAAE,WADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjWsC,EAqWtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArWsC,EAyWtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAzWsC,EA6WtC;AACED,EAAAA,KAAK,EAAE,SADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CA7WsC,CAAxC;eAmXeF,gB", "sourcesContent": ["import { NotchDevice } from './privateTypes';\n\nconst devicesWithNotch: NotchDevice[] = [\n  {\n    brand: 'Apple',\n    model: 'iPhone 15',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 15 Plus',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 15 Pro',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 15 Pro Max',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 14',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 14 Plus',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 14 Pro',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 14 Pro Max',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 13 mini',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 13',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 13 Pro',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 13 Pro Max',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 12 mini',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 12',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 12 Pro',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 12 Pro Max',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 11',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 11 Pro',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 11 Pro Max',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone X',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone XS',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone XS Max',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone XR',\n  },\n  {\n    brand: 'Asus',\n    model: 'ZenFone 5',\n  },\n  {\n    brand: 'Asus',\n    model: 'ZenFone 5z',\n  },\n  {\n    brand: 'google',\n    model: 'Pixel 3 XL',\n  },\n  {\n    brand: 'google',\n    model: 'Pixel 4a',\n  },\n  {\n    brand: 'Huawei',\n    model: 'P20',\n  },\n  {\n    brand: 'Huawei',\n    model: 'P20 Plus',\n  },\n  {\n    brand: 'Huawei',\n    model: 'P20 Lite',\n  },\n  {\n    brand: 'Huawei',\n    model: 'ANE-LX1',\n  },\n  {\n    brand: 'Huawei',\n    model: 'INE-LX1',\n  },\n  {\n    brand: 'Huawei',\n    model: 'POT-LX1',\n  },\n  {\n    brand: 'Huawei',\n    model: 'Honor Play',\n  },\n  {\n    brand: 'Huawei',\n    model: 'Honor 10',\n  },\n  {\n    brand: 'Huawei',\n    model: 'Mate 20 Lite',\n  },\n  {\n    brand: 'Huawei',\n    model: 'Mate 20 Pro',\n  },\n  {\n    brand: 'Huawei',\n    model: 'ELE-L29', // P30\n  },\n  {\n    brand: 'Huawei',\n    model: 'P30 Lite',\n  },\n  {\n    brand: 'Huawei',\n    model: 'P30 Pro',\n  },\n  {\n    brand: 'Huawei',\n    model: 'JNY-LX1', // P40 Lite\n  },\n  {\n    brand: 'Huawei',\n    model: 'Nova 3',\n  },\n  {\n    brand: 'Huawei',\n    model: 'Nova 3i',\n  },\n  {\n    brand: 'Leagoo',\n    model: 'S9',\n  },\n  {\n    brand: 'LG',\n    model: 'G7',\n  },\n  {\n    brand: 'LG',\n    model: 'G7 ThinQ',\n  },\n  {\n    brand: 'LG',\n    model: 'G7+ ThinQ',\n  },\n  {\n    brand: 'LG',\n    model: 'LM-Q910', //G7 One\n  },\n  {\n    brand: 'LG',\n    model: 'LM-G710', //G7 ThinQ\n  },\n  {\n    brand: 'LG',\n    model: 'LM-V405', //V40 ThinQ\n  },\n  {\n    brand: 'Motorola',\n    model: 'Moto g7 Play',\n  },\n  {\n    brand: 'Motorola',\n    model: 'Moto g7 Power',\n  },\n  {\n    brand: 'Motorola',\n    model: 'One',\n  },\n  {\n    brand: 'Motorola',\n    model: 'Motorola One Vision',\n  },\n  {\n    brand: 'Nokia',\n    model: '5.1 Plus',\n  },\n  {\n    brand: 'Nokia',\n    model: 'Nokia 6.1 Plus',\n  },\n  {\n    brand: 'Nokia',\n    model: '7.1',\n  },\n  {\n    brand: 'Nokia',\n    model: '8.1',\n  },\n  {\n    brand: 'OnePlus',\n    model: '6',\n  },\n  {\n    brand: 'OnePlus',\n    model: 'A6003',\n  },\n  {\n    brand: 'ONEPLUS',\n    model: 'A6000',\n  },\n  {\n    brand: 'OnePlus',\n    model: 'OnePlus A6003',\n  },\n  {\n    brand: 'OnePlus',\n    model: 'ONEPLUS A6010',\n  },\n  {\n    brand: 'OnePlus',\n    model: 'ONEPLUS A6013',\n  },\n  {\n    brand: 'OnePlus',\n    model: 'ONEPLUS A6000',\n  },\n  {\n    brand: 'Oppo',\n    model: 'R15',\n  },\n  {\n    brand: 'Oppo',\n    model: 'R15 Pro',\n  },\n  {\n    brand: 'Oppo',\n    model: 'F7',\n  },\n  {\n    brand: 'Oukitel',\n    model: 'U18',\n  },\n  {\n    brand: 'Redmi',\n    model: 'M2004J19C',\n  },\n  {\n    brand: 'Sharp',\n    model: 'Aquos S3',\n  },\n  {\n    brand: 'Vivo',\n    model: 'V9',\n  },\n  {\n    brand: 'Vivo',\n    model: 'X21',\n  },\n  {\n    brand: 'Vivo',\n    model: 'X21 UD',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'MI 8',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'MI 8 Explorer Edition',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'MI 8 SE',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'MI 8 UD',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'MI 8 Lite',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'Mi 9',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'POCO F1',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'POCOPHONE F1',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'Redmi 6 Pro',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'Redmi Note 7',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'Redmi 7',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'Redmi Note 8',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'Redmi Note 8 Pro',\n  },\n  {\n    brand: 'xiaomi',\n    model: 'Mi A2 Lite',\n  },\n  {\n    brand: 'Blackview',\n    model: 'A30',\n  },\n  {\n    brand: 'Samsung',\n    model: 'SM-A202F',\n  },\n  {\n    brand: 'Samsung',\n    model: 'SM-A217F',\n  },\n  {\n    brand: 'Samsung',\n    model: 'SM-A715F',\n  },\n];\n\nexport default devicesWithNotch;\n"]}