{"version": 3, "file": "plugin-resolver.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "path", "_interopRequireWildcard", "_resolveFrom", "_errors", "_modules", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "pluginFileName", "exports", "resolvePluginForModule", "projectRoot", "pluginReference", "moduleNameIsDirectFileReference", "pluginScriptFile", "resolveFrom", "silent", "isPluginFile", "endsWith", "sep", "filePath", "moduleNameIsPackageReference", "pluginPackageFile", "fileExists", "packageMainEntry", "PluginError", "pathIsFilePath", "name", "match", "slashCount", "split", "length", "startsWith", "normalizeStaticPlugin", "plugin", "Array", "isArray", "assert", "undefined", "assertInternalProjectRoot", "resolveConfigPluginFunction", "resolveConfigPluginFunctionWithInfo", "pluginFile", "result", "requirePluginFile", "error", "SyntaxError", "learnMoreLink", "pluginError", "message", "stack", "resolveConfigPluginExport"], "sources": ["../../src/utils/plugin-resolver.ts"], "sourcesContent": ["import assert from 'assert';\nimport * as path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { PluginError } from './errors';\nimport { ConfigPlugin, StaticPlugin } from '../Plugin.types';\nimport { fileExists } from './modules';\n// Default plugin entry file name.\nexport const pluginFileName = 'app.plugin.js';\n\n/**\n * Resolve the config plugin from a node module or package.\n * If the module or package does not include a config plugin, this function throws a `PluginError`.\n * The resolution is done in following order:\n *   1. Is the reference a relative file path or an import specifier with file path? e.g. `./file.js`, `pkg/file.js` or `@org/pkg/file.js`?\n *     - Resolve the config plugin as-is\n *   2. If the reference a module? e.g. `expo-font`\n *     - Resolve the root `app.plugin.js` file within the module, e.g. `expo-font/app.plugin.js`\n *   3. Does the module have a valid config plugin in the `main` field?\n *     - Resolve the `main` entry point as config plugin\n */\nexport function resolvePluginForModule(\n  projectRoot: string,\n  pluginReference: string\n): { filePath: string; isPluginFile: boolean } {\n  if (moduleNameIsDirectFileReference(pluginReference)) {\n    // Only resolve `./file.js`, `package/file.js`, `@org/package/file.js`\n    const pluginScriptFile = resolveFrom.silent(projectRoot, pluginReference);\n    if (pluginScriptFile) {\n      return {\n        // NOTE(cedric): `path.sep` is required here, we are resolving the absolute path, not the plugin reference\n        isPluginFile: pluginScriptFile.endsWith(path.sep + pluginFileName),\n        filePath: pluginScriptFile,\n      };\n    }\n  } else if (moduleNameIsPackageReference(pluginReference)) {\n    // Only resolve `package -> package/app.plugin.js`, `@org/package -> @org/package/app.plugin.js`\n    const pluginPackageFile = resolveFrom.silent(\n      projectRoot,\n      `${pluginReference}/${pluginFileName}`\n    );\n    if (pluginPackageFile && fileExists(pluginPackageFile)) {\n      return { isPluginFile: true, filePath: pluginPackageFile };\n    }\n    // Try to resole the `main` entry as config plugin\n    const packageMainEntry = resolveFrom.silent(projectRoot, pluginReference);\n    if (packageMainEntry) {\n      return { isPluginFile: false, filePath: packageMainEntry };\n    }\n  }\n\n  throw new PluginError(\n    `Failed to resolve plugin for module \"${pluginReference}\" relative to \"${projectRoot}\". Do you have node modules installed?`,\n    'PLUGIN_NOT_FOUND'\n  );\n}\n\n// TODO: Test windows\nfunction pathIsFilePath(name: string): boolean {\n  // Matches lines starting with: . / ~/\n  return !!name.match(/^(\\.|~\\/|\\/)/g);\n}\n\nexport function moduleNameIsDirectFileReference(name: string): boolean {\n  if (pathIsFilePath(name)) {\n    return true;\n  }\n\n  const slashCount = name.split('/')?.length;\n  // Orgs (like @expo/config ) should have more than one slash to be a direct file.\n  if (name.startsWith('@')) {\n    return slashCount > 2;\n  }\n\n  // Regular packages should be considered direct reference if they have more than one slash.\n  return slashCount > 1;\n}\n\nexport function moduleNameIsPackageReference(name: string): boolean {\n  const slashCount = name.split('/')?.length;\n  return name.startsWith('@') ? slashCount === 2 : slashCount === 1;\n}\n\nexport function normalizeStaticPlugin(plugin: StaticPlugin | ConfigPlugin | string): StaticPlugin {\n  if (Array.isArray(plugin)) {\n    assert(\n      plugin.length > 0 && plugin.length < 3,\n      `Wrong number of arguments provided for static config plugin, expected either 1 or 2, got ${plugin.length}`\n    );\n    return plugin;\n  }\n  return [plugin, undefined];\n}\n\nexport function assertInternalProjectRoot(projectRoot?: string): asserts projectRoot {\n  assert(\n    projectRoot,\n    `Unexpected: Config \\`_internal.projectRoot\\` isn't defined by expo-cli, this is a bug.`\n  );\n}\n\n// Resolve the module function and assert type\nexport function resolveConfigPluginFunction(projectRoot: string, pluginReference: string) {\n  const { plugin } = resolveConfigPluginFunctionWithInfo(projectRoot, pluginReference);\n  return plugin;\n}\n\n// Resolve the module function and assert type\nexport function resolveConfigPluginFunctionWithInfo(projectRoot: string, pluginReference: string) {\n  const { filePath: pluginFile, isPluginFile } = resolvePluginForModule(\n    projectRoot,\n    pluginReference\n  );\n  let result: any;\n  try {\n    result = requirePluginFile(pluginFile);\n  } catch (error) {\n    if (error instanceof SyntaxError) {\n      const learnMoreLink = `Learn more: https://docs.expo.dev/guides/config-plugins/#creating-a-plugin`;\n      // If the plugin reference is a node module, and that node module has a syntax error, then it probably doesn't have an official config plugin.\n      if (!isPluginFile && !moduleNameIsDirectFileReference(pluginReference)) {\n        const pluginError = new PluginError(\n          `Package \"${pluginReference}\" does not contain a valid config plugin.\\n${learnMoreLink}\\n\\n${error.message}`,\n          'INVALID_PLUGIN_IMPORT'\n        );\n        pluginError.stack = error.stack;\n        throw pluginError;\n      }\n    }\n    throw error;\n  }\n\n  const plugin = resolveConfigPluginExport({\n    plugin: result,\n    pluginFile,\n    pluginReference,\n    isPluginFile,\n  });\n  return { plugin, pluginFile, pluginReference, isPluginFile };\n}\n\n/**\n * - Resolve the exported contents of an Expo config (be it default or module.exports)\n * - Assert no promise exports\n * - Return config type\n * - Serialize config\n *\n * @param props.plugin plugin results\n * @param props.pluginFile plugin file path\n * @param props.pluginReference the string used to reference the plugin\n * @param props.isPluginFile is file path from the app.plugin.js module root\n */\nexport function resolveConfigPluginExport({\n  plugin,\n  pluginFile,\n  pluginReference,\n  isPluginFile,\n}: {\n  plugin: any;\n  pluginFile: string;\n  pluginReference: string;\n  isPluginFile: boolean;\n}): ConfigPlugin<unknown> {\n  if (plugin.default != null) {\n    plugin = plugin.default;\n  }\n  if (typeof plugin !== 'function') {\n    const learnMoreLink = `Learn more: https://docs.expo.dev/guides/config-plugins/#creating-a-plugin`;\n    // If the plugin reference is a node module, and that node module does not export a function then it probably doesn't have a config plugin.\n    if (!isPluginFile && !moduleNameIsDirectFileReference(pluginReference)) {\n      throw new PluginError(\n        `Package \"${pluginReference}\" does not contain a valid config plugin. Module must export a function from file: ${pluginFile}\\n${learnMoreLink}`,\n        'INVALID_PLUGIN_TYPE'\n      );\n    }\n    throw new PluginError(\n      `Plugin \"${pluginReference}\" must export a function from file: ${pluginFile}. ${learnMoreLink}`,\n      'INVALID_PLUGIN_TYPE'\n    );\n  }\n\n  return plugin;\n}\n\nfunction requirePluginFile(filePath: string): any {\n  try {\n    return require(filePath);\n  } catch (error) {\n    // TODO: Improve error messages\n    throw error;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,KAAA;EAAA,MAAAH,IAAA,GAAAI,uBAAA,CAAAF,OAAA;EAAAC,IAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,aAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,YAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,QAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAO,SAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,QAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAQ,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAhB,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AACvC;AACO,MAAMmB,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,eAAe;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,sBAAsBA,CACpCC,WAAmB,EACnBC,eAAuB,EACsB;EAC7C,IAAIC,+BAA+B,CAACD,eAAe,CAAC,EAAE;IACpD;IACA,MAAME,gBAAgB,GAAGC,sBAAW,CAACC,MAAM,CAACL,WAAW,EAAEC,eAAe,CAAC;IACzE,IAAIE,gBAAgB,EAAE;MACpB,OAAO;QACL;QACAG,YAAY,EAAEH,gBAAgB,CAACI,QAAQ,CAACnC,IAAI,CAAD,CAAC,CAACoC,GAAG,GAAGX,cAAc,CAAC;QAClEY,QAAQ,EAAEN;MACZ,CAAC;IACH;EACF,CAAC,MAAM,IAAIO,4BAA4B,CAACT,eAAe,CAAC,EAAE;IACxD;IACA,MAAMU,iBAAiB,GAAGP,sBAAW,CAACC,MAAM,CAC1CL,WAAW,EACX,GAAGC,eAAe,IAAIJ,cAAc,EACtC,CAAC;IACD,IAAIc,iBAAiB,IAAI,IAAAC,qBAAU,EAACD,iBAAiB,CAAC,EAAE;MACtD,OAAO;QAAEL,YAAY,EAAE,IAAI;QAAEG,QAAQ,EAAEE;MAAkB,CAAC;IAC5D;IACA;IACA,MAAME,gBAAgB,GAAGT,sBAAW,CAACC,MAAM,CAACL,WAAW,EAAEC,eAAe,CAAC;IACzE,IAAIY,gBAAgB,EAAE;MACpB,OAAO;QAAEP,YAAY,EAAE,KAAK;QAAEG,QAAQ,EAAEI;MAAiB,CAAC;IAC5D;EACF;EAEA,MAAM,KAAIC,qBAAW,EACnB,wCAAwCb,eAAe,kBAAkBD,WAAW,wCAAwC,EAC5H,kBACF,CAAC;AACH;;AAEA;AACA,SAASe,cAAcA,CAACC,IAAY,EAAW;EAC7C;EACA,OAAO,CAAC,CAACA,IAAI,CAACC,KAAK,CAAC,eAAe,CAAC;AACtC;AAEO,SAASf,+BAA+BA,CAACc,IAAY,EAAW;EACrE,IAAID,cAAc,CAACC,IAAI,CAAC,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,MAAME,UAAU,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,EAAEC,MAAM;EAC1C;EACA,IAAIJ,IAAI,CAACK,UAAU,CAAC,GAAG,CAAC,EAAE;IACxB,OAAOH,UAAU,GAAG,CAAC;EACvB;;EAEA;EACA,OAAOA,UAAU,GAAG,CAAC;AACvB;AAEO,SAASR,4BAA4BA,CAACM,IAAY,EAAW;EAClE,MAAME,UAAU,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,EAAEC,MAAM;EAC1C,OAAOJ,IAAI,CAACK,UAAU,CAAC,GAAG,CAAC,GAAGH,UAAU,KAAK,CAAC,GAAGA,UAAU,KAAK,CAAC;AACnE;AAEO,SAASI,qBAAqBA,CAACC,MAA4C,EAAgB;EAChG,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACzB,IAAAG,iBAAM,EACJH,MAAM,CAACH,MAAM,GAAG,CAAC,IAAIG,MAAM,CAACH,MAAM,GAAG,CAAC,EACtC,4FAA4FG,MAAM,CAACH,MAAM,EAC3G,CAAC;IACD,OAAOG,MAAM;EACf;EACA,OAAO,CAACA,MAAM,EAAEI,SAAS,CAAC;AAC5B;AAEO,SAASC,yBAAyBA,CAAC5B,WAAoB,EAAuB;EACnF,IAAA0B,iBAAM,EACJ1B,WAAW,EACX,wFACF,CAAC;AACH;;AAEA;AACO,SAAS6B,2BAA2BA,CAAC7B,WAAmB,EAAEC,eAAuB,EAAE;EACxF,MAAM;IAAEsB;EAAO,CAAC,GAAGO,mCAAmC,CAAC9B,WAAW,EAAEC,eAAe,CAAC;EACpF,OAAOsB,MAAM;AACf;;AAEA;AACO,SAASO,mCAAmCA,CAAC9B,WAAmB,EAAEC,eAAuB,EAAE;EAChG,MAAM;IAAEQ,QAAQ,EAAEsB,UAAU;IAAEzB;EAAa,CAAC,GAAGP,sBAAsB,CACnEC,WAAW,EACXC,eACF,CAAC;EACD,IAAI+B,MAAW;EACf,IAAI;IACFA,MAAM,GAAGC,iBAAiB,CAACF,UAAU,CAAC;EACxC,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIA,KAAK,YAAYC,WAAW,EAAE;MAChC,MAAMC,aAAa,GAAG,4EAA4E;MAClG;MACA,IAAI,CAAC9B,YAAY,IAAI,CAACJ,+BAA+B,CAACD,eAAe,CAAC,EAAE;QACtE,MAAMoC,WAAW,GAAG,KAAIvB,qBAAW,EACjC,YAAYb,eAAe,8CAA8CmC,aAAa,OAAOF,KAAK,CAACI,OAAO,EAAE,EAC5G,uBACF,CAAC;QACDD,WAAW,CAACE,KAAK,GAAGL,KAAK,CAACK,KAAK;QAC/B,MAAMF,WAAW;MACnB;IACF;IACA,MAAMH,KAAK;EACb;EAEA,MAAMX,MAAM,GAAGiB,yBAAyB,CAAC;IACvCjB,MAAM,EAAES,MAAM;IACdD,UAAU;IACV9B,eAAe;IACfK;EACF,CAAC,CAAC;EACF,OAAO;IAAEiB,MAAM;IAAEQ,UAAU;IAAE9B,eAAe;IAAEK;EAAa,CAAC;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASkC,yBAAyBA,CAAC;EACxCjB,MAAM;EACNQ,UAAU;EACV9B,eAAe;EACfK;AAMF,CAAC,EAAyB;EACxB,IAAIiB,MAAM,CAACxC,OAAO,IAAI,IAAI,EAAE;IAC1BwC,MAAM,GAAGA,MAAM,CAACxC,OAAO;EACzB;EACA,IAAI,OAAOwC,MAAM,KAAK,UAAU,EAAE;IAChC,MAAMa,aAAa,GAAG,4EAA4E;IAClG;IACA,IAAI,CAAC9B,YAAY,IAAI,CAACJ,+BAA+B,CAACD,eAAe,CAAC,EAAE;MACtE,MAAM,KAAIa,qBAAW,EACnB,YAAYb,eAAe,sFAAsF8B,UAAU,KAAKK,aAAa,EAAE,EAC/I,qBACF,CAAC;IACH;IACA,MAAM,KAAItB,qBAAW,EACnB,WAAWb,eAAe,uCAAuC8B,UAAU,KAAKK,aAAa,EAAE,EAC/F,qBACF,CAAC;EACH;EAEA,OAAOb,MAAM;AACf;AAEA,SAASU,iBAAiBA,CAACxB,QAAgB,EAAO;EAChD,IAAI;IACF,OAAOtC,OAAO,CAACsC,QAAQ,CAAC;EAC1B,CAAC,CAAC,OAAOyB,KAAK,EAAE;IACd;IACA,MAAMA,KAAK;EACb;AACF", "ignoreList": []}