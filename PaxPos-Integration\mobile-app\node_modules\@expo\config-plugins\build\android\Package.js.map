{"version": 3, "file": "Package.js", "names": ["_debug", "data", "_interopRequireDefault", "require", "_fs", "_glob", "_path", "_Paths", "_androidPlugins", "_withDangerousMod", "_modules", "_warnings", "e", "__esModule", "default", "debug", "Debug", "withPackageGradle", "config", "withAppBuildGradle", "modResults", "language", "contents", "setPackageInBuildGradle", "addWarningAndroid", "exports", "withPackageRefactor", "withDangerousMod", "renamePackageOnDisk", "modRequest", "projectRoot", "getPackage", "android", "package", "getPackageRoot", "type", "path", "join", "getCurrentPackageName", "packageRoot", "mainApplication", "getProjectFilePath", "packagePath", "dirname", "packagePathParts", "relative", "split", "sep", "filter", "Boolean", "getCurrentPackageForProjectFile", "fileName", "filePath", "globSync", "getCurrentPackageNameForType", "newPackageName", "renameJniOnDiskForType", "packageName", "renamePackageOnDiskForType", "currentPackageName", "jniRoot", "filesToUpdate", "cwd", "absolute", "for<PERSON>ach", "filepath", "fs", "lstatSync", "isFile", "includes", "extname", "readFileSync", "toString", "replace", "RegExp", "transformJavaClassDescriptor", "writeFileSync", "directoryExistsAsync", "currentPackagePath", "newPackagePath", "mkdirSync", "recursive", "relativePath", "moveFileSync", "oldPathParts", "length", "pathToCheck", "files", "readdirSync", "rmdirSync", "pop", "push", "kotlinSanitizedPackageName", "kotlinSanitized", "replacePackageName", "src", "dest", "renameSync", "buildGradle", "pattern", "getApplicationIdAsync", "buildGradlePath", "getAppBuildGradleFilePath", "existsSync", "promises", "readFile", "matchResult", "match", "content", "old<PERSON>ame", "newName", "oldNameEscaped", "stringsToWrap", "parts", "cleanParts", "map", "part", "cleanName"], "sources": ["../../src/android/Package.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport Debug from 'debug';\nimport fs from 'fs';\nimport { globSync } from 'glob';\nimport path from 'path';\n\nimport { getAppBuildGradleFilePath, getProjectFilePath } from './Paths';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAppBuildGradle } from '../plugins/android-plugins';\nimport { withDangerousMod } from '../plugins/withDangerousMod';\nimport { directoryExistsAsync } from '../utils/modules';\nimport { addWarningAndroid } from '../utils/warnings';\n\nconst debug = Debug('expo:config-plugins:android:package');\n\nexport const withPackageGradle: ConfigPlugin = (config) => {\n  return withAppBuildGradle(config, (config) => {\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = setPackageInBuildGradle(config, config.modResults.contents);\n    } else {\n      addWarningAndroid(\n        'android.package',\n        `Cannot automatically configure app build.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\nexport const withPackageRefactor: ConfigPlugin = (config) => {\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      await renamePackageOnDisk(config, config.modRequest.projectRoot);\n      return config;\n    },\n  ]);\n};\n\nexport function getPackage(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.package ?? null;\n}\n\nfunction getPackageRoot(projectRoot: string, type: 'main' | 'debug') {\n  return path.join(projectRoot, 'android', 'app', 'src', type, 'java');\n}\n\nfunction getCurrentPackageName(projectRoot: string, packageRoot: string) {\n  const mainApplication = getProjectFilePath(projectRoot, 'MainApplication');\n  const packagePath = path.dirname(mainApplication);\n  const packagePathParts = path.relative(packageRoot, packagePath).split(path.sep).filter(Boolean);\n\n  return packagePathParts.join('.');\n}\n\nfunction getCurrentPackageForProjectFile(\n  projectRoot: string,\n  packageRoot: string,\n  fileName: string,\n  type: string\n) {\n  const filePath = globSync(\n    path.join(projectRoot, `android/app/src/${type}/java/**/${fileName}.@(java|kt)`)\n  )[0];\n\n  if (!filePath) {\n    return null;\n  }\n\n  const packagePath = path.dirname(filePath);\n  const packagePathParts = path.relative(packageRoot, packagePath).split(path.sep).filter(Boolean);\n\n  return packagePathParts.join('.');\n}\n\nfunction getCurrentPackageNameForType(projectRoot: string, type: string): string | null {\n  const packageRoot = getPackageRoot(projectRoot, type as any);\n\n  if (type === 'main') {\n    return getCurrentPackageName(projectRoot, packageRoot);\n  }\n  // debug, etc..\n  return getCurrentPackageForProjectFile(projectRoot, packageRoot, '*', type);\n}\n\n// NOTE(brentvatne): this assumes that our MainApplication.java file is in the root of the package\n// this makes sense for standard react-native projects but may not apply in customized projects, so if\n// we want this to be runnable in any app we need to handle other possibilities\nexport async function renamePackageOnDisk(\n  config: Pick<ExpoConfig, 'android'>,\n  projectRoot: string\n) {\n  const newPackageName = getPackage(config);\n  if (newPackageName === null) {\n    return;\n  }\n\n  for (const type of ['debug', 'main', 'release']) {\n    await renameJniOnDiskForType({ projectRoot, type, packageName: newPackageName });\n    await renamePackageOnDiskForType({ projectRoot, type, packageName: newPackageName });\n  }\n}\n\nexport async function renameJniOnDiskForType({\n  projectRoot,\n  type,\n  packageName,\n}: {\n  projectRoot: string;\n  type: string;\n  packageName: string;\n}) {\n  if (!packageName) {\n    return;\n  }\n\n  const currentPackageName = getCurrentPackageNameForType(projectRoot, type);\n  if (!currentPackageName || !packageName || currentPackageName === packageName) {\n    return;\n  }\n\n  const jniRoot = path.join(projectRoot, 'android', 'app', 'src', type, 'jni');\n  const filesToUpdate = [...globSync('**/*', { cwd: jniRoot, absolute: true })];\n  // Replace all occurrences of the path in the project\n  filesToUpdate.forEach((filepath: string) => {\n    try {\n      if (fs.lstatSync(filepath).isFile() && ['.h', '.cpp'].includes(path.extname(filepath))) {\n        let contents = fs.readFileSync(filepath).toString();\n        contents = contents.replace(\n          new RegExp(transformJavaClassDescriptor(currentPackageName).replace(/\\//g, '\\\\/'), 'g'),\n          transformJavaClassDescriptor(packageName)\n        );\n        fs.writeFileSync(filepath, contents);\n      }\n    } catch {\n      debug(`Error updating \"${filepath}\" for type \"${type}\"`);\n    }\n  });\n}\n\nexport async function renamePackageOnDiskForType({\n  projectRoot,\n  type,\n  packageName,\n}: {\n  projectRoot: string;\n  type: string;\n  packageName: string;\n}) {\n  if (!packageName) {\n    return;\n  }\n\n  const currentPackageName = getCurrentPackageNameForType(projectRoot, type);\n  debug(`Found package \"${currentPackageName}\" for type \"${type}\"`);\n  if (!currentPackageName || currentPackageName === packageName) {\n    return;\n  }\n  debug(`Refactor \"${currentPackageName}\" to \"${packageName}\" for type \"${type}\"`);\n  const packageRoot = getPackageRoot(projectRoot, type as any);\n  // Set up our paths\n  if (!(await directoryExistsAsync(packageRoot))) {\n    debug(`- skipping refactor of missing directory: ${packageRoot}`);\n    return;\n  }\n\n  const currentPackagePath = path.join(packageRoot, ...currentPackageName.split('.'));\n  const newPackagePath = path.join(packageRoot, ...packageName.split('.'));\n\n  // Create the new directory\n  fs.mkdirSync(newPackagePath, { recursive: true });\n\n  // Move everything from the old directory over\n  globSync('**/*', { cwd: currentPackagePath }).forEach((relativePath) => {\n    const filepath = path.join(currentPackagePath, relativePath);\n    if (fs.lstatSync(filepath).isFile()) {\n      moveFileSync(filepath, path.join(newPackagePath, relativePath));\n    } else {\n      fs.mkdirSync(filepath, { recursive: true });\n    }\n  });\n\n  // Remove the old directory recursively from com/old/package to com/old and com,\n  // as long as the directories are empty\n  const oldPathParts = currentPackageName.split('.');\n  while (oldPathParts.length) {\n    const pathToCheck = path.join(packageRoot, ...oldPathParts);\n    try {\n      const files = fs.readdirSync(pathToCheck);\n      if (files.length === 0) {\n        fs.rmdirSync(pathToCheck);\n      }\n    } finally {\n      oldPathParts.pop();\n    }\n  }\n\n  const filesToUpdate = [...globSync('**/*', { cwd: newPackagePath, absolute: true })];\n  // Only update the BUCK file to match the main package name\n  if (type === 'main') {\n    // NOTE(EvanBacon): We dropped this file in SDK 48 but other templates may still use it.\n    filesToUpdate.push(path.join(projectRoot, 'android', 'app', 'BUCK'));\n  }\n\n  const kotlinSanitizedPackageName = kotlinSanitized(packageName);\n  // Replace all occurrences of the path in the project\n  filesToUpdate.forEach((filepath: string) => {\n    try {\n      if (fs.lstatSync(filepath).isFile()) {\n        let contents = fs.readFileSync(filepath).toString();\n        if (path.extname(filepath) === '.kt') {\n          contents = replacePackageName(contents, currentPackageName, kotlinSanitizedPackageName);\n        } else {\n          contents = replacePackageName(contents, currentPackageName, packageName);\n        }\n        if (['.h', '.cpp'].includes(path.extname(filepath))) {\n          contents = contents.replace(\n            new RegExp(transformJavaClassDescriptor(currentPackageName).replace(/\\//g, '\\\\'), 'g'),\n            transformJavaClassDescriptor(packageName)\n          );\n        }\n        fs.writeFileSync(filepath, contents);\n      }\n    } catch {\n      debug(`Error updating \"${filepath}\" for type \"${type}\"`);\n    }\n  });\n}\n\nfunction moveFileSync(src: string, dest: string) {\n  fs.mkdirSync(path.dirname(dest), { recursive: true });\n  fs.renameSync(src, dest);\n}\n\nexport function setPackageInBuildGradle(config: Pick<ExpoConfig, 'android'>, buildGradle: string) {\n  const packageName = getPackage(config);\n  if (packageName === null) {\n    return buildGradle;\n  }\n\n  const pattern = new RegExp(`(applicationId|namespace) ['\"].*['\"]`, 'g');\n  return buildGradle.replace(pattern, `$1 '${packageName}'`);\n}\n\nexport async function getApplicationIdAsync(projectRoot: string): Promise<string | null> {\n  const buildGradlePath = getAppBuildGradleFilePath(projectRoot);\n  if (!fs.existsSync(buildGradlePath)) {\n    return null;\n  }\n  const buildGradle = await fs.promises.readFile(buildGradlePath, 'utf8');\n  const matchResult = buildGradle.match(/applicationId ['\"](.*)['\"]/);\n  // TODO add fallback for legacy cases to read from AndroidManifest.xml\n  return matchResult?.[1] ?? null;\n}\n\n/**\n * Replace the package name with the new package name, in the given source.\n * This has to be limited to avoid accidentally replacing imports when the old package name overlaps.\n */\nfunction replacePackageName(content: string, oldName: string, newName: string) {\n  const oldNameEscaped = oldName.replace(/\\./g, '\\\\.');\n\n  return (\n    content\n      // Replace any quoted instances \"com.old\" -> \"com.new\"\n      .replace(new RegExp(`\"${oldNameEscaped}\"`, 'g'), `\"${newName}\"`)\n      // Replace special non-quoted instances, only when prefixed by package or namespace\n      .replace(new RegExp(`(package|namespace)(\\\\s+)${oldNameEscaped}`, 'g'), `$1$2${newName}`)\n      // Replace special import instances, without overlapping with other imports (trailing `.` to close it off)\n      .replace(new RegExp(`(import\\\\s+)${oldNameEscaped}\\\\.`, 'g'), `$1${newName}.`)\n  );\n}\n\n/**\n * Transform a java package name to java class descriptor,\n * e.g. `com.helloworld` -> `Lcom/helloworld`.\n */\nfunction transformJavaClassDescriptor(packageName: string) {\n  return `L${packageName.replace(/\\./g, '/')}`;\n}\n\n/**\n * Make a package name safe to use in a kotlin file,\n * e.g. is.pvin.hello -> `is`.pvin.hello\n */\nexport function kotlinSanitized(packageName: string) {\n  const stringsToWrap = ['is', 'in', 'as', 'fun'];\n\n  const parts = packageName.split('.');\n  const cleanParts = parts.map((part) => (stringsToWrap.includes(part) ? '`' + part + '`' : part));\n\n  const cleanName = cleanParts.join('.');\n  return cleanName;\n}\n"], "mappings": ";;;;;;;;;;;;;AACA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,OAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,MAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAO,gBAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,eAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,kBAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,iBAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,SAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,QAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,UAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,SAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsD,SAAAC,uBAAAU,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEtD,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,qCAAqC,CAAC;AAEnD,MAAMC,iBAA+B,GAAIC,MAAM,IAAK;EACzD,OAAO,IAAAC,oCAAkB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC5C,IAAIA,MAAM,CAACE,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CH,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGC,uBAAuB,CAACL,MAAM,EAAEA,MAAM,CAACE,UAAU,CAACE,QAAQ,CAAC;IAC1F,CAAC,MAAM;MACL,IAAAE,6BAAiB,EACf,iBAAiB,EACjB,oEACF,CAAC;IACH;IACA,OAAON,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAR,iBAAA,GAAAA,iBAAA;AAEK,MAAMS,mBAAiC,GAAIR,MAAM,IAAK;EAC3D,OAAO,IAAAS,oCAAgB,EAACT,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChB,MAAMU,mBAAmB,CAACV,MAAM,EAAEA,MAAM,CAACW,UAAU,CAACC,WAAW,CAAC;IAChE,OAAOZ,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAEK,SAASK,UAAUA,CAACb,MAAmC,EAAE;EAC9D,OAAOA,MAAM,CAACc,OAAO,EAAEC,OAAO,IAAI,IAAI;AACxC;AAEA,SAASC,cAAcA,CAACJ,WAAmB,EAAEK,IAAsB,EAAE;EACnE,OAAOC,eAAI,CAACC,IAAI,CAACP,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAEK,IAAI,EAAE,MAAM,CAAC;AACtE;AAEA,SAASG,qBAAqBA,CAACR,WAAmB,EAAES,WAAmB,EAAE;EACvE,MAAMC,eAAe,GAAG,IAAAC,2BAAkB,EAACX,WAAW,EAAE,iBAAiB,CAAC;EAC1E,MAAMY,WAAW,GAAGN,eAAI,CAACO,OAAO,CAACH,eAAe,CAAC;EACjD,MAAMI,gBAAgB,GAAGR,eAAI,CAACS,QAAQ,CAACN,WAAW,EAAEG,WAAW,CAAC,CAACI,KAAK,CAACV,eAAI,CAACW,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;EAEhG,OAAOL,gBAAgB,CAACP,IAAI,CAAC,GAAG,CAAC;AACnC;AAEA,SAASa,+BAA+BA,CACtCpB,WAAmB,EACnBS,WAAmB,EACnBY,QAAgB,EAChBhB,IAAY,EACZ;EACA,MAAMiB,QAAQ,GAAG,IAAAC,gBAAQ,EACvBjB,eAAI,CAACC,IAAI,CAACP,WAAW,EAAE,mBAAmBK,IAAI,YAAYgB,QAAQ,aAAa,CACjF,CAAC,CAAC,CAAC,CAAC;EAEJ,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,MAAMV,WAAW,GAAGN,eAAI,CAACO,OAAO,CAACS,QAAQ,CAAC;EAC1C,MAAMR,gBAAgB,GAAGR,eAAI,CAACS,QAAQ,CAACN,WAAW,EAAEG,WAAW,CAAC,CAACI,KAAK,CAACV,eAAI,CAACW,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;EAEhG,OAAOL,gBAAgB,CAACP,IAAI,CAAC,GAAG,CAAC;AACnC;AAEA,SAASiB,4BAA4BA,CAACxB,WAAmB,EAAEK,IAAY,EAAiB;EACtF,MAAMI,WAAW,GAAGL,cAAc,CAACJ,WAAW,EAAEK,IAAW,CAAC;EAE5D,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAOG,qBAAqB,CAACR,WAAW,EAAES,WAAW,CAAC;EACxD;EACA;EACA,OAAOW,+BAA+B,CAACpB,WAAW,EAAES,WAAW,EAAE,GAAG,EAAEJ,IAAI,CAAC;AAC7E;;AAEA;AACA;AACA;AACO,eAAeP,mBAAmBA,CACvCV,MAAmC,EACnCY,WAAmB,EACnB;EACA,MAAMyB,cAAc,GAAGxB,UAAU,CAACb,MAAM,CAAC;EACzC,IAAIqC,cAAc,KAAK,IAAI,EAAE;IAC3B;EACF;EAEA,KAAK,MAAMpB,IAAI,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE;IAC/C,MAAMqB,sBAAsB,CAAC;MAAE1B,WAAW;MAAEK,IAAI;MAAEsB,WAAW,EAAEF;IAAe,CAAC,CAAC;IAChF,MAAMG,0BAA0B,CAAC;MAAE5B,WAAW;MAAEK,IAAI;MAAEsB,WAAW,EAAEF;IAAe,CAAC,CAAC;EACtF;AACF;AAEO,eAAeC,sBAAsBA,CAAC;EAC3C1B,WAAW;EACXK,IAAI;EACJsB;AAKF,CAAC,EAAE;EACD,IAAI,CAACA,WAAW,EAAE;IAChB;EACF;EAEA,MAAME,kBAAkB,GAAGL,4BAA4B,CAACxB,WAAW,EAAEK,IAAI,CAAC;EAC1E,IAAI,CAACwB,kBAAkB,IAAI,CAACF,WAAW,IAAIE,kBAAkB,KAAKF,WAAW,EAAE;IAC7E;EACF;EAEA,MAAMG,OAAO,GAAGxB,eAAI,CAACC,IAAI,CAACP,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAEK,IAAI,EAAE,KAAK,CAAC;EAC5E,MAAM0B,aAAa,GAAG,CAAC,GAAG,IAAAR,gBAAQ,EAAC,MAAM,EAAE;IAAES,GAAG,EAAEF,OAAO;IAAEG,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC;EAC7E;EACAF,aAAa,CAACG,OAAO,CAAEC,QAAgB,IAAK;IAC1C,IAAI;MACF,IAAIC,aAAE,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACG,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACjC,eAAI,CAACkC,OAAO,CAACL,QAAQ,CAAC,CAAC,EAAE;QACtF,IAAI3C,QAAQ,GAAG4C,aAAE,CAACK,YAAY,CAACN,QAAQ,CAAC,CAACO,QAAQ,CAAC,CAAC;QACnDlD,QAAQ,GAAGA,QAAQ,CAACmD,OAAO,CACzB,IAAIC,MAAM,CAACC,4BAA4B,CAAChB,kBAAkB,CAAC,CAACc,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,EACvFE,4BAA4B,CAAClB,WAAW,CAC1C,CAAC;QACDS,aAAE,CAACU,aAAa,CAACX,QAAQ,EAAE3C,QAAQ,CAAC;MACtC;IACF,CAAC,CAAC,MAAM;MACNP,KAAK,CAAC,mBAAmBkD,QAAQ,eAAe9B,IAAI,GAAG,CAAC;IAC1D;EACF,CAAC,CAAC;AACJ;AAEO,eAAeuB,0BAA0BA,CAAC;EAC/C5B,WAAW;EACXK,IAAI;EACJsB;AAKF,CAAC,EAAE;EACD,IAAI,CAACA,WAAW,EAAE;IAChB;EACF;EAEA,MAAME,kBAAkB,GAAGL,4BAA4B,CAACxB,WAAW,EAAEK,IAAI,CAAC;EAC1EpB,KAAK,CAAC,kBAAkB4C,kBAAkB,eAAexB,IAAI,GAAG,CAAC;EACjE,IAAI,CAACwB,kBAAkB,IAAIA,kBAAkB,KAAKF,WAAW,EAAE;IAC7D;EACF;EACA1C,KAAK,CAAC,aAAa4C,kBAAkB,SAASF,WAAW,eAAetB,IAAI,GAAG,CAAC;EAChF,MAAMI,WAAW,GAAGL,cAAc,CAACJ,WAAW,EAAEK,IAAW,CAAC;EAC5D;EACA,IAAI,EAAE,MAAM,IAAA0C,+BAAoB,EAACtC,WAAW,CAAC,CAAC,EAAE;IAC9CxB,KAAK,CAAC,6CAA6CwB,WAAW,EAAE,CAAC;IACjE;EACF;EAEA,MAAMuC,kBAAkB,GAAG1C,eAAI,CAACC,IAAI,CAACE,WAAW,EAAE,GAAGoB,kBAAkB,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC;EACnF,MAAMiC,cAAc,GAAG3C,eAAI,CAACC,IAAI,CAACE,WAAW,EAAE,GAAGkB,WAAW,CAACX,KAAK,CAAC,GAAG,CAAC,CAAC;;EAExE;EACAoB,aAAE,CAACc,SAAS,CAACD,cAAc,EAAE;IAAEE,SAAS,EAAE;EAAK,CAAC,CAAC;;EAEjD;EACA,IAAA5B,gBAAQ,EAAC,MAAM,EAAE;IAAES,GAAG,EAAEgB;EAAmB,CAAC,CAAC,CAACd,OAAO,CAAEkB,YAAY,IAAK;IACtE,MAAMjB,QAAQ,GAAG7B,eAAI,CAACC,IAAI,CAACyC,kBAAkB,EAAEI,YAAY,CAAC;IAC5D,IAAIhB,aAAE,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE;MACnCe,YAAY,CAAClB,QAAQ,EAAE7B,eAAI,CAACC,IAAI,CAAC0C,cAAc,EAAEG,YAAY,CAAC,CAAC;IACjE,CAAC,MAAM;MACLhB,aAAE,CAACc,SAAS,CAACf,QAAQ,EAAE;QAAEgB,SAAS,EAAE;MAAK,CAAC,CAAC;IAC7C;EACF,CAAC,CAAC;;EAEF;EACA;EACA,MAAMG,YAAY,GAAGzB,kBAAkB,CAACb,KAAK,CAAC,GAAG,CAAC;EAClD,OAAOsC,YAAY,CAACC,MAAM,EAAE;IAC1B,MAAMC,WAAW,GAAGlD,eAAI,CAACC,IAAI,CAACE,WAAW,EAAE,GAAG6C,YAAY,CAAC;IAC3D,IAAI;MACF,MAAMG,KAAK,GAAGrB,aAAE,CAACsB,WAAW,CAACF,WAAW,CAAC;MACzC,IAAIC,KAAK,CAACF,MAAM,KAAK,CAAC,EAAE;QACtBnB,aAAE,CAACuB,SAAS,CAACH,WAAW,CAAC;MAC3B;IACF,CAAC,SAAS;MACRF,YAAY,CAACM,GAAG,CAAC,CAAC;IACpB;EACF;EAEA,MAAM7B,aAAa,GAAG,CAAC,GAAG,IAAAR,gBAAQ,EAAC,MAAM,EAAE;IAAES,GAAG,EAAEiB,cAAc;IAAEhB,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC;EACpF;EACA,IAAI5B,IAAI,KAAK,MAAM,EAAE;IACnB;IACA0B,aAAa,CAAC8B,IAAI,CAACvD,eAAI,CAACC,IAAI,CAACP,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;EACtE;EAEA,MAAM8D,0BAA0B,GAAGC,eAAe,CAACpC,WAAW,CAAC;EAC/D;EACAI,aAAa,CAACG,OAAO,CAAEC,QAAgB,IAAK;IAC1C,IAAI;MACF,IAAIC,aAAE,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE;QACnC,IAAI9C,QAAQ,GAAG4C,aAAE,CAACK,YAAY,CAACN,QAAQ,CAAC,CAACO,QAAQ,CAAC,CAAC;QACnD,IAAIpC,eAAI,CAACkC,OAAO,CAACL,QAAQ,CAAC,KAAK,KAAK,EAAE;UACpC3C,QAAQ,GAAGwE,kBAAkB,CAACxE,QAAQ,EAAEqC,kBAAkB,EAAEiC,0BAA0B,CAAC;QACzF,CAAC,MAAM;UACLtE,QAAQ,GAAGwE,kBAAkB,CAACxE,QAAQ,EAAEqC,kBAAkB,EAAEF,WAAW,CAAC;QAC1E;QACA,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAACY,QAAQ,CAACjC,eAAI,CAACkC,OAAO,CAACL,QAAQ,CAAC,CAAC,EAAE;UACnD3C,QAAQ,GAAGA,QAAQ,CAACmD,OAAO,CACzB,IAAIC,MAAM,CAACC,4BAA4B,CAAChB,kBAAkB,CAAC,CAACc,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EACtFE,4BAA4B,CAAClB,WAAW,CAC1C,CAAC;QACH;QACAS,aAAE,CAACU,aAAa,CAACX,QAAQ,EAAE3C,QAAQ,CAAC;MACtC;IACF,CAAC,CAAC,MAAM;MACNP,KAAK,CAAC,mBAAmBkD,QAAQ,eAAe9B,IAAI,GAAG,CAAC;IAC1D;EACF,CAAC,CAAC;AACJ;AAEA,SAASgD,YAAYA,CAACY,GAAW,EAAEC,IAAY,EAAE;EAC/C9B,aAAE,CAACc,SAAS,CAAC5C,eAAI,CAACO,OAAO,CAACqD,IAAI,CAAC,EAAE;IAAEf,SAAS,EAAE;EAAK,CAAC,CAAC;EACrDf,aAAE,CAAC+B,UAAU,CAACF,GAAG,EAAEC,IAAI,CAAC;AAC1B;AAEO,SAASzE,uBAAuBA,CAACL,MAAmC,EAAEgF,WAAmB,EAAE;EAChG,MAAMzC,WAAW,GAAG1B,UAAU,CAACb,MAAM,CAAC;EACtC,IAAIuC,WAAW,KAAK,IAAI,EAAE;IACxB,OAAOyC,WAAW;EACpB;EAEA,MAAMC,OAAO,GAAG,IAAIzB,MAAM,CAAC,sCAAsC,EAAE,GAAG,CAAC;EACvE,OAAOwB,WAAW,CAACzB,OAAO,CAAC0B,OAAO,EAAE,OAAO1C,WAAW,GAAG,CAAC;AAC5D;AAEO,eAAe2C,qBAAqBA,CAACtE,WAAmB,EAA0B;EACvF,MAAMuE,eAAe,GAAG,IAAAC,kCAAyB,EAACxE,WAAW,CAAC;EAC9D,IAAI,CAACoC,aAAE,CAACqC,UAAU,CAACF,eAAe,CAAC,EAAE;IACnC,OAAO,IAAI;EACb;EACA,MAAMH,WAAW,GAAG,MAAMhC,aAAE,CAACsC,QAAQ,CAACC,QAAQ,CAACJ,eAAe,EAAE,MAAM,CAAC;EACvE,MAAMK,WAAW,GAAGR,WAAW,CAACS,KAAK,CAAC,4BAA4B,CAAC;EACnE;EACA,OAAOD,WAAW,GAAG,CAAC,CAAC,IAAI,IAAI;AACjC;;AAEA;AACA;AACA;AACA;AACA,SAASZ,kBAAkBA,CAACc,OAAe,EAAEC,OAAe,EAAEC,OAAe,EAAE;EAC7E,MAAMC,cAAc,GAAGF,OAAO,CAACpC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;EAEpD,OACEmC;EACE;EAAA,CACCnC,OAAO,CAAC,IAAIC,MAAM,CAAC,IAAIqC,cAAc,GAAG,EAAE,GAAG,CAAC,EAAE,IAAID,OAAO,GAAG;EAC/D;EAAA,CACCrC,OAAO,CAAC,IAAIC,MAAM,CAAC,4BAA4BqC,cAAc,EAAE,EAAE,GAAG,CAAC,EAAE,OAAOD,OAAO,EAAE;EACxF;EAAA,CACCrC,OAAO,CAAC,IAAIC,MAAM,CAAC,eAAeqC,cAAc,KAAK,EAAE,GAAG,CAAC,EAAE,KAAKD,OAAO,GAAG,CAAC;AAEpF;;AAEA;AACA;AACA;AACA;AACA,SAASnC,4BAA4BA,CAAClB,WAAmB,EAAE;EACzD,OAAO,IAAIA,WAAW,CAACgB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;AAC9C;;AAEA;AACA;AACA;AACA;AACO,SAASoB,eAAeA,CAACpC,WAAmB,EAAE;EACnD,MAAMuD,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;EAE/C,MAAMC,KAAK,GAAGxD,WAAW,CAACX,KAAK,CAAC,GAAG,CAAC;EACpC,MAAMoE,UAAU,GAAGD,KAAK,CAACE,GAAG,CAAEC,IAAI,IAAMJ,aAAa,CAAC3C,QAAQ,CAAC+C,IAAI,CAAC,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG,GAAGA,IAAK,CAAC;EAEhG,MAAMC,SAAS,GAAGH,UAAU,CAAC7E,IAAI,CAAC,GAAG,CAAC;EACtC,OAAOgF,SAAS;AAClB", "ignoreList": []}