import groovy.json.JsonSlurper
import java.nio.file.Paths


// Object representing a gradle project.
class ExpoModuleGradleProject {
  // Name of the Android project
  String name

  // Path to the folder with Android project
  String sourceDir

  // Gradle projects containing AAR files
  ExpoModuleGradleAarProject[] aarProjects

  ExpoModuleGradleProject(Object data) {
    this.name = data.name
    this.sourceDir = data.sourceDir
    this.aarProjects = data.aarProjects.collect { new ExpoModuleGradleAarProject(it) }
  }
}

// Object representing a gradle plugin
class ExpoModuleGradlePlugin {
  // ID of the gradle plugin
  String id

  // Artifact group
  String group

  // Path to the plugin folder
  String sourceDir

  Boolean applyToRootProject

  ExpoModuleGradlePlugin(Object data) {
    this.id = data.id
    this.group = data.group
    this.sourceDir = data.sourceDir
    this.applyToRootProject = data.applyToRootProject
  }
}

// Object representing an gradle project containing AAR file
class ExpoModuleGradleAarProject {
  // Name of the project
  String name

  // Path to the AAR file
  String aarFilePath

  // Project directory
  String projectDir

  ExpoModuleGradleAarProject(Object data) {
    this.name = data.name
    this.aarFilePath = data.aarFilePath
    this.projectDir = data.projectDir
  }
}

// Object representing a module.
class ExpoModule {
  // Name of the JavaScript package
  String name

  // Version of the package, loaded from `package.json`
  String version

  // Gradle projects
  ExpoModuleGradleProject[] projects

  // Gradle plugins
  ExpoModuleGradlePlugin[] plugins

  ExpoModule(Object data) {
    this.name = data.packageName
    this.version = data.packageVersion
    this.projects = data.projects.collect { new ExpoModuleGradleProject(it) }
    this.plugins = data.plugins.collect { new ExpoModuleGradlePlugin(it) }
  }

  List<ExpoModuleGradleAarProject> allAarProjects() {
    return projects.collect { it.aarProjects }.flatten()
  }
}

// Object representing a maven repository.
class MavenRepo {
  String url
  Object credentials
  String authentication

  MavenRepo(Object data) {
    this.url = data.url
    this.credentials = data.credentials
    this.authentication = data.authentication
  }
}

class ExpoAutolinkingManager {
  private File projectDir
  private Map options
  private Object cachedResolvingResults

  static String generatedPackageListNamespace = 'expo.modules'
  static String generatedPackageListFilename = 'ExpoModulesPackageList.java'
  static String generatedFilesSrcDir = 'generated/expo/src/main/java'

  ExpoAutolinkingManager(File projectDir, Map options = [:]) {
    this.projectDir = projectDir
    this.options = options
  }

  Object resolve(ProviderFactory providers, shouldUseCachedValue=false) {
    if (cachedResolvingResults) {
      return cachedResolvingResults
    }
    if (shouldUseCachedValue) {
      logger.warn("Warning: Expo modules were resolved multiple times. Probably something is wrong with the project configuration.")
    }
    String[] args = convertOptionsToCommandArgs('resolve', this.options)
    args += ['--json']

    String output = providers.exec {
      workingDir(projectDir)
      commandLine(args)
    }.standardOutput.asText.get().trim()
    Object json = new JsonSlurper().parseText(output)

    cachedResolvingResults = json
    return json
  }

  boolean shouldUseAAR() {
    return options?.useAAR == true
  }

  ExpoModule[] getModules(ProviderFactory providers, shouldUseCachedValue=false) {
    Object json = resolve(providers, shouldUseCachedValue)
    return json.modules.collect { new ExpoModule(it) }
  }

  MavenRepo[] getExtraMavenRepos(ProviderFactory providers, shouldUseCachedValue=false) {
    Object json = resolve(providers, shouldUseCachedValue)
    return json.extraDependencies.collect { new MavenRepo(it) }
  }

  static String getGeneratedFilePath(Project project) {
    return Paths.get(
      project.buildDir.toString(),
      generatedFilesSrcDir,
      generatedPackageListNamespace.replace('.', '/'),
      generatedPackageListFilename
    ).toString()
  }

  static void generatePackageList(Project project, Map options) {
    String[] args = convertOptionsToCommandArgs('generate-package-list', options)

    // Construct absolute path to generated package list.
    def generatedFilePath = getGeneratedFilePath(project)

    args += [
      '--namespace',
      generatedPackageListNamespace,
      '--target',
      generatedFilePath
    ]

    if (options == null) {
      // Options are provided only when settings.gradle was configured.
      // If not or opted-out from autolinking, the generated list should be empty.
      args += '--empty'
    }

    project.providers.exec {
      workingDir(project.rootDir)
      commandLine(args)
    }.result.get().assertNormalExitValue()
  }

  static private String[] convertOptionsToCommandArgs(String command, Map options) {
    String[] args = [
      'node',
      '--no-warnings',
      '--eval',
      // Resolve the `expo` > `expo-modules-autolinking` chain from the project root
      'require(require.resolve(\'expo-modules-autolinking\', { paths: [require.resolve(\'expo\')] }))(process.argv.slice(1))',
      '--',
      command,
      '--platform',
      'android'
    ]

    def searchPaths = options?.get("searchPaths", options?.get("modulesPaths", null))
    if (searchPaths) {
      args += searchPaths
    }

    if (options?.ignorePaths) {
      args += '--ignore-paths'
      args += options.ignorePaths
    }

    if (options?.exclude) {
      args += '--exclude'
      args += options.exclude
    }

    return args
  }
}

class Colors {
  static final String GREEN = "\u001B[32m"
  static final String YELLOW = "\u001B[33m"
  static final String RESET = "\u001B[0m"
}
class Emojis {
  static final String INFORMATION = "\u2139\uFE0F"
}

// We can't cast a manager that is created in `settings.gradle` to the `ExpoAutolinkingManager`
// because if someone is using `buildSrc`, the `ExpoAutolinkingManager` class
// will be loaded by two different class loader - `settings.gradle` will use a diffrent loader.
// In the JVM, classes are equal only if were loaded by the same loader.
// There is nothing that we can do in that case, but to make our code safer, we check if the class name is the same.
def validateExpoAutolinkingManager(manager) {
  assert ExpoAutolinkingManager.name == manager.getClass().name
  return manager
}

// Here we split the implementation, depending on Gradle context.
// `rootProject` is a `ProjectDescriptor` if this file is imported in `settings.gradle` context,
// otherwise we can assume it is imported in `build.gradle`.
if (rootProject instanceof ProjectDescriptor) {
  // Method to be used in `settings.gradle`. Options passed here will have an effect in `build.gradle` context as well,
  // i.e. adding the dependencies and generating the package list.
  ext.useExpoModules = { Map options = [:] ->
    ExpoAutolinkingManager manager = new ExpoAutolinkingManager(rootProject.projectDir, options)
    ExpoModule[] modules = manager.getModules(providers)
    MavenRepo[] extraMavenRepos = manager.getExtraMavenRepos(providers)

    for (module in modules) {
      for (moduleProject in module.projects) {
        include(":${moduleProject.name}")
        project(":${moduleProject.name}").projectDir = new File(moduleProject.sourceDir)
      }
      for (modulePlugin in module.plugins) {
        includeBuild(new File(modulePlugin.sourceDir))
      }
      for (moduleAarProject in module.allAarProjects()) {
        include(":${moduleAarProject.name}")
        def projectDir = new File(moduleAarProject.projectDir)
        if (!projectDir.exists()) {
          projectDir.mkdirs()
        }
        project(":${moduleAarProject.name}").projectDir = projectDir
      }
    }

    gradle.beforeProject { project ->
      // Setup AAR projects
      for (module in modules) {
        for (moduleAarProject in module.allAarProjects()) {
          if (project.name == moduleAarProject.name) {
            project.configurations.maybeCreate('default')
            project.artifacts.add('default', file(moduleAarProject.aarFilePath))
          }
        }
      }

      // Setup autolinking from the root project
      if (project !== project.rootProject) {
        return
      }
      def rootProject = project

      // Add plugin classpath to the root project
      for (module in modules) {
        for (modulePlugin in module.plugins) {
          rootProject.buildscript.dependencies.add('classpath', "${modulePlugin.group}:${modulePlugin.id}")
        }
      }

      // Add extra maven repositories to allprojects
      for (mavenRepo in extraMavenRepos) {
        println "Adding extra maven repository - '${mavenRepo.url}'"
      }
      rootProject.allprojects { eachProject ->
        eachProject.repositories {
          for (mavenRepo in extraMavenRepos) {
            maven {
              url "${mavenRepo.url}"
              if (mavenRepo.credentials != null) {
                if (mavenRepo.credentials.username && mavenRepo.credentials.password) {
                  credentials {
                    username mavenRepo.credentials.username
                    password mavenRepo.credentials.password
                  }
                } else if (mavenRepo.credentials.name && mavenRepo.credentials.value) {
                  credentials(HttpHeaderCredentials) {
                    name mavenRepo.credentials.name
                    value mavenRepo.credentials.value
                  }
                } else if (mavenRepo.credentials.accessKey && mavenRepo.credentials.secretKey) {
                  credentials(AwsCredentials) {
                    accessKey mavenRepo.credentials.accessKey
                    secretKey mavenRepo.credentials.secretKey
                    sessionToken mavenRepo.credentials.sessionToken
                  }
                }
              }
              if (mavenRepo.authentication != null) {
                authentication {
                  if (mavenRepo.authentication == "basic") {
                    basic(BasicAuthentication)
                  } else if (mavenRepo.authentication == "digest") {
                    digest(DigestAuthentication)
                  } else if (mavenRepo.authentication == "header") {
                    header(HttpHeaderAuthentication)
                  }
                }
              }
            }
          }
        }
      }
    }

    // Apply plugins for all app projects
    gradle.afterProject { project ->
      if (!project.plugins.hasPlugin('com.android.application')) {
        return
      }
      for (module in modules) {
        for (modulePlugin in module.plugins) {
          if (!modulePlugin.applyToRootProject) {
            continue
          }
          println " ${Emojis.INFORMATION}  ${Colors.YELLOW}Applying gradle plugin${Colors.RESET} '${Colors.GREEN}${modulePlugin.id}${Colors.RESET}' (${module.name}@${module.version})"
          project.plugins.apply(modulePlugin.id)
        }
      }
    }

    // Save the manager in the shared context, so that we can later use it in `build.gradle`.
    gradle.ext.expoAutolinkingManager = manager
  }
} else {
  def addModule = { DependencyHandler handler, String projectName, Boolean useAAR ->
    Project dependency = rootProject.project(":${projectName}")

    if (useAAR) {
      handler.add('api', "${dependency.group}:${projectName}:${dependency.version}")
    } else {
      handler.add('api', dependency)
    }
  }

  def addDependencies = { DependencyHandler handler, Project project ->
    def manager = validateExpoAutolinkingManager(gradle.ext.expoAutolinkingManager)
    def modules = manager.getModules(project.providers, true)

    if (!modules.length) {
      return
    }

    println ''
    println 'Using expo modules'

    for (module in modules) {
      // Don't link itself
      if (module.name == project.name) {
        continue
      }
      // Can remove this once we move all the interfaces into the core.
      if (module.name.endsWith('-interface')) {
        continue
      }

      for (moduleProject in module.projects) {
        addModule(handler, moduleProject.name, manager.shouldUseAAR())
        println "  - ${Colors.GREEN}${moduleProject.name}${Colors.RESET} (${module.version})"
      }
    }

    println ''
  }

  // Adding dependencies
  ext.addExpoModulesDependencies = { DependencyHandler handler, Project project ->
    // Return early if `useExpoModules` was not called in `settings.gradle`
    if (!gradle.ext.has('expoAutolinkingManager')) {
      logger.error('Error: Autolinking is not set up in `settings.gradle`: expo modules won\'t be autolinked.')
      return
    }

    def manager = validateExpoAutolinkingManager(gradle.ext.expoAutolinkingManager)

    if (rootProject.findProject(':expo-modules-core')) {
      // `expo` requires `expo-modules-core` as a dependency, even if autolinking is turned off.
      addModule(handler, 'expo-modules-core', manager.shouldUseAAR())
    } else {
      logger.error('Error: `expo-modules-core` project is not included by autolinking.')
    }

    // If opted-in not to autolink modules as dependencies
    if (manager.options == null) {
      return
    }

    addDependencies(handler, project)
  }

  // Generating the package list
  ext.generatedFilesSrcDir = ExpoAutolinkingManager.generatedFilesSrcDir

  ext.generateExpoModulesPackageList = {
    // Get options used in `settings.gradle` or null if it wasn't set up.
    Map options = gradle.ext.has('expoAutolinkingManager') ? gradle.ext.expoAutolinkingManager.options : null

    if (options == null) {
      // TODO(@tsapeta): Temporarily muted this error — uncomment it once we start migrating from autolinking v1 to v2
      // logger.error('Autolinking is not set up in `settings.gradle`: generated package list with expo modules will be empty.')
    }
    ExpoAutolinkingManager.generatePackageList(project, options)
  }

  ext.getGenerateExpoModulesPackagesListPath = {
    return ExpoAutolinkingManager.getGeneratedFilePath(project)
  }

  ext.getModulesConfig = {
    if (!gradle.ext.has('expoAutolinkingManager')) {
      return null
    }

    def modules = gradle.ext.expoAutolinkingManager.resolve(project.providers, true).modules
    return modules.toString()
  }

  ext.ensureDependeciesWereEvaluated = { Project project ->
    if (!gradle.ext.has('expoAutolinkingManager')) {
      return
    }

    def modules = gradle.ext.expoAutolinkingManager.getModules(project.providers, true)
    for (module in modules) {
      for (moduleProject in module.projects) {
        def dependency = project.findProject(":${moduleProject.name}")
        if (dependency == null) {
          logger.warn("Coudn't find project ${moduleProject.name}. Please, make sure that `useExpoModules` was called in `settings.gradle`.")
          continue
        }

        // Prevent circular dependencies
        if (moduleProject.name == project.name) {
          continue
        }

        project.evaluationDependsOn(":${moduleProject.name}")
      }
    }
  }
}
