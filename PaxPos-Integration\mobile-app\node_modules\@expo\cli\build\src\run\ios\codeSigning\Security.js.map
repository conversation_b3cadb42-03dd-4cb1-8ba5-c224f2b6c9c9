{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/Security.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport forge from 'node-forge';\n\nimport { SecurityBinPrerequisite } from '../../../start/doctor/SecurityBinPrerequisite';\nimport { CommandError } from '../../../utils/errors';\n\nexport type CertificateSigningInfo = {\n  /**\n   * @example 'AA00AABB0A'\n   */\n  signingCertificateId: string;\n  /**\n   * @example 'Apple Development: Evan Bacon (AA00AABB0A)'\n   */\n  codeSigningInfo?: string;\n  /**\n   * @example '650 Industries, Inc.'\n   */\n  appleTeamName?: string;\n  /**\n   * @example 'A1BCDEF234'\n   */\n  appleTeamId?: string;\n};\n\nexport async function getSecurityPemAsync(id: string) {\n  const pem = (await spawnAsync('security', ['find-certificate', '-c', id, '-p'])).stdout?.trim?.();\n  if (!pem) {\n    throw new CommandError(`Failed to get PEM certificate for ID \"${id}\" using the 'security' bin`);\n  }\n  return pem;\n}\n\nexport async function getCertificateForSigningIdAsync(id: string): Promise<forge.pki.Certificate> {\n  const pem = await getSecurityPemAsync(id);\n  return forge.pki.certificateFromPem(pem);\n}\n\n/**\n * Get the signing identities from the security bin. Return a list of parsed values with duplicates removed.\n * @returns A list like ['Apple Development: <EMAIL> (BB00AABB0A)', 'Apple Developer: Evan Bacon (AA00AABB0A)']\n */\nexport async function findIdentitiesAsync(): Promise<string[]> {\n  await SecurityBinPrerequisite.instance.assertAsync();\n\n  const results = (\n    await spawnAsync('security', ['find-identity', '-p', 'codesigning', '-v'])\n  ).stdout.trim?.();\n  // Returns a string like:\n  // 1) 12222234253761286351826735HGKDHAJGF45283 \"Apple Development: Evan Bacon (AA00AABB0A)\" (CSSMERR_TP_CERT_REVOKED)\n  // 2) 12312234253761286351826735HGKDHAJGF45283 \"Apple Development: <EMAIL> (BB00AABB0A)\"\n  // 3) 12442234253761286351826735HGKDHAJGF45283 \"iPhone Distribution: Evan Bacon (CC00AABB0B)\" (CSSMERR_TP_CERT_REVOKED)\n  // 4) 15672234253761286351826735HGKDHAJGF45283 \"Apple Development: Evan Bacon (AA00AABB0A)\"\n  //  4 valid identities found\n\n  const parsed = results\n    .split('\\n')\n    .map((line) => extractCodeSigningInfo(line))\n    .filter(Boolean) as string[];\n\n  // Remove duplicates\n  return [...new Set(parsed)];\n}\n\n/**\n * @param value '  2) 12312234253761286351826735HGKDHAJGF45283 \"Apple Development: <EMAIL> (BB00AABB0A)\"'\n * @returns 'Apple Development: Evan Bacon (PH75MDXG4H)'\n */\nexport function extractCodeSigningInfo(value: string): string | null {\n  return value.match(/^\\s*\\d+\\).+\"(.+Develop(ment|er).+)\"$/)?.[1] ?? null;\n}\n\nexport async function resolveIdentitiesAsync(\n  identities: string[]\n): Promise<CertificateSigningInfo[]> {\n  const values = identities.map(extractSigningId).filter(Boolean) as string[];\n  return Promise.all(values.map(resolveCertificateSigningInfoAsync));\n}\n\n/**\n * @param signingCertificateId 'AA00AABB0A'\n */\nexport async function resolveCertificateSigningInfoAsync(\n  signingCertificateId: string\n): Promise<CertificateSigningInfo> {\n  const certificate = await getCertificateForSigningIdAsync(signingCertificateId);\n  return {\n    signingCertificateId,\n    codeSigningInfo: certificate.subject.getField('CN')?.value,\n    appleTeamName: certificate.subject.getField('O')?.value,\n    appleTeamId: certificate.subject.getField('OU')?.value,\n  };\n}\n\n/**\n * @param codeSigningInfo 'Apple Development: Evan Bacon (AA00AABB0A)'\n * @returns 'AA00AABB0A'\n */\nexport function extractSigningId(codeSigningInfo: string): string | null {\n  return codeSigningInfo.match(/.*\\(([a-zA-Z0-9]+)\\)/)?.[1] ?? null;\n}\n"], "names": ["extractCodeSigningInfo", "extractSigningId", "findIdentitiesAsync", "getCertificateForSigningIdAsync", "getSecurityPemAsync", "resolveCertificateSigningInfoAsync", "resolveIdentitiesAsync", "id", "pem", "spawnAsync", "stdout", "trim", "CommandError", "forge", "pki", "certificateFromPem", "SecurityBinPrerequisite", "instance", "assertAsync", "results", "parsed", "split", "map", "line", "filter", "Boolean", "Set", "value", "match", "identities", "values", "Promise", "all", "signingCertificateId", "certificate", "codeSigningInfo", "subject", "getField", "appleTeamName", "appleTeamId"], "mappings": ";;;;;;;;;;;IAoEgBA,sBAAsB;eAAtBA;;IA8BAC,gBAAgB;eAAhBA;;IAxDMC,mBAAmB;eAAnBA;;IATAC,+BAA+B;eAA/BA;;IARAC,mBAAmB;eAAnBA;;IAyDAC,kCAAkC;eAAlCA;;IAVAC,sBAAsB;eAAtBA;;;;gEAxEC;;;;;;;gEACL;;;;;;yCAEsB;wBACX;;;;;;AAqBtB,eAAeF,oBAAoBG,EAAU;QACtC,cAAA;IAAZ,MAAMC,OAAM,UAAA,AAAC,CAAA,MAAMC,IAAAA,qBAAU,EAAC,YAAY;QAAC;QAAoB;QAAMF;QAAI;KAAK,CAAA,EAAGG,MAAM,sBAA3E,eAAA,QAA6EC,IAAI,qBAAjF,kBAAA;IACZ,IAAI,CAACH,KAAK;QACR,MAAM,IAAII,oBAAY,CAAC,CAAC,sCAAsC,EAAEL,GAAG,0BAA0B,CAAC;IAChG;IACA,OAAOC;AACT;AAEO,eAAeL,gCAAgCI,EAAU;IAC9D,MAAMC,MAAM,MAAMJ,oBAAoBG;IACtC,OAAOM,oBAAK,CAACC,GAAG,CAACC,kBAAkB,CAACP;AACtC;AAMO,eAAeN;QAGJ,cAAA;IAFhB,MAAMc,gDAAuB,CAACC,QAAQ,CAACC,WAAW;IAElD,MAAMC,WAAU,eAAA,CAAA,UAAA,AACd,CAAA,MAAMV,IAAAA,qBAAU,EAAC,YAAY;QAAC;QAAiB;QAAM;QAAe;KAAK,CAAA,EACzEC,MAAM,EAACC,IAAI,qBAFG,kBAAA;IAGhB,yBAAyB;IACzB,qHAAqH;IACrH,8FAA8F;IAC9F,uHAAuH;IACvH,2FAA2F;IAC3F,4BAA4B;IAE5B,MAAMS,SAASD,QACZE,KAAK,CAAC,MACNC,GAAG,CAAC,CAACC,OAASvB,uBAAuBuB,OACrCC,MAAM,CAACC;IAEV,oBAAoB;IACpB,OAAO;WAAI,IAAIC,IAAIN;KAAQ;AAC7B;AAMO,SAASpB,uBAAuB2B,KAAa;QAC3CA;IAAP,OAAOA,EAAAA,eAAAA,MAAMC,KAAK,CAAC,4DAAZD,YAAqD,CAAC,EAAE,KAAI;AACrE;AAEO,eAAerB,uBACpBuB,UAAoB;IAEpB,MAAMC,SAASD,WAAWP,GAAG,CAACrB,kBAAkBuB,MAAM,CAACC;IACvD,OAAOM,QAAQC,GAAG,CAACF,OAAOR,GAAG,CAACjB;AAChC;AAKO,eAAeA,mCACpB4B,oBAA4B;QAKTC,+BACFA,gCACFA;IALf,MAAMA,cAAc,MAAM/B,gCAAgC8B;IAC1D,OAAO;QACLA;QACAE,eAAe,GAAED,gCAAAA,YAAYE,OAAO,CAACC,QAAQ,CAAC,0BAA7BH,8BAAoCP,KAAK;QAC1DW,aAAa,GAAEJ,iCAAAA,YAAYE,OAAO,CAACC,QAAQ,CAAC,yBAA7BH,+BAAmCP,KAAK;QACvDY,WAAW,GAAEL,iCAAAA,YAAYE,OAAO,CAACC,QAAQ,CAAC,0BAA7BH,+BAAoCP,KAAK;IACxD;AACF;AAMO,SAAS1B,iBAAiBkC,eAAuB;QAC/CA;IAAP,OAAOA,EAAAA,yBAAAA,gBAAgBP,KAAK,CAAC,4CAAtBO,sBAA+C,CAAC,EAAE,KAAI;AAC/D"}