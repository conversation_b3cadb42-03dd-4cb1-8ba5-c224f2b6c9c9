declare const _default: import("./createIconSet").Icon<"email" | "windows" | "ie" | "chrome" | "github" | "android" | "google" | "amazon" | "dropbox" | "googleplus" | "skype" | "youtube" | "twitter" | "weibo" | "dribbble" | "instagram" | "evernote" | "facebook" | "flattr" | "flickr" | "foursquare" | "grooveshark" | "lastfm" | "linkedin" | "paypal" | "pinterest" | "print" | "rss" | "scribd" | "smashing" | "soundcloud" | "spotify" | "stumbleupon" | "tumblr" | "vimeo" | "vk" | "xing" | "yelp" | "cart" | "pocket" | "html5" | "bitcoin" | "bitbucket" | "wordpress" | "openid" | "yahoo" | "reddit" | "delicious" | "digg" | "drupal" | "steam" | "angellist" | "viadeo" | "quora" | "podcast" | "meetup" | "blogger" | "buffer" | "itunes" | "stripe" | "disqus" | "wikipedia" | "pinboard" | "call" | "gmail" | "acrobat" | "aol" | "appnet" | "appstore" | "cal" | "cloudapp" | "creativecommons" | "dwolla" | "eventasaurus" | "eventbrite" | "eventful" | "fivehundredpx" | "forrst" | "googleplay" | "gowalla" | "guest" | "instapaper" | "intensedebate" | "klout" | "lanyrd" | "lego" | "lkdto" | "logmein" | "macstore" | "myspace" | "ninetyninedesigns" | "opentable" | "persona" | "plancast" | "plurk" | "posterous" | "songkick" | "stackoverflow" | "statusnet" | "ycombinator", "zocial">;
export default _default;
//# sourceMappingURL=Zocial.d.ts.map