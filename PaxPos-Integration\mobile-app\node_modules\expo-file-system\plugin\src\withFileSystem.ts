import { AndroidConfig, ConfigPlugin, createRunOncePlugin } from 'expo/config-plugins';

const pkg = require('expo-file-system/package.json');

const withFileSystem: ConfigPlugin = (config) => {
  return AndroidConfig.Permissions.withPermissions(config, [
    'android.permission.READ_EXTERNAL_STORAGE',
    'android.permission.WRITE_EXTERNAL_STORAGE',
    'android.permission.INTERNET',
  ]);
};

export default createRunOncePlugin(withFileSystem, pkg.name, pkg.version);
