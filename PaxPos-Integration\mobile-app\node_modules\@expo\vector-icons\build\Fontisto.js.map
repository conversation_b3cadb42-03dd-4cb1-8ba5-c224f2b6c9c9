{"version": 3, "file": "Fontisto.js", "sourceRoot": "", "sources": ["../src/Fontisto.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,YAAY,CAAC;AAEb,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,uDAAuD,CAAC;AACzE,OAAO,QAAQ,MAAM,4DAA4D,CAAC;AAElF,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AAE1D,eAAe,OAAO,CAAC", "sourcesContent": ["/**\n * Feather icon set component.\n * Usage: <Feather name=\"icon-name\" size={20} color=\"#4F8EF7\" />\n */\n\"use client\";\n\nimport createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Fontisto.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Fontisto.json';\n\nconst iconSet = createIconSet(glyphMap, 'Fontisto', font);\n\nexport default iconSet;\n"]}