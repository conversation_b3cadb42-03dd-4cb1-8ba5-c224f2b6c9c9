{"version": 3, "file": "Parser.js", "sourceRoot": "", "sources": ["../src/Parser.ts"], "names": [], "mappings": ";;;AAAA,+BAAgC;AAGhC,yCAAsC;AACtC,+CAA4C;AAO5C,SAAS,SAAS,CAAC,IAAsB;IACvC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7C,CAAC;AAkBD,MAAa,MAAM;IAiBjB,YAAmB,SAAoB;QAApB,cAAS,GAAT,SAAS,CAAW;QAbvC,qBAAgB,GAAY,KAAK,CAAC;QAElC,aAAQ,GAA8B,EAAE,CAAC;QAKzC,kBAAa,GAAkB;YAC7B,KAAK,EAAE,EAAE;SACV,CAAC;QAEF,iBAAY,GAAc,EAAE,CAAC;IAEa,CAAC;IAEpC,KAAK,CAAC,IAAY;QACvB,MAAM,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAExD,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACrC,CAAC;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACnC,CAAC;QACD,IAAI,mBAAmB,EAAE,CAAC;YACxB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,4BAA4B,EAAE,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACvC,CAAC;QACD,IAAI,IAAI,CAAC,4BAA4B,EAAE,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACvC,CAAC;QAED,IAAI,oBAAoB,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE3B,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAE3B,OAAO,IAAA,yBAAW,EAAC,IAAI,EAAE;YACvB;gBACE,mBAAQ,CAAC,eAAe;gBACxB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACrB,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,SAAS;oBACf,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD;gBACE,mBAAQ,CAAC,oBAAoB;gBAC7B,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACjB,SAAS,CAAC,YAAY,CAAC;oBACrB,IAAI,EAAE,OAAO;oBACb,aAAa,EAAE,EAAE;oBACjB,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD;gBACE,mBAAQ,CAAC,wBAAwB;gBACjC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACjB,SAAS,CAAC,YAAY,CAAC;oBACrB,IAAI,EAAE,WAAW;oBACjB,aAAa,EAAE,EAAE;oBACjB,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD;gBACE,mBAAQ,CAAC,sBAAsB;gBAC/B,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACjB,SAAS,CAAC,YAAY,CAAC;oBACrB,IAAI,EAAE,SAAS;oBACf,aAAa,EAAE,EAAE;oBACjB,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD,CAAC,mBAAQ,CAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAC1E;gBACE,mBAAQ,CAAC,oBAAoB;gBAC7B,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACjB,SAAS,CAAC,YAAY,CAAC;oBACrB,IAAI,EAAE,OAAO;oBACb,aAAa,EAAE,EAAE;oBACjB,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD;gBACE,mBAAQ,CAAC,gBAAgB;gBACzB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACzB,IAAI,IAAI,GAAG,EAAE,CAAC;oBACd,IAAI,EAAE,GAAG,EAAE,CAAC;oBACZ,uCAAuC;oBACvC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;wBACxB,IAAI,GAAG,EAAE,CAAC;wBACV,EAAE,GAAG,EAAE,CAAC;oBACV,CAAC;oBACD,OAAO,SAAS,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,EAAS,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;gBACtF,CAAC;aACF;YACD,CAAC,mBAAQ,CAAC,0BAA0B,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;YACtF,CAAC,mBAAQ,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC5E;gBACE,mBAAQ,CAAC,0BAA0B;gBACnC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACrB,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD;gBACE,mBAAQ,CAAC,gBAAgB;gBACzB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACrB,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD,CAAC,mBAAQ,CAAC,MAAM,CAAC,iCAAiC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC1F;gBACE,mBAAQ,CAAC,MAAM,CAAC,qCAAqC;gBACrD,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;aACtC;YACD,CAAC,mBAAQ,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC/E;gBACE,mBAAQ,CAAC,eAAe;gBACxB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACzB,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,EAAS;oBACf,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD,CAAC,mBAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC1F;gBACE,mBAAQ,CAAC,mBAAmB;gBAC5B,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACrB,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,YAAY;oBAClB,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD;gBACE,mBAAQ,CAAC,0BAA0B;gBACnC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACrB,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,mBAAmB;oBACzB,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YAED,CAAC,mBAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAEnE,CAAC,mBAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACjE;gBACE,mBAAQ,CAAC,6BAA6B;gBACtC,CAAC,CAAC,EAAE,AAAD,EAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,yBAAyB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;aACtF;YACD;gBACE,mBAAQ,CAAC,yBAAyB;gBAClC,CAAC,CAAC,EAAE,AAAD,EAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;aAC1E;YAED;gBACE,mBAAQ,CAAC,wBAAwB;gBACjC,GAAG,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC,QAAS,EAAE,cAAc,EAAE,KAAK,CAAC;aAC1F;YACD;gBACE,mBAAQ,CAAC,uBAAuB;gBAChC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC,QAAS,EAAE,EAAE,EAAE,EAAE,CAAC;aACrF;YACD;gBACE,mBAAQ,CAAC,oBAAoB;gBAC7B,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;aACpE;YACD,CAAC,mBAAQ,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC5E,CAAC,mBAAQ,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC5E;gBACE,mBAAQ,CAAC,MAAM,CAAC,0BAA0B;gBAC1C,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,EAAE,EAAE,CAAC;aACzD;YACD;gBACE,mBAAQ,CAAC,qBAAqB;gBAC9B,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACrB,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,kBAAkB;oBACxB,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD;gBACE,mBAAQ,CAAC,QAAQ,CAAC,kBAAkB;gBACpC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACb,0FAA0F;oBAC1F,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;wBACjC,OAAO,EAAE,CAAC;oBACZ,CAAC;oBACD,OAAO,SAAS,CAAC,eAAe,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gBAC5C,CAAC;aACF;YACD,CAAC,mBAAQ,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACzE;gBACE,mBAAQ,CAAC,eAAe;gBACxB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACrB,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,SAAS;oBACf,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD;gBACE,mBAAQ,CAAC,eAAe;gBACxB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAC7B,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD,CAAC,mBAAQ,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACtF;gBACE,mBAAQ,CAAC,0BAA0B;gBACnC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;aAC9D;YACD,CAAC,mBAAQ,CAAC,yBAAyB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACzF;gBACE,mBAAQ,CAAC,wBAAwB;gBACjC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;aAC5D;YACD,CAAC,mBAAQ,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC3E;gBACE,mBAAQ,CAAC,0BAA0B;gBACnC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACrB,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;oBACjD,OAAO,SAAS,CAAC,mBAAmB,CAAC;wBACnC,IAAI,EAAE,sBAAsB;wBAC5B,QAAQ;wBACR,QAAQ;wBACR,MAAM,EAAE,EAAE;wBACV,OAAO,EAAE,EAAE;qBACZ,CAAC,CAAC;gBACL,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,8BAA8B;gBACvC,qBAAqB;gBACrB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,0BAA0B,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;aACrF;YACD,CAAC,mBAAQ,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACtF;gBACE,mBAAQ,CAAC,mBAAmB;gBAC5B,CAAC,CACC,AADA;gBAEA,YAAY;gBACZ,EAAE;gBACF,WAAW;gBACX,EAAE;gBACF,YAAY;gBACZ,EAAE;gBACF,OAAO;gBACP,EAAE;gBACF,OAAO;gBACP,EAAE,EACF,EAAE,EACF,EAAE,EACH,EAAE,EAAE,CACH,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,YAAY;oBAClB,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;oBACZ,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YAED,CAAC,mBAAQ,CAAC,2BAA2B,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;YACzF,CAAC,mBAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;YACzE;gBACE,mBAAQ,CAAC,4BAA4B;gBACrC,CAAC,CAAC,EAAE,EAAE,EAAE,AAAD,EAAG,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC;aAC1D;YACD,CAAC,mBAAQ,CAAC,0BAA0B,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACrF,CAAC,mBAAQ,CAAC,wBAAwB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;YACrF,CAAC,mBAAQ,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YACrE;gBACE,mBAAQ,CAAC,aAAa;gBACtB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACrB,SAAS,CAAC,mBAAmB,CAAC;oBAC5B,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,EAAE;oBACZ,2CAA2C;oBAC3C,QAAQ,EAAE,EAAE,IAAI,IAAA,eAAQ,EAAC,EAAE,IAAI,EAAE,CAAC;oBAClC,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;iBACZ,CAAC;aACL;YACD,CAAC,mBAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACxE,CAAC,mBAAQ,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;YAEnF,CAAC,mBAAQ,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACtF,CAAC,mBAAQ,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YACpF;gBACE,mBAAQ,CAAC,QAAQ,CAAC,+BAA+B;gBACjD,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,yBAAyB,CAAC,EAAE,CAAC;aACpD;YACD;gBACE,mBAAQ,CAAC,MAAM,CAAC,qBAAqB;gBACrC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACzB,OAAO,SAAS,CAAC,4BAA4B,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC7E,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,QAAQ,CAAC,8BAA8B;gBAChD,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACzB,OAAO,SAAS,CAAC,4BAA4B,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC/E,CAAC;aACF;YACD,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAC1C,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,IAAY;QAClC,OAAO,IAAA,yBAAW,EAChB,IAAI,EACJ;YACE;gBACE,mBAAQ,CAAC,0BAA0B;gBACnC,GAAG,EAAE;oBACH,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;oBACvB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;oBAC9B,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACrB,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,yBAAyB;gBAClC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACb,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;oBACpB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACrB,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,4BAA4B;gBACrC,GAAG,EAAE;oBACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACxB,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,oBAAoB;gBAC7B,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACrB,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;aAC3E;YACD;gBACE,mBAAQ,CAAC,uBAAuB;gBAChC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACb,IAAI,CAAC,YAAY,CAAC;oBAChB,IAAI,EAAE,EAAE;oBACR,SAAS,EAAE,IAAI,CAAC,SAAU;oBAC1B,QAAQ,EAAE,IAAI,CAAC,QAAS;oBACxB,MAAM,EAAE,EAAE;iBACX,CAAC;aACL;YACD;gBACE,mBAAQ,CAAC,wBAAwB;gBACjC,GAAG,EAAE,CACH,IAAI,CAAC,YAAY,CAAC;oBAChB,IAAI,EAAE,KAAK;oBACX,SAAS,EAAE,IAAI,CAAC,SAAU;oBAC1B,QAAQ,EAAE,IAAI,CAAC,QAAS;oBACxB,MAAM,EAAE,cAAc;iBACvB,CAAC;aACL;SACF,EACD,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,IAAY;QACnC,MAAM,WAAW,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAmB,EAAE,EAAE;YACvD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC;YAC9B,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,CAAC;QAClC,CAAC,CAAC;QAEF,MAAM,OAAO,GAAG,IAAA,yBAAW,EAAC,IAAI,EAAE;YAChC;gBACE,mBAAQ,CAAC,MAAM,CAAC,qBAAqB;gBACrC,OAAO,CAAC,EAAE;oBACR,sCAAsC;oBACtC,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,CAAC,CAAC,EAAE,CAAC;wBACjB,OAAO,WAAW,CAAC;oBACrB,CAAC;oBACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,WAAW,CAAC,OAAO,CAAC,CAAC;oBACrB,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,QAAQ,CAAC,uBAAuB;gBACzC,OAAO,CAAC,EAAE;oBACR,wCAAwC;oBACxC,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,CAAC,CAAC,EAAE,CAAC;wBACjB,OAAO,WAAW,CAAC;oBACrB,CAAC;oBACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAC9B,WAAW,CAAC,OAAO,CAAC,CAAC;oBACrB,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,MAAM,CAAC,cAAc;gBAC9B,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;oBACT,sBAAsB;oBACtB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC;gBAChC,CAAC;aACF;YACD;gBACE,IAAI;gBACJ,GAAG,EAAE;;oBACH,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACnD,+DAA+D;wBAC/D,0EAA0E;wBAC1E,sDAAsD;wBAEtD,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,MAAA,MAAA,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,0CAAG,CAAC,CAAC,mCAAI,IAAI,CAAC;oBAC9E,CAAC;oBACD,OAAO,WAAW,CAAC;gBACrB,CAAC;aACF;SACF,CAAC,CAAC;QAEH,OAAO,OAAO,KAAK,WAAW,CAAC;IACjC,CAAC;IAEO,wBAAwB,CAAC,IAAY;QAC3C,MAAM,gBAAgB,GAAG,CAAC,CAAC,EAAE,EAAE,CAAmB,EAAE,EAAE;YACpD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,EAAE,CAAC;YAEhC,0CAA0C;YAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC/E,IAAI,YAAY,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpC,kEAAkE;gBAClE,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC/D,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC;YACtC,CAAC;YAED,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACtC,CAAC,CAAC;QAEF,IAAA,yBAAW,EAAC,IAAI,EAAE;YAChB,CAAC,mBAAQ,CAAC,MAAM,CAAC,gCAAgC,EAAE,gBAAgB,CAAC;YACpE,CAAC,mBAAQ,CAAC,MAAM,CAAC,gCAAgC,EAAE,gBAAgB,CAAC;SACrE,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,OAAO,GAAG,IAAA,yBAAW,EAAC,IAAI,EAAE;YAChC;gBACE,mBAAQ,CAAC,MAAM,CAAC,8BAA8B;gBAC9C,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;oBACT,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,CAAC;gBACjC,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,MAAM,CAAC,wCAAwC;gBACxD,GAAG,EAAE;oBACH,yBAAyB;oBACzB,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC7C,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,MAAM,CAAC,yCAAyC;gBACzD,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;oBACT,yBAAyB;oBACzB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpC,CAAC;aACF;YACD;gBACE,IAAI;gBACJ,GAAG,EAAE;oBACH,OAAO,WAAW,CAAC;gBACrB,CAAC;aACF;SACF,CAAC,CAAC;QACH,OAAO,OAAO,KAAK,WAAW,CAAC;IACjC,CAAC;IAED,0CAA0C;IAClC,iBAAiB;QACvB,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;IAClE,CAAC;IAEO,mBAAmB;QACzB,OAAO,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;IAClE,CAAC;IAEO,uBAAuB;;QAC7B,OAAO,CAAC,CAAC,CAAA,MAAA,IAAI,CAAC,YAAY,0CAAE,MAAM,CAAA,IAAI,CAAC,CAAC,CAAA,MAAA,IAAI,CAAC,YAAY,0CAAE,MAAM,CAAA,IAAI,CAAC,CAAC,CAAA,MAAA,IAAI,CAAC,YAAY,0CAAE,IAAI,CAAA,CAAC;IACjG,CAAC;IAEO,4BAA4B;;QAClC,OAAO,CACL,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,OAAO,MAAI,MAAA,IAAI,CAAC,aAAa,0CAAE,MAAM,CAAA,KAAI,MAAA,IAAI,CAAC,aAAa,0CAAE,SAAS,CAAA,CAC3F,CAAC;IACJ,CAAC;IAEO,4BAA4B;;QAClC,OAAO,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,OAAO,KAAI,CAAA,MAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,KAAK,0CAAE,MAAM,IAAG,CAAC,CAAC;IAC9E,CAAC;IAEO,kBAAkB;QACxB,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CACtC,KAAK,CAAC,QAAS,EACf,KAAK,CAAC,QAAS,EACf,KAAK,CAAC,MAAO,EACb,KAAK,CAAC,IAAK,EACX,KAAK,CAAC,MAAO,CACd,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CACxC,OAAO,CAAC,QAAS,EACjB,OAAO,CAAC,QAAS,EACjB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,MAAM,CACf,CAAC;IACJ,CAAC;IAEO,sBAAsB;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAClD,IAAI,CAAC,aAAa,CAAC,OAAQ,EAC3B,IAAI,CAAC,aAAa,CAAC,MAAO,EAC1B,IAAI,CAAC,aAAa,CAAC,SAAU,CAC9B,CAAC;QACF,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,sBAAsB;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAClD,IAAI,CAAC,aAAa,CAAC,OAAQ,EAC3B,IAAI,CAAC,aAAa,CAAC,KAAK,EACxB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAC/B,CAAC;QACF,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,sBAAsB;QAC5B,IAAI,CAAC,aAAa,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;QACnC,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACvC,CAAC;IAEO,YAAY,CAAC,EACnB,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,MAAM,GAMP;QACC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;YAC5B,QAAQ,EAAE,IAAI;YACd,MAAM;YACN,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,eAAuB;QACnD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAChC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1E,CAAC;IAEO,mBAAmB;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;IACpD,CAAC;CACF;AA9nBD,wBA8nBC", "sourcesContent": ["import { basename } from 'path';\n\nimport { Formatter } from './Formatter';\nimport { Matchers } from './Matchers';\nimport { switchRegex } from './switchRegex';\n\nexport type Failure = {\n  filePath: string;\n  testCase: string;\n  reason: string;\n};\nfunction unescaped(args: RegExpMatchArray): string[] {\n  return args.map(v => v.replace(/\\\\/g, ''));\n}\n\ninterface TestIssue {\n  reason?: string;\n  cursor?: string;\n  line?: string;\n  filePath?: string;\n  fileName?: string;\n}\n\ninterface LinkerFailure {\n  message?: string;\n  symbol?: string;\n  reference?: string;\n  files: string[];\n  isWarning?: boolean;\n}\n\nexport class Parser {\n  testSuite?: string;\n  testCase?: string;\n  testsDone?: boolean;\n  formattedSummary: boolean = false;\n\n  failures: Record<string, Failure[]> = {};\n\n  formattingLinkerFailure?: boolean;\n  formattingWarning?: boolean;\n  formattingError?: boolean;\n  linkerFailure: LinkerFailure = {\n    files: [],\n  };\n\n  currentIssue: TestIssue = {};\n\n  constructor(public formatter: Formatter) {}\n\n  public parse(text: string): void | string {\n    const matchedErrorResults = this.updateErrorState(text);\n\n    if (this.shouldFormatWarning()) {\n      return this.formatCompileWarning();\n    }\n    if (this.shouldFormatError()) {\n      return this.formatCompileError();\n    }\n    if (matchedErrorResults) {\n      return '';\n    }\n\n    const matchedLinkerResults = this.updateLinkerFailureState(text);\n\n    if (this.shouldFormatUndefinedSymbols()) {\n      return this.formatUndefinedSymbols();\n    }\n    if (this.shouldFormatDuplicateSymbols()) {\n      return this.formatDuplicateSymbols();\n    }\n\n    if (matchedLinkerResults) {\n      return '';\n    }\n\n    this.updateTestState(text);\n\n    const { formatter } = this;\n\n    return switchRegex(text, [\n      [\n        Matchers.ANALYZE_MATCHER,\n        ([, $1, $2, $3, $4]) =>\n          formatter.formatFileOperation({\n            type: 'Analyze',\n            filePath: $1,\n            fileName: $2,\n            target: $3,\n            project: $4,\n          }),\n      ],\n      [\n        Matchers.BUILD_TARGET_MATCHER,\n        ([, $1, $2, $3]) =>\n          formatter.formatTarget({\n            type: 'Build',\n            configuration: $3,\n            target: $1,\n            project: $2,\n          }),\n      ],\n      [\n        Matchers.AGGREGATE_TARGET_MATCHER,\n        ([, $1, $2, $3]) =>\n          formatter.formatTarget({\n            type: 'Aggregate',\n            configuration: $3,\n            target: $1,\n            project: $2,\n          }),\n      ],\n      [\n        Matchers.ANALYZE_TARGET_MATCHER,\n        ([, $1, $2, $3]) =>\n          formatter.formatTarget({\n            type: 'Analyze',\n            configuration: $3,\n            target: $1,\n            project: $2,\n          }),\n      ],\n      [Matchers.CLEAN_REMOVE_MATCHER, ([$0]) => formatter.formatCleanRemove($0)],\n      [\n        Matchers.CLEAN_TARGET_MATCHER,\n        ([, $1, $2, $3]) =>\n          formatter.formatTarget({\n            type: 'Clean',\n            configuration: $3,\n            target: $1,\n            project: $2,\n          }),\n      ],\n      [\n        Matchers.ANY_COPY_MATCHER,\n        ([, $1, $2, $3, $4, $5]) => {\n          let from = $3;\n          let to = $2;\n          // Flipped with CpResource and CpHeader\n          if ($1.startsWith('Cp')) {\n            from = $2;\n            to = $3;\n          }\n          return formatter.formatCopy({ type: $1 as any, from, to, target: $4, project: $5 });\n        },\n      ],\n      [Matchers.CHECK_DEPENDENCIES_MATCHER, ([$0]) => formatter.formatCheckDependencies($0)],\n      [Matchers.Errors.CLANG_ERROR_MATCHER, ([, $1]) => formatter.formatError($1)],\n      [\n        Matchers.CODESIGN_FRAMEWORK_MATCHER,\n        ([, $1, $2, $3, $4]) =>\n          formatter.formatFileOperation({\n            type: 'CodeSign',\n            fileName: $2,\n            filePath: $1,\n            target: $3,\n            project: $4,\n          }),\n      ],\n      [\n        Matchers.CODESIGN_MATCHER,\n        ([, $1, $2, $3, $4]) =>\n          formatter.formatFileOperation({\n            type: 'CodeSign',\n            fileName: $2,\n            filePath: $1,\n            target: $3,\n            project: $4,\n          }),\n      ],\n      [Matchers.Errors.CHECK_DEPENDENCIES_ERRORS_MATCHER, ([, $1]) => formatter.formatError($1)],\n      [\n        Matchers.Errors.PROVISIONING_PROFILE_REQUIRED_MATCHER,\n        ([, $1]) => formatter.formatError($1),\n      ],\n      [Matchers.Errors.NO_CERTIFICATE_MATCHER, ([, $1]) => formatter.formatError($1)],\n      [\n        Matchers.COMPILE_MATCHER,\n        ([, $1, $2, $3, $4, $5]) =>\n          formatter.formatFileOperation({\n            type: $1 as any,\n            fileName: $3,\n            filePath: $2,\n            target: $4,\n            project: $5,\n          }),\n      ],\n      [Matchers.COMPILE_COMMAND_MATCHER, ([, $1, $2]) => formatter.formatCompileCommand($1, $2)],\n      [\n        Matchers.COMPILE_XIB_MATCHER,\n        ([, $1, $2, $3, $4]) =>\n          formatter.formatFileOperation({\n            type: 'CompileXIB',\n            fileName: $2,\n            filePath: $1,\n            target: $3,\n            project: $4,\n          }),\n      ],\n      [\n        Matchers.COMPILE_STORYBOARD_MATCHER,\n        ([, $1, $2, $3, $4]) =>\n          formatter.formatFileOperation({\n            type: 'CompileStoryboard',\n            fileName: $2,\n            filePath: $1,\n            target: $3,\n            project: $4,\n          }),\n      ],\n\n      [Matchers.EXECUTED_MATCHER, () => this.formatSummaryIfNeeded(text)],\n\n      [Matchers.REMARK_MATCHER, ([, $1]) => formatter.formatRemark($1)],\n      [\n        Matchers.COMPILE_SWIFT_SOURCES_MATCHER,\n        ([, , $2, $3, $4, $5, $6]) => formatter.formatCompileSwiftSources($2, $3, $4, $5, $6),\n      ],\n      [\n        Matchers.EMIT_SWIFT_MODULE_MATCHER,\n        ([, , $2, $3, $4, $5]) => formatter.formatEmitSwiftModule($2, $3, $4, $5),\n      ],\n\n      [\n        Matchers.RESTARTING_TESTS_MATCHER,\n        () => formatter.formatFailingTest(this.testSuite!, this.testCase!, 'Test crashed', 'n/a'),\n      ],\n      [\n        Matchers.UI_FAILING_TEST_MATCHER,\n        ([, $1, $2]) => formatter.formatFailingTest(this.testSuite!, this.testCase!, $2, $1),\n      ],\n      [\n        Matchers.FAILING_TEST_MATCHER,\n        ([, $1, $2, $3, $4]) => formatter.formatFailingTest($2, $3, $4, $1),\n      ],\n      [Matchers.Errors.FATAL_ERROR_MATCHER, ([, $1]) => formatter.formatError($1)],\n      [Matchers.Errors.RSYNC_ERROR_MATCHER, ([, $1]) => formatter.formatError($1)],\n      [\n        Matchers.Errors.FILE_MISSING_ERROR_MATCHER,\n        ([, $1, $2]) => formatter.formatFileMissingError($1, $2),\n      ],\n      [\n        Matchers.GENERATE_DSYM_MATCHER,\n        ([, $1, $2, $3, $4]) =>\n          formatter.formatFileOperation({\n            type: 'GenerateDSYMFile',\n            filePath: $1,\n            fileName: $2,\n            target: $3,\n            project: $4,\n          }),\n      ],\n      [\n        Matchers.Warnings.LD_WARNING_MATCHER,\n        ([, $1, $2]) => {\n          // Skip printing ld warnings when we're collecting multiline ld duplicate symbol warnings.\n          if (this.linkerFailure.isWarning) {\n            return '';\n          }\n          return formatter.formatLdWarning($1 + $2);\n        },\n      ],\n      [Matchers.Errors.LD_ERROR_MATCHER, ([, $1]) => formatter.formatError($1)],\n      [\n        Matchers.LIBTOOL_MATCHER,\n        ([, $1, $2, $3, $4]) =>\n          formatter.formatFileOperation({\n            type: 'Libtool',\n            filePath: $1,\n            fileName: $2,\n            target: $3,\n            project: $4,\n          }),\n      ],\n      [\n        Matchers.LINKING_MATCHER,\n        ([, $1, $2, $3, $4, $5, $6]) =>\n          formatter.formatFileOperation({\n            type: 'Ld',\n            filePath: $1,\n            fileName: $2,\n            linkType: $3,\n            arch: $4,\n            target: $5,\n            project: $6,\n          }),\n      ],\n      [Matchers.Errors.MODULE_INCLUDES_ERROR_MATCHER, ([, $1]) => formatter.formatError($1)],\n      [\n        Matchers.TEST_CASE_MEASURED_MATCHER,\n        ([, $1, $2, $3]) => formatter.formatMeasuringTest($1, $2, $3),\n      ],\n      [Matchers.TEST_CASE_PENDING_MATCHER, ([, $1, $2]) => formatter.formatPendingTest($1, $2)],\n      [\n        Matchers.TEST_CASE_PASSED_MATCHER,\n        ([, $1, $2, $3]) => formatter.formatPassingTest($1, $2, $3),\n      ],\n      [Matchers.Errors.PODS_ERROR_MATCHER, ([, $1]) => formatter.formatError($1)],\n      [\n        Matchers.PROCESS_INFO_PLIST_MATCHER,\n        ([, $1, $2, $3, $4]) => {\n          const [filePath, fileName] = unescaped([$1, $2]);\n          return formatter.formatFileOperation({\n            type: 'ProcessInfoPlistFile',\n            fileName,\n            filePath,\n            target: $3,\n            project: $4,\n          });\n        },\n      ],\n      [\n        Matchers.PHASE_SCRIPT_EXECUTION_MATCHER,\n        // @ts-ignore: spread\n        ([, $1, $2, $3]) => formatter.formatPhaseScriptExecution(...unescaped([$1]), $2, $3),\n      ],\n      [Matchers.PHASE_SUCCESS_MATCHER, ([, $1, $2]) => formatter.formatPhaseSuccess($1, $2)],\n      [\n        Matchers.PROCESS_PCH_MATCHER,\n        ([\n          ,\n          // pch / gch\n          $1,\n          // filename\n          $2,\n          // extra pch\n          $3,\n          // type\n          $4,\n          // arch\n          $5,\n          $6,\n          $7,\n        ]) =>\n          formatter.formatFileOperation({\n            type: 'ProcessPCH',\n            filePath: $1,\n            fileName: $2,\n            linkType: $4,\n            arch: $5,\n            target: $6,\n            project: $7,\n          }),\n      ],\n\n      [Matchers.PROCESS_PCH_COMMAND_MATCHER, ([, $1]) => formatter.formatProcessPchCommand($1)],\n      [Matchers.PREPROCESS_MATCHER, ([, $1]) => formatter.formatPreprocess($1)],\n      [\n        Matchers.TESTS_RUN_COMPLETION_MATCHER,\n        ([, $1, , $3]) => formatter.formatTestRunFinished($1, $3),\n      ],\n      [Matchers.TEST_SUITE_STARTED_MATCHER, ([, $1]) => formatter.formatTestRunStarted($1)],\n      [Matchers.TEST_SUITE_START_MATCHER, ([, $1]) => formatter.formatTestSuiteStarted($1)],\n      [Matchers.TIFFUTIL_MATCHER, ([, $1]) => formatter.formatTiffutil($1)],\n      [\n        Matchers.TOUCH_MATCHER,\n        ([, $1, $2, $3, $4]) =>\n          formatter.formatFileOperation({\n            type: 'Touch',\n            filePath: $1,\n            // file name is undefined in newer projects\n            fileName: $2 || basename($1 || ''),\n            target: $3,\n            project: $4,\n          }),\n      ],\n      [Matchers.WRITE_FILE_MATCHER, ([, $1]) => formatter.formatWriteFile($1)],\n      [Matchers.WRITE_AUXILIARY_FILES, ([$0]) => formatter.formatWriteAuxiliaryFiles($0)],\n\n      [Matchers.SHELL_COMMAND_MATCHER, ([, $1, $2]) => formatter.formatShellCommand($1, $2)],\n      [Matchers.Warnings.GENERIC_WARNING_MATCHER, ([, $1]) => formatter.formatWarning($1)],\n      [\n        Matchers.Warnings.WILL_NOT_BE_CODE_SIGNED_MATCHER,\n        ([, $1]) => formatter.formatWillNotBeCodeSigned($1),\n      ],\n      [\n        Matchers.Errors.COMPILE_ERROR_MATCHER,\n        ([, $1, $2, $3, $4, $5]) => {\n          return formatter.formatSingleLineCompileIssue('error', $1, $2, $3, $4, $5);\n        },\n      ],\n      [\n        Matchers.Warnings.COMPILE_WARNING_INLINE_MATCHER,\n        ([, $1, $2, $3, $4, $5]) => {\n          return formatter.formatSingleLineCompileIssue('warning', $1, $2, $3, $4, $5);\n        },\n      ],\n      [null, () => formatter.formatOther(text)],\n    ]);\n  }\n\n  private updateTestState(text: string) {\n    return switchRegex(\n      text,\n      [\n        [\n          Matchers.TEST_SUITE_STARTED_MATCHER,\n          () => {\n            this.testsDone = false;\n            this.formattedSummary = false;\n            this.failures = {};\n          },\n        ],\n        [\n          Matchers.TEST_CASE_STARTED_MATCHER,\n          ([, $1, $2]) => {\n            this.testSuite = $1;\n            this.testCase = $2;\n          },\n        ],\n        [\n          Matchers.TESTS_RUN_COMPLETION_MATCHER,\n          () => {\n            this.testsDone = true;\n          },\n        ],\n        [\n          Matchers.FAILING_TEST_MATCHER,\n          ([, $1, $2, $3, $4]) =>\n            this.storeFailure({ file: $1, testSuite: $2, testCase: $3, reason: $4 }),\n        ],\n        [\n          Matchers.UI_FAILING_TEST_MATCHER,\n          ([, $1, $2]) =>\n            this.storeFailure({\n              file: $1,\n              testSuite: this.testSuite!,\n              testCase: this.testCase!,\n              reason: $2,\n            }),\n        ],\n        [\n          Matchers.RESTARTING_TESTS_MATCHER,\n          () =>\n            this.storeFailure({\n              file: 'n/a',\n              testSuite: this.testSuite!,\n              testCase: this.testCase!,\n              reason: 'Test crashed',\n            }),\n        ],\n      ],\n      true\n    );\n  }\n\n  private updateErrorState(text: string): boolean {\n    const updateError = ([, $1, $2, $3]: RegExpMatchArray) => {\n      this.currentIssue.reason = $3;\n      this.currentIssue.filePath = $1;\n      this.currentIssue.fileName = $2;\n    };\n\n    const results = switchRegex(text, [\n      [\n        Matchers.Errors.COMPILE_ERROR_MATCHER,\n        matches => {\n          // Prevent matching a one-liner error.\n          if (matches?.[4]) {\n            return 'unmatched';\n          }\n          this.formattingError = true;\n          updateError(matches);\n          return null;\n        },\n      ],\n      [\n        Matchers.Warnings.COMPILE_WARNING_MATCHER,\n        matches => {\n          // Prevent matching a one-liner warning.\n          if (matches?.[4]) {\n            return 'unmatched';\n          }\n          this.formattingWarning = true;\n          updateError(matches);\n          return null;\n        },\n      ],\n      [\n        Matchers.Errors.CURSOR_MATCHER,\n        ([, $1]) => {\n          // is trim === chomp ?\n          this.currentIssue.cursor = $1;\n        },\n      ],\n      [\n        null,\n        () => {\n          if (this.formattingError || this.formattingWarning) {\n            // Framework compile errors can have formatting like babel now:\n            // '  305 |     std::__construct_at(__p, std::forward<_Args>(__args)...);'\n            // So we need to strip the prefix ('  305 |') portion.\n\n            this.currentIssue.line = text.match(/^(?:[\\s]+\\d+ \\|\\s)?(.*)/)?.[1] ?? text;\n          }\n          return 'unmatched';\n        },\n      ],\n    ]);\n\n    return results !== 'unmatched';\n  }\n\n  private updateLinkerFailureState(text: string): boolean {\n    const handleLinkerFail = ([, $1]: RegExpMatchArray) => {\n      this.linkerFailure.message = $1;\n\n      // Some linker failures can be warnings...\n      const messageMatch = this.linkerFailure.message.match(/^(ld: )warning: (.*)/m);\n      if (messageMatch && messageMatch[2]) {\n        // Format like: `ld: duplicate symbol` to match other ld warnings.\n        this.linkerFailure.message = messageMatch[1] + messageMatch[2];\n        this.linkerFailure.isWarning = true;\n      }\n\n      this.formattingLinkerFailure = true;\n    };\n\n    switchRegex(text, [\n      [Matchers.Errors.LINKER_UNDEFINED_SYMBOLS_MATCHER, handleLinkerFail],\n      [Matchers.Errors.LINKER_DUPLICATE_SYMBOLS_MATCHER, handleLinkerFail],\n    ]);\n\n    if (!this.formattingLinkerFailure) {\n      return false;\n    }\n\n    const results = switchRegex(text, [\n      [\n        Matchers.Errors.SYMBOL_REFERENCED_FROM_MATCHER,\n        ([, $1]) => {\n          this.linkerFailure.symbol = $1;\n        },\n      ],\n      [\n        Matchers.Errors.LINKER_UNDEFINED_SYMBOL_LOCATION_MATCHER,\n        () => {\n          // TODO: trim === strip ?\n          this.linkerFailure.reference = text.trim();\n        },\n      ],\n      [\n        Matchers.Errors.LINKER_DUPLICATE_SYMBOLS_LOCATION_MATCHER,\n        ([, $1]) => {\n          // TODO: trim === strip ?\n          this.linkerFailure.files.push($1);\n        },\n      ],\n      [\n        null,\n        () => {\n          return 'unmatched';\n        },\n      ],\n    ]);\n    return results !== 'unmatched';\n  }\n\n  // TODO: clean up the mess around all this\n  private shouldFormatError(): boolean {\n    return !!this.formattingError && this.errorOrWarningIsPresent();\n  }\n\n  private shouldFormatWarning() {\n    return this.formattingWarning && this.errorOrWarningIsPresent();\n  }\n\n  private errorOrWarningIsPresent(): boolean {\n    return !!this.currentIssue?.reason && !!this.currentIssue?.cursor && !!this.currentIssue?.line;\n  }\n\n  private shouldFormatUndefinedSymbols() {\n    return (\n      this.linkerFailure?.message && this.linkerFailure?.symbol && this.linkerFailure?.reference\n    );\n  }\n\n  private shouldFormatDuplicateSymbols() {\n    return this.linkerFailure?.message && this.linkerFailure?.files?.length > 1;\n  }\n\n  private formatCompileError() {\n    const error = { ...this.currentIssue };\n    this.currentIssue = {};\n    this.formattingError = false;\n    return this.formatter.formatCompileError(\n      error.fileName!,\n      error.filePath!,\n      error.reason!,\n      error.line!,\n      error.cursor!\n    );\n  }\n\n  private formatCompileWarning() {\n    const warning = { ...this.currentIssue };\n    this.currentIssue = {};\n    this.formattingWarning = false;\n    return this.formatter.formatCompileWarning(\n      warning.fileName!,\n      warning.filePath!,\n      warning.reason!,\n      warning.line,\n      warning.cursor\n    );\n  }\n\n  private formatUndefinedSymbols() {\n    const result = this.formatter.formatUndefinedSymbols(\n      this.linkerFailure.message!,\n      this.linkerFailure.symbol!,\n      this.linkerFailure.reference!\n    );\n    this.resetLinkerFormatState();\n    return result;\n  }\n\n  private formatDuplicateSymbols() {\n    const result = this.formatter.formatDuplicateSymbols(\n      this.linkerFailure.message!,\n      this.linkerFailure.files,\n      !!this.linkerFailure.isWarning\n    );\n    this.resetLinkerFormatState();\n    return result;\n  }\n\n  private resetLinkerFormatState() {\n    this.linkerFailure = { files: [] };\n    this.formattingLinkerFailure = false;\n  }\n\n  private storeFailure({\n    file,\n    testCase,\n    testSuite,\n    reason,\n  }: {\n    file: string;\n    testSuite: string;\n    testCase: string;\n    reason: string;\n  }) {\n    if (!this.failures) {\n      this.failures = {};\n    }\n    if (!Array.isArray(this.failures[testSuite])) {\n      this.failures[testSuite] = [];\n    }\n\n    this.failures[testSuite].push({\n      filePath: file,\n      reason,\n      testCase,\n    });\n  }\n\n  private formatSummaryIfNeeded(executedMessage: string) {\n    if (!this.shouldFormatSummary()) {\n      return '';\n    }\n\n    this.formattedSummary = true;\n    return this.formatter.formatTestSummary(executedMessage, this.failures);\n  }\n\n  private shouldFormatSummary(): boolean {\n    return !!this.testsDone && !this.formattedSummary;\n  }\n}\n"]}