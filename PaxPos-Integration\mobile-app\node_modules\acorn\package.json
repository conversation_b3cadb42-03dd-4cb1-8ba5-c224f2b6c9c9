{"name": "acorn", "description": "ECMAScript parser", "homepage": "https://github.com/acornjs/acorn", "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "exports": {".": [{"import": "./dist/acorn.mjs", "require": "./dist/acorn.js", "default": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "version": "8.15.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "https://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "web": "https://rreverser.com/"}, {"name": "<PERSON>", "web": "http://adrianheine.de"}], "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn.git"}, "license": "MIT", "scripts": {"prepare": "cd ..; npm run build:main"}, "bin": {"acorn": "bin/acorn"}}