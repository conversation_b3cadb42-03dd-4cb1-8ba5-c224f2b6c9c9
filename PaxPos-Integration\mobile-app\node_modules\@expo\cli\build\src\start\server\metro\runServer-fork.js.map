{"version": 3, "sources": ["../../../../../src/start/server/metro/runServer-fork.ts"], "sourcesContent": ["// Copyright © 2023 650 Industries.\n// Copyright (c) Meta Platforms, Inc. and affiliates.\n//\n// Forks https://github.com/facebook/metro/blob/b80d9a0f638ee9fb82ff69cd3c8d9f4309ca1da2/packages/metro/src/index.flow.js#L57\n// and adds the ability to access the bundler instance.\nimport assert from 'assert';\nimport http from 'http';\nimport https from 'https';\nimport Metro, { RunServerOptions, Server } from 'metro';\nimport MetroHmrServer from 'metro/src/HmrServer';\nimport createWebsocketServer from 'metro/src/lib/createWebsocketServer';\nimport { ConfigT } from 'metro-config';\nimport { parse } from 'url';\nimport type { WebSocketServer } from 'ws';\n\nimport { MetroBundlerDevServer } from './MetroBundlerDevServer';\nimport { Log } from '../../../log';\nimport { getRunningProcess } from '../../../utils/getRunningProcess';\nimport type { ConnectAppType } from '../middleware/server.types';\n\nexport const runServer = async (\n  metroBundler: MetroBundlerDevServer,\n  config: ConfigT,\n  {\n    hasReducedPerformance = false,\n    host,\n    onError,\n    onReady,\n    secureServerOptions,\n    waitForBundler = false,\n    websocketEndpoints = {},\n    watch,\n  }: RunServerOptions,\n  {\n    mockServer,\n  }: {\n    // Use a mock server object instead of creating a real server, this is used in export cases where we want to reuse codepaths but not actually start a server.\n    mockServer: boolean;\n  }\n): Promise<{\n  server: http.Server | https.Server;\n  hmrServer: MetroHmrServer | null;\n  metro: Server;\n}> => {\n  // await earlyPortCheck(host, config.server.port);\n\n  // if (secure != null || secureCert != null || secureKey != null) {\n  //   // eslint-disable-next-line no-console\n  //   console.warn(\n  //     chalk.inverse.yellow.bold(' DEPRECATED '),\n  //     'The `secure`, `secureCert`, and `secureKey` options are now deprecated. ' +\n  //       'Use the `secureServerOptions` object instead to pass options to ' +\n  //       \"Metro's https development server.\",\n  //   );\n  // }\n\n  const { middleware, end, metroServer } = await Metro.createConnectMiddleware(config, {\n    hasReducedPerformance,\n    waitForBundler,\n    watch,\n  });\n\n  if (!mockServer) {\n    assert(typeof (middleware as any).use === 'function');\n  }\n  const serverApp = middleware as ConnectAppType;\n\n  let httpServer: http.Server | https.Server;\n\n  if (secureServerOptions != null) {\n    httpServer = https.createServer(secureServerOptions, serverApp);\n  } else {\n    httpServer = http.createServer(serverApp);\n  }\n\n  httpServer.on('error', (error) => {\n    if ('code' in error && error.code === 'EADDRINUSE') {\n      // If `Error: listen EADDRINUSE: address already in use :::8081` then print additional info\n      // about the process before throwing.\n      const info = getRunningProcess(config.server.port);\n      if (info) {\n        Log.error(\n          `Port ${config.server.port} is busy running ${info.command} in: ${info.directory}`\n        );\n      }\n    }\n\n    if (onError) {\n      onError(error);\n    }\n    end();\n  });\n\n  // Disable any kind of automatic timeout behavior for incoming\n  // requests in case it takes the packager more than the default\n  // timeout of 120 seconds to respond to a request.\n  httpServer.timeout = 0;\n\n  httpServer.on('close', () => {\n    end();\n  });\n\n  // Extend the close method to ensure all websocket servers are closed, and connections are terminated\n  const originalClose = httpServer.close.bind(httpServer);\n\n  httpServer.close = function closeHttpServer(callback) {\n    originalClose(callback);\n\n    // Close all websocket servers, including possible client connections (see: https://github.com/websockets/ws/issues/2137#issuecomment-1507469375)\n    for (const endpoint of Object.values(websocketEndpoints) as WebSocketServer[]) {\n      endpoint.close();\n      endpoint.clients.forEach((client) => client.terminate());\n    }\n\n    // Forcibly close active connections\n    this.closeAllConnections();\n    return this;\n  };\n\n  if (mockServer) {\n    return { server: httpServer, hmrServer: null, metro: metroServer };\n  }\n\n  return new Promise<{\n    server: http.Server | https.Server;\n    hmrServer: MetroHmrServer;\n    metro: Server;\n  }>((resolve, reject) => {\n    httpServer.on('error', (error) => {\n      reject(error);\n    });\n\n    httpServer.listen(config.server.port, host, () => {\n      if (onReady) {\n        onReady(httpServer);\n      }\n\n      const hmrServer = new MetroHmrServer(\n        metroServer.getBundler(),\n        metroServer.getCreateModuleId(),\n        config\n      );\n\n      Object.assign(websocketEndpoints, {\n        '/hot': createWebsocketServer({\n          websocketServer: hmrServer,\n        }),\n      });\n\n      httpServer.on('upgrade', (request, socket, head) => {\n        const { pathname } = parse(request.url!);\n        if (pathname != null && websocketEndpoints[pathname]) {\n          websocketEndpoints[pathname].handleUpgrade(request, socket, head, (ws) => {\n            websocketEndpoints[pathname].emit('connection', ws, request);\n          });\n        } else {\n          socket.destroy();\n        }\n      });\n\n      resolve({ server: httpServer, hmrServer, metro: metroServer });\n    });\n  });\n};\n"], "names": ["runServer", "metroBundler", "config", "hasReducedPerformance", "host", "onError", "onReady", "secureServerOptions", "waitFor<PERSON><PERSON>ler", "websocketEndpoints", "watch", "mockServer", "middleware", "end", "metroServer", "Metro", "createConnectMiddleware", "assert", "use", "serverApp", "httpServer", "https", "createServer", "http", "on", "error", "code", "info", "getRunningProcess", "server", "port", "Log", "command", "directory", "timeout", "originalClose", "close", "bind", "closeHttpServer", "callback", "endpoint", "Object", "values", "clients", "for<PERSON>ach", "client", "terminate", "closeAllConnections", "hmrServer", "metro", "Promise", "resolve", "reject", "listen", "MetroHmrServer", "getBundler", "getCreateModuleId", "assign", "createWebsocketServer", "websocketServer", "request", "socket", "head", "pathname", "parse", "url", "handleUpgrade", "ws", "emit", "destroy"], "mappings": "AAAA,mCAAmC;AACnC,qDAAqD;AACrD,EAAE;AACF,6HAA6H;AAC7H,uDAAuD;;;;;+BAgB1CA;;;eAAAA;;;;gEAfM;;;;;;;gEACF;;;;;;;gEACC;;;;;;;gEAC8B;;;;;;;gEACrB;;;;;;;gEACO;;;;;;;yBAEZ;;;;;;qBAIF;mCACc;;;;;;AAG3B,MAAMA,YAAY,OACvBC,cACAC,QACA,EACEC,wBAAwB,KAAK,EAC7BC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,mBAAmB,EACnBC,iBAAiB,KAAK,EACtBC,qBAAqB,CAAC,CAAC,EACvBC,KAAK,EACY,EACnB,EACEC,UAAU,EAIX;IAMD,kDAAkD;IAElD,mEAAmE;IACnE,2CAA2C;IAC3C,kBAAkB;IAClB,iDAAiD;IACjD,mFAAmF;IACnF,6EAA6E;IAC7E,6CAA6C;IAC7C,OAAO;IACP,IAAI;IAEJ,MAAM,EAAEC,UAAU,EAAEC,GAAG,EAAEC,WAAW,EAAE,GAAG,MAAMC,gBAAK,CAACC,uBAAuB,CAACd,QAAQ;QACnFC;QACAK;QACAE;IACF;IAEA,IAAI,CAACC,YAAY;QACfM,IAAAA,iBAAM,EAAC,OAAO,AAACL,WAAmBM,GAAG,KAAK;IAC5C;IACA,MAAMC,YAAYP;IAElB,IAAIQ;IAEJ,IAAIb,uBAAuB,MAAM;QAC/Ba,aAAaC,gBAAK,CAACC,YAAY,CAACf,qBAAqBY;IACvD,OAAO;QACLC,aAAaG,eAAI,CAACD,YAAY,CAACH;IACjC;IAEAC,WAAWI,EAAE,CAAC,SAAS,CAACC;QACtB,IAAI,UAAUA,SAASA,MAAMC,IAAI,KAAK,cAAc;YAClD,2FAA2F;YAC3F,qCAAqC;YACrC,MAAMC,OAAOC,IAAAA,oCAAiB,EAAC1B,OAAO2B,MAAM,CAACC,IAAI;YACjD,IAAIH,MAAM;gBACRI,QAAG,CAACN,KAAK,CACP,CAAC,KAAK,EAAEvB,OAAO2B,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAEH,KAAKK,OAAO,CAAC,KAAK,EAAEL,KAAKM,SAAS,EAAE;YAEtF;QACF;QAEA,IAAI5B,SAAS;YACXA,QAAQoB;QACV;QACAZ;IACF;IAEA,8DAA8D;IAC9D,+DAA+D;IAC/D,kDAAkD;IAClDO,WAAWc,OAAO,GAAG;IAErBd,WAAWI,EAAE,CAAC,SAAS;QACrBX;IACF;IAEA,qGAAqG;IACrG,MAAMsB,gBAAgBf,WAAWgB,KAAK,CAACC,IAAI,CAACjB;IAE5CA,WAAWgB,KAAK,GAAG,SAASE,gBAAgBC,QAAQ;QAClDJ,cAAcI;QAEd,iJAAiJ;QACjJ,KAAK,MAAMC,YAAYC,OAAOC,MAAM,CAACjC,oBAA0C;YAC7E+B,SAASJ,KAAK;YACdI,SAASG,OAAO,CAACC,OAAO,CAAC,CAACC,SAAWA,OAAOC,SAAS;QACvD;QAEA,oCAAoC;QACpC,IAAI,CAACC,mBAAmB;QACxB,OAAO,IAAI;IACb;IAEA,IAAIpC,YAAY;QACd,OAAO;YAAEkB,QAAQT;YAAY4B,WAAW;YAAMC,OAAOnC;QAAY;IACnE;IAEA,OAAO,IAAIoC,QAIR,CAACC,SAASC;QACXhC,WAAWI,EAAE,CAAC,SAAS,CAACC;YACtB2B,OAAO3B;QACT;QAEAL,WAAWiC,MAAM,CAACnD,OAAO2B,MAAM,CAACC,IAAI,EAAE1B,MAAM;YAC1C,IAAIE,SAAS;gBACXA,QAAQc;YACV;YAEA,MAAM4B,YAAY,IAAIM,CAAAA,YAAa,SAAC,CAClCxC,YAAYyC,UAAU,IACtBzC,YAAY0C,iBAAiB,IAC7BtD;YAGFuC,OAAOgB,MAAM,CAAChD,oBAAoB;gBAChC,QAAQiD,IAAAA,gCAAqB,EAAC;oBAC5BC,iBAAiBX;gBACnB;YACF;YAEA5B,WAAWI,EAAE,CAAC,WAAW,CAACoC,SAASC,QAAQC;gBACzC,MAAM,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,YAAK,EAACJ,QAAQK,GAAG;gBACtC,IAAIF,YAAY,QAAQtD,kBAAkB,CAACsD,SAAS,EAAE;oBACpDtD,kBAAkB,CAACsD,SAAS,CAACG,aAAa,CAACN,SAASC,QAAQC,MAAM,CAACK;wBACjE1D,kBAAkB,CAACsD,SAAS,CAACK,IAAI,CAAC,cAAcD,IAAIP;oBACtD;gBACF,OAAO;oBACLC,OAAOQ,OAAO;gBAChB;YACF;YAEAlB,QAAQ;gBAAEtB,QAAQT;gBAAY4B;gBAAWC,OAAOnC;YAAY;QAC9D;IACF;AACF"}