{"version": 3, "file": "PodfileTracer.js", "sourceRoot": "", "sources": ["../../src/utils/PodfileTracer.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AAExB,iFAA8E;AAC9E,2DAAwD;AACxD,mEAAgE;AAChE,yDAAmE;AAEnE;;GAEG;AACH,MAAa,aAAa;IACxB,MAAM,CAAC,MAAM,CAAC,WAAmB,EAAE,EAAE,YAAY,KAA0C,EAAE;;QAC3F,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;QAClE,MAAM,eAAe,GAAG,YAAE,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAC7D,MAAM,cAAc,GAAG,CAAA,MAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,0CAAG,CAAC,CAAC,KAAI,EAAE,CAAC;QAC5E,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC;YAClC,WAAW;YACX,cAAc;YACd,OAAO,EAAE,MAAA,IAAA,mCAAgB,EAAC,eAAe,CAAC,mCAAI,EAAE;SACjD,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;IAClC,CAAC;IAED,YACS,KAIN;QAJM,UAAK,GAAL,KAAK,CAIX;QAGH,uCAAuC;QACvC,+BAA0B,GAAG,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAwE7F,4BAAuB,GAAG,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAmB/E,8CAAyC,GAAG,cAAc,CAChE,IAAI,CAAC,6CAA6C,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9D,CAAC;IAhGC,CAAC;IAKJ,sCAAsC,CACpC,MAAc;QAEd,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;QAED,mEAAmE;QACnE,IAAI,MAAM,IAAI,aAAa,EAAE,CAAC;YAC5B,OAAO,EAAE,IAAI,EAAE,aAAa,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;QAC9D,CAAC;QAED,gDAAgD;QAChD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,4BAA4B;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,iCAAiC,CAAC;gBACjD,MAAM;gBACN,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;aACjC,CAAC,CAAC;YACH,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7D,CAAC;QAED,6CAA6C;QAE7C,IAAI,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,0GAA0G;YAC1G,uJAAuJ;YACvJ,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvE,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,EAAE,CAAC;YACnB,6CAA6C;YAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,iCAAiC,CAAC;gBACjD,MAAM,EAAE,MAAM,CAAC,GAAG;gBAClB,QAAQ,EAAE,MAAM,CAAC,MAAM;aACxB,CAAC,CAAC;YACH,IAAI,GAAG,EAAE,CAAC;gBACR,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,YAAY,CAAC,MAAc;QACzB,OAAO,CACL,IAAI,CAAC,KAAK,CAAC,cAAc;YACzB,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,MAAM,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CACzF,CAAC;IACJ,CAAC;IAED,iBAAiB,CACf,QAAgB,EAChB,MAAe;QAEf,MAAM,UAAU,GAAG,IAAA,qCAAiB,EAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;QACnD,CAAC;aAAM,IAAI,CAAC,MAAM,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAID,mCAAmC,CAAC,GAAY;;QAC9C,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,IAAA,2DAA4B,EAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;QAE1E,8EAA8E;QAC9E,MAAM,QAAQ,GAAG,MAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAAC,UAAU,CAAC,mCAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAAC,OAAO,CAAC,mCAAI,IAAI,CAAC;QAEjF,IAAI,OAAO,IAAI,QAAQ,EAAE,CAAC;YACxB,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAChD,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAMD,gJAAgJ;IAChJ,iCAAiC,CAAC,KAA2C;QAC3E,OAAO,IAAI,CAAC,yCAAyC,CAAC;YACpD,GAAG,EAAE,KAAK,CAAC,MAAM;YACjB,IAAI,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;SACvB,CAAC,CAAC;IACL,CAAC;IAED,6CAA6C,CAAC,QAAgB;QAC5D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,iBAAiB,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACnE,8CAA8C;QAC9C,IAAI,CAAC;YACH,MAAM,oBAAoB,GAAG,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAC1E,OAAO,OAAO,CAAC,oBAAoB,CAAC,CAAC;QACvC,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAA,6CAAqB,EAAC,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;CACF;AAhJD,sCAgJC;AAED,SAAS,OAAO,CAAsD,IAAO;IAC3E,MAAM,KAAK,GAAwB,EAAE,CAAC;IACtC,OAAO,UAAU,GAAW;QAC1B,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;QACpB,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACzB,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;QACpB,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CAIrB,IAAO;IACP,MAAM,KAAK,GAAwB,EAAE,CAAC;IACtC,OAAO,UAAU,EAAE,GAAG,EAAE,IAAI,EAA+B;QACzD,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;QACpB,CAAC;QACD,aAAa;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC7B,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;QACpB,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED,oEAAoE;AACpE,MAAM,aAAa,GAA2B;IAC5C,4CAA4C;IAC5C,mCAAmC,EAAE,cAAc;IACnD,OAAO,EAAE,cAAc;IACvB,UAAU;IACV,0BAA0B,EAAE,cAAc;IAC1C,eAAe,EAAE,cAAc;IAC/B,mBAAmB,EAAE,cAAc;IACnC,UAAU,EAAE,cAAc;IAC1B,OAAO,EAAE,cAAc;IACvB,iBAAiB,EAAE,cAAc;CAClC,CAAC", "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport { getFirstExternalSourceForPod } from './getFirstExternalSourceForPod';\nimport { getNodeModuleName } from './getNodeModuleName';\nimport { getPackageJsonForPath } from './getPackageJsonForPath';\nimport { parsePodfileLock, PodfileLock } from './parsePodfileLock';\n\n/**\n * A utility for tracing dependencies from a Podfile.lock.\n */\nexport class PodfileTracer {\n  static create(projectRoot: string, { xcodeProject }: { xcodeProject?: { name: string } } = {}) {\n    const podfileLock = path.join(projectRoot, 'ios', 'Podfile.lock');\n    const podfileContents = fs.readFileSync(podfileLock, 'utf8');\n    const rootTargetName = xcodeProject?.name.match(/.*\\/(.*)\\.\\w+/)?.[1] || '';\n    const formatter = new PodfileTracer({\n      projectRoot,\n      rootTargetName,\n      podfile: parsePodfileLock(podfileContents) ?? {},\n    });\n\n    return formatter;\n  }\n\n  get podfile() {\n    return this.props.podfile || {};\n  }\n\n  constructor(\n    public props: {\n      projectRoot: string;\n      rootTargetName?: string;\n      podfile: PodfileLock;\n    }\n  ) {}\n\n  // Wrap the expensive method in a cache\n  getNodeModuleNameForTarget = memoize(this.getNodeModuleNameForTargetWithoutCache.bind(this));\n\n  getNodeModuleNameForTargetWithoutCache(\n    target: string\n  ): { name: string; isRootTarget: boolean } | null {\n    if (!target) {\n      return null;\n    }\n\n    // Check the list of known pods that are hardcoded into the system.\n    if (target in knownPackages) {\n      return { name: knownPackages[target], isRootTarget: false };\n    }\n\n    // Check if the target matches the root project.\n    if (this.isRootTarget(target)) {\n      // Get the root package.json\n      const pkg = this.getPackageJsonAnyFilePathInModule({\n        target,\n        filePath: this.props.projectRoot,\n      });\n      return pkg ? { name: pkg.name, isRootTarget: true } : null;\n    }\n\n    // Otherwise, start tracing for dependencies.\n\n    let source = this.getExternalSourceForPod(target);\n    if (!source) {\n      // Some modules are formatted incorrectly in Xcode like `EXUpdates-EXUpdates` or `EXConstants-EXConstants`\n      // here we'll attempt to split the value, ensure there's more than one copy, and that all copies are the same, then we'll check against that new value.\n      const parts = target.split('-');\n      if (!!parts[0] && parts.length > 1 && parts.every(s => s === parts[0])) {\n        source = this.getExternalSourceForPod(parts[0]);\n      }\n    }\n\n    if (source?.source) {\n      // Finally attempt to trace the podspec file.\n      const pkg = this.getPackageJsonAnyFilePathInModule({\n        target: source.pod,\n        filePath: source.source,\n      });\n      if (pkg) {\n        return { name: pkg.name, isRootTarget: false };\n      }\n    }\n\n    return null;\n  }\n\n  isRootTarget(target: string) {\n    return (\n      this.props.rootTargetName &&\n      (target === this.props.rootTargetName || target === `Pods-${this.props.rootTargetName}`)\n    );\n  }\n\n  getNodeModuleName(\n    filePath: string,\n    target?: string\n  ): { name: string; isRootTarget: boolean } | null {\n    const moduleName = getNodeModuleName(filePath);\n\n    if (moduleName) {\n      return { name: moduleName, isRootTarget: false };\n    } else if (!target) {\n      return null;\n    }\n\n    return this.getNodeModuleNameForTarget(target);\n  }\n\n  getExternalSourceForPod = memoize(this.getExternalSourceForPodWithoutCache.bind(this));\n\n  getExternalSourceForPodWithoutCache(pod?: string): { pod: string; source: string } | null {\n    if (!pod) {\n      return null;\n    }\n\n    const results = getFirstExternalSourceForPod(this.podfile, { name: pod });\n\n    // Keep tracing until we get to a development pod with a local file reference.\n    const filePath = results?.source[':podspec'] ?? results?.source[':path'] ?? null;\n\n    if (results && filePath) {\n      return { pod: results.pod, source: filePath };\n    }\n\n    return null;\n  }\n\n  private memoizedGetPackageJsonAnyFilePathInModule = memoizeTrigger(\n    this.getPackageJsonAnyFilePathInModuleWithoutCache.bind(this)\n  );\n\n  /** This can be a path like `/app/node_modules/expo-camera/ios` or `/app/node_modules/react-native-webrtc` depending on where the podspec is. */\n  getPackageJsonAnyFilePathInModule(props: { target: string; filePath: string }) {\n    return this.memoizedGetPackageJsonAnyFilePathInModule({\n      key: props.target,\n      args: [props.filePath],\n    });\n  }\n\n  getPackageJsonAnyFilePathInModuleWithoutCache(filePath: string): Record<string, any> | null {\n    if (!this.props.projectRoot || !filePath) {\n      return null;\n    }\n\n    const nativeProjectRoot = path.join(this.props.projectRoot, 'ios');\n    // In the case of the root level podspec file.\n    try {\n      const rootLevelPkgJsonPath = path.join(nativeProjectRoot, 'package.json');\n      return require(rootLevelPkgJsonPath);\n    } catch {\n      return getPackageJsonForPath(path.join(nativeProjectRoot, filePath));\n    }\n  }\n}\n\nfunction memoize<T extends (key: string) => any, Ret = ReturnType<T>>(func: T) {\n  const cache: Record<string, Ret> = {};\n  return function (key: string): Ret {\n    if (key in cache) {\n      return cache[key];\n    }\n    const result = func(key);\n    cache[key] = result;\n    return result;\n  };\n}\n\nfunction memoizeTrigger<\n  T extends (...args: any[]) => any,\n  Args = Parameters<T>,\n  Ret = ReturnType<T>\n>(func: T): (props: { key: string; args: Args }) => Ret {\n  const cache: Record<string, Ret> = {};\n  return function ({ key, args }: { key: string; args: Args }): Ret {\n    if (key in cache) {\n      return cache[key];\n    }\n    // @ts-ignore\n    const result = func(...args);\n    cache[key] = result;\n    return result;\n  };\n}\n\n// A list of packages that aren't linked through cocoapods directly.\nconst knownPackages: Record<string, string> = {\n  // Added to ReactCore as a `resource_bundle`\n  'React-Core-AccessibilityResources': 'react-native',\n  YogaKit: 'react-native',\n  // flipper\n  'Flipper-DoubleConversion': 'react-native',\n  'Flipper-Folly': 'react-native',\n  'OpenSSL-Universal': 'react-native',\n  FlipperKit: 'react-native',\n  Flipper: 'react-native',\n  'Flipper-RSocket': 'react-native',\n};\n"]}