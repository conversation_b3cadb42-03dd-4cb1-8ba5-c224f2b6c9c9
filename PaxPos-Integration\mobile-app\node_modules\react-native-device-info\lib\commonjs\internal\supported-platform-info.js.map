{"version": 3, "sources": ["supported-platform-info.ts"], "names": ["memo", "clearMemo", "getSupportedFunction", "supportedPlatforms", "getter", "defaultGetter", "supportedMap", "filter", "key", "Platform", "OS", "for<PERSON>ach", "select", "default", "getSupportedPlatformInfoSync", "defaultValue", "memoKey", "undefined", "output", "getSupportedPlatformInfoAsync", "Promise", "resolve", "getSupportedPlatformInfoFunctions", "syncGetter", "asyncParams"], "mappings": ";;;;;;;;;;AAAA;;AAWA;AACA,IAAIA,IAAc,GAAG,EAArB;;AAEO,SAASC,SAAT,GAAqB;AAC1BD,EAAAA,IAAI,GAAG,EAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,oBAAT,CACEC,kBADF,EAEEC,MAFF,EAGEC,aAHF,EAIa;AACX,MAAIC,YAAiB,GAAG,EAAxB;AACAH,EAAAA,kBAAkB,CACfI,MADH,CACWC,GAAD,IAASC,sBAASC,EAAT,IAAeF,GADlC,EAEGG,OAFH,CAEYH,GAAD,IAAUF,YAAY,CAACE,GAAD,CAAZ,GAAoBJ,MAFzC;AAGA,SAAOK,sBAASG,MAAT,CAAgB,EACrB,GAAGN,YADkB;AAErBO,IAAAA,OAAO,EAAER;AAFY,GAAhB,CAAP;AAID;AAED;AACA;AACA;AACA;;;AACO,SAASS,4BAAT,CAAyC;AAC9CV,EAAAA,MAD8C;AAE9CD,EAAAA,kBAF8C;AAG9CY,EAAAA,YAH8C;AAI9CC,EAAAA;AAJ8C,CAAzC,EAKsC;AAC3C,MAAIA,OAAO,IAAIhB,IAAI,CAACgB,OAAD,CAAJ,IAAiBC,SAAhC,EAA2C;AACzC,WAAOjB,IAAI,CAACgB,OAAD,CAAX;AACD,GAFD,MAEO;AACL,UAAME,MAAM,GAAGhB,oBAAoB,CAACC,kBAAD,EAAqBC,MAArB,EAA6B,MAAMW,YAAnC,CAApB,EAAf;;AACA,QAAIC,OAAJ,EAAa;AACXhB,MAAAA,IAAI,CAACgB,OAAD,CAAJ,GAAgBE,MAAhB;AACD;;AACD,WAAOA,MAAP;AACD;AACF;AAED;AACA;AACA;AACA;;;AACO,eAAeC,6BAAf,CAAgD;AACrDf,EAAAA,MADqD;AAErDD,EAAAA,kBAFqD;AAGrDY,EAAAA,YAHqD;AAIrDC,EAAAA;AAJqD,CAAhD,EAKgD;AACrD,MAAIA,OAAO,IAAIhB,IAAI,CAACgB,OAAD,CAAJ,IAAiBC,SAAhC,EAA2C;AACzC,WAAOjB,IAAI,CAACgB,OAAD,CAAX;AACD,GAFD,MAEO;AACL,UAAME,MAAM,GAAG,MAAMhB,oBAAoB,CAACC,kBAAD,EAAqBC,MAArB,EAA6B,MACpEgB,OAAO,CAACC,OAAR,CAAgBN,YAAhB,CADuC,CAApB,EAArB;;AAGA,QAAIC,OAAJ,EAAa;AACXhB,MAAAA,IAAI,CAACgB,OAAD,CAAJ,GAAgBE,MAAhB;AACD;;AAED,WAAOA,MAAP;AACD;AACF;AAED;AACA;AACA;AACA;;;AACO,SAASI,iCAAT,CAA8C;AACnDC,EAAAA,UADmD;AAEnD,KAAGC;AAFgD,CAA9C,EAGyE;AAC9E,SAAO,CACL,MAAML,6BAA6B,CAACK,WAAD,CAD9B,EAEL,MAAMV,4BAA4B,CAAC,EAAE,GAAGU,WAAL;AAAkBpB,IAAAA,MAAM,EAAEmB;AAA1B,GAAD,CAF7B,CAAP;AAID", "sourcesContent": ["import { Platform } from 'react-native';\n\nimport {\n  Platform<PERSON>rray,\n  Getter,\n  GetSupportedPlatformInfoAsyncParams,\n  GetSupportedPlatformInfoSyncParams,\n  GetSupportedPlatformInfoFunctionsParams,\n} from './privateTypes';\n\ntype MemoType = { [key: string]: any };\n// centralized memo object\nlet memo: MemoType = {};\n\nexport function clearMemo() {\n  memo = {};\n}\n\n/**\n * function returns the proper getter based current platform X supported platforms\n * @param supportedPlatforms array of supported platforms (OS)\n * @param getter desired function used to get info\n * @param defaultGetter getter that returns a default value if desired getter is not supported by current platform\n */\nfunction getSupportedFunction<T>(\n  supportedPlatforms: PlatformArray,\n  getter: Getter<T>,\n  defaultGetter: Getter<T>\n): Getter<T> {\n  let supportedMap: any = {};\n  supportedPlatforms\n    .filter((key) => Platform.OS == key)\n    .forEach((key) => (supportedMap[key] = getter));\n  return Platform.select({\n    ...supportedMap,\n    default: defaultGetter,\n  });\n}\n\n/**\n * function used to get desired info synchronously — with optional memoization\n * @param param0\n */\nexport function getSupportedPlatformInfoSync<T>({\n  getter,\n  supportedPlatforms,\n  defaultValue,\n  memoKey,\n}: GetSupportedPlatformInfoSyncParams<T>): T {\n  if (memoKey && memo[memoKey] != undefined) {\n    return memo[memoKey];\n  } else {\n    const output = getSupportedFunction(supportedPlatforms, getter, () => defaultValue)();\n    if (memoKey) {\n      memo[memoKey] = output;\n    }\n    return output;\n  }\n}\n\n/**\n * function used to get desired info asynchronously — with optional memoization\n * @param param0\n */\nexport async function getSupportedPlatformInfoAsync<T>({\n  getter,\n  supportedPlatforms,\n  defaultValue,\n  memoKey,\n}: GetSupportedPlatformInfoAsyncParams<T>): Promise<T> {\n  if (memoKey && memo[memoKey] != undefined) {\n    return memo[memoKey];\n  } else {\n    const output = await getSupportedFunction(supportedPlatforms, getter, () =>\n      Promise.resolve(defaultValue)\n    )();\n    if (memoKey) {\n      memo[memoKey] = output;\n    }\n\n    return output;\n  }\n}\n\n/**\n * function that returns array of getter functions [async, sync]\n * @param param0\n */\nexport function getSupportedPlatformInfoFunctions<T>({\n  syncGetter,\n  ...asyncParams\n}: GetSupportedPlatformInfoFunctionsParams<T>): [Getter<Promise<T>>, Getter<T>] {\n  return [\n    () => getSupportedPlatformInfoAsync(asyncParams),\n    () => getSupportedPlatformInfoSync({ ...asyncParams, getter: syncGetter }),\n  ];\n}\n"]}