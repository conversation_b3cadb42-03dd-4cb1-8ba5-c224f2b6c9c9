{"version": 3, "sources": ["index.js"], "names": ["deviceInfoEmitter", "NativeEventEmitter", "NativeModules", "RNDeviceInfo", "batteryCharging", "batteryLevel", "powerState", "_readPowerState", "battery", "level", "charging", "chargingtime", "dischargingtime", "lowPowerMode", "batteryState", "getMaxMemorySync", "window", "performance", "memory", "jsHeapSizeLimit", "getInstallReferrerSync", "document", "referrer", "isAirplaneModeSync", "navigator", "onLine", "getUserAgentSync", "userAgent", "isLocationEnabledSync", "geolocation", "getTotalMemorySync", "deviceMemory", "getUsedMemorySync", "usedJSHeapSize", "init", "getBattery", "then", "addEventListener", "emit", "getBaseOsSync", "platform", "macosPlatforms", "windowsPlatforms", "iosPlatforms", "os", "indexOf", "test", "getInstallReferrer", "getUserAgent", "isBatteryCharging", "isBatteryChargingSync", "isCameraPresent", "mediaDevices", "enumerateDevices", "devices", "find", "d", "kind", "isCameraPresentSync", "console", "log", "getBatteryLevel", "getBatteryLevelSync", "isLocationEnabled", "isAirplaneMode", "getBaseOs", "getTotalDiskCapacity", "storage", "estimate", "quota", "getTotalDiskCapacitySync", "getFreeDiskStorage", "usage", "getFreeDiskStorageSync", "getMaxMemory", "getUsed<PERSON><PERSON><PERSON>", "getTotalMemory", "getPowerState", "getPowerStateSync"], "mappings": ";;;;;;;AAAA;;AAEA,MAAMA,iBAAiB,GAAG,IAAIC,+BAAJ,CAAuBC,2BAAcC,YAArC,CAA1B;AAEA,IAAIC,eAAe,GAAG,KAAtB;AAAA,IACEC,YAAY,GAAG,CAAC,CADlB;AAAA,IAEEC,UAAU,GAAG,EAFf;;AAIA,MAAMC,eAAe,GAAIC,OAAD,IAAa;AACnC,QAAM;AAAEC,IAAAA,KAAF;AAASC,IAAAA,QAAT;AAAmBC,IAAAA,YAAnB;AAAiCC,IAAAA;AAAjC,MAAqDJ,OAA3D;AAEA,SAAO;AACLH,IAAAA,YAAY,EAAEI,KADT;AAELI,IAAAA,YAAY,EAAE,KAFT;AAGLC,IAAAA,YAAY,EAAEL,KAAK,KAAK,CAAV,GAAc,MAAd,GAAuBC,QAAQ,GAAG,UAAH,GAAgB,WAHxD;AAILC,IAAAA,YAJK;AAKLC,IAAAA;AALK,GAAP;AAOD,CAVD;;AAYO,MAAMG,gBAAgB,GAAG,MAAM;AACpC,MAAIC,MAAM,CAACC,WAAP,IAAsBD,MAAM,CAACC,WAAP,CAAmBC,MAA7C,EAAqD;AACnD,WAAOF,MAAM,CAACC,WAAP,CAAmBC,MAAnB,CAA0BC,eAAjC;AACD;;AACD,SAAO,CAAC,CAAR;AACD,CALM;;;;AAOA,MAAMC,sBAAsB,GAAG,MAAM;AAC1C,SAAOC,QAAQ,CAACC,QAAhB;AACD,CAFM;;;;AAIA,MAAMC,kBAAkB,GAAG,MAAM;AACtC,SAAO,CAAC,CAACC,SAAS,CAACC,MAAnB;AACD,CAFM;;;;AAIA,MAAMC,gBAAgB,GAAG,MAAM;AACpC,SAAOV,MAAM,CAACQ,SAAP,CAAiBG,SAAxB;AACD,CAFM;;;;AAIA,MAAMC,qBAAqB,GAAG,MAAM;AACzC,SAAO,CAAC,CAACJ,SAAS,CAACK,WAAnB;AACD,CAFM;;;;AAIA,MAAMC,kBAAkB,GAAG,MAAM;AACtC,MAAIN,SAAS,CAACO,YAAd,EAA4B;AAC1B,WAAOP,SAAS,CAACO,YAAV,GAAyB,UAAhC;AACD;;AACD,SAAO,CAAC,CAAR;AACD,CALM;;;;AAOA,MAAMC,iBAAiB,GAAG,MAAM;AACrC,MAAIhB,MAAM,CAACC,WAAP,IAAsBD,MAAM,CAACC,WAAP,CAAmBC,MAA7C,EAAqD;AACnD,WAAOF,MAAM,CAACC,WAAP,CAAmBC,MAAnB,CAA0Be,cAAjC;AACD;;AACD,SAAO,CAAC,CAAR;AACD,CALM;;;;AAOP,MAAMC,IAAI,GAAG,MAAM;AACjB,MAAI,OAAOV,SAAP,KAAqB,WAArB,IAAoC,CAACA,SAAS,CAACW,UAAnD,EAA+D;AAE/DX,EAAAA,SAAS,CAACW,UAAV,GAAuBC,IAAvB,CAA6B5B,OAAD,IAAa;AACvCJ,IAAAA,eAAe,GAAGI,OAAO,CAACE,QAA1B;AAEAF,IAAAA,OAAO,CAAC6B,gBAAR,CAAyB,gBAAzB,EAA2C,MAAM;AAC/C,YAAM;AAAE3B,QAAAA;AAAF,UAAeF,OAArB;AAEAJ,MAAAA,eAAe,GAAGM,QAAlB;AACAJ,MAAAA,UAAU,GAAGC,eAAe,CAACC,OAAD,CAA5B;AAEAR,MAAAA,iBAAiB,CAACsC,IAAlB,CAAuB,kCAAvB,EAA2DhC,UAA3D;AACD,KAPD;AASAE,IAAAA,OAAO,CAAC6B,gBAAR,CAAyB,aAAzB,EAAwC,MAAM;AAC5C,YAAM;AAAE5B,QAAAA;AAAF,UAAYD,OAAlB;AAEAH,MAAAA,YAAY,GAAGI,KAAf;AACAH,MAAAA,UAAU,GAAGC,eAAe,CAACC,OAAD,CAA5B;AAEAR,MAAAA,iBAAiB,CAACsC,IAAlB,CAAuB,oCAAvB,EAA6D7B,KAA7D;;AACA,UAAIA,KAAK,GAAG,GAAZ,EAAiB;AACfT,QAAAA,iBAAiB,CAACsC,IAAlB,CAAuB,gCAAvB,EAAyD7B,KAAzD;AACD;AACF,KAVD;AAWD,GAvBD;AAwBD,CA3BD;;AA6BA,MAAM8B,aAAa,GAAG,MAAM;AAC1B,QAAMZ,SAAS,GAAGX,MAAM,CAACQ,SAAP,CAAiBG,SAAnC;AAAA,QACEa,QAAQ,GAAGxB,MAAM,CAACQ,SAAP,CAAiBgB,QAD9B;AAAA,QAEEC,cAAc,GAAG,CAAC,WAAD,EAAc,UAAd,EAA0B,QAA1B,EAAoC,QAApC,CAFnB;AAAA,QAGEC,gBAAgB,GAAG,CAAC,OAAD,EAAU,OAAV,EAAmB,SAAnB,EAA8B,OAA9B,CAHrB;AAAA,QAIEC,YAAY,GAAG,CAAC,QAAD,EAAW,MAAX,EAAmB,MAAnB,CAJjB;AAMA,MAAIC,EAAE,GAAGJ,QAAT;;AAEA,MAAIC,cAAc,CAACI,OAAf,CAAuBL,QAAvB,MAAqC,CAAC,CAA1C,EAA6C;AAC3CI,IAAAA,EAAE,GAAG,QAAL;AACD,GAFD,MAEO,IAAID,YAAY,CAACE,OAAb,CAAqBL,QAArB,MAAmC,CAAC,CAAxC,EAA2C;AAChDI,IAAAA,EAAE,GAAG,KAAL;AACD,GAFM,MAEA,IAAIF,gBAAgB,CAACG,OAAjB,CAAyBL,QAAzB,MAAuC,CAAC,CAA5C,EAA+C;AACpDI,IAAAA,EAAE,GAAG,SAAL;AACD,GAFM,MAEA,IAAI,UAAUE,IAAV,CAAenB,SAAf,CAAJ,EAA+B;AACpCiB,IAAAA,EAAE,GAAG,SAAL;AACD,GAFM,MAEA,IAAI,CAACA,EAAD,IAAO,QAAQE,IAAR,CAAaN,QAAb,CAAX,EAAmC;AACxCI,IAAAA,EAAE,GAAG,OAAL;AACD;;AAED,SAAOA,EAAP;AACD,CAtBD;;AAwBAV,IAAI;AACJ;AACA;AACA;;AAEO,MAAMa,kBAAkB,GAAG,YAAY;AAC5C,SAAO3B,sBAAsB,EAA7B;AACD,CAFM;;;;AAIA,MAAM4B,YAAY,GAAG,YAAY;AACtC,SAAOtB,gBAAgB,EAAvB;AACD,CAFM;;;;AAIA,MAAMuB,iBAAiB,GAAG,YAAY;AAC3C,MAAIzB,SAAS,CAACW,UAAd,EAA0B;AACxB,WAAOX,SAAS,CAACW,UAAV,GAAuBC,IAAvB,CAA4B5B,OAAO,IAAIA,OAAO,CAACE,QAA/C,CAAP;AACD;;AACD,SAAO,KAAP;AACD,CALM;;;;AAOA,MAAMwC,qBAAqB,GAAG,MAAM;AACzC,SAAO9C,eAAP;AACD,CAFM;;;;AAIA,MAAM+C,eAAe,GAAG,YAAY;AACzC,MAAI3B,SAAS,CAAC4B,YAAV,IAA0B5B,SAAS,CAAC4B,YAAV,CAAuBC,gBAArD,EAAuE;AACrE,WAAO7B,SAAS,CAAC4B,YAAV,CAAuBC,gBAAvB,GAA0CjB,IAA1C,CAA+CkB,OAAO,IAAI;AAC/D,aAAO,CAAC,CAACA,OAAO,CAACC,IAAR,CAAcC,CAAD,IAAOA,CAAC,CAACC,IAAF,KAAW,YAA/B,CAAT;AACD,KAFM,CAAP;AAGD;;AACD,SAAO,KAAP;AACD,CAPM;;;;AASA,MAAMC,mBAAmB,GAAG,MAAM;AACvCC,EAAAA,OAAO,CAACC,GAAR,CACE,2FADF;AAGA,SAAO,KAAP;AACD,CALM;;;;AAOA,MAAMC,eAAe,GAAG,YAAY;AACzC,MAAIrC,SAAS,CAACW,UAAd,EAA0B;AACxB,WAAOX,SAAS,CAACW,UAAV,GAAuBC,IAAvB,CAA4B5B,OAAO,IAAIA,OAAO,CAACC,KAA/C,CAAP;AACD;;AACD,SAAO,CAAC,CAAR;AACD,CALM;;;;AAOA,MAAMqD,mBAAmB,GAAG,MAAM;AACvC,SAAOzD,YAAP;AACD,CAFM;;;;AAIA,MAAM0D,iBAAiB,GAAG,YAAY;AAC3C,SAAOnC,qBAAqB,EAA5B;AACD,CAFM;;;;AAIA,MAAMoC,cAAc,GAAG,YAAY;AACxC,SAAOzC,kBAAkB,EAAzB;AACD,CAFM;;;;AAIA,MAAM0C,SAAS,GAAG,YAAY;AACnC,SAAO1B,aAAa,EAApB;AACD,CAFM;;;;AAIA,MAAM2B,oBAAoB,GAAG,YAAY;AAC9C,MAAI1C,SAAS,CAAC2C,OAAV,IAAqB3C,SAAS,CAAC2C,OAAV,CAAkBC,QAA3C,EAAqD;AACnD,WAAO5C,SAAS,CAAC2C,OAAV,CAAkBC,QAAlB,GAA6BhC,IAA7B,CAAkC,CAAC;AAAEiC,MAAAA;AAAF,KAAD,KAAeA,KAAjD,CAAP;AACD;;AACD,SAAO,CAAC,CAAR;AACD,CALM;;;;AAOA,MAAMC,wBAAwB,GAAG,MAAM;AAC5CX,EAAAA,OAAO,CAACC,GAAR,CACE,qGADF;AAGA,SAAO,CAAC,CAAR;AACD,CALM;;;;AAOA,MAAMW,kBAAkB,GAAG,YAAY;AAC5C,MAAI/C,SAAS,CAAC2C,OAAV,IAAqB3C,SAAS,CAAC2C,OAAV,CAAkBC,QAA3C,EAAqD;AACnD,WAAO5C,SAAS,CAAC2C,OAAV,CAAkBC,QAAlB,GAA6BhC,IAA7B,CAAkC,CAAC;AAAEiC,MAAAA,KAAF;AAASG,MAAAA;AAAT,KAAD,KAAsBH,KAAK,GAAGG,KAAhE,CAAP;AACD;;AACD,SAAO,CAAC,CAAR;AACD,CALM;;;;AAOA,MAAMC,sBAAsB,GAAG,MAAM;AAC1Cd,EAAAA,OAAO,CAACC,GAAR,CACE,iGADF;AAGA,SAAO,CAAC,CAAR;AACD,CALM;;;;AAOA,MAAMc,YAAY,GAAG,YAAY;AACtC,SAAO3D,gBAAgB,EAAvB;AACD,CAFM;;;;AAIA,MAAM4D,aAAa,GAAG,YAAY;AACvC,SAAO3C,iBAAiB,EAAxB;AACD,CAFM;;;;AAIA,MAAM4C,cAAc,GAAG,YAAY;AACxC,SAAO9C,kBAAkB,EAAzB;AACD,CAFM;;;;AAIA,MAAM+C,aAAa,GAAG,YAAY;AACvC,MAAIrD,SAAS,CAACW,UAAd,EAA0B;AACxB,WAAOX,SAAS,CAACW,UAAV,GAAuBC,IAAvB,CAA6B5B,OAAD,IAAaD,eAAe,CAACC,OAAD,CAAxD,CAAP;AACD;;AACD,SAAO,EAAP;AACD,CALM;;;;AAOA,MAAMsE,iBAAiB,GAAG,MAAM;AACrC,SAAOxE,UAAP;AACD,CAFM", "sourcesContent": ["import { NativeEventEmitter, NativeModules } from 'react-native';\n\nconst deviceInfoEmitter = new NativeEventEmitter(NativeModules.RNDeviceInfo);\n\nlet batteryCharging = false,\n  batteryLevel = -1,\n  powerState = {};\n\nconst _readPowerState = (battery) => {\n  const { level, charging, chargingtime, dischargingtime } = battery;\n\n  return {\n    batteryLevel: level,\n    lowPowerMode: false,\n    batteryState: level === 1 ? 'full' : charging ? 'charging' : 'unplugged',\n    chargingtime,\n    dischargingtime,\n  };\n};\n\nexport const getMaxMemorySync = () => {\n  if (window.performance && window.performance.memory) {\n    return window.performance.memory.jsHeapSizeLimit;\n  }\n  return -1;\n};\n\nexport const getInstallReferrerSync = () => {\n  return document.referrer;\n};\n\nexport const isAirplaneModeSync = () => {\n  return !!navigator.onLine;\n};\n\nexport const getUserAgentSync = () => {\n  return window.navigator.userAgent;\n};\n\nexport const isLocationEnabledSync = () => {\n  return !!navigator.geolocation;\n};\n\nexport const getTotalMemorySync = () => {\n  if (navigator.deviceMemory) {\n    return navigator.deviceMemory * 1000000000;\n  }\n  return -1;\n};\n\nexport const getUsedMemorySync = () => {\n  if (window.performance && window.performance.memory) {\n    return window.performance.memory.usedJSHeapSize;\n  }\n  return -1;\n};\n\nconst init = () => {\n  if (typeof navigator === 'undefined' || !navigator.getBattery) return;\n\n  navigator.getBattery().then((battery) => {\n    batteryCharging = battery.charging;\n\n    battery.addEventListener('chargingchange', () => {\n      const { charging } = battery;\n\n      batteryCharging = charging;\n      powerState = _readPowerState(battery);\n\n      deviceInfoEmitter.emit('RNDeviceInfo_powerStateDidChange', powerState);\n    });\n\n    battery.addEventListener('levelchange', () => {\n      const { level } = battery;\n\n      batteryLevel = level;\n      powerState = _readPowerState(battery);\n\n      deviceInfoEmitter.emit('RNDeviceInfo_batteryLevelDidChange', level);\n      if (level < 0.2) {\n        deviceInfoEmitter.emit('RNDeviceInfo_batteryLevelIsLow', level);\n      }\n    });\n  });\n};\n\nconst getBaseOsSync = () => {\n  const userAgent = window.navigator.userAgent,\n    platform = window.navigator.platform,\n    macosPlatforms = ['Macintosh', 'MacIntel', 'MacPPC', 'Mac68K'],\n    windowsPlatforms = ['Win32', 'Win64', 'Windows', 'WinCE'],\n    iosPlatforms = ['iPhone', 'iPad', 'iPod'];\n\n  let os = platform;\n\n  if (macosPlatforms.indexOf(platform) !== -1) {\n    os = 'Mac OS';\n  } else if (iosPlatforms.indexOf(platform) !== -1) {\n    os = 'iOS';\n  } else if (windowsPlatforms.indexOf(platform) !== -1) {\n    os = 'Windows';\n  } else if (/Android/.test(userAgent)) {\n    os = 'Android';\n  } else if (!os && /Linux/.test(platform)) {\n    os = 'Linux';\n  }\n\n  return os;\n};\n\ninit();\n/**\n * react-native-web empty polyfill.\n */\n\nexport const getInstallReferrer = async () => {\n  return getInstallReferrerSync();\n};\n\nexport const getUserAgent = async () => {\n  return getUserAgentSync();\n};\n\nexport const isBatteryCharging = async () => {\n  if (navigator.getBattery) {\n    return navigator.getBattery().then(battery => battery.charging);\n  }\n  return false;\n};\n\nexport const isBatteryChargingSync = () => {\n  return batteryCharging;\n};\n\nexport const isCameraPresent = async () => {\n  if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {\n    return navigator.mediaDevices.enumerateDevices().then(devices => {\n      return !!devices.find((d) => d.kind === 'videoinput');\n    });\n  }\n  return false;\n};\n\nexport const isCameraPresentSync = () => {\n  console.log(\n    '[react-native-device-info] isCameraPresentSync not supported - please use isCameraPresent'\n  );\n  return false;\n};\n\nexport const getBatteryLevel = async () => {\n  if (navigator.getBattery) {\n    return navigator.getBattery().then(battery => battery.level);\n  }\n  return -1;\n};\n\nexport const getBatteryLevelSync = () => {\n  return batteryLevel;\n};\n\nexport const isLocationEnabled = async () => {\n  return isLocationEnabledSync();\n};\n\nexport const isAirplaneMode = async () => {\n  return isAirplaneModeSync();\n};\n\nexport const getBaseOs = async () => {\n  return getBaseOsSync();\n};\n\nexport const getTotalDiskCapacity = async () => {\n  if (navigator.storage && navigator.storage.estimate) {\n    return navigator.storage.estimate().then(({ quota }) => quota)\n  }\n  return -1;\n};\n\nexport const getTotalDiskCapacitySync = () => {\n  console.log(\n    '[react-native-device-info] getTotalDiskCapacitySync not supported - please use getTotalDiskCapacity'\n  );\n  return -1;\n};\n\nexport const getFreeDiskStorage = async () => {\n  if (navigator.storage && navigator.storage.estimate) {\n    return navigator.storage.estimate().then(({ quota, usage }) => quota - usage)\n  }\n  return -1;\n};\n\nexport const getFreeDiskStorageSync = () => {\n  console.log(\n    '[react-native-device-info] getFreeDiskStorageSync not supported - please use getFreeDiskStorage'\n  );\n  return -1;\n};\n\nexport const getMaxMemory = async () => {\n  return getMaxMemorySync();\n};\n\nexport const getUsedMemory = async () => {\n  return getUsedMemorySync();\n};\n\nexport const getTotalMemory = async () => {\n  return getTotalMemorySync();\n};\n\nexport const getPowerState = async () => {\n  if (navigator.getBattery) {\n    return navigator.getBattery().then((battery) => _readPowerState(battery))\n  }\n  return {};\n};\n\nexport const getPowerStateSync = () => {\n  return powerState;\n};\n"]}