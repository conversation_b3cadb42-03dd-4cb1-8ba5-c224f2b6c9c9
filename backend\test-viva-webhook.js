/**
 * Test script for Viva Webhook Integration
 * 
 * Tests the webhook endpoints and WebSocket functionality
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/v1';

// Test webhook health endpoint
async function testWebhookHealth() {
  try {
    console.log('🔍 Testing webhook health endpoint...');
    const response = await axios.get(`${BASE_URL}/viva/webhook/health`);
    console.log('✅ Health check passed:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

// Test webhook verification endpoint
async function testWebhookVerification() {
  try {
    console.log('🔍 Testing webhook verification endpoint...');
    const response = await axios.get(`${BASE_URL}/viva/webhook/verify`);
    console.log('✅ Verification endpoint response:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Verification endpoint failed:', error.message);
    return false;
  }
}

// Test webhook event processing
async function testWebhookEvent() {
  try {
    console.log('🔍 Testing webhook event processing...');
    
    const testEvent = {
      Url: "https://demo.vivapayments.com/api/messages/events/1796",
      EventData: {
        Moto: false,
        BinId: 414746,
        IsDcc: false,
        Ucaf: null,
        Email: "<EMAIL>",
        Phone: "+**********",
        BankId: "VISA",
        Systemic: false,
        BatchId: "***********",
        Switching: false,
        ParentId: null,
        Amount: 1000, // €10.00 in cents
        ChannelId: "web",
        TerminalId: 12345,
        MerchantId: "30481af3-63d9-42cd-93ea-1937a972b76d",
        OrderCode: ****************,
        ProductId: null,
        StatusId: "F", // Success
        FullName: "John Doe",
        ResellerId: null,
        DualMessage: false,
        InsDate: new Date().toISOString(),
        TotalFee: 50,
        CardToken: "CT_12345",
        CardNumber: "414746XXXXXX0133",
        Descriptor: "Test Payment",
        TipAmount: 0,
        SourceCode: "Default",
        SourceName: "Test Source",
        Latitude: null,
        Longitude: null,
        CompanyName: "Test Company",
        TransactionId: "test-transaction-" + Date.now(),
        CompanyTitle: "Test Company Ltd",
        PanEntryMode: "05",
        ReferenceNumber: 471543,
        ResponseCode: "00",
        CurrencyCode: "978", // EUR
        OrderCulture: "en-US",
        MerchantTrns: "Test transaction description",
        CustomerTrns: "Payment for order #123",
        IsManualRefund: false,
        TargetPersonId: null,
        TargetWalletId: null,
        AcquirerApproved: true,
        LoyaltyTriggered: false,
        TransactionTypeId: 5,
        AuthorizationId: "471543",
        TotalInstallments: 1,
        CardCountryCode: "GR",
        CardIssuingBank: "Test Bank",
        RedeemedAmount: 0,
        ClearanceDate: null,
        ConversionRate: 1.0,
        CurrentInstallment: 1,
        OriginalAmount: 1000,
        Tags: ["test", "webhook"],
        BillId: null,
        ConnectedAccountId: null,
        ResellerSourceCode: null,
        ResellerSourceName: null,
        MerchantCategoryCode: 5999,
        ResellerCompanyName: null,
        CardUniqueReference: "CUR_12345",
        OriginalCurrencyCode: "978",
        ExternalTransactionId: null,
        ResellerSourceAddress: null,
        CardExpirationDate: "12/25",
        ServiceId: null,
        RetrievalReferenceNumber: "**********12",
        AssignedMerchantUsers: [],
        AssignedResellerUsers: [],
        CardTypeId: 1,
        ResponseEventId: null,
        ElectronicCommerceIndicator: "05",
        OrderServiceId: 1,
        ApplicationIdentifierTerminal: null,
        IntegrationId: null,
        CardProductCategoryId: 1,
        CardProductAccountTypeId: 1,
        DigitalWalletId: 0,
        DccSessionId: null,
        DccMarkup: null,
        DccDifferenceOverEcb: null
      },
      Created: new Date().toISOString(),
      CorrelationId: "test-correlation-" + Date.now(),
      EventTypeId: 1796, // Transaction Payment Created
      Delay: null,
      RetryCount: 0,
      RetryDelayInSeconds: null,
      MessageId: "test-message-" + Date.now(),
      RecipientId: "test-recipient",
      MessageTypeId: 512
    };

    const response = await axios.post(`${BASE_URL}/viva/webhook`, testEvent, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Webhook event processed successfully:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Webhook event processing failed:', error.response?.data || error.message);
    return false;
  }
}

// Test failed transaction webhook
async function testFailedTransactionWebhook() {
  try {
    console.log('🔍 Testing failed transaction webhook...');
    
    const testEvent = {
      Url: "https://demo.vivapayments.com/api/messages/events/1798",
      EventData: {
        Amount: 1000,
        MerchantId: "30481af3-63d9-42cd-93ea-1937a972b76d",
        OrderCode: 2271655739472610,
        StatusId: "E", // Error/Failed
        TransactionId: "test-failed-transaction-" + Date.now(),
        ResponseCode: "05", // Do not honor
        InsDate: new Date().toISOString(),
        MerchantTrns: "Failed test transaction",
        CustomerTrns: "Payment failed for order #124"
      },
      Created: new Date().toISOString(),
      CorrelationId: "test-failed-correlation-" + Date.now(),
      EventTypeId: 1798, // Transaction Failed
      Delay: null,
      RetryCount: 0,
      RetryDelayInSeconds: null,
      MessageId: "test-failed-message-" + Date.now(),
      RecipientId: "test-recipient",
      MessageTypeId: 512
    };

    const response = await axios.post(`${BASE_URL}/viva/webhook`, testEvent, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Failed transaction webhook processed successfully:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Failed transaction webhook processing failed:', error.response?.data || error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting Viva Webhook Integration Tests\n');
  
  const tests = [
    { name: 'Webhook Health', fn: testWebhookHealth },
    { name: 'Webhook Verification', fn: testWebhookVerification },
    { name: 'Successful Payment Webhook', fn: testWebhookEvent },
    { name: 'Failed Payment Webhook', fn: testFailedTransactionWebhook }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    console.log(`\n📋 Running test: ${test.name}`);
    const result = await test.fn();
    if (result) {
      passed++;
    } else {
      failed++;
    }
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / tests.length) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Viva webhook integration is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the server logs and configuration.');
  }
}

// Handle command line execution
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testWebhookHealth,
  testWebhookVerification,
  testWebhookEvent,
  testFailedTransactionWebhook,
  runTests
};
