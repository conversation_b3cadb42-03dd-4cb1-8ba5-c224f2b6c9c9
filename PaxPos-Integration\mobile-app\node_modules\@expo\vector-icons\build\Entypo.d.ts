declare const _default: import("./createIconSet").Icon<"link" | "email" | "image" | "text" | "menu" | "radio" | "switch" | "forward" | "back" | "retweet" | "minus" | "plus" | "info" | "check" | "book" | "swap" | "mail" | "home" | "laptop" | "star" | "save" | "user" | "phone" | "notification" | "inbox" | "lock" | "cloud" | "eye" | "camera" | "export" | "heart" | "calculator" | "github" | "upload" | "download" | "tag" | "calendar" | "database" | "key" | "flag" | "man" | "wallet" | "sound" | "login" | "edit" | "warning" | "dropbox" | "skype" | "youtube" | "twitter" | "behance" | "dribbble" | "instagram" | "500px" | "500px-with-circle" | "add-to-list" | "add-user" | "address" | "adjust" | "air" | "aircraft" | "aircraft-landing" | "aircraft-take-off" | "align-bottom" | "align-horizontal-middle" | "align-left" | "align-right" | "align-top" | "align-vertical-middle" | "app-store" | "archive" | "area-graph" | "arrow-bold-down" | "arrow-bold-left" | "arrow-bold-right" | "arrow-bold-up" | "arrow-down" | "arrow-left" | "arrow-long-down" | "arrow-long-left" | "arrow-long-right" | "arrow-long-up" | "arrow-right" | "arrow-up" | "arrow-with-circle-down" | "arrow-with-circle-left" | "arrow-with-circle-right" | "arrow-with-circle-up" | "attachment" | "awareness-ribbon" | "back-in-time" | "baidu" | "bar-graph" | "basecamp" | "battery" | "beamed-note" | "bell" | "blackboard" | "block" | "bookmark" | "bookmarks" | "bowl" | "box" | "briefcase" | "browser" | "brush" | "bucket" | "bug" | "cake" | "ccw" | "chat" | "chevron-down" | "chevron-left" | "chevron-right" | "chevron-small-down" | "chevron-small-left" | "chevron-small-right" | "chevron-small-up" | "chevron-thin-down" | "chevron-thin-left" | "chevron-thin-right" | "chevron-thin-up" | "chevron-up" | "chevron-with-circle-down" | "chevron-with-circle-left" | "chevron-with-circle-right" | "chevron-with-circle-up" | "circle" | "circle-with-cross" | "circle-with-minus" | "circle-with-plus" | "circular-graph" | "clapperboard" | "classic-computer" | "clipboard" | "clock" | "code" | "cog" | "colours" | "compass" | "controller-fast-backward" | "controller-fast-forward" | "controller-jump-to-start" | "controller-next" | "controller-paus" | "controller-play" | "controller-record" | "controller-stop" | "controller-volume" | "copy" | "creative-cloud" | "creative-commons" | "creative-commons-attribution" | "creative-commons-noderivs" | "creative-commons-noncommercial-eu" | "creative-commons-noncommercial-us" | "creative-commons-public-domain" | "creative-commons-remix" | "creative-commons-share" | "creative-commons-sharealike" | "credit" | "credit-card" | "crop" | "cross" | "cup" | "cw" | "cycle" | "dial-pad" | "direction" | "document" | "document-landscape" | "documents" | "dot-single" | "dots-three-horizontal" | "dots-three-vertical" | "dots-two-horizontal" | "dots-two-vertical" | "dribbble-with-circle" | "drink" | "drive" | "drop" | "emoji-flirt" | "emoji-happy" | "emoji-neutral" | "emoji-sad" | "erase" | "eraser" | "evernote" | "eye-with-line" | "facebook" | "facebook-with-circle" | "feather" | "fingerprint" | "flash" | "flashlight" | "flat-brush" | "flattr" | "flickr" | "flickr-with-circle" | "flow-branch" | "flow-cascade" | "flow-line" | "flow-parallel" | "flow-tree" | "flower" | "folder" | "folder-images" | "folder-music" | "folder-video" | "foursquare" | "funnel" | "game-controller" | "gauge" | "github-with-circle" | "globe" | "google-" | "google--with-circle" | "google-drive" | "google-hangouts" | "google-play" | "graduation-cap" | "grid" | "grooveshark" | "hair-cross" | "hand" | "heart-outlined" | "help" | "help-with-circle" | "hour-glass" | "houzz" | "icloud" | "image-inverted" | "images" | "infinity" | "info-with-circle" | "instagram-with-circle" | "install" | "keyboard" | "lab-flask" | "landline" | "language" | "lastfm" | "lastfm-with-circle" | "layers" | "leaf" | "level-down" | "level-up" | "lifebuoy" | "light-bulb" | "light-down" | "light-up" | "line-graph" | "linkedin" | "linkedin-with-circle" | "list" | "location" | "location-pin" | "lock-open" | "log-out" | "loop" | "magnet" | "magnifying-glass" | "mail-with-circle" | "map" | "mask" | "medal" | "medium" | "medium-with-circle" | "megaphone" | "merge" | "message" | "mic" | "mixi" | "mobile" | "modern-mic" | "moon" | "mouse" | "mouse-pointer" | "music" | "network" | "new" | "new-message" | "news" | "newsletter" | "note" | "notifications-off" | "old-mobile" | "old-phone" | "onedrive" | "open-book" | "palette" | "paper-plane" | "paypal" | "pencil" | "picasa" | "pie-chart" | "pin" | "pinterest" | "pinterest-with-circle" | "popup" | "power-plug" | "price-ribbon" | "price-tag" | "print" | "progress-empty" | "progress-full" | "progress-one" | "progress-two" | "publish" | "qq" | "qq-with-circle" | "quote" | "raft" | "raft-with-circle" | "rainbow" | "rdio" | "rdio-with-circle" | "remove-user" | "renren" | "reply" | "reply-all" | "resize-100" | "resize-full-screen" | "rocket" | "round-brush" | "rss" | "ruler" | "scissors" | "scribd" | "select-arrows" | "share" | "share-alternative" | "shareable" | "shield" | "shop" | "shopping-bag" | "shopping-basket" | "shopping-cart" | "shuffle" | "signal" | "sina-weibo" | "skype-with-circle" | "slideshare" | "smashing" | "sound-mix" | "sound-mute" | "soundcloud" | "sports-club" | "spotify" | "spotify-with-circle" | "spreadsheet" | "squared-cross" | "squared-minus" | "squared-plus" | "star-outlined" | "stopwatch" | "stumbleupon" | "stumbleupon-with-circle" | "suitcase" | "swarm" | "sweden" | "tablet" | "tablet-mobile-combo" | "text-document" | "text-document-inverted" | "thermometer" | "thumbs-down" | "thumbs-up" | "thunder-cloud" | "ticket" | "time-slot" | "tools" | "traffic-cone" | "trash" | "tree" | "triangle-down" | "triangle-left" | "triangle-right" | "triangle-up" | "tripadvisor" | "trophy" | "tumblr" | "tumblr-with-circle" | "tv" | "twitter-with-circle" | "typing" | "uninstall" | "unread" | "untag" | "upload-to-cloud" | "users" | "v-card" | "video" | "video-camera" | "vimeo" | "vimeo-with-circle" | "vine" | "vine-with-circle" | "vinyl" | "vk" | "vk-alternitive" | "vk-with-circle" | "voicemail" | "water" | "windows-store" | "xing" | "xing-with-circle" | "yelp" | "youko" | "youko-with-circle" | "youtube-with-circle", "entypo">;
export default _default;
//# sourceMappingURL=Entypo.d.ts.map