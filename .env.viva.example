# Viva Wallet Payment Configuration
#
# Demo environment credentials provided by user

# Basic Auth Credentials (for payment status and cancellation)
VIVA_MERCHANT_ID=30481af3-63d9-42cd-93ea-1937a972b76d
VIVA_API_KEY=SothunZ2FxVRMkq666sbxbxB6VNbJG

# OAuth2 Credentials (for payment creation - Smart Checkout)
VIVA_CLIENT_ID=00pp9ggt8otvtzyfy3sv7y0d5u56oleukdkd7mma293z8.apps.vivapayments.com
VIVA_CLIENT_SECRET=YOUR_CLIENT_SECRET_HERE

# Configuration
VIVA_SOURCE_CODE=Default
VIVA_ENVIRONMENT=demo
VIVA_ENABLED=true

# Webhook Configuration
VIVA_WEBHOOK_VERIFICATION_KEY=YOUR_WEBHOOK_VERIFICATION_KEY_HERE

# IMPORTANT: You need to get the CLIENT_SECRET for the Smart Checkout credentials
# To get the Client Secret:
# 1. Log into your Viva demo account at https://demo.vivapayments.com/
# 2. Go to Settings > API Access > Smart Checkout Credentials
# 3. Click on your Client ID to view/generate the Client Secret
# 4. Replace YOUR_CLIENT_SECRET_HERE above with the actual secret

# WEBHOOK SETUP INSTRUCTIONS:
# 1. Generate webhook verification key by calling: GET /api/v1/viva/webhook/verify
# 2. Set up webhook in Viva Self Care:
#    - URL: https://your-domain.com/api/v1/viva/webhook
#    - Event Type: Transaction Payment Created (1796)
#    - Verify the URL using the verification key
# 3. Replace YOUR_WEBHOOK_VERIFICATION_KEY_HERE with the generated key

# Alternative Client IDs available (if needed):
# Account Transactions: msp2e65gfhh01k8m0xh3969mi72f6db3816ds4b0apg53.apps.vivapayments.com

# For production, change VIVA_ENVIRONMENT to 'production' and use production credentials
