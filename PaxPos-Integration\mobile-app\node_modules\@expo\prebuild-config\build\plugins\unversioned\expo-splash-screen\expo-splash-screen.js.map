{"version": 3, "file": "expo-splash-screen.js", "names": ["_withAndroidSplashScreen", "data", "require", "_withIosSplashScreen", "_createLegacyPlugin", "_default", "exports", "default", "createLegacyPlugin", "packageName", "fallback", "withAndroidSplashScreen", "withIosSplashScreen"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/expo-splash-screen.ts"], "sourcesContent": ["import { withAndroidSplashScreen } from './withAndroidSplashScreen';\nimport { withIosSplashScreen } from './withIosSplashScreen';\nimport { createLegacyPlugin } from '../createLegacyPlugin';\n\nexport default createLegacyPlugin({\n  packageName: 'expo-splash-screen',\n  fallback: [withAndroidSplashScreen, withIosSplashScreen],\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,yBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,wBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,qBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,oBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,oBAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,mBAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2D,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE5C,IAAAC,wCAAkB,EAAC;EAChCC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAACC,kDAAuB,EAAEC,0CAAmB;AACzD,CAAC,CAAC", "ignoreList": []}