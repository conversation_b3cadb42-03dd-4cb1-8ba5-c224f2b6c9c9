declare const FA6Style: {
    regular: string;
    light: string;
    solid: string;
    brand: string;
    sharp: string;
    sharpLight: string;
    sharpSolid: string;
    duotone: string;
    thin: string;
};
declare function createFA6iconSet(glyphMap: any, metadata: {} | undefined, fonts: any, pro?: boolean): any;
export { createFA6iconSet, FA6Style };
//# sourceMappingURL=createIconSetFromFontAwesome6.d.ts.map