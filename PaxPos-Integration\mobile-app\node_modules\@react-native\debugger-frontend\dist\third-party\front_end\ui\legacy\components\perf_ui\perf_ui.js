import*as e from"../../../../core/i18n/i18n.js";import*as t from"../../../../services/window_bounds/window_bounds.js";import*as i from"../../theme_support/theme_support.js";import*as s from"../../../../core/common/common.js";import*as r from"../../../../core/platform/platform.js";import*as n from"../../../../core/root/root.js";import*as o from"../../../components/render_coordinator/render_coordinator.js";import*as a from"../../legacy.js";import*as l from"../../../../core/host/host.js";import*as h from"../../../../models/bindings/bindings.js";import*as d from"../../../../models/trace/trace.js";import*as c from"../../../components/buttons/buttons.js";import*as u from"../../../visual_logging/visual_logging.js";import*as m from"../../../../core/sdk/sdk.js";import*as g from"../../../../models/workspace/workspace.js";import"../source_frame/source_frame.js";import*as p from"../../../components/icon_button/icon_button.js";import*as f from"../../../lit-html/lit-html.js";const v={congrats:"Congrats, you win!",ps:"PS: You can also open the game by typing `fixme`"},w=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/BrickBreaker.ts",v),b=e.i18n.getLocalizedString.bind(void 0,w),T=15,y=10,E=[{light:"rgb(224,240,255)",mediumLighter:"rgb(176,208,255)",mediumDarker:"rgb(112,160,221)",dark:"rgb(0,92,153)"},{light:"rgb(253, 216, 229)",mediumLighter:"rgb(250, 157, 188)",mediumDarker:"rgb(249, 98, 154)",dark:"rgb(254, 5, 105)"},{light:"rgb(254, 234, 234)",mediumLighter:"rgb(255, 216, 216)",mediumDarker:"rgb(255, 195, 195)",dark:"rgb(235, 125, 138)"},{light:"rgb(226,183,206)",mediumLighter:"rgb(219,124,165)",mediumDarker:"rgb(146,60,129)",dark:"rgb(186, 85, 255)"},{light:"rgb(206,255,206)",mediumLighter:"rgb(128,255,128)",mediumDarker:"rgb(0,246,0)",dark:"rgb(0,187,0)"},{light:"rgb(255, 188, 181)",mediumLighter:"rgb(254, 170, 170)",mediumDarker:"rgb(215, 59, 43)",dark:"rgb(187, 37, 23)"},{light:"rgb(236, 254, 250)",mediumLighter:"rgb(204, 255, 245)",mediumDarker:"rgb(164, 240, 233)",dark:"rgb(72,189,144)"},{light:"rgb(255, 225, 185)",mediumLighter:"rgb(255, 204, 141)",mediumDarker:"rgb(240, 140, 115)",dark:"rgb(211, 96, 117)"},{light:"rgb(218, 255, 248)",mediumLighter:"rgb(177, 235, 236)",mediumDarker:"rgb(112, 214, 214)",dark:"rgb(34, 205, 181)"}];class x extends HTMLElement{timelineFlameChart;#e;#t;#i;#s;#r;#n=0;#o=new Set;#a=new Map;#l=this.#h.bind(this);#d=this.#c.bind(this);#u=this.#m.bind(this);#g=this.#p.bind(this);#f=this.#v.bind(this);#w=t.WindowBoundsService.WindowBoundsServiceImpl.instance().getDevToolsBoundingElement();#b=0;#T=!1;#y=devicePixelRatio;#E=0;#x=0;#C=0;#S=0;#L=0;#k=!1;#P=!1;#D=0;#I=0;#R=0;#M=150;#H=150;#O=1500;#B=this.#O-this.#H;#A=0;#G=0;#F;constructor(e){super(),this.timelineFlameChart=e,this.#e=this.createChild("canvas","fill"),this.#t=this.#e.getContext("2d"),this.#i=document.createElement("canvas"),this.#s=this.#i.getContext("2d");const t=Math.floor(Math.random()*E.length);this.#F=E[t],this.#r=this.createChild("div"),this.#r.classList.add("scorePanel"),this.#r.style.borderImage="linear-gradient("+this.#F.mediumDarker+","+this.#F.dark+") 1",this.initButton()}initButton(){const e=this.createChild("div");e.classList.add("game-close-button"),e.innerHTML="<b><span style='font-size: 1.2em; color: white'>x</span></b>",e.style.background=this.#F.dark,e.style.boxShadow=this.#F.dark+" 1px 1px, "+this.#F.mediumDarker+" 3px 3px, "+this.#F.mediumLighter+" 5px 5px",e.addEventListener("click",this.#p.bind(this)),this.appendChild(e)}connectedCallback(){this.#T=!0,this.#z(),this.#w.addEventListener("keydown",this.#l),document.addEventListener("keydown",this.#l,!1),document.addEventListener("keyup",this.#d,!1),document.addEventListener("keypress",this.#u,!1),window.addEventListener("resize",this.#g),document.addEventListener("mousemove",this.#f,!1),this.tabIndex=1,this.focus()}disconnectedCallback(){this.#w.removeEventListener("keydown",this.#l),window.removeEventListener("resize",this.#g),document.removeEventListener("keydown",this.#l,!1),document.removeEventListener("keyup",this.#d,!1),window.removeEventListener("resize",this.#g),document.removeEventListener("keypress",this.#u,!1),document.removeEventListener("mousemove",this.#f,!1)}#W(){const e=window.devicePixelRatio,t=Math.round(this.offsetHeight*e),i=Math.round(this.offsetWidth*e);this.#e.height=t,this.#e.width=i,this.#e.style.height=t/e+"px",this.#e.style.width=i/e+"px"}#p(){this.#T=!1,this.remove()}#z(){this.#W(),this.#A=Math.max(.1,(this.offsetHeight-this.#H)/this.#B),this.#G=10*this.#A;const e=this.timelineFlameChart.drawTrackOnCanvas("Main",this.#t,y);if(null===e||0===e.visibleEntries.size)return console.error("Could not draw game"),void this.#p();this.#n=e.top,this.#o=e.visibleEntries,this.#b=this.#n+this.timelineFlameChart.getCanvas().getBoundingClientRect().top-this.timelineFlameChart.getScrollOffset(),requestAnimationFrame((()=>this.#N(e.top,e.height)))}#N(e,t){if(0===e)return void this.#U();const i=window.devicePixelRatio,s=Math.round(e*i),r=Math.max(e-4,0),n=Math.round(r*i),o=this.#e;this.#i.height=o.height,this.#i.width=o.width,this.#i.style.height=o.style.height,this.#i.style.width=o.style.width,this.#s.drawImage(o,0,s,o.width,t*i,0,n,o.width,t*i),this.#W(),this.#t.drawImage(this.#i,0,0),requestAnimationFrame((()=>this.#N(r,t)))}#c(e){"Right"===e.key||"ArrowRight"===e.key||"d"===e.key?(this.#k=!1,e.preventDefault()):"Left"===e.key||"ArrowLeft"===e.key||"a"===e.key?(this.#P=!1,e.preventDefault()):e.stopImmediatePropagation()}#m(e){e.stopImmediatePropagation(),e.preventDefault()}#h(e){"Escape"===e.key?(this.#p(),e.stopImmediatePropagation()):"Right"===e.key||"ArrowRight"===e.key||"d"===e.key?(this.#k=!0,e.preventDefault()):"Left"===e.key||"ArrowLeft"===e.key||"a"===e.key?(this.#P=!0,e.preventDefault()):(e.preventDefault(),e.stopImmediatePropagation())}#v(e){this.#L=Math.max(e.offsetX-this.#M/2,0),this.#L=Math.min(this.#L,this.offsetWidth-this.#M)}#U(){this.#E=this.offsetWidth/2,this.#x=this.offsetHeight-T-y,this.#C=0,this.#S=-Math.SQRT2*this.#G,this.#L=(this.#e.width-this.#M)/2,this.#k=!1,this.#P=!1,this.#D=this.timelineFlameChart.getBarHeight(),this.#R=this.#o.size,this.#I=Math.max(Math.round(this.#R/17),2),this.#_()}#V(){this.#E=this.offsetWidth/2,this.#x=this.offsetHeight-T-y,this.#C=0,this.#S=-Math.SQRT2*this.#G}#X(){if(!this.#t)return;const e=this.#t.createRadialGradient(this.#E+2.5,this.#x-2.5,0,this.#E+2.5,this.#x-2.5,y);e.addColorStop(.3,this.#F.mediumLighter),e.addColorStop(.6,this.#F.mediumDarker),e.addColorStop(1,this.#F.dark),this.#t.beginPath(),this.#t.arc(this.#E,this.#x,y,0,2*Math.PI),this.#t.fillStyle=e,this.#t.fill(),this.#t.closePath()}#$(){if(!this.#t)return;const e=this.#t.createRadialGradient(this.#L+this.#M/3,this.offsetHeight-T-3.75,0,this.#L+this.#M/3,this.offsetHeight-T-3.75,this.#M/2);e.addColorStop(.3,this.#F.dark),e.addColorStop(1,this.#F.mediumDarker),this.#t.beginPath(),this.#t.rect(this.#L,this.offsetHeight-T,this.#M,T),this.#t.fillStyle=e,this.#t.fill(),this.#t.closePath()}#K(){if(this.#t)for(const e of this.#a.values())this.#t.beginPath(),this.#t.rect(e.x,e.y,e.width+.5,this.#D+.5),this.#t.fillStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-neutral-container",this),this.#t.fill(),this.#t.closePath()}#_(){if(this.#y!==devicePixelRatio&&(this.#T=!1),0===this.#I)return window.alert("GAME OVER"),void this.#p();if(0===this.#R)return void this.#j();this.#t.clearRect(0,0,this.#e.width,this.#e.height),this.#t.drawImage(this.#i,0,0),this.#t.save(),this.#t.scale(devicePixelRatio,devicePixelRatio),this.#s.save(),this.#s.scale(devicePixelRatio,devicePixelRatio),this.#K(),this.#X(),this.#$(),this.#Y();const e=`<div><b><span style='font-size: 1.3em; color:  ${this.#F.dark}'>&#x2764;&#xfe0f; ${this.#I}</span></b></div>`,t=`<div><b><span style='font-size: 1.3em; color: ${this.#F.dark}'> 🧱 ${this.#R}</span></b></div>`;if(this.#r.innerHTML=e+t,this.#R=this.#o.size-this.#a.size,this.#G=(10+6*this.#a.size/this.#o.size)*this.#A,this.#M=150-65*this.#a.size/this.#o.size,(this.#E+this.#C>this.offsetWidth-y||this.#E+this.#C<y)&&(this.#C=-this.#C),this.#x+this.#S<y)this.#S=-this.#S;else if(this.#x+this.#S>this.offsetHeight-y&&this.#S>0)if(this.#E>this.#L-y&&this.#E<this.#L+this.#M+y){let e=Math.min(this.#E,this.#L+this.#M);e=Math.max(e,this.#L);const t=(e-this.#L)*this.#G*2/this.#M;this.#C=-this.#G+t,this.#S=-Math.sqrt(2*Math.pow(this.#G,2)-Math.pow(this.#C,2))}else this.#V(),this.#L=(this.offsetWidth-this.#M)/2,this.#I--;const i=Math.round(this.clientWidth/60);this.#k&&this.#L<this.offsetWidth-this.#M?this.#L+=i:this.#P&&this.#L>0&&(this.#L-=i),this.#E+=Math.round(this.#C),this.#x+=Math.round(this.#S),this.#t.restore(),this.#s.restore(),this.#T&&requestAnimationFrame(this.#_.bind(this))}#Y(){const e=this.timelineFlameChart.getCanvas().getBoundingClientRect(),t=this.#x+this.#b-e.top,i=this.timelineFlameChart.coordinatesToEntryIndex(this.#E,t+y),s=this.timelineFlameChart.coordinatesToEntryIndex(this.#E,t-y),r=this.timelineFlameChart.coordinatesToEntryIndex(this.#E+y,t),n=this.timelineFlameChart.coordinatesToEntryIndex(this.#E-y,t),o=y/Math.SQRT2,a=this.timelineFlameChart.coordinatesToEntryIndex(this.#E+o,t+o),l=this.timelineFlameChart.coordinatesToEntryIndex(this.#E-o,t+o),h=this.timelineFlameChart.coordinatesToEntryIndex(this.#E+o,t-o),d=this.timelineFlameChart.coordinatesToEntryIndex(this.#E-o,t-o),c=t=>{const i=this.timelineFlameChart.entryIndexToCoordinates(t);if(i){const s=Math.max(i.x-e.left,0);this.#a.set(t,{x:s-.5,y:i.y-this.#b-.5,width:this.timelineFlameChart.entryWidth(t)})}};if(i>-1&&!this.#a.has(i)&&this.#o.has(i))return this.#S=-this.#S,void c(i);if(s>-1&&!this.#a.has(s)&&this.#o.has(s))return this.#S=-this.#S,void c(s);if(r>-1&&!this.#a.has(r)&&this.#o.has(r))return this.#C=-this.#C,void c(r);if(n>-1&&!this.#a.has(n)&&this.#o.has(n))return this.#C=-this.#C,void c(n);const u=[a,l,h,d];for(const e of u)if(e>-1&&!this.#a.has(e)&&this.#o.has(e))return this.#C=-this.#C,this.#S=-this.#S,void c(e)}#Z(e,t){return Math.floor(Math.random()*(t-e)+e)}#j(){this.#W();let e=0;const t=this.offsetWidth/15,i=.7*this.offsetHeight/15,s=[],r=()=>40*Math.random()-20,n=()=>{for(let e=0;e<75;e++){const n=document.createElement("span");n.className="confetti-100",n.append(this.#q(e%15*t+r(),e%5*i+r())),s.push(window.setTimeout((()=>this.append(n)),100*Math.random())),s.push(window.setTimeout((()=>{n.remove()}),1e3))}++e<6?setTimeout(n,100*Math.random()+400):(window.alert(`${b(v.congrats)}\n${b(v.ps)}`),s.forEach((e=>clearTimeout(e))),this.#p())};n()}#q(e,t){const i=400,s=["💯","🎉","🎊"],r=document.createElement("span");return r.textContent=s[this.#Z(0,s.length)],r.className="confetti-100-particle",r.style.setProperty("--rotation",this.#Z(-1080,1080)+"deg"),r.style.setProperty("--to-X",this.#Z(-400,i)+"px"),r.style.setProperty("--to-Y",this.#Z(-400,i)+"px"),r.style.left=e+"px",r.style.top=t+"px",r}}customElements.define("brick-breaker",x);var C=Object.freeze({__proto__:null,BrickBreaker:x}),S={cssContent:".chart-viewport-v-scroll{position:absolute;top:0;right:0;bottom:0;overflow-x:hidden;z-index:200;padding-left:1px}.chart-viewport-v-scroll.always-show-scrollbar{overflow-y:scroll}:host-context(.platform-mac) .chart-viewport-v-scroll{right:2px;top:3px;bottom:3px}:host-context(.platform-mac) ::-webkit-scrollbar{width:8px}:host-context(.platform-mac) ::-webkit-scrollbar-thumb{background-color:var(--color-scrollbar-mac);border-radius:50px}:host-context(.platform-mac) .chart-viewport-v-scroll:hover::-webkit-scrollbar-thumb{background-color:var(--color-scrollbar-mac-hover)}:host-context(.overlay-scrollbar-enabled) ::-webkit-scrollbar{width:10px}:host-context(.overlay-scrollbar-enabled) ::-webkit-scrollbar-thumb{background-color:var(--color-scrollbar-other)}:host-context(.overlay-scrollbar-enabled) .chart-viewport-v-scroll:hover::-webkit-scrollbar-thumb{background-color:var(--color-scrollbar-other-hover)}.chart-viewport-selection-overlay{position:absolute;z-index:100;background-color:var(--sys-color-state-ripple-primary);border-color:var(--sys-color-primary);border-width:0 1px;border-style:solid;pointer-events:none;top:0;bottom:0;text-align:center}.chart-viewport-selection-overlay .time-span{white-space:nowrap;position:absolute;left:0;right:0;bottom:0}\n/*# sourceURL=chartViewport.css */\n"};let L=null;function k(){if(L)return L;const e=getComputedStyle(document.body);return L=e.fontFamily?e.fontFamily:l.Platform.fontFamily(),L}const P="11px";var D=Object.freeze({__proto__:null,getFontFamilyForCanvas:k,DEFAULT_FONT_SIZE:P});const I=8,R=2,M=4,H=12,O="8px";function B(e,t,i){e.moveTo(0,i),e.lineTo(t,i)}function A(e,t,s,r,n,o="--sys-color-on-surface"){const a=new Path2D(n);e.save(),e.translate(t,s),e.fillStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"),e.fillRect(0,0,r,r),e.fillStyle=i.ThemeSupport.instance().getComputedValue(o);const l=r/20;e.scale(l,l),e.fill(a),e.restore()}function G(e,t,s,r,n){e.save(),e.beginPath(),e.fillStyle=r,function(e,t,i,s,r,n){e.save();const o=i+r,a=t+s,l=.25*n;e.beginPath(),e.moveTo(t+n,i),e.lineTo(a-n,i),e.bezierCurveTo(a-l,i,a,i+l,a,i+n),e.lineTo(a,o-n),e.bezierCurveTo(a,o-l,a-l,o,a-n,o),e.lineTo(t+n,o),e.bezierCurveTo(t+l,o,t,o-l,t,o-n),e.lineTo(t,i+n),e.bezierCurveTo(t,i+l,t+l,i,t+n,i),e.closePath(),e.restore()}(e,t,s,I,I,R),e.fill(),e.textBaseline="top",e.fillStyle=i.ThemeSupport.instance().getComputedValue("--color-text-primary"),e.font=`${O} ${k()}`,e.fillText(n,t+I+M,s);const o=e.measureText(n).width,a=t+I+M+o;return e.restore(),a+H}var F={cssContent:'.flame-chart-main-pane{overflow:hidden;--selected-group-background:hsl(215deg 85% 98%);--selected-group-border:hsl(216deg 68% 54%)}:host-context(.theme-with-dark-background) .flame-chart-main-pane{--selected-group-background:hsl(215deg 85% 15%);--selected-group-border:hsl(216deg 68% 46%)}.flame-chart-marker-highlight-element{position:absolute;top:1px;height:18px;width:6px;margin:0 -3px;content:"";display:block}.flame-chart-canvas:focus-visible{border-top:1px solid var(--sys-color-state-focus-ring);border-bottom:1px solid var(--sys-color-state-focus-ring)}.flame-chart-highlight-element{position:absolute;pointer-events:none;background-color:var(--sys-color-state-hover-on-subtle)}.reveal-descendants-arrow-highlight-element{position:absolute;pointer-events:none;background-color:var(--sys-color-state-hover-on-subtle)}.flame-chart-selected-element{position:absolute;pointer-events:none;outline:2px solid var(--sys-color-primary);background-color:var(--sys-color-state-ripple-primary)}.flame-chart-search-element{position:absolute;pointer-events:none;outline:1px solid var(--sys-color-on-surface-subtle);background-color:var(--sys-color-state-ripple-neutral-on-subtle)}.chart-cursor-element{position:absolute;top:0;bottom:0;z-index:100;width:2px;background-color:var(--sys-color-primary);pointer-events:none;&.using-new-overlays{background-color:var(--ref-palette-pink50)}}.flame-chart-entry-info:not(:empty){z-index:2000;position:absolute;contain:content;background-color:var(--sys-color-cdt-base-container);pointer-events:none;padding:4px 8px;white-space:nowrap;max-width:80%;box-shadow:var(--drop-shadow)}.flame-chart-entry-info table tr td:empty{padding:0}.flame-chart-entry-info table tr td:not(:empty){padding:0 5px;white-space:nowrap}.flame-chart-entry-info table tr td:first-child{font-weight:bold}.flame-chart-entry-info table tr td span{margin-right:5px}.flame-chart-edit-confirm{position:fixed;bottom:10px;right:10px}\n/*# sourceURL=flameChart.css */\n'},z={cssContent:".resources-dividers{position:absolute;left:0;right:0;top:0;z-index:-100;bottom:0}.resources-event-dividers{position:absolute;left:0;right:0;height:100%;top:0;z-index:300;pointer-events:none}.resources-dividers-label-bar{position:absolute;top:0;left:0;right:0;background-clip:padding-box;height:20px;z-index:200;pointer-events:none;overflow:hidden}.resources-divider{position:absolute;width:1px;top:0;bottom:0;background-color:var(--sys-color-divider)}.resources-event-divider{position:absolute;width:1px;top:0;bottom:0;z-index:300}.resources-divider-label{position:absolute;top:4px;right:3px;font-size:80%;white-space:nowrap;pointer-events:none}.timeline-grid-header{height:20px;pointer-events:none}\n/*# sourceURL=timelineGrid.css */\n"};const W=new Map;class N{element;dividersElementInternal;gridHeaderElement;eventDividersElement;dividersLabelBarElementInternal;constructor(){this.element=document.createElement("div"),i.ThemeSupport.instance().appendStyle(this.element,z),this.dividersElementInternal=this.element.createChild("div","resources-dividers"),this.gridHeaderElement=document.createElement("div"),this.gridHeaderElement.classList.add("timeline-grid-header"),this.eventDividersElement=this.gridHeaderElement.createChild("div","resources-event-dividers"),this.dividersLabelBarElementInternal=this.gridHeaderElement.createChild("div","resources-dividers-label-bar"),this.element.appendChild(this.gridHeaderElement)}static calculateGridOffsets(e,t){const i=e.computePosition(e.maximumBoundary());let s=i/64,r=e.boundarySpan()/s;const n=i/e.boundarySpan(),o=Math.ceil(Math.log(r)/Math.LN10);r=Math.pow(10,o),r*n>=320&&(r/=5),r*n>=128&&(r/=2);const a=Math.ceil((e.minimumBoundary()-e.zeroTime())/r)*r+e.zeroTime();let l=e.maximumBoundary();l+=64/n,s=Math.ceil((l-a)/r),r||(s=0);const h=[];for(let i=0;i<s;++i){const s=a+100*r*i/100,n=e.computePosition(s);n<(t||0)||h.push({position:Math.floor(n),time:s})}return{offsets:h,precision:Math.max(0,-Math.floor(Math.log(1.01*r)/Math.LN10))}}static drawCanvasGrid(e,t){e.save(),e.scale(window.devicePixelRatio,window.devicePixelRatio);const i=Math.floor(e.canvas.height/window.devicePixelRatio);e.strokeStyle=getComputedStyle(document.body).getPropertyValue("--app-color-strokestyle"),e.lineWidth=1,e.translate(.5,.5),e.beginPath();for(const s of t.offsets)e.moveTo(s.position,0),e.lineTo(s.position,i);e.stroke(),e.restore()}static drawCanvasHeaders(e,t,s,r,n,o){e.save(),e.scale(window.devicePixelRatio,window.devicePixelRatio);const a=Math.ceil(e.canvas.width/window.devicePixelRatio);e.beginPath(),e.fillStyle=i.ThemeSupport.instance().getComputedValue("--color-background-opacity-50"),e.fillRect(0,0,a,n),e.fillStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),e.textBaseline="hanging",e.font=`${P} ${k()}`;for(const i of t.offsets){const t=s(i.time),n=e.measureText(t).width,a=i.position-n-4;(!o||o<a)&&e.fillText(t,a,r)}e.restore()}get dividersElement(){return this.dividersElementInternal}get dividersLabelBarElement(){return this.dividersLabelBarElementInternal}removeDividers(){this.dividersElementInternal.removeChildren(),this.dividersLabelBarElementInternal.removeChildren()}updateDividers(e,t){const i=N.calculateGridOffsets(e,t),s=i.offsets,r=i.precision,n=this.dividersElementInternal.clientWidth;let o=this.dividersElementInternal.firstChild,a=this.dividersLabelBarElementInternal.firstChild;for(let t=0;t<s.length;++t){if(!o){o=document.createElement("div"),o.className="resources-divider",this.dividersElementInternal.appendChild(o),a=document.createElement("div"),a.className="resources-divider";const e=document.createElement("div");e.className="resources-divider-label",W.set(a,e),a.appendChild(e),this.dividersLabelBarElementInternal.appendChild(a)}const i=s[t].time,l=s[t].position;if(a){const t=W.get(a);t&&(t.textContent=e.formatValue(i,r))}const h=100*l/n;o.style.left=h+"%",a&&(a.style.left=h+"%"),o=o.nextSibling,a&&(a=a.nextSibling)}for(;o;){const e=o.nextSibling;if(this.dividersElementInternal.removeChild(o),!e)break;o=e}for(;a;){const e=a.nextSibling;if(this.dividersLabelBarElementInternal.removeChild(a),!e)break;a=e}return!0}addEventDivider(e){this.eventDividersElement.appendChild(e)}addEventDividers(e){this.gridHeaderElement.removeChild(this.eventDividersElement);for(const t of e)this.eventDividersElement.appendChild(t);this.gridHeaderElement.appendChild(this.eventDividersElement)}removeEventDividers(){this.eventDividersElement.removeChildren()}hideEventDividers(){this.eventDividersElement.classList.add("hidden")}showEventDividers(){this.eventDividersElement.classList.remove("hidden")}hideDividers(){this.dividersElementInternal.classList.add("hidden")}showDividers(){this.dividersElementInternal.classList.remove("hidden")}setScrollTop(e){this.dividersLabelBarElementInternal.style.top=e+"px",this.eventDividersElement.style.top=e+"px"}}var U=Object.freeze({__proto__:null,TimelineGrid:N});const _={flameChart:"Flame Chart",sHovered:"{PH1} hovered",sSelected:"{PH1} selected",sExpanded:"{PH1} expanded",sCollapsed:"{PH1} collapsed",hideFunction:"Hide function",hideChildren:"Hide children",hideRepeatingChildren:"Hide repeating children",removeScriptFromIgnoreList:"Remove script from ignore list",addScriptToIgnoreList:"Add script to ignore list",resetChildren:"Reset children",resetTrace:"Reset trace",enterTrackConfigurationMode:"Configure tracks…",exitTrackConfigurationMode:"Finish configuring tracks"},V=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/FlameChart.ts",_),X=e.i18n.getLocalizedString.bind(void 0,V),$=8,K=6+$/2,j=16;class Y extends(s.ObjectWrapper.eventMixin(a.Widget.VBox)){groupExpansionSetting;groupExpansionState;groupHiddenState;flameChartDelegate;chartViewport;dataProvider;candyStripePattern;contextMenu;viewportElement;canvas;popoverElement;markerHighlighElement;highlightElement;revealDescendantsArrowHighlightElement;selectedElement=null;rulerEnabled;barHeight;hitMarginPx;textBaseline;textPadding;highlightedMarkerIndex;highlightedEntryIndex;selectedEntryIndex;rawTimelineDataLength;markerPositions;lastMouseOffsetX;selectedGroupIndex;keyboardFocusedGroup;offsetWidth;offsetHeight;dragStartX;dragStartY;lastMouseOffsetY;minimumBoundaryInternal;maxDragOffset;timelineLevels;visibleLevelOffsets;visibleLevels;visibleLevelHeights;groupOffsets;rawTimelineData;forceDecorationCache;entryColorsCache;totalTime;lastPopoverState;#Q=0;#J;#ee;#te;#ie=[];#se=!1;#re=null;#ne=!0;constructor(e,t,s={}){super(!0),this.#J=`${P} ${k()}`,this.registerRequiredCSS(F),this.contentElement.classList.add("flame-chart-main-pane"),"boolean"==typeof s.selectedElementOutline&&(this.#ne=s.selectedElementOutline),this.groupExpansionSetting=s.groupExpansionSetting,this.groupExpansionState=s.groupExpansionSetting?.get()||{},this.groupHiddenState={},this.flameChartDelegate=t;let r=!0;"boolean"==typeof s.useOverlaysForCursorRuler&&(r=!s.useOverlaysForCursorRuler),this.chartViewport=new ie(this,{enableCursorElement:r}),this.chartViewport.show(this.contentElement),this.dataProvider=e,this.viewportElement=this.chartViewport.viewportElement,this.canvas=this.viewportElement.createChild("canvas","fill"),this.candyStripePattern=null,this.canvas.tabIndex=0,a.ARIAUtils.setLabel(this.canvas,X(_.flameChart)),a.ARIAUtils.markAsTree(this.canvas),this.setDefaultFocusedElement(this.canvas),this.canvas.classList.add("flame-chart-canvas"),this.canvas.addEventListener("mousemove",this.onMouseMove.bind(this),!1),this.canvas.addEventListener("mouseout",this.onMouseOut.bind(this),!1),this.canvas.addEventListener("click",this.onClick.bind(this),!1),this.canvas.addEventListener("keydown",this.onKeyDown.bind(this),!1),this.canvas.addEventListener("contextmenu",this.onContextMenu.bind(this),!1),this.popoverElement=s.tooltipElement||this.viewportElement.createChild("div","flame-chart-entry-info"),this.markerHighlighElement=this.viewportElement.createChild("div","flame-chart-marker-highlight-element"),this.highlightElement=this.viewportElement.createChild("div","flame-chart-highlight-element"),this.revealDescendantsArrowHighlightElement=this.viewportElement.createChild("div","reveal-descendants-arrow-highlight-element"),this.#ne&&(this.selectedElement=this.viewportElement.createChild("div","flame-chart-selected-element")),this.canvas.addEventListener("focus",(()=>{this.dispatchEventToListeners("CanvasFocused")}),!1),a.UIUtils.installDragHandle(this.viewportElement,this.startDragging.bind(this),this.dragging.bind(this),this.endDragging.bind(this),null),this.rulerEnabled=!0,this.barHeight=17,this.hitMarginPx=3,this.textBaseline=5,this.textPadding=5,this.chartViewport.setWindowTimes(e.minimumBoundary(),e.minimumBoundary()+e.totalTime()),this.highlightedMarkerIndex=-1,this.highlightedEntryIndex=-1,this.selectedEntryIndex=-1,this.#te=-1,this.rawTimelineDataLength=0,this.markerPositions=new Map,this.lastMouseOffsetX=0,this.selectedGroupIndex=-1,this.lastPopoverState={entryIndex:-1,groupIndex:-1,hiddenEntriesPopover:!1},this.keyboardFocusedGroup=-1,i.ThemeSupport.instance().addEventListener(i.ThemeChangeEvent.eventName,(()=>{this.scheduleUpdate()}))}willHide(){this.hideHighlight()}canvasBoundingClientRect(){return this.#re||(this.#re=this.canvas.getBoundingClientRect()),this.#re}setTooltipYPixelAdjustment(e){e!==this.#Q&&(this.#Q=e,this.popoverElement.children.length&&this.updatePopoverOffset())}getBarHeight(){return this.barHeight}setBarHeight(e){this.barHeight=e}setTextBaseline(e){this.textBaseline=e}setTextPadding(e){this.textPadding=e}enableRuler(e){this.rulerEnabled=e}alwaysShowVerticalScroll(){this.chartViewport.alwaysShowVerticalScroll()}disableRangeSelection(){this.chartViewport.disableRangeSelection()}highlightEntry(e){this.highlightedEntryIndex!==e&&this.dataProvider.entryColor(e)&&(this.highlightedEntryIndex=e,this.updateElementPosition(this.highlightElement,this.highlightedEntryIndex),this.dispatchEventToListeners("EntryHighlighted",e))}highlightAllEntries(e){for(const t of e){const e=this.viewportElement.createChild("div","flame-chart-search-element");this.#ie.push(e),e.id=t.toString(),this.updateElementPosition(e,t)}}removeSearchResultHighlights(){for(const e of this.#ie)e.remove();this.#ie=[]}hideHighlight(){-1===this.#te&&(this.popoverElement.removeChildren(),this.lastPopoverState={entryIndex:-1,groupIndex:-1,hiddenEntriesPopover:!1}),-1!==this.highlightedEntryIndex&&(this.highlightedEntryIndex=-1,this.updateElementPosition(this.highlightElement,this.highlightedEntryIndex),this.dispatchEventToListeners("EntryHighlighted",-1))}createCandyStripePattern(){const e=17,t=document.createElement("canvas");t.width=e,t.height=e;const i=t.getContext("2d");i.translate(8.5,8.5),i.rotate(.25*Math.PI),i.translate(-8.5,-8.5),i.fillStyle="rgba(255, 0, 0, 0.8)";for(let e=-17;e<34;e+=3)i.fillRect(e,-17,1,51);return i.createPattern(t,"repeat")}resetCanvas(){const e=window.devicePixelRatio,t=Math.round(this.offsetWidth*e),i=Math.round(this.offsetHeight*e);this.canvas.width=t,this.canvas.height=i,this.canvas.style.width=t/e+"px",this.canvas.style.height=i/e+"px"}windowChanged(e,t,i){this.flameChartDelegate.windowChanged(e,t,i)}updateRangeSelection(e,t){this.flameChartDelegate.updateRangeSelection(e,t)}setSize(e,t){this.offsetWidth=e,this.offsetHeight=t}startDragging(e){return this.hideHighlight(),this.maxDragOffset=0,this.dragStartX=e.pageX,this.dragStartY=e.pageY,!0}dragging(e){const t=e.pageX-this.dragStartX,i=e.pageY-this.dragStartY;this.maxDragOffset=Math.max(this.maxDragOffset,Math.sqrt(t*t+i*i))}endDragging(e){this.updateHighlight()}timelineData(e){if(!this.dataProvider)return null;const t=this.dataProvider.timelineData(e);return(t!==this.rawTimelineData||t&&t.entryStartTimes.length!==this.rawTimelineDataLength)&&this.processTimelineData(t),this.rawTimelineData||null}revealEntry(e){const t=this.timelineData();if(!t)return;const i=this.chartViewport.windowLeftTime(),s=this.chartViewport.windowRightTime(),r=t.entryStartTimes[e],n=t.entryTotalTimes[e],o=r+n;let a=Math.min(n,s-i);const l=t.entryLevels[e];this.chartViewport.setScrollOffset(this.levelToOffset(l),this.levelHeight(l));const h=(s-i)/this.offsetWidth;if(a=Math.max(a,30*h),i>o){const e=i-o+a;this.windowChanged(i-e,s-e,!0)}else if(s<r){const e=r-s+a;this.windowChanged(i+e,s+e,!0)}}setWindowTimes(e,t,i){this.chartViewport.setWindowTimes(e,t,i),this.updateHighlight()}onMouseMove(e){this.#te=-1;const t=e;if(this.lastMouseOffsetX=t.offsetX,this.lastMouseOffsetY=t.offsetY,!this.enabled())return;if(this.chartViewport.isDragging())return;const i=d.Types.Timing.MilliSeconds(this.chartViewport.pixelToTime(t.offsetX));this.dispatchEventToListeners("MouseMove",{mouseEvent:t,timeInMicroSeconds:d.Helpers.Timing.millisecondsToMicroseconds(i)});const{groupIndex:s,hoverType:r}=this.coordinatesToGroupIndexAndHoverType(t.offsetX,t.offsetY);switch(r){case"TRACK_CONFIG_UP_BUTTON":case"TRACK_CONFIG_DOWN_BUTTON":case"TRACK_CONFIG_HIDE_BUTTON":case"TRACK_CONFIG_SHOW_BUTTON":{this.hideHighlight(),this.viewportElement.style.cursor="pointer";const e=this.#oe(s,r);return void(e&&(this.popoverElement.appendChild(e),this.updatePopoverOffset()))}case"INSIDE_TRACK_HEADER":return this.updateHighlight(),void(this.viewportElement.style.cursor="pointer");case"INSIDE_TRACK":return void this.updateHighlight()}}#oe(e,t){const i=this.rawTimelineData?.groups[e];if(!i)return null;const s=r.StringUtilities.trimMiddle(i.name,20);let n="";switch(t){case"TRACK_CONFIG_UP_BUTTON":n=`Move ${s} track up`;break;case"TRACK_CONFIG_DOWN_BUTTON":n=`Move ${s} track down`;break;case"TRACK_CONFIG_HIDE_BUTTON":n=this.groupIsLastVisibleTopLevel(i)?"Can not hide the last top level track":`Hide ${s} track`;break;case"TRACK_CONFIG_SHOW_BUTTON":n=`Show ${s} track`;break;default:return null}const o=document.createElement("div");return o.createChild("span","timeline-info-title").textContent=n,o}updateHighlight(){const e=this.coordinatesToEntryIndex(this.lastMouseOffsetX,this.lastMouseOffsetY);if(this.updateHiddenChildrenArrowHighlighPosition(e),-1===e){this.hideHighlight();const{groupIndex:e,hoverType:t}=this.coordinatesToGroupIndexAndHoverType(this.lastMouseOffsetX,this.lastMouseOffsetY);return"INSIDE_TRACK_HEADER"===t&&this.#ae(e),void(e>=0&&this.rawTimelineData&&this.rawTimelineData.groups&&this.rawTimelineData.groups[e].selectable?this.viewportElement.style.cursor="pointer":this.viewportElement.style.cursor="default")}this.chartViewport.isDragging()||(this.#le(e),this.viewportElement.style.cursor=this.dataProvider.canJumpToEntry(e)?"pointer":"default",this.highlightEntry(e))}onMouseOut(){this.lastMouseOffsetX=-1,this.lastMouseOffsetY=-1,this.hideHighlight()}showPopoverForSearchResult(e){this.#te=e,this.#le(e)}#le(e){const t=this.isMouseOverRevealChildrenArrow(this.lastMouseOffsetX,e);if(e===this.lastPopoverState.entryIndex&&t===this.lastPopoverState.hiddenEntriesPopover)return this.updatePopoverOffset();this.popoverElement.removeChildren();const i=this.timelineData();if(!i)return;const s=i.groups.at(this.selectedGroupIndex),r=t&&s?this.dataProvider.prepareHighlightedHiddenEntriesArrowInfo&&this.dataProvider.prepareHighlightedHiddenEntriesArrowInfo(e):this.dataProvider.prepareHighlightedEntryInfo(e);r&&(this.popoverElement.appendChild(r),this.updatePopoverOffset()),this.lastPopoverState={entryIndex:e,groupIndex:-1,hiddenEntriesPopover:t}}#ae(e){if(e===this.lastPopoverState.groupIndex)return this.updatePopoverOffset();this.popoverElement.removeChildren();const t=this.timelineData();if(!t)return;const i=t.groups.at(e);i?.description&&(this.popoverElement.innerText=i?.description,this.updatePopoverOffset()),this.lastPopoverState={groupIndex:e,entryIndex:-1,hiddenEntriesPopover:!1}}updatePopoverOffset(){let e=this.lastMouseOffsetX,t=this.lastMouseOffsetY;if(-1!==this.#te){const i=this.entryIndexToCoordinates(this.selectedEntryIndex),{x:s,y:r}=this.canvas.getBoundingClientRect();e=i?.x?i.x-s:e,t=i?.y?i.y-r:t}const i=this.popoverElement.parentElement?this.popoverElement.parentElement.clientWidth:0,s=this.popoverElement.parentElement?this.popoverElement.parentElement.clientHeight:0,n=this.popoverElement.clientWidth,o=this.popoverElement.clientHeight;let a,l;for(let h=0;h<4;++h){const d=2&h?-10-n:10,c=1&h?-6-o:6;if(a=r.NumberUtilities.clamp(e+d,0,i-n),l=r.NumberUtilities.clamp(t+c,0,s-o),a>=e||e>=a+n||l>=t||t>=l+o)break}this.popoverElement.style.left=a+"px",this.popoverElement.style.top=(l||0)+this.#Q+"px"}onClick(e){const t=e;this.focus();if(this.maxDragOffset>5)return;const{groupIndex:i,hoverType:s}=this.coordinatesToGroupIndexAndHoverType(t.offsetX,t.offsetY);if(i>=0)switch(s){case"TRACK_CONFIG_UP_BUTTON":return void this.moveGroupUp(i);case"TRACK_CONFIG_DOWN_BUTTON":return void this.moveGroupDown(i);case"TRACK_CONFIG_HIDE_BUTTON":if(this.groupIsLastVisibleTopLevel(this.rawTimelineData?.groups[i]))return;return void this.hideGroup(i);case"TRACK_CONFIG_SHOW_BUTTON":return void this.showGroup(i);case"INSIDE_TRACK_HEADER":return this.#he(i),void this.toggleGroupExpand(i);case"INSIDE_TRACK":{this.#he(i);const e=this.timelineData(),s=l.Platform.isMac()?t.metaKey:t.ctrlKey;if(t.shiftKey&&-1!==this.highlightedEntryIndex&&e){const t=e.entryStartTimes[this.highlightedEntryIndex],i=t+e.entryTotalTimes[this.highlightedEntryIndex];this.chartViewport.setRangeSelection(t,i)}else s&&-1!==this.highlightedEntryIndex&&e?this.dispatchEventToListeners("AnnotateEntry",this.highlightedEntryIndex):(this.chartViewport.onClick(t),this.dispatchEventToListeners("EntryInvoked",this.highlightedEntryIndex));return}}}#he(e){if(e<0||this.selectedGroupIndex===e)return;if(!this.rawTimelineData)return;const t=this.rawTimelineData.groups;if(!t)return;this.keyboardFocusedGroup=e,this.scrollGroupIntoView(e);const i=t[e].name;t[e].selectable?(this.selectedGroupIndex=e,this.flameChartDelegate.updateSelectedGroup(this,t[e]),this.draw(),a.ARIAUtils.alert(X(_.sSelected,{PH1:i}))):(this.deselectAllGroups(),a.ARIAUtils.alert(X(_.sHovered,{PH1:i})))}deselectAllGroups(){this.selectedGroupIndex=-1,this.flameChartDelegate.updateSelectedGroup(this,null),this.draw()}deselectAllEntries(){this.selectedEntryIndex=-1,this.rawTimelineData?.resetFlowData(),this.draw()}isGroupFocused(e){return e===this.selectedGroupIndex||e===this.keyboardFocusedGroup}scrollGroupIntoView(e){if(e<0)return;if(!this.rawTimelineData)return;const t=this.rawTimelineData.groups,i=this.groupOffsets;if(!i||!t)return;const s=i[e];let r=i[e+1];e===t.length-1&&(r+=t[e].style.padding);const n=0===e?0:s,o=Math.min(r-n,this.chartViewport.chartHeight());this.chartViewport.setScrollOffset(n,o)}toggleGroupExpand(e){e<0||!this.isGroupCollapsible(e)||this.rawTimelineData&&this.rawTimelineData.groups&&this.expandGroup(e,!this.rawTimelineData.groups[e].expanded)}expandGroup(e,t=!0,i=!1){if(e<0||!this.isGroupCollapsible(e))return;if(!this.rawTimelineData)return;const s=this.rawTimelineData.groups;if(!s)return;const r=s[e];if(r.expanded=t,this.groupExpansionState[r.name]=r.expanded,this.groupExpansionSetting&&this.groupExpansionSetting.set(this.groupExpansionState),this.updateLevelPositions(),this.updateHighlight(),!r.expanded){const t=this.timelineData();if(t){const i=t.entryLevels[this.selectedEntryIndex];this.selectedEntryIndex>=0&&i>=r.startLevel&&(e>=s.length-1||s[e+1].startLevel>i)&&(this.selectedEntryIndex=-1,this.rawTimelineData.resetFlowData())}}if(this.updateHeight(),this.draw(),this.scrollGroupIntoView(e),!i){const t=s[e].name,i=r.expanded?X(_.sExpanded,{PH1:t}):X(_.sCollapsed,{PH1:t});a.ARIAUtils.alert(i)}}moveGroupUp(e){if(!(e<0)&&this.rawTimelineData&&this.rawTimelineData.groups&&this.#ee){for(let t=0;t<this.#ee.children.length;t++){const i=this.#ee.children[t];if(i.index===e&&t>=1){this.#ee.children[t]=this.#ee.children[t-1],this.#ee.children[t-1]=i;break}}this.updateLevelPositions(),this.updateHighlight(),this.updateHeight(),this.draw()}}moveGroupDown(e){if(!(e<0)&&this.rawTimelineData&&this.rawTimelineData.groups&&this.#ee){for(let t=0;t<this.#ee.children.length;t++){const i=this.#ee.children[t];if(i.index===e&&t<=this.#ee.children.length-2){this.#ee.children[t]=this.#ee.children[t+1],this.#ee.children[t+1]=i;break}}this.updateLevelPositions(),this.updateHighlight(),this.updateHeight(),this.draw()}}hideGroup(e){this.#de(e,!0)}showGroup(e){this.#de(e,!1)}#de(e,t){if(e<0)return;if(!this.rawTimelineData||!this.rawTimelineData.groups)return;const i=this.rawTimelineData.groups;if(!i)return;const s=i[e];s.hidden=t,this.groupHiddenState[s.name]=s.hidden,this.updateLevelPositions(),this.updateHighlight(),this.updateHeight(),this.draw()}modifyTree(e,t){this.timelineData()&&(this.dataProvider.modifyTree?.(t,e),this.dataProvider.timelineData(!0),this.dataProvider.buildFlowForInitiator?.(t),this.update())}getPossibleActions(){const e=this.timelineData();if(!e)return;const t=e.groups.at(this.selectedGroupIndex);return t&&t.expanded&&t.showStackContextMenu?this.dataProvider.findPossibleContextMenuActions?.(this.selectedEntryIndex):void 0}#ce(e){if(this.#se)return;this.contextMenu=new a.ContextMenu.ContextMenu(e,{useSoftMenu:!0});const t=X(_.enterTrackConfigurationMode);this.contextMenu.defaultSection().appendItem(t,(()=>{this.#ue()}),{jslogContext:"track-configuration-enter"}),this.contextMenu.show()}#me(e){if(!1===this.#se)return;this.contextMenu=new a.ContextMenu.ContextMenu(e,{useSoftMenu:!0});const t=X(_.exitTrackConfigurationMode);this.contextMenu.defaultSection().appendItem(t,(()=>{this.#ge()}),{jslogContext:"track-configuration-exit"}),this.contextMenu.show()}#pe(){return Boolean(this.dataProvider.hasTrackConfigurationMode&&this.dataProvider.hasTrackConfigurationMode())}onContextMenu(e){const{groupIndex:t,hoverType:i}=this.coordinatesToGroupIndexAndHoverType(e.offsetX,e.offsetY);if(this.#se)return void this.#me(e);if("INSIDE_TRACK_HEADER"===i&&this.#pe()&&this.#ce(e),-1===this.highlightedEntryIndex)return;if(this.dispatchEventToListeners("EntryInvoked",this.highlightedEntryIndex),this.setSelectedEntry(this.highlightedEntryIndex),this.#he(t),this.dataProvider.customizedContextMenu&&(this.contextMenu=this.dataProvider.customizedContextMenu(e,this.highlightedEntryIndex),this.contextMenu))return void this.contextMenu.show();if(!this.dataProvider.modifyTree)return;const s=this.getPossibleActions();if(!s)return;this.contextMenu=new a.ContextMenu.ContextMenu(e,{useSoftMenu:!0});this.contextMenu.defaultSection().appendItem(X(_.hideFunction),(()=>{this.modifyTree("MERGE_FUNCTION",this.selectedEntryIndex)}),{disabled:!s?.MERGE_FUNCTION,jslogContext:"hide-function"}).setShortcut("H");this.contextMenu.defaultSection().appendItem(X(_.hideChildren),(()=>{this.modifyTree("COLLAPSE_FUNCTION",this.selectedEntryIndex)}),{disabled:!s?.COLLAPSE_FUNCTION,jslogContext:"hide-children"}).setShortcut("C");this.contextMenu.defaultSection().appendItem(X(_.hideRepeatingChildren),(()=>{this.modifyTree("COLLAPSE_REPEATING_DESCENDANTS",this.selectedEntryIndex)}),{disabled:!s?.COLLAPSE_REPEATING_DESCENDANTS,jslogContext:"hide-repeating-children"}).setShortcut("R");this.contextMenu.defaultSection().appendItem(X(_.resetChildren),(()=>{this.modifyTree("RESET_CHILDREN",this.selectedEntryIndex)}),{disabled:!s?.RESET_CHILDREN,jslogContext:"reset-children"}).setShortcut("U"),this.contextMenu.defaultSection().appendItem(X(_.resetTrace),(()=>{this.modifyTree("UNDO_ALL_ACTIONS",this.selectedEntryIndex)}),{disabled:!s?.UNDO_ALL_ACTIONS,jslogContext:"reset-trace"});const r=this.dataProvider.eventByIndex?.(this.selectedEntryIndex);if(r&&r instanceof d.Handlers.ModelHandlers.Frames.TimelineFrame==!1){const e=d.Types.TraceEvents.isProfileCall(r)?r.callFrame.url:void 0;e&&(h.IgnoreListManager.IgnoreListManager.instance().isUserIgnoreListedURL(e)?this.contextMenu.defaultSection().appendItem(X(_.removeScriptFromIgnoreList),(()=>{h.IgnoreListManager.IgnoreListManager.instance().unIgnoreListURL(e)}),{jslogContext:"remove-from-ignore-list"}):this.contextMenu.defaultSection().appendItem(X(_.addScriptToIgnoreList),(()=>{h.IgnoreListManager.IgnoreListManager.instance().ignoreListURL(e)}),{jslogContext:"add-to-ignore-list"}))}this.contextMenu.show()}handleFlameChartTransformEvent(e){if(-1===this.selectedEntryIndex)return;const t=this.getPossibleActions();if(!t)return;const i=e;let s=!1;"KeyH"===i.code&&t.MERGE_FUNCTION?(this.modifyTree("MERGE_FUNCTION",this.selectedEntryIndex),s=!0):"KeyC"===i.code&&t.COLLAPSE_FUNCTION?(this.modifyTree("COLLAPSE_FUNCTION",this.selectedEntryIndex),s=!0):"KeyR"===i.code&&t.COLLAPSE_REPEATING_DESCENDANTS?(this.modifyTree("COLLAPSE_REPEATING_DESCENDANTS",this.selectedEntryIndex),s=!0):"KeyU"===i.code&&(this.modifyTree("RESET_CHILDREN",this.selectedEntryIndex),s=!0),s&&i.consume(!0)}onKeyDown(e){if(!a.KeyboardShortcut.KeyboardShortcut.hasNoModifiers(e)||!this.timelineData())return;let t=this.handleSelectionNavigation(e);!t&&this.rawTimelineData&&this.rawTimelineData.groups&&(t=this.handleKeyboardGroupNavigation(e)),t||this.handleFlameChartTransformEvent(e)}bindCanvasEvent(e,t){this.canvas.addEventListener(e,t)}drawTrackOnCanvas(e,t,i){const s=this.timelineData();if(!s)return null;const r=this.offsetWidth,n=this.offsetHeight;t.save();const o=window.devicePixelRatio;t.scale(o,o),t.fillStyle="rgba(0, 0, 0, 0)",t.fillRect(0,0,r,n),t.font=this.#J;const a=this.rawTimelineData?.groups||[],l=this.groupOffsets;if(!a.length||!l)return null;const h=a.findIndex((t=>t.name.includes(e)));if(h<0)return null;this.scrollGroupIntoView(h);const d=a[h].startLevel,c=a[h+1].startLevel,u=l[h],m=l[h+1],{colorBuckets:g,titleIndices:p}=this.getDrawableData(t,s),f=e=>{const t=Math.min(this.#fe(s,e),r);return s.entryLevels[e]>=d&&s.entryLevels[e]<c&&t>i};let v=[];for(const[e,{indexes:i}]of g){const r=i.filter(f);v=[...v,...r],this.#ve(t,s,e,r)}const w=p.filter(f);return this.drawEventTitles(t,s,w,r),t.restore(),{top:l[h],height:m-u,visibleEntries:new Set(v)}}handleKeyboardGroupNavigation(e){const t=e;let i=!1,s=!1;return"ArrowUp"===t.code?i=this.selectPreviousGroup():"ArrowDown"===t.code?i=this.selectNextGroup():"ArrowLeft"===t.code?this.keyboardFocusedGroup>=0&&(this.expandGroup(this.keyboardFocusedGroup,!1),i=!0):"ArrowRight"===t.code?this.keyboardFocusedGroup>=0&&(this.expandGroup(this.keyboardFocusedGroup,!0),this.selectFirstChild(),i=!0):"Enter"===t.key&&(s=this.selectFirstEntryInCurrentGroup(),i=s),i&&!s&&this.deselectAllEntries(),i&&t.consume(!0),i}selectFirstEntryInCurrentGroup(){if(!this.rawTimelineData)return!1;const e=this.rawTimelineData.groups;if(this.keyboardFocusedGroup<0||!e)return!1;const t=e[this.keyboardFocusedGroup].startLevel;if(t<0)return!1;if(this.keyboardFocusedGroup<e.length-1&&e[this.keyboardFocusedGroup+1].startLevel===t)return!1;if(!this.timelineLevels)return!1;const i=this.timelineLevels[t][0];return this.expandGroup(this.keyboardFocusedGroup,!0),this.setSelectedEntry(i),!0}selectPreviousGroup(){if(this.keyboardFocusedGroup<=0)return!1;const e=this.getGroupIndexToSelect(-1);return this.#he(e),!0}selectNextGroup(){if(!this.rawTimelineData||!this.rawTimelineData.groups)return!1;if(this.keyboardFocusedGroup>=this.rawTimelineData.groups.length-1)return!1;const e=this.getGroupIndexToSelect(1);return this.#he(e),!0}getGroupIndexToSelect(e){if(!this.rawTimelineData||!this.rawTimelineData.groups)throw new Error("No raw timeline data");const t=this.rawTimelineData.groups;let i,s,r=this.keyboardFocusedGroup;do{r+=e,i=this.rawTimelineData.groups[r].name,s=-1!==this.keyboardFocusedGroup&&t[r].style.nestingLevel>t[this.keyboardFocusedGroup].style.nestingLevel}while(r>0&&r<t.length-1&&(!i||s));return r}selectFirstChild(){if(!this.rawTimelineData||!this.rawTimelineData.groups)return;const e=this.rawTimelineData.groups;if(this.keyboardFocusedGroup<0||this.keyboardFocusedGroup>=e.length-1)return;const t=this.keyboardFocusedGroup+1;e[t].style.nestingLevel>e[this.keyboardFocusedGroup].style.nestingLevel&&this.#he(t)}handleSelectionNavigation(e){if(-1===this.selectedEntryIndex)return!1;const t=this.timelineData();if(!t)return!1;function i(e,i){if(!t)throw new Error("No timeline data");const s=t.entryStartTimes[e],r=t.entryStartTimes[i],n=s+t.entryTotalTimes[e];return s<r+t.entryTotalTimes[i]&&r<n}const s=e,n=a.KeyboardShortcut.Keys;if(s.keyCode===n.Left.code||s.keyCode===n.Right.code){const i=t.entryLevels[this.selectedEntryIndex],o=this.timelineLevels?this.timelineLevels[i]:[];let a=r.ArrayUtilities.lowerBound(o,this.selectedEntryIndex,((e,t)=>e-t));return a+=s.keyCode===n.Left.code?-1:1,e.consume(!0),a>=0&&a<o.length&&this.dispatchEventToListeners("EntrySelected",o[a]),!0}if(s.keyCode===n.Up.code||s.keyCode===n.Down.code){let e=t.entryLevels[this.selectedEntryIndex];if(e+=s.keyCode===n.Up.code?-1:1,e<0||this.timelineLevels&&e>=this.timelineLevels.length)return this.deselectAllEntries(),s.consume(!0),!0;const o=t.entryStartTimes[this.selectedEntryIndex]+t.entryTotalTimes[this.selectedEntryIndex]/2,a=this.timelineLevels?this.timelineLevels[e]:[];let l=r.ArrayUtilities.upperBound(a,o,(function(e,i){if(!t)throw new Error("No timeline data");return e-t.entryStartTimes[i]}))-1;return!i(this.selectedEntryIndex,a[l])&&(++l,l>=a.length||!i(this.selectedEntryIndex,a[l]))?"ArrowDown"!==s.code&&(this.deselectAllEntries(),s.consume(!0),!0):(s.consume(!0),this.dispatchEventToListeners("EntrySelected",a[l]),!0)}return"Enter"===e.key&&(e.consume(!0),this.dispatchEventToListeners("EntryInvoked",this.selectedEntryIndex),!0)}coordinatesToEntryIndex(e,t){if(e<0||t<0)return-1;const i=this.timelineData();if(!i)return-1;if(t+=this.chartViewport.scrollOffset(),!this.visibleLevelOffsets||!this.visibleLevelHeights||!this.visibleLevels)throw new Error("No visible level offsets or heights");let s=-1;for(let e=0;e<this.dataProvider.maxStackDepth();e++)if(t>=this.visibleLevelOffsets[e]&&t<this.visibleLevelOffsets[e]+(this.visibleLevels[e]?this.visibleLevelHeights[e]:0)){s=e;break}if(s<0||!this.visibleLevels[s])return-1;if(t-this.visibleLevelOffsets[s]>this.levelHeight(s))return-1;for(const[t,r]of this.markerPositions)if(i.entryLevels[t]===s&&r.x<=e&&e<r.x+r.width)return t;const n=i.entryStartTimes,o=this.timelineLevels?this.timelineLevels[s]:[];if(!o||!o.length)return-1;const a=this.chartViewport.pixelToTime(e),l=Math.max(r.ArrayUtilities.upperBound(o,a,((e,t)=>e-n[t]))-1,0);function h(t){if(void 0===t)return!1;if(!i)return!1;const s=n[t],r=i.entryTotalTimes[t],o=this.chartViewport.timeToPosition(s),a=this.chartViewport.timeToPosition(s+r);return o-this.hitMarginPx<e&&e<a+this.hitMarginPx}let d=o[l];return h.call(this,d)?d:(d=o[l+1],h.call(this,d)?d:-1)}isMouseOverRevealChildrenArrow(e,t){if(!this.entryHasDecoration(t,"HIDDEN_DESCENDANTS_ARROW"))return!1;const i=this.timelineData();if(!i)return!1;const s=i.entryStartTimes[t],r=i.entryTotalTimes[t],n=this.chartViewport.timeToPosition(s+r);return n-this.#we(i,t)-this.hitMarginPx<e&&e<n+this.hitMarginPx}entryIndexToCoordinates(e){const t=this.timelineData(),{x:i,y:s}=this.canvas.getBoundingClientRect();if(!t||!this.visibleLevelOffsets)return null;return{x:this.chartViewport.timeToPosition(t.entryStartTimes[e])+i,y:this.visibleLevelOffsets[t.entryLevels[e]]-this.chartViewport.scrollOffset()+s}}entryTitle(e){return this.dataProvider.entryTitle(e)}getCanvasOffset(){return this.canvas.getBoundingClientRect()}getCanvas(){return this.canvas}getScrollOffset(){return this.chartViewport.scrollOffset()}getContextMenu(){return this.contextMenu}coordinatesToGroupIndexAndHoverType(e,t){if(!this.rawTimelineData||!this.rawTimelineData.groups||!this.groupOffsets)return{groupIndex:-1,hoverType:"ERROR"};if(e<0||t<0)return{groupIndex:-1,hoverType:"ERROR"};t+=this.chartViewport.scrollOffset();const i=this.rawTimelineData.groups||[];if(this.#ee){const s=[];function r(e){s.push(e.index);for(const t of e.children)r(t)}if(r(this.#ee),s.shift(),s.length!==i.length)return console.warn("The data from the group tree doesn't match the data from DataProvider."),{groupIndex:-1,hoverType:"ERROR"};s.push(i.length);for(let n=0;n<s.length;n++){const o=s[n],a=s[n+1]??s.length;if(t>=this.groupOffsets[o]&&t<this.groupOffsets[a]){const l=this.canvas.getContext("2d");l.save(),l.font=this.#J;const h=6+(this.#se?63:0)+this.labelWidthForGroup(l,i[o]);l.restore();const d=t>=this.groupOffsets[o]&&t<this.groupOffsets[o]+i[o].style.height;if(!this.#se)return d&&e<=h?{groupIndex:o,hoverType:"INSIDE_TRACK_HEADER"}:{groupIndex:o,hoverType:"INSIDE_TRACK"};if(d){if(6<=e&&e<22)return{groupIndex:o,hoverType:"TRACK_CONFIG_UP_BUTTON"};if(25<=e&&e<41)return{groupIndex:o,hoverType:"TRACK_CONFIG_DOWN_BUTTON"};if(44<=e&&e<60)return{groupIndex:o,hoverType:i[o].hidden?"TRACK_CONFIG_SHOW_BUTTON":"TRACK_CONFIG_HIDE_BUTTON"};if(d&&e<=h)return{groupIndex:o,hoverType:"INSIDE_TRACK_HEADER"}}}}}return{groupIndex:-1,hoverType:"OUTSIDE_TRACKS"}}#ue(){const e=document.createElement("div");e.classList.add("flame-chart-edit-confirm");const t=new c.Button.Button;t.data={variant:"primary",jslogContext:"track-configuration-exit"},t.innerText=X(_.exitTrackConfigurationMode),e.appendChild(t),t.addEventListener("click",(()=>{this.#ge()})),this.viewportElement.appendChild(e),this.#se=!0,this.updateLevelPositions(),this.draw()}#be(){const e=this.viewportElement.querySelector(".flame-chart-edit-confirm");e&&this.viewportElement.removeChild(e)}#ge(){this.#be(),this.#se=!1,this.updateLevelPositions(),this.draw()}markerIndexBeforeTime(e){const t=this.timelineData();if(!t)throw new Error("No timeline data");if(!t.markers)throw new Error("No timeline markers");return r.ArrayUtilities.lowerBound(t.markers,e,((e,t)=>e-t.startTime()))}draw(){const e=this.timelineData();if(!e)return;this.resetCanvas(),this.dispatchEventToListeners("LatestDrawDimensions",{chart:{widthPixels:this.offsetWidth,heightPixels:this.offsetHeight,scrollOffsetPixels:this.chartViewport.scrollOffset(),allGroupsCollapsed:this.rawTimelineData?.groups.every((e=>!e.expanded))??!0},traceWindow:d.Helpers.Timing.traceWindowFromMilliSeconds(this.minimumBoundary(),this.maximumBoundary())});const t=this.offsetWidth,s=this.offsetHeight,r=this.canvas.getContext("2d");r.save();const n=window.devicePixelRatio,o=this.chartViewport.scrollOffset();r.scale(n,n),r.fillStyle="rgba(0, 0, 0, 0)",r.fillRect(0,0,t,s),r.translate(0,-o),r.font=this.#J;const{markerIndices:a,colorBuckets:l,titleIndices:h}=this.getDrawableData(r,e);r.save(),this.forEachGroupInViewport(((e,s,n,o,a)=>{this.isGroupFocused(s)&&(r.fillStyle=i.ThemeSupport.instance().getComputedValue("--selected-group-background",this.contentElement),r.fillRect(0,e,t,a-n.style.padding))})),r.restore();const c=this.rawTimelineData?.groups||[],u=c.findIndex((e=>e.name.includes("Main"))),m=c.at(u),g=m?.startLevel,p=c.at(u+1)?.startLevel,f=i=>{if(u<0||void 0===g||void 0===p)return!1;const s=Math.min(this.#fe(e,i),t);return e.entryLevels[i]>=g&&e.entryLevels[i]<p&&s>10};let v=!1;for(const[t,{indexes:i}]of l)v||(v=i.some(f)),this.#ve(r,e,t,i);this.dispatchEventToListeners("ChartPlayableStateChange",v),this.drawMarkers(r,e,a),this.drawEventTitles(r,e,h,t);const w=Array.from(l.values()).map((e=>e.indexes)).flat();this.#Te(r,e,w),r.restore(),this.drawGroupHeaders(t,s),this.drawFlowEvents(r,e),this.drawMarkerLines();const b=N.calculateGridOffsets(this),T=this.dataProvider.mainFrameNavigationStartEvents?.()||[];let y=0;const E=e=>{if(0===T.length)return this.formatValue(e,b.precision);if(T.length>y+1){const t=T[y+1];e>d.Helpers.Timing.microSecondsToMilliseconds(t.ts)&&y++}const t=T[y];if(t){e-=d.Helpers.Timing.microSecondsToMilliseconds(t.ts)-this.zeroTime()}return this.formatValue(e,b.precision)};N.drawCanvasGrid(r,b),this.rulerEnabled&&N.drawCanvasHeaders(r,b,E,3,Z),this.updateElementPosition(this.highlightElement,this.highlightedEntryIndex),this.updateElementPosition(this.selectedElement,this.selectedEntryIndex),-1!==this.#te&&this.showPopoverForSearchResult(this.#te);for(const e of this.#ie)this.updateElementPosition(e,Number(e.id));this.updateMarkerHighlight()}#ve(e,t,i,s){e.save(),e.beginPath();for(let i=0;i<s.length;++i){const r=s[i];this.#ye(e,t,r)}e.fillStyle=i,e.fill(),e.restore()}#Te(e,t,i){const{entryTotalTimes:s,entryStartTimes:r,entryLevels:n}=t;e.save();for(let o=0;o<i.length;++o){const a=i[o],l=t.entryDecorations.at(a);if(!l||l.length<1)continue;l.length>1&&Q(l);const h=r[a],c=s[a],u=this.timeToPositionClipped(h),m=n[a],g=this.#we(t,a),p=this.levelToOffset(m);let f=this.#fe(t,a);for(const i of l)switch(i.type){case"CANDY":{const s=d.Helpers.Timing.microSecondsToMilliseconds(i.startAtTime);if(c<s)continue;this.candyStripePattern||(this.candyStripePattern=this.createCandyStripePattern()),e.save(),e.beginPath();const r=this.timeToPositionClipped(h+s),n=i.endAtTime?d.Helpers.Timing.microSecondsToMilliseconds(i.endAtTime):h+c,o=this.timeToPositionClipped(n);this.#ye(e,t,a,{startX:r,width:o-r}),e.fillStyle=this.candyStripePattern,e.fill(),e.restore();break}case"WARNING_TRIANGLE":{if(void 0!==i.customEndTime){const e=d.Helpers.Timing.microSecondsToMilliseconds(i.customEndTime);f=this.timeToPositionClipped(e)-u}const t=8;e.save(),e.beginPath(),e.rect(u,p,f,g),e.clip(),e.beginPath(),e.fillStyle="red",e.moveTo(u+f-t,p),e.lineTo(u+f,p),e.lineTo(u+f,p+t),e.fill(),e.restore();break}case"HIDDEN_DESCENDANTS_ARROW":e.save(),e.beginPath(),e.rect(u,p,f,g);if(f>2*g){const t=7,i=5,s=6;e.clip(),e.beginPath(),e.fillStyle="#474747";const r=u+f-t-i,n=p+s;e.moveTo(r,n);const o=u+f-i,a=p+s;e.lineTo(o,a);const l=u+f-i-t/2,h=p+g-s;e.lineTo(l,h)}else{const t=8;e.clip(),e.beginPath(),e.fillStyle="#474747",e.moveTo(u+f-t,p+g),e.lineTo(u+f,p+g),e.lineTo(u+f,p+t)}e.fill(),e.restore();break}}e.restore()}#ye(e,t,i,s){const{entryTotalTimes:r,entryStartTimes:n,entryLevels:o}=t,a=r[i];if(isNaN(a))return;const l=n[i],h=s?.startX??this.timeToPositionClipped(l),d=o[i],c=this.#we(t,i),u=this.levelToOffset(d),m=s?.width??this.#fe(t,i);0!==m&&e.rect(h,u,m-.5,c-1)}#we(e,t){const{entryLevels:i}=e,s=i[t];return this.levelHeight(s)}entryWidth(e){const t=this.timelineData();return t?this.#fe(t,e):0}#fe(e,t){const{entryTotalTimes:i,entryStartTimes:s}=e,r=i[t],n=s[t],o=this.timeToPositionClipped(n),a=this.timeToPositionClipped(n+r);return Math.max(a-o,1)}getDrawableData(e,t){const i=[],s=[],{entryTotalTimes:n,entryStartTimes:o}=t,l=this.chartViewport.scrollOffset(),h=2*this.textPadding+a.UIUtils.measureTextWidth(e,"…"),d=this.chartViewport.pixelToTimeOffset(h),c=new Map;for(let e=0;e<this.dataProvider.maxStackDepth();++e){if(this.levelToOffset(e)+this.levelHeight(e)<l||this.levelToOffset(e)>l+this.offsetHeight)continue;if(!this.visibleLevels||!this.visibleLevels[e])continue;if(!this.timelineLevels)break;const t=this.timelineLevels[e];let a=1/0;for(let l=r.ArrayUtilities.lowerBound(t,this.chartViewport.windowRightTime(),((e,t)=>e-o[t]))-1;l>=0;--l){const r=t[l],h=n[r];if(isNaN(h)){s.push(r);continue}(h>=d||this.forceDecorationCache?.[r])&&i.push(r);const u=o[r],m=u+h,g=Boolean(this.dataProvider.forceDrawableLevel?.(e));if(m<=this.chartViewport.windowLeftTime()&&!g)break;const p=this.timeToPositionClipped(u);if(!(p>=a)&&(a=p,this.entryColorsCache)){const e=this.entryColorsCache[r];let t=c.get(e);t||(t={indexes:[]},c.set(e,t)),t.indexes.push(r)}}}return{colorBuckets:c,titleIndices:i,markerIndices:s}}drawGroupHeaders(e,t){const r=this.canvas.getContext("2d"),n=this.chartViewport.scrollOffset(),o=window.devicePixelRatio;if(!this.rawTimelineData)return;const l=this.rawTimelineData.groups||[];if(!l.length)return;const h=this.groupOffsets;if(null==h)return;const d=h[h.length-1];r.save(),r.scale(o,o),r.translate(0,-n),r.font=this.#J,r.fillStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"),this.forEachGroupInViewport(((t,i,s)=>{const n=s.style.padding;n<5||r.fillRect(0,t-n+2,e,n-4)})),l.length&&d<n+t&&r.fillRect(0,d+2,e,n+t-d),r.strokeStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-neutral-container"),r.beginPath(),this.forEachGroupInViewport(((t,i,s,n)=>{n||s.style.padding<4||B(r,e,t-2.5)})),B(r,e,d+1.5),r.stroke(),this.forEachGroupInViewport(((t,i,s)=>{if(s.style.useFirstLineForOverview)return;if(!this.isGroupCollapsible(i)||s.expanded)return void(!s.style.shareHeaderLine&&this.isGroupFocused(i)&&(r.fillStyle=s.style.backgroundColor,r.fillRect(0,t,e,s.style.height)));let n=i+1;for(;n<l.length&&l[n].style.nestingLevel>s.style.nestingLevel;)n++;const o=n<l.length?l[n].startLevel:this.dataProvider.maxStackDepth();this.drawCollapsedOverviewForGroup(s,t,o)})),r.save();const c=l.length>1,u=this.#se?63:0;this.forEachGroupInViewport(((e,t,n)=>{if(r.font=this.#J,this.isGroupCollapsible(t)&&!n.expanded||n.style.shareHeaderLine){const o=this.labelWidthForGroup(r,n);if(this.isGroupFocused(t))r.fillStyle=i.ThemeSupport.instance().getComputedValue("--selected-group-background",this.contentElement);else{const e=s.Color.parse(n.style.backgroundColor);e&&(r.fillStyle=e.setAlpha(.8).asString())}r.fillRect(u+6,e+2,o,n.style.height-4)}r.fillStyle=this.#se&&n.hidden?i.ThemeSupport.instance().getComputedValue("--sys-color-token-subtle",this.contentElement):n.style.color;const o=u+K*(n.style.nestingLevel+1)+$/2+3;if(r.fillText(n.name,o,e+n.style.height-this.textBaseline),this.#se&&n.hidden&&r.fillRect(o,e+n.style.height/2,a.UIUtils.measureTextWidth(r,n.name),1),n.legends&&n.expanded&&function(e,t,i,s){e.save(),e.translate(t,i);let r=0;for(const t of s)r=G(e,r,4,t.color,t.category);e.restore()}(r,6+this.labelWidthForGroup(r,n)+16,e,n.legends),c&&this.#se){const t=n.hidden?"--sys-color-token-subtle":"--sys-color-on-surface";0===n.style.nestingLevel&&(A(r,6,e,j,"M9.25 17V5.875L7.062 8.062L6 7L10 3L14 7L12.938 8.062L10.75 5.875V17H9.25Z",t),A(r,25,e,j,"M9.25 3V14.125L7.062 11.938L6 13L10 17L14 13L12.938 11.938L10.75 14.125V3H9.25Z",t)),A(r,44,e,j,n.hidden?"M10 13.5C10.972 13.5 11.7983 13.1597 12.479 12.479C13.1597 11.7983 13.5 10.972 13.5 10C13.5 9.028 13.1597 8.20167 12.479 7.521C11.7983 6.84033 10.972 6.5 10 6.5C9.028 6.5 8.20167 6.84033 7.521 7.521C6.84033 8.20167 6.5 9.028 6.5 10C6.5 10.972 6.84033 11.7983 7.521 12.479C8.20167 13.1597 9.028 13.5 10 13.5ZM10 12C9.44467 12 8.97233 11.8057 8.583 11.417C8.19433 11.0277 8 10.5553 8 10C8 9.44467 8.19433 8.97233 8.583 8.583C8.97233 8.19433 9.44467 8 10 8C10.5553 8 11.0277 8.19433 11.417 8.583C11.8057 8.97233 12 9.44467 12 10C12 10.5553 11.8057 11.0277 11.417 11.417C11.0277 11.8057 10.5553 12 10 12ZM10 16C8.014 16 6.20833 15.455 4.583 14.365C2.95833 13.2743 1.764 11.8193 1 10C1.764 8.18067 2.95833 6.72567 4.583 5.635C6.20833 4.545 8.014 4 10 4C11.986 4 13.7917 4.545 15.417 5.635C17.0417 6.72567 18.236 8.18067 19 10C18.236 11.8193 17.0417 13.2743 15.417 14.365C13.7917 15.455 11.986 16 10 16ZM10 14.5C11.5553 14.5 12.9927 14.0973 14.312 13.292C15.632 12.486 16.646 11.3887 17.354 10C16.646 8.61133 15.632 7.514 14.312 6.708C12.9927 5.90267 11.5553 5.5 10 5.5C8.44467 5.5 7.00733 5.90267 5.688 6.708C4.368 7.514 3.354 8.61133 2.646 10C3.354 11.3887 4.368 12.486 5.688 13.292C7.00733 14.0973 8.44467 14.5 10 14.5Z":"M13.2708 11.1459L11.9792 9.85419C12.0347 9.32641 11.875 8.87155 11.5 8.4896C11.125 8.10766 10.6736 7.94446 10.1458 8.00002L8.85417 6.70835C9.03472 6.63891 9.22222 6.58683 9.41667 6.5521C9.61111 6.51738 9.80556 6.50002 10 6.50002C10.9722 6.50002 11.7986 6.8403 12.4792 7.52085C13.1597 8.20141 13.5 9.0278 13.5 10C13.5 10.1945 13.4826 10.3889 13.4479 10.5834C13.4132 10.7778 13.3542 10.9653 13.2708 11.1459ZM16.0417 13.9167L14.9583 12.8334C15.4583 12.4445 15.9132 12.0174 16.3229 11.5521C16.7326 11.0868 17.0764 10.5695 17.3542 10C16.6736 8.59724 15.6701 7.49655 14.3438 6.69794C13.0174 5.89933 11.5694 5.50002 10 5.50002C9.63889 5.50002 9.28472 5.52085 8.9375 5.56252C8.59028 5.60419 8.25 5.67363 7.91667 5.77085L6.70833 4.56252C7.23611 4.35419 7.77431 4.20835 8.32292 4.12502C8.87153 4.04169 9.43056 4.00002 10 4.00002C11.9861 4.00002 13.8021 4.53821 15.4479 5.6146C17.0938 6.69099 18.2778 8.1528 19 10C18.6944 10.7917 18.2882 11.5104 17.7813 12.1563C17.2743 12.8021 16.6944 13.3889 16.0417 13.9167ZM16 18.125L13.2917 15.4167C12.7639 15.6111 12.2257 15.757 11.6771 15.8542C11.1285 15.9514 10.5694 16 10 16C8.01389 16 6.19792 15.4618 4.55208 14.3854C2.90625 13.309 1.72222 11.8472 1 10C1.30556 9.20835 1.70833 8.48613 2.20833 7.83335C2.70833 7.18058 3.29167 6.5903 3.95833 6.06252L1.875 3.97919L2.9375 2.91669L17.0625 17.0625L16 18.125ZM5.02083 7.14585C4.53472 7.53474 4.08333 7.96183 3.66667 8.4271C3.25 8.89238 2.90972 9.41669 2.64583 10C3.32639 11.4028 4.32986 12.5035 5.65625 13.3021C6.98264 14.1007 8.43056 14.5 10 14.5C10.3611 14.5 10.7153 14.4757 11.0625 14.4271C11.4097 14.3785 11.7569 14.3125 12.1042 14.2292L11.1667 13.2917C10.9722 13.3611 10.7778 13.4132 10.5833 13.4479C10.3889 13.4827 10.1944 13.5 10 13.5C9.02778 13.5 8.20139 13.1597 7.52083 12.4792C6.84028 11.7986 6.5 10.9722 6.5 10C6.5 9.80558 6.52431 9.61113 6.57292 9.41669C6.62153 9.22224 6.66667 9.0278 6.70833 8.83335L5.02083 7.14585Z",this.groupIsLastVisibleTopLevel(n)?"--sys-color-state-disabled":t)}})),r.restore(),r.fillStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-token-subtle"),this.forEachGroupInViewport(((e,t,i)=>{this.isGroupCollapsible(t)&&function(e,t,i,s){const r=$*Math.sqrt(3)/2,n=Math.round(r/2);e.save(),e.beginPath(),e.translate(t,i),e.rotate(s?Math.PI/2:0),e.moveTo(-n,-$/2),e.lineTo(-n,$/2),e.lineTo(r-n,0),e.fill(),e.restore()}(r,u+K*(i.style.nestingLevel+1),e+i.style.height-this.textBaseline-$/2,!this.#se&&Boolean(i.expanded))})),r.strokeStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-neutral-outline"),r.beginPath(),r.stroke(),this.forEachGroupInViewport(((e,t,s,n,o)=>{if(this.isGroupFocused(t)){const t=2,n=10;r.fillStyle=i.ThemeSupport.instance().getComputedValue("--selected-group-border",this.contentElement),r.fillRect(0,e-t,t,o-s.style.padding+2*t),r.fillRect(0,e-t,n,t),r.fillRect(0,e+o-s.style.padding,n,t)}})),r.restore()}drawMarkers(e,t,i){const{entryStartTimes:s,entryLevels:r}=t;this.markerPositions.clear(),e.textBaseline="alphabetic",e.save(),e.beginPath();let n=-1,o=-1/0;for(let t=i.length-1;t>=0;--t){const l=i[t],h=this.dataProvider.entryTitle(l);if(!h)continue;const d=s[l],c=r[l];n!==c&&(o=-1/0);const u=Math.max(this.chartViewport.timeToPosition(d),o),m=this.levelToOffset(c),g=this.levelHeight(c),p=4,f=Math.ceil(a.UIUtils.measureTextWidth(e,h))+2*p;o=u+f+1,n=c,this.markerPositions.set(l,{x:u,width:f}),e.fillStyle=this.dataProvider.entryColor(l),e.fillRect(u,m,f,g-1),e.fillStyle="white",e.fillText(h,u+p,m+g-this.textBaseline)}e.strokeStyle="rgba(0, 0, 0, 0.2)",e.stroke(),e.restore()}drawEventTitles(e,t,i,s){const r=this.chartViewport.timeToPixel(),n=this.textPadding;e.save(),e.beginPath();const{entryStartTimes:o,entryLevels:l}=t;for(let h=0;h<i.length;++h){const d=i[h],c=o[d],u=this.timeToPositionClipped(c),m=Math.min(this.#fe(t,d),s),g=l[d],p=this.levelToOffset(g);let f=this.dataProvider.entryTitle(d);const v=this.#we(t,d);if(f&&f.length){e.font=this.#J;const t=this.entryHasDecoration(d,"HIDDEN_DESCENDANTS_ARROW")&&m>2*v?m-n-this.barHeight:m-2*n;f=a.UIUtils.trimTextMiddle(e,f,t)}const w=this.chartViewport.timeToPosition(c);this.dataProvider.decorateEntry(d,e,f,u,p,m,v,w,r)||f&&f.length&&(e.fillStyle=this.dataProvider.textColor(d),e.fillText(f,u+n,p+v-this.textBaseline))}e.restore()}forEachGroup(e){if(!this.rawTimelineData)return;const t=this.rawTimelineData.groups||[];if(!t.length)return;const i=this.groupOffsets;if(!i)return;const s=[{nestingLevel:-1,visible:!0}];for(let r=0;r<t.length;++r){const n=i[r],o=t[r];let a=!0,l=s[s.length-1];for(;l&&l.nestingLevel>=o.style.nestingLevel;)s.pop(),a=!1,l=s[s.length-1];l=s[s.length-1];const h=!!l&&l.visible,d=!o.hidden&&h&&(!this.isGroupCollapsible(r)||o.expanded);if(s.push({nestingLevel:o.style.nestingLevel,visible:Boolean(d)}),!this.#ee)return;const c=[];function u(e){c.push(e.index);for(const t of e.children)u(t)}if(u(this.#ee),c.shift(),c.length!==t.length)return void console.warn("The data from the group tree doesn't match the data from DataProvider.");c.push(t.length);const m=c.indexOf(r),g=i[c[m+1]];(this.#se||h&&!o.hidden)&&e(n,r,o,a,g-n)}}forEachGroupInViewport(e){const t=this.chartViewport.scrollOffset();this.forEachGroup(((i,s,r,n,o)=>{i-r.style.padding>t+this.offsetHeight||i+o<t||e(i,s,r,n,o)}))}labelWidthForGroup(e,t){return K*(t.style.nestingLevel+1)+$/2+3+a.UIUtils.measureTextWidth(e,t.name)+3-6}drawCollapsedOverviewForGroup(e,t,i){const n=new s.SegmentedRange.SegmentedRange((function(e,t){return e.data===t.data&&e.end+.4>t.end?e:null})),o=this.chartViewport.windowLeftTime(),a=this.chartViewport.windowRightTime(),l=this.canvas.getContext("2d"),h=e.style.height;if(!this.rawTimelineData)return;const d=this.rawTimelineData.entryStartTimes,c=this.rawTimelineData.entryTotalTimes,u=this.chartViewport.timeToPixel();for(let m=e.startLevel;m<i;++m){const i=this.timelineLevels?this.timelineLevels[m]:[];let g=1/0;for(let m=r.ArrayUtilities.lowerBound(i,a,((e,t)=>e-d[t]))-1;m>=0;--m){const r=i[m],a=d[r],p=this.timeToPositionClipped(a),f=a+c[r];if(isNaN(f)||p>=g)continue;if(f<=o)break;g=p;const v=this.entryColorsCache?this.entryColorsCache[r]:"",w=this.timeToPositionClipped(f);if(e.style.useDecoratorsForOverview&&this.dataProvider.forceDecoration(r)){const e=this.chartViewport.timeToPosition(a),i=this.#fe(this.rawTimelineData,r);l.beginPath(),l.fillStyle=v,l.fillRect(p,t,i,h-1),this.dataProvider.decorateEntry(r,l,"",p,t,i,h,e,u)}else n.append(new s.SegmentedRange.Segment(p,w,v))}}const m=n.segments().slice().sort(((e,t)=>e.data.localeCompare(t.data)));let g;l.beginPath();for(let e=0;e<m.length;++e){const i=m[e];g!==m[e].data&&(l.fill(),l.beginPath(),g=m[e].data,l.fillStyle=g),l.rect(i.begin,t,i.end-i.begin,h)}l.fill()}drawFlowEvents(e,t){const i=this.timelineData();if(!i)return;const{entryTotalTimes:s,entryStartTimes:r,entryLevels:n}=t,o=window.devicePixelRatio,a=this.chartViewport.scrollOffset();e.save(),e.scale(o,o),e.translate(0,-a),e.fillStyle="#7f5050",e.strokeStyle="#7f5050";for(let o=0;o<i.initiatorsData.length;++o){const a=i.initiatorsData[o],l=r[a.initiatorIndex],h=r[a.initiatorIndex]+s[a.initiatorIndex],d=r[a.eventIndex],c=h<d,u=c?h:Math.max(l,this.chartViewport.pixelToTime(5)),m=d;if(m<this.chartViewport.windowLeftTime())continue;let g=this.chartViewport.timeToPosition(u),p=this.chartViewport.timeToPosition(m);if(a.isInitiatorHidden){const{circleEndX:i}=this.drawCircleAroundCollapseArrow(a.initiatorIndex,e,t);i&&(g=i)}if(a.isEntryHidden){const{circleStartX:i}=this.drawCircleAroundCollapseArrow(a.eventIndex,e,t);i&&(p=i)}const f=n[a.initiatorIndex],v=n[a.eventIndex],w=this.levelToOffset(f)+this.levelHeight(f)/2,b=this.levelToOffset(v)+this.levelHeight(v)/2,T=p-g;c?(e.beginPath(),e.moveTo(g,w),e.lineTo(g+T/2,w),e.lineTo(g+T/2,b),e.lineTo(p,b),e.stroke()):(e.beginPath(),e.moveTo(g,w),e.lineTo(g,b),e.lineTo(p,b),e.stroke()),T>3?(e.lineWidth=.5,e.beginPath(),e.moveTo(p,b),e.lineTo(p-6,b-3),e.lineTo(p-6,b+3),e.fill()):e.lineWidth=.2}e.restore()}drawCircleAroundCollapseArrow(e,t,i){const s=i.entryDecorations.at(e);if(!s||!s.find((e=>"HIDDEN_DESCENDANTS_ARROW"===e.type)))return{};const{entryStartTimes:r,entryLevels:n}=i,o=this.#fe(i,e);if(o<2*this.barHeight)return{};const a=r[e],l=this.timeToPositionClipped(a),h=n[e],d=this.#we(i,e),c=this.levelToOffset(h);t.save(),t.beginPath(),t.rect(l,c,o,d),t.clip(),t.lineWidth=1,t.beginPath(),t.fillStyle="#474747";const u=l+o-this.barHeight/2,m=c+this.barHeight/2;return t.beginPath(),t.arc(u,m,6,0,2*Math.PI),t.stroke(),t.restore(),{circleStartX:u-6,circleEndX:u+6}}drawMarkerLines(){const e=this.timelineData();if(!e)return;const t=e.markers,i=this.markerIndexBeforeTime(this.minimumBoundary()),s=this.maximumBoundary(),r=this.chartViewport.timeToPixel(),n=this.canvas.getContext("2d");n.save();const o=window.devicePixelRatio;n.scale(o,o),n.translate(0,3);const a=Z-1;for(let e=i;e<t.length;e++){const i=t[e].startTime();if(i>s)break;t[e].draw(n,this.chartViewport.timeToPosition(i),a,r)}n.restore()}updateMarkerHighlight(){const e=this.markerHighlighElement;e.parentElement&&e.remove();const t=this.highlightedMarkerIndex;if(-1===t)return;const i=this.timelineData();if(!i)return;const s=i.markers[t],r=this.timeToPositionClipped(s.startTime());a.Tooltip.Tooltip.install(e,s.title()||"");const n=e.style;n.left=r+"px",n.backgroundColor=s.color(),this.viewportElement.appendChild(e)}processTimelineData(e){if(!e)return this.timelineLevels=null,this.visibleLevelOffsets=null,this.visibleLevels=null,this.groupOffsets=null,this.rawTimelineData=null,this.forceDecorationCache=null,this.entryColorsCache=null,this.rawTimelineDataLength=0,this.#ee=null,this.selectedGroupIndex=-1,this.keyboardFocusedGroup=-1,void this.flameChartDelegate.updateSelectedGroup(this,null);this.rawTimelineData=e,this.rawTimelineDataLength=e.entryStartTimes.length,this.forceDecorationCache=new Array(this.rawTimelineDataLength),this.entryColorsCache=new Array(this.rawTimelineDataLength);for(let e=0;e<this.rawTimelineDataLength;++e)this.forceDecorationCache[e]=this.dataProvider.forceDecoration(e)??!1,this.entryColorsCache[e]=this.dataProvider.entryColor(e);const t=new Uint32Array(this.dataProvider.maxStackDepth()+1);for(let i=0;i<e.entryLevels.length;++i)++t[e.entryLevels[i]];const i=new Array(t.length);for(let e=0;e<i.length;++e)i[e]=new Uint32Array(t[e]),t[e]=0;for(let s=0;s<e.entryLevels.length;++s){const r=e.entryLevels[s];i[r][t[r]++]=s}this.timelineLevels=i;const s=this.rawTimelineData.groups||[];for(let e=0;e<s.length;++e){const t=this.groupExpansionState[s[e].name],i=this.groupHiddenState[s[e].name];void 0!==t&&(s[e].expanded=t),void 0!==i&&(s[e].hidden=i)}this.#ee?this.updateGroupTree(s,this.#ee):this.#ee=this.buildGroupTree(s),this.updateLevelPositions(),this.updateHeight(),-1===this.selectedGroupIndex&&(this.selectedGroupIndex=e.selectedGroup?s.indexOf(e.selectedGroup):-1),this.keyboardFocusedGroup=this.selectedGroupIndex,this.flameChartDelegate.updateSelectedGroup(this,e.selectedGroup)}#Ee(e,t,i){return{index:t,nestingLevel:e.style.nestingLevel,startLevel:e.startLevel,endLevel:i,children:[]}}buildGroupTree(e){const t={index:-1,nestingLevel:-1,startLevel:0,endLevel:e.length?e[0].startLevel:this.dataProvider.maxStackDepth(),children:[]},i=[t];for(let t=0;t<e.length;t++){const s=e[t],r=s.style.nestingLevel;let n=i[i.length-1];for(;n&&n.nestingLevel>=r;)i.pop(),n=i[i.length-1];const o=e[t+1],a=o?.startLevel??this.dataProvider.maxStackDepth(),l=this.#Ee(s,t,a);n.children.push(l),i.push(l)}return t}updateGroupTree(e,t){const i=this.dataProvider.maxStackDepth();!function t(s){const r=s.index;if(r<0)s.startLevel=0,s.endLevel=e.length?e[0].startLevel:i;else{if(!e[r])return void console.warn("The |groups| is changed. Please make sure the flamechart is reset after data change in the data provider");s.startLevel=e[r].startLevel;const t=e[r+1];s.endLevel=t?.startLevel??i}for(const e of s.children)t(e)}(t)}#xe(e,t,i){if(!(this.visibleLevels&&this.visibleLevelOffsets&&this.visibleLevelHeights&&this.groupOffsets))return t;const s=this.rawTimelineData?.groups;if(!s)return t;if(e.index>=s.length)return console.warn("The data from the group tree is outdated. Please make sure the flamechart is reset after data change in the data provider"),t;e.index>=0&&(this.groupOffsets[e.index]=t,(this.#se&&s[e.index].name||!s[e.index].hidden&&i&&!s[e.index].style.shareHeaderLine)&&(t+=s[e.index].style.height));let r=!1;if(e.index<0)r=!0;else{const t=!(this.isGroupCollapsible(e.index)&&!s[e.index].expanded);r=!s[e.index].hidden&&t}const n=r&&i;for(let r=e.startLevel;r<e.endLevel;r++){if(r>=this.dataProvider.maxStackDepth())return console.warn("The data from the group tree is outdated. Please make sure the flamechart is reset after data change in the data provider"),t;const o=r===e.startLevel;let a,l;if(e.index<0)a=!0;else{const t=o&&s[e.index].style.useFirstLineForOverview;a=!s[e.index].hidden&&i&&(n||t)}if(s[e.index]){const t=o&&!s[e.index].style.shareHeaderLine,i=this.isGroupCollapsible(e.index)&&!s[e.index].expanded;l=t||i?s[e.index].style.height:s[e.index].style.itemsHeight??this.barHeight}else l=this.barHeight;this.visibleLevels[r]=!this.#se&&Boolean(a),this.visibleLevelOffsets[r]=t,this.visibleLevelHeights[r]=this.#se?0:l,(e.index<0||!s[e.index].hidden&&(a||i&&s[e.index].style.shareHeaderLine&&o))&&(t+=this.visibleLevelHeights[r])}if(0===e.children.length)return t;for(const i of e.children)(this.#se&&s[i.index].name||n&&!s[i.index]?.hidden&&i!==e.children[0])&&(t+=s[i.index].style.padding??0),t=this.#xe(i,t,n);return t}updateLevelPositions(){if(!this.#ee)return void console.warn("Please make sure the new timeline data is processed before update the level positions.");const e=this.dataProvider.maxStackDepth(),t=this.rawTimelineData?.groups||[];this.visibleLevelOffsets=new Uint32Array(e+1),this.visibleLevelHeights=new Uint32Array(e),this.visibleLevels=new Array(e),this.groupOffsets=new Uint32Array(t.length+1);let i=this.rulerEnabled?Z+2:2;i=this.#xe(this.#ee,i,!0),this.groupOffsets[t.length]=i,this.visibleLevelOffsets[e]=i}isGroupCollapsible(e){if(!this.rawTimelineData||e<0)return;const t=this.rawTimelineData.groups||[],i=t[e].style;if(!i.shareHeaderLine||!i.collapsible)return Boolean(i.collapsible);const s=e+1>=t.length;if(!s&&t[e+1].style.nestingLevel>i.nestingLevel)return!0;return(s?this.dataProvider.maxStackDepth():t[e+1].startLevel)!==t[e].startLevel+1||i.height!==i.itemsHeight}groupIsLastVisibleTopLevel(e){if(!e)return!0;const t=this.#ee?.children.filter((e=>!this.rawTimelineData?.groups[e.index].hidden)).length;return 1===t&&0===e.style.nestingLevel&&!e.hidden}setSelectedEntry(e){this.isMouseOverRevealChildrenArrow(this.lastMouseOffsetX,e)&&this.modifyTree("RESET_CHILDREN",e),this.selectedEntryIndex!==e&&(-1!==e&&this.chartViewport.hideRangeSelection(),this.selectedEntryIndex=e,this.revealEntry(e),this.updateElementPosition(this.selectedElement,this.selectedEntryIndex),this.update())}entryHasDecoration(e,t){const i=this.timelineData();if(!i)return!1;const s=i.entryDecorations.at(e);return!!(s&&s.length>=1)&&s.some((e=>e.type===t))}getMarkerPixelsForEntryIndex(e){return this.markerPositions.get(e)??null}updateElementPosition(e,t,i){if(!e)return;if(e.classList.add("hidden"),-1===t)return;const s=this.timelineData();if(!s)return;const r=s.entryStartTimes[t],n=s.entryTotalTimes[t];let o=0,a=0,l=!0;if(Number.isNaN(n)){const e=this.markerPositions.get(t);e?(o=e.x,a=e.width):l=!1}else o=this.chartViewport.timeToPosition(r),a=n*this.chartViewport.timeToPixel();if(o+a<=0||o>=this.offsetWidth)return;const h=o+a/2;a=Math.max(a,2),o=h-a/2;const d=s.entryLevels[t],c=this.levelToOffset(d)-this.chartViewport.scrollOffset(),u=this.levelHeight(d),m=e.style;i?(m.top=c+"px",m.width=u+"px",m.height=u+"px",m.left=o+a-u+"px"):(m.top=c+"px",m.width=a+"px",m.height=u-1+"px",m.left=o+"px"),e.classList.toggle("hidden",!l),this.viewportElement.appendChild(e)}updateHiddenChildrenArrowHighlighPosition(e){this.revealDescendantsArrowHighlightElement.classList.add("hidden"),-1!==e&&this.isMouseOverRevealChildrenArrow(this.lastMouseOffsetX,e)&&this.updateElementPosition(this.revealDescendantsArrowHighlightElement,e,!0)}timeToPositionClipped(e){return r.NumberUtilities.clamp(this.chartViewport.timeToPosition(e),0,this.offsetWidth)}groupIndexToOffsetForTest(e){if(!this.groupOffsets)throw new Error("No visible group offsets");return this.groupOffsets[e]}setEditModeForTest(e){this.#se=e}levelIsVisible(e){if(!this.visibleLevels)throw new Error("No level visiblibities");return this.visibleLevels[e]}levelToOffset(e){if(!this.visibleLevelOffsets)throw new Error("No visible level offsets");return this.visibleLevelOffsets[e]}levelHeight(e){if(!this.visibleLevelHeights)throw new Error("No visible level heights");return this.visibleLevelHeights[e]}updateBoundaries(){this.totalTime=this.dataProvider.totalTime(),this.minimumBoundaryInternal=this.dataProvider.minimumBoundary(),this.chartViewport.setBoundaries(this.minimumBoundaryInternal,this.totalTime)}updateHeight(){const e=this.levelToOffset(this.dataProvider.maxStackDepth())+2;this.chartViewport.setContentHeight(e)}onResize(){this.#re=null,this.scheduleUpdate()}update(){this.timelineData()&&(this.updateHeight(),this.updateBoundaries(),this.draw(),this.chartViewport.isDragging()||this.updateHighlight())}reset(){this.#se&&(this.#be(),this.#se=!1),this.chartViewport.reset(),this.rawTimelineData=null,this.rawTimelineDataLength=0,this.#ee=null,this.highlightedMarkerIndex=-1,this.highlightedEntryIndex=-1,this.selectedEntryIndex=-1,this.selectedGroupIndex=-1}scheduleUpdate(){this.chartViewport.scheduleUpdate()}enabled(){return 0!==this.rawTimelineDataLength}computePosition(e){return this.chartViewport.timeToPosition(e)}formatValue(e,t){return this.dataProvider.formatValue(e-this.zeroTime(),t)}maximumBoundary(){return d.Types.Timing.MilliSeconds(this.chartViewport.windowRightTime())}minimumBoundary(){return d.Types.Timing.MilliSeconds(this.chartViewport.windowLeftTime())}zeroTime(){return d.Types.Timing.MilliSeconds(this.dataProvider.minimumBoundary())}boundarySpan(){return d.Types.Timing.MilliSeconds(this.maximumBoundary()-this.minimumBoundary())}}const Z=15,q={CANDY:1,WARNING_TRIANGLE:2,HIDDEN_DESCENDANTS_ARROW:3};function Q(e){e.sort(((e,t)=>q[e.type]-q[t.type]))}class J{entryLevels;entryTotalTimes;entryStartTimes;entryDecorations;groups;markers;initiatorsData;selectedGroup;constructor(e,t,i,s,r=[],n=[]){this.entryLevels=e,this.entryTotalTimes=t,this.entryStartTimes=i,this.entryDecorations=r,this.groups=s||[],this.markers=[],this.initiatorsData=n||[],this.selectedGroup=null}static create(e){return new J(e.entryLevels,e.entryTotalTimes,e.entryStartTimes,e.groups,e.entryDecorations||[],e.initiatorsData||[])}static createEmpty(){return new J([],[],[],[])}resetFlowData(){this.initiatorsData=[]}}var ee=Object.freeze({__proto__:null,ARROW_SIDE:$,EDIT_ICON_WIDTH:j,FlameChartDelegate:class{windowChanged(e,t,i){}updateRangeSelection(e,t){}updateSelectedGroup(e,t){}},FlameChart:Y,RulerHeight:Z,MinimalTimeWindowMs:.5,sortDecorationsForRenderingOrder:Q,FlameChartTimelineData:J});const te=o.RenderCoordinator.RenderCoordinator.instance();class ie extends a.Widget.VBox{delegate;viewportElement;alwaysShowVerticalScrollInternal;rangeSelectionEnabled;vScrollElement;vScrollContent;selectionOverlay;selectedTimeSpanLabel;cursorElement;isDraggingInternal;totalHeight;offsetHeight;scrollTop;rangeSelectionStart;rangeSelectionEnd;dragStartPointX;dragStartPointY;dragStartScrollTop;visibleLeftTime;visibleRightTime;offsetWidth;targetLeftTime;targetRightTime;selectionOffsetShiftX;selectionStartX;lastMouseOffsetX;minimumBoundary;totalTime;isUpdateScheduled;cancelWindowTimesAnimation;#Ce;#Se=n.Runtime.experiments.isEnabled("perf-panel-annotations");constructor(e,t){super(),this.#Ce=t,this.registerRequiredCSS(S),this.delegate=e,this.viewportElement=this.contentElement.createChild("div","fill"),this.viewportElement.addEventListener("mousemove",this.updateCursorPosition.bind(this),!1),this.viewportElement.addEventListener("mouseout",this.onMouseOut.bind(this),!1),this.viewportElement.addEventListener("wheel",this.onMouseWheel.bind(this),!1),this.viewportElement.addEventListener("keydown",this.onChartKeyDown.bind(this),!1),this.viewportElement.addEventListener("keyup",this.onChartKeyUp.bind(this),!1),a.UIUtils.installDragHandle(this.viewportElement,this.startDragging.bind(this),this.dragging.bind(this),this.endDragging.bind(this),"-webkit-grabbing",null),a.UIUtils.installDragHandle(this.viewportElement,this.startRangeSelection.bind(this),this.rangeSelectionDragging.bind(this),this.endRangeSelection.bind(this),"text",null),this.alwaysShowVerticalScrollInternal=!1,this.rangeSelectionEnabled=!0,this.vScrollElement=this.contentElement.createChild("div","chart-viewport-v-scroll"),this.vScrollContent=this.vScrollElement.createChild("div"),this.vScrollElement.addEventListener("scroll",this.onScroll.bind(this),!1),this.selectionOverlay=this.contentElement.createChild("div","chart-viewport-selection-overlay hidden"),this.selectedTimeSpanLabel=this.selectionOverlay.createChild("div","time-span"),this.cursorElement=this.contentElement.createChild("div","chart-cursor-element hidden"),this.#Se&&this.cursorElement.classList.add("using-new-overlays"),this.reset(),this.rangeSelectionStart=null,this.rangeSelectionEnd=null}alwaysShowVerticalScroll(){this.alwaysShowVerticalScrollInternal=!0,this.vScrollElement.classList.add("always-show-scrollbar")}disableRangeSelection(){this.rangeSelectionEnabled=!1,this.rangeSelectionStart=null,this.rangeSelectionEnd=null,this.updateRangeSelectionOverlay()}isDragging(){return this.isDraggingInternal}elementsToRestoreScrollPositionsFor(){return[this.vScrollElement]}updateScrollBar(){const e=this.alwaysShowVerticalScrollInternal||this.totalHeight>this.offsetHeight;this.vScrollElement.classList.contains("hidden")===e&&(this.vScrollElement.classList.toggle("hidden",!e),this.updateContentElementSize())}onResize(){this.updateScrollBar(),this.updateContentElementSize(),this.scheduleUpdate()}reset(){this.vScrollElement.scrollTop=0,this.scrollTop=0,this.rangeSelectionStart=null,this.rangeSelectionEnd=null,this.isDraggingInternal=!1,this.dragStartPointX=0,this.dragStartPointY=0,this.dragStartScrollTop=0,this.visibleLeftTime=0,this.visibleRightTime=0,this.offsetWidth=0,this.offsetHeight=0,this.totalHeight=0,this.targetLeftTime=0,this.targetRightTime=0,this.isUpdateScheduled=!1,this.updateContentElementSize()}updateContentElementSize(){let e=this.vScrollElement.offsetLeft;e||(e=this.contentElement.offsetWidth),this.offsetWidth=e,this.offsetHeight=this.contentElement.offsetHeight,this.delegate.setSize(this.offsetWidth,this.offsetHeight)}setContentHeight(e){this.totalHeight=e,this.vScrollContent.style.height=e+"px",this.updateScrollBar(),this.updateContentElementSize(),this.scrollTop+this.offsetHeight<=e||(this.scrollTop=Math.max(0,e-this.offsetHeight),this.vScrollElement.scrollTop=this.scrollTop)}setScrollOffset(e,t){t=t||0,this.vScrollElement.scrollTop>e?this.vScrollElement.scrollTop=e:this.vScrollElement.scrollTop<e-this.offsetHeight+t&&(this.vScrollElement.scrollTop=e-this.offsetHeight+t)}scrollOffset(){return this.vScrollElement.scrollTop}chartHeight(){return this.offsetHeight}setBoundaries(e,t){this.minimumBoundary=e,this.totalTime=t}onMouseWheel(e){const t=e,i=t.shiftKey!==("zoom"===s.Settings.Settings.instance().moduleSetting("flamechart-mouse-wheel-action").get()),r=!i&&(t.deltaY||53===Math.abs(t.deltaX)),n=i&&Math.abs(t.deltaX)>Math.abs(t.deltaY);if(r)this.vScrollElement.scrollTop+=(t.deltaY||t.deltaX)/53*this.offsetHeight/8;else if(n)this.handlePanGesture(t.deltaX,!0);else{const e=1/53;this.handleZoomGesture(Math.pow(1.2,(t.deltaY||t.deltaX)*e)-1)}e.consume(!0)}startDragging(e){return!e.shiftKey&&(this.isDraggingInternal=!0,this.dragStartPointX=e.pageX,this.dragStartPointY=e.pageY,this.dragStartScrollTop=this.vScrollElement.scrollTop,this.viewportElement.style.cursor="",!0)}dragging(e){const t=this.dragStartPointX-e.pageX;this.dragStartPointX=e.pageX,this.handlePanGesture(t);const i=this.dragStartPointY-e.pageY;this.vScrollElement.scrollTop=this.dragStartScrollTop+i}endDragging(){this.isDraggingInternal=!1}startRangeSelection(e){if(!e.shiftKey||!this.rangeSelectionEnabled)return!1;if(this.isDraggingInternal=!0,this.selectionOffsetShiftX=e.offsetX-e.pageX,this.selectionStartX=e.offsetX,!this.#Se){const e=this.selectionOverlay.style;e.left=this.selectionStartX+"px",e.width="1px",this.selectedTimeSpanLabel.textContent="",this.selectionOverlay.classList.remove("hidden")}return!0}endRangeSelection(){this.isDraggingInternal=!1,this.selectionStartX=null}hideRangeSelection(){this.selectionOverlay.classList.add("hidden"),this.rangeSelectionStart=null,this.rangeSelectionEnd=null}setRangeSelection(e,t){this.rangeSelectionEnabled&&(this.rangeSelectionStart=Math.min(e,t),this.rangeSelectionEnd=Math.max(e,t),this.updateRangeSelectionOverlay(),this.delegate.updateRangeSelection(this.rangeSelectionStart,this.rangeSelectionEnd))}onClick(e){const t=e,i=this.pixelToTime(t.offsetX);null!==this.rangeSelectionStart&&null!==this.rangeSelectionEnd&&i>=this.rangeSelectionStart&&i<=this.rangeSelectionEnd||this.hideRangeSelection()}rangeSelectionDragging(e){const t=r.NumberUtilities.clamp(e.pageX+this.selectionOffsetShiftX,0,this.offsetWidth),i=this.pixelToTime(this.selectionStartX||0),s=this.pixelToTime(t);this.setRangeSelection(i,s)}updateRangeSelectionOverlay(){if(this.#Se)return;const t=this.rangeSelectionStart||0,i=this.rangeSelectionEnd||0,s=100,n=r.NumberUtilities.clamp(this.timeToPosition(t),-100,this.offsetWidth+s),o=r.NumberUtilities.clamp(this.timeToPosition(i),-100,this.offsetWidth+s),a=this.selectionOverlay.style;a.left=n+"px",a.width=o-n+"px";const l=i-t;this.selectedTimeSpanLabel.textContent=e.TimeUtilities.preciseMillisToString(l,2)}onScroll(){this.scrollTop=this.vScrollElement.scrollTop,this.scheduleUpdate()}onMouseOut(){this.lastMouseOffsetX=-1,this.showCursor(!1)}updateCursorPosition(e){const t=e;this.lastMouseOffsetX=t.offsetX;const i=this.#Ce.enableCursorElement&&t.shiftKey&&!t.metaKey;this.showCursor(i),i&&(this.cursorElement.style.left=t.offsetX+"px")}pixelToTime(e){return this.pixelToTimeOffset(e)+this.visibleLeftTime}pixelToTimeOffset(e){return e*(this.visibleRightTime-this.visibleLeftTime)/this.offsetWidth}timeToPosition(e){return Math.floor((e-this.visibleLeftTime)/(this.visibleRightTime-this.visibleLeftTime)*this.offsetWidth)}timeToPixel(){return this.offsetWidth/(this.visibleRightTime-this.visibleLeftTime)}showCursor(e){this.cursorElement.classList.toggle("hidden",!e||this.isDraggingInternal)}onChartKeyDown(e){const t=e;this.showCursor(t.shiftKey),this.handleZoomPanKeys(e)}onChartKeyUp(e){const t=e;this.showCursor(t.shiftKey)}handleZoomPanKeys(e){if(!a.KeyboardShortcut.KeyboardShortcut.hasNoModifiers(e))return;const t=e,i=t.shiftKey?.8:.3,s=t.shiftKey?320:160;switch(t.code){case"KeyA":this.handlePanGesture(-s,!0);break;case"KeyD":this.handlePanGesture(s,!0);break;case"KeyW":this.handleZoomGesture(-i);break;case"KeyS":this.handleZoomGesture(i);break;default:return}e.consume(!0)}handleZoomGesture(e){const t={left:this.targetLeftTime,right:this.targetRightTime},i=this.pixelToTime(this.lastMouseOffsetX||0);t.left+=(t.left-i)*e,t.right+=(t.right-i)*e,this.requestWindowTimes(t,!0)}handlePanGesture(e,t){const i={left:this.targetLeftTime,right:this.targetRightTime},s=r.NumberUtilities.clamp(this.pixelToTimeOffset(e),this.minimumBoundary-i.left,this.totalTime+this.minimumBoundary-i.right);i.left+=s,i.right+=s,this.requestWindowTimes(i,Boolean(t))}requestWindowTimes(e,t){const i=this.minimumBoundary+this.totalTime;e.left<this.minimumBoundary?(e.right=Math.min(e.right+this.minimumBoundary-e.left,i),e.left=this.minimumBoundary):e.right>i&&(e.left=Math.max(e.left-e.right+i,this.minimumBoundary),e.right=i),e.right-e.left<.5||this.delegate.windowChanged(e.left,e.right,t)}scheduleUpdate(){this.cancelWindowTimesAnimation||this.isUpdateScheduled||(this.isUpdateScheduled=!0,te.write((()=>{this.isUpdateScheduled=!1,this.update()})))}update(){this.updateRangeSelectionOverlay(),this.delegate.update()}setWindowTimes(e,t,i){if(e!==this.targetLeftTime||t!==this.targetRightTime){if(!i||0===this.visibleLeftTime||this.visibleRightTime===1/0||0===e&&t===1/0||e===1/0&&t===1/0)return this.targetLeftTime=e,this.targetRightTime=t,this.visibleLeftTime=e,this.visibleRightTime=t,void this.scheduleUpdate();this.cancelWindowTimesAnimation&&(this.cancelWindowTimesAnimation(),this.visibleLeftTime=this.targetLeftTime,this.visibleRightTime=this.targetRightTime),this.targetLeftTime=e,this.targetRightTime=t,this.cancelWindowTimesAnimation=a.UIUtils.animateFunction(this.element.window(),function(e,t){this.visibleLeftTime=e,this.visibleRightTime=t,this.update()}.bind(this),[{from:this.visibleLeftTime,to:e},{from:this.visibleRightTime,to:t}],100,(()=>{this.cancelWindowTimesAnimation=null}))}}windowLeftTime(){return this.visibleLeftTime}windowRightTime(){return this.visibleRightTime}}var se=Object.freeze({__proto__:null,ChartViewport:ie}),re={cssContent:".film-strip-view{overflow-x:auto;overflow-y:hidden;align-content:flex-start;min-height:81px}.film-strip-view .frame .time{font-size:10px;margin-top:2px}.film-strip-view .label{margin:auto;font-size:18px;color:var(--sys-color-token-subtle)}.film-strip-view .frame{background:none;border:none;display:flex;flex-direction:column;align-items:center;padding:4px;flex:none;cursor:pointer}.film-strip-view .frame .thumbnail{min-width:24px;display:flex;flex-direction:row;align-items:center;pointer-events:none;margin:4px 0 2px;border:2px solid transparent}.film-strip-view .frame:hover .thumbnail,\n.film-strip-view .frame:focus .thumbnail{border-color:var(--sys-color-primary)}.film-strip-view .frame .thumbnail img{height:auto;width:auto;max-width:80px;max-height:50px;pointer-events:none;box-shadow:0 0 3px var(--box-shadow-outline-color);flex:0 0 auto}.film-strip-view .frame:hover .thumbnail img,\n.film-strip-view .frame:focus .thumbnail img{box-shadow:none}\n/*# sourceURL=filmStripView.css */\n"};const ne={doubleclickToZoomImageClickTo:"Doubleclick to zoom image. Click to view preceding requests.",screenshotForSSelectToView:"Screenshot for {PH1} - select to view preceding requests.",screenshot:"Screenshot",previousFrame:"Previous frame",nextFrame:"Next frame"},oe=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/FilmStripView.ts",ne),ae=e.i18n.getLocalizedString.bind(void 0,oe);class le extends(s.ObjectWrapper.eventMixin(a.Widget.HBox)){statusLabel;zeroTime=d.Types.Timing.MilliSeconds(0);#Le=null;constructor(){super(!0),this.registerRequiredCSS(re),this.contentElement.classList.add("film-strip-view"),this.statusLabel=this.contentElement.createChild("div","label"),this.reset()}static setImageData(e,t){t&&(e.src=t)}setModel(e){this.#Le=e,this.zeroTime=d.Helpers.Timing.microSecondsToMilliseconds(e.zeroTime),this.#Le.frames.length?this.update():this.reset()}createFrameElement(t){const i=d.Helpers.Timing.microSecondsToMilliseconds(t.screenshotEvent.ts),s=e.TimeUtilities.millisToString(i-this.zeroTime),r=document.createElement("button");r.classList.add("frame"),a.Tooltip.Tooltip.install(r,ae(ne.doubleclickToZoomImageClickTo)),r.createChild("div","time").textContent=s,r.tabIndex=0,r.setAttribute("jslog",`${u.preview("film-strip").track({click:!0,dblclick:!0})}`),r.setAttribute("aria-label",ae(ne.screenshotForSSelectToView,{PH1:s})),a.ARIAUtils.markAsButton(r);const n=r.createChild("div","thumbnail").createChild("img");return n.alt=ae(ne.screenshot),r.addEventListener("mousedown",this.onMouseEvent.bind(this,"FrameSelected",i),!1),r.addEventListener("mouseenter",this.onMouseEvent.bind(this,"FrameEnter",i),!1),r.addEventListener("mouseout",this.onMouseEvent.bind(this,"FrameExit",i),!1),r.addEventListener("dblclick",this.onDoubleClick.bind(this,t),!1),r.addEventListener("focusin",this.onMouseEvent.bind(this,"FrameEnter",i),!1),r.addEventListener("focusout",this.onMouseEvent.bind(this,"FrameExit",i),!1),le.setImageData(n,t.screenshotEvent.args.dataUri),r}update(){const e=this.#Le?.frames;if(!e||e.length<1)return;const t=e.map((e=>this.createFrameElement(e)));this.contentElement.removeChildren();for(const e of t)this.contentElement.appendChild(e)}onMouseEvent(e,t){this.dispatchEventToListeners(e,t)}onDoubleClick(e){this.#Le&&he.fromFilmStrip(this.#Le,e.index)}reset(){this.zeroTime=d.Types.Timing.MilliSeconds(0),this.contentElement.removeChildren(),this.contentElement.appendChild(this.statusLabel)}setStatusText(e){this.statusLabel.textContent=e}}class he{fragment;widget;index;dialog=null;#ke;static fromFilmStrip(e,t){const i={source:"TraceEngine",frames:e.frames,index:t,zeroTime:d.Helpers.Timing.microSecondsToMilliseconds(e.zeroTime)};return new he(i)}constructor(e){this.#ke=e,this.index=e.index;const t=a.UIUtils.createTextButton("◀",this.onPrevFrame.bind(this));a.Tooltip.Tooltip.install(t,ae(ne.previousFrame));const i=a.UIUtils.createTextButton("▶",this.onNextFrame.bind(this));a.Tooltip.Tooltip.install(i,ae(ne.nextFrame)),this.fragment=a.Fragment.Fragment.build`
      <x-widget flex=none margin=12px>
        <x-hbox overflow=auto border='1px solid #ddd'>
          <img $='image' data-film-strip-dialog-img style="max-height: 80vh; max-width: 80vw;"></img>
        </x-hbox>
        <x-hbox x-center justify-content=center margin-top=10px>
          ${t}
          <x-hbox $='time' margin=8px></x-hbox>
          ${i}
        </x-hbox>
      </x-widget>
    `,this.widget=this.fragment.element(),this.widget.tabIndex=0,this.widget.addEventListener("keydown",this.keyDown.bind(this),!1),this.dialog=null,this.render()}hide(){this.dialog&&this.dialog.hide()}#Pe(){return this.#ke.frames.length}#De(){return this.#ke.zeroTime}resize(){this.dialog||(this.dialog=new a.Dialog.Dialog,this.dialog.contentElement.appendChild(this.widget),this.dialog.setDefaultFocusedElement(this.widget),this.dialog.show()),this.dialog.setSizeBehavior("MeasureContent")}keyDown(e){const t=e;switch(t.key){case"ArrowLeft":l.Platform.isMac()&&t.metaKey?this.onFirstFrame():this.onPrevFrame();break;case"ArrowRight":l.Platform.isMac()&&t.metaKey?this.onLastFrame():this.onNextFrame();break;case"Home":this.onFirstFrame();break;case"End":this.onLastFrame()}}onPrevFrame(){this.index>0&&--this.index,this.render()}onNextFrame(){this.index<this.#Pe()-1&&++this.index,this.render()}onFirstFrame(){this.index=0,this.render()}onLastFrame(){this.index=this.#Pe()-1,this.render()}render(){const t=this.#ke.frames[this.index],i=d.Helpers.Timing.microSecondsToMilliseconds(t.screenshotEvent.ts);this.fragment.$("time").textContent=e.TimeUtilities.millisToString(i-this.#De());const s=this.fragment.$("image");s.setAttribute("data-frame-index",this.index.toString()),le.setImageData(s,t.screenshotEvent.args.dataUri),this.resize()}}var de=Object.freeze({__proto__:null,FilmStripView:le,Dialog:he});var ce=Object.freeze({__proto__:null,GCActionDelegate:class{handleAction(e,t){for(const e of m.TargetManager.TargetManager.instance().models(m.HeapProfilerModel.HeapProfilerModel))e.collectGarbage();return!0}}});let ue,me;class ge{helper;constructor(){this.helper=new fe("performance")}static instance(e={forceNew:null}){const{forceNew:t}=e;return ue&&!t||(ue=new ge),ue}reset(){this.helper.reset()}appendLegacyCPUProfile(e,t){const i=[e.profileHead],s=(e.profileEndTime-e.profileStartTime)/e.totalHitCount;for(;i.length;){const e=i.pop().children;for(let r=0;r<e.length;++r){const n=e[r];if(i.push(n),n.url&&n.positionTicks)for(let e=0;e<n.positionTicks.length;++e){const i=n.positionTicks[e],r=i.line,o=i.ticks*s;this.helper.addLineData(t,n.url,r,o)}}}}appendCPUProfile(e,t){if(!e.lines)return this.appendLegacyCPUProfile(e,t),void this.helper.scheduleUpdate();if(e.samples){for(let i=1;i<e.samples.length;++i){const s=e.lines[i];if(!s)continue;const r=e.nodeByIndex(i);if(!r)continue;const n=Number(r.scriptId)||r.url;if(!n)continue;const o=e.timestamps[i]-e.timestamps[i-1];this.helper.addLineData(t,n,s,o)}this.helper.scheduleUpdate()}}}class pe{helper;constructor(){this.helper=new fe("memory")}static instance(e={forceNew:null}){const{forceNew:t}=e;return me&&!t||(me=new pe),me}reset(){this.helper.reset()}appendHeapProfile(e,t){const i=this.helper;!function e(s){if(s.children.forEach(e),!s.selfSize)return;const r=Number(s.callFrame.scriptId)||s.callFrame.url;if(!r)return;const n=s.callFrame.lineNumber+1;i.addLineData(t,r,n,s.selfSize)}(e.head),i.scheduleUpdate()}}class fe{type;locationPool;updateTimer;lineData;constructor(e){this.type=e,this.locationPool=new h.LiveLocation.LiveLocationPool,this.updateTimer=null,this.reset()}reset(){this.lineData=new Map,this.scheduleUpdate()}addLineData(e,t,i,s){let r=this.lineData.get(e);r||(r=new Map,this.lineData.set(e,r));let n=r.get(t);n||(n=new Map,r.set(t,n)),n.set(i,(n.get(i)||0)+s)}scheduleUpdate(){this.updateTimer||(this.updateTimer=window.setTimeout((()=>{this.updateTimer=null,this.doUpdate()}),0))}async doUpdate(){this.locationPool.disposeAll();const e=new Map,t=[];for(const[i,s]of this.lineData){const r=i?i.model(m.DebuggerModel.DebuggerModel):null;for(const[i,n]of s){const s=g.Workspace.WorkspaceImpl.instance();if(r){const s=h.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance();for(const o of n){const n=o[0]-1,a=o[1],l="string"==typeof i?r.createRawLocationByURL(i,n,0):r.createRawLocationByScriptId(String(i),n,0);l&&t.push(s.rawLocationToUILocation(l).then((t=>{if(t){let i=e.get(t.uiSourceCode);i||(i=new Map,e.set(t.uiSourceCode,i)),i.set(t.lineNumber+1,a)}})))}}else if("string"==typeof i){const t=s.uiSourceCodeForURL(i);t&&e.set(t,n)}}await Promise.all(t);for(const[t,i]of e)t.setDecorationData(this.type,i)}for(const t of g.Workspace.WorkspaceImpl.instance().uiSourceCodes())e.has(t)||t.setDecorationData(this.type,void 0)}}var ve=Object.freeze({__proto__:null,Performance:ge,Memory:pe,Helper:fe});let we;class be{running;sessionId;loadEventCallback;setting;constructor(){this.running=!1,this.sessionId=0,this.loadEventCallback=()=>{},this.setting=s.Settings.Settings.instance().moduleSetting("memory-live-heap-profile"),this.setting.addChangeListener((e=>e.data?this.startProfiling():this.stopProfiling())),this.setting.get()&&this.startProfiling()}static instance(e={forceNew:null}){const{forceNew:t}=e;return we&&!t||(we=new be),we}async run(){}modelAdded(e){e.startSampling(1e4)}modelRemoved(e){}async startProfiling(){if(this.running)return;this.running=!0;const e=this.sessionId;m.TargetManager.TargetManager.instance().observeModels(m.HeapProfilerModel.HeapProfilerModel,this),m.TargetManager.TargetManager.instance().addModelListener(m.ResourceTreeModel.ResourceTreeModel,m.ResourceTreeModel.Events.Load,this.loadEventFired,this);do{const t=m.TargetManager.TargetManager.instance().models(m.HeapProfilerModel.HeapProfilerModel),i=await Promise.all(t.map((e=>e.getSamplingProfile())));if(e!==this.sessionId)break;pe.instance().reset();for(let e=0;e<i.length;++e){const s=i[e];s&&pe.instance().appendHeapProfile(s,t[e].target())}await Promise.race([new Promise((e=>window.setTimeout(e,l.InspectorFrontendHost.isUnderTest()?10:5e3))),new Promise((e=>{this.loadEventCallback=e}))])}while(e===this.sessionId);m.TargetManager.TargetManager.instance().unobserveModels(m.HeapProfilerModel.HeapProfilerModel,this),m.TargetManager.TargetManager.instance().removeModelListener(m.ResourceTreeModel.ResourceTreeModel,m.ResourceTreeModel.Events.Load,this.loadEventFired,this);for(const e of m.TargetManager.TargetManager.instance().models(m.HeapProfilerModel.HeapProfilerModel))e.stopSampling();pe.instance().reset()}stopProfiling(){this.running&&(this.running=!1,this.sessionId++)}loadEventFired(){this.loadEventCallback()}}var Te=Object.freeze({__proto__:null,LiveHeapProfile:be});const ye={lowest:"Lowest",low:"Low",medium:"Medium",high:"High",highest:"Highest"},Ee=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/NetworkPriorities.ts",ye),xe=e.i18n.getLocalizedString.bind(void 0,Ee);const Ce=new Map;const Se=new Map;function Le(){return 0===Se.size&&(Se.set("VeryLow",xe(ye.lowest)),Se.set("Low",xe(ye.low)),Se.set("Medium",xe(ye.medium)),Se.set("High",xe(ye.high)),Se.set("VeryHigh",xe(ye.highest))),Se}const ke=new Map;var Pe=Object.freeze({__proto__:null,uiLabelForNetworkPriority:function(e){return Le().get(e)||""},uiLabelToNetworkPriority:function(e){0===Ce.size&&Le().forEach(((e,t)=>Ce.set(e,t)));const t=Ce.get(e);if(t)return t;throw new Error("Priority not found")},priorityUILabelMap:Le,networkPriorityWeight:function(e){return 0===ke.size&&(ke.set("VeryLow",1),ke.set("Low",2),ke.set("Medium",3),ke.set("High",4),ke.set("VeryHigh",5)),ke.get(e)||0}}),De={cssContent:'.overview-grid-window-selector{position:absolute;top:0;bottom:0;background-color:var(--sys-color-state-ripple-primary);z-index:250;pointer-events:none}.overview-grid-window-resizer{position:absolute;top:0;height:19px;width:10px;margin-left:-5px;background-color:var(--sys-color-tonal-container);border:1px solid var(--sys-color-tonal-outline);z-index:500;border-radius:3px}.overview-grid-window-resizer::before,\n.overview-grid-window-resizer::after{content:"";width:1px;background:var(--sys-color-primary);height:7px;position:absolute;left:2px;top:5px;border-radius:1px}.overview-grid-window-resizer::after{left:5px}.overview-grid-window-resizer:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.overview-grid-cursor-area{position:absolute;left:0;right:0;top:20px;bottom:0;z-index:500;cursor:text}.overview-grid-cursor-position{position:absolute;top:0;bottom:0;width:2px;background-color:var(--sys-color-primary);z-index:500;pointer-events:none;visibility:hidden;overflow:hidden}.window-curtain-left,\n.window-curtain-right{background-color:var(--sys-color-state-ripple-primary);position:absolute;top:0;height:100%;z-index:300;pointer-events:none;border:2px none var(--sys-color-tonal-outline)}.window-curtain-left{left:0;border-right-style:solid}.window-curtain-right{right:0;border-left-style:solid}.create-breadcrumb-button-container{visibility:hidden;opacity:0%;transition:opacity 100ms 250ms;display:flex;position:absolute;top:15px;justify-content:center;z-index:600;left:0;right:0}.is-breadcrumb-button-visible{visibility:visible;opacity:100%}.create-breadcrumb-button-container.with-screenshots{top:20px}.create-breadcrumb-button{display:flex;flex-shrink:0;align-items:center;background:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow-depth-3);border-radius:50px;padding:5px 10px;gap:2px}.create-breadcrumb-button:active{cursor:default}.create-breadcrumb-button:hover{background:var(--sys-color-neutral-container)}@media (forced-colors: active){.overview-grid-cursor-position{forced-color-adjust:none;background-color:Highlight}.window-curtain-left,\n  .window-curtain-right{background-color:transparent;border-color:ButtonText}.overview-grid-window-resizer{background-color:ButtonText}.overview-grid-window-resizer:hover,\n  .overview-grid-window-resizer:active,\n  .overview-grid-window-resizer:focus-visible{forced-color-adjust:none;background-color:Highlight}}\n/*# sourceURL=overviewGrid.css */\n'};const Ie={overviewGridWindow:"Overview grid window",leftResizer:"Left Resizer",rightResizer:"Right Resizer"},Re=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/OverviewGrid.ts",Ie),Me=e.i18n.getLocalizedString.bind(void 0,Re);class He{element;grid;window;constructor(e,t){this.element=document.createElement("div"),this.element.id=e+"-overview-container",this.grid=new N,this.grid.element.id=e+"-overview-grid",this.grid.setScrollTop(0),this.element.appendChild(this.grid.element),this.window=new Ae(this.element,this.grid.dividersLabelBarElement,t)}enableCreateBreadcrumbsButton(){return this.window.enableCreateBreadcrumbsButton()}set showingScreenshots(e){this.window.showingScreenshots=e}clientWidth(){return this.element.clientWidth}updateDividers(e){this.grid.updateDividers(e)}addEventDividers(e){this.grid.addEventDividers(e)}removeEventDividers(){this.grid.removeEventDividers()}reset(){this.window.reset()}windowLeft(){return this.window.windowLeft||0}windowRight(){return this.window.windowRight||0}setWindow(e,t){this.window.setWindow(e,t)}addEventListener(e,t,i){return this.window.addEventListener(e,t,i)}setClickHandler(e){this.window.setClickHandler(e)}zoom(e,t){this.window.zoom(e,t)}setResizeEnabled(e){this.window.setEnabled(e)}}const Oe=14,Be=10;class Ae extends s.ObjectWrapper.ObjectWrapper{parentElement;calculator;leftResizeElement;rightResizeElement;leftCurtainElement;rightCurtainElement;breadcrumbButtonContainerElement;createBreadcrumbButton;curtainsRange;breadcrumbZoomIcon;overviewWindowSelector;offsetLeft;dragStartPoint;dragStartLeft;dragStartRight;windowLeft;windowRight;enabled;clickHandler;resizerParentOffsetLeft;#Ie=!1;#Re=!1;constructor(e,t,s){super(),this.parentElement=e,this.parentElement.classList.add("parent-element"),a.ARIAUtils.markAsGroup(this.parentElement),this.calculator=s,a.ARIAUtils.setLabel(this.parentElement,Me(Ie.overviewGridWindow)),a.UIUtils.installDragHandle(this.parentElement,this.startWindowSelectorDragging.bind(this),this.windowSelectorDragging.bind(this),this.endWindowSelectorDragging.bind(this),"text",null),t&&a.UIUtils.installDragHandle(t,this.startWindowDragging.bind(this),this.windowDragging.bind(this),null,"-webkit-grabbing","-webkit-grab"),this.parentElement.addEventListener("wheel",this.onMouseWheel.bind(this),!0),this.parentElement.addEventListener("dblclick",this.resizeWindowMaximum.bind(this),!0),i.ThemeSupport.instance().appendStyle(this.parentElement,De),this.leftResizeElement=e.createChild("div","overview-grid-window-resizer"),a.UIUtils.installDragHandle(this.leftResizeElement,this.resizerElementStartDragging.bind(this),this.leftResizeElementDragging.bind(this),null,"ew-resize"),this.rightResizeElement=e.createChild("div","overview-grid-window-resizer"),a.UIUtils.installDragHandle(this.rightResizeElement,this.resizerElementStartDragging.bind(this),this.rightResizeElementDragging.bind(this),null,"ew-resize"),a.ARIAUtils.setLabel(this.leftResizeElement,Me(Ie.leftResizer)),a.ARIAUtils.markAsSlider(this.leftResizeElement);this.leftResizeElement.addEventListener("keydown",(e=>this.handleKeyboardResizing(e,!1))),this.leftResizeElement.addEventListener("click",this.onResizerClicked),a.ARIAUtils.setLabel(this.rightResizeElement,Me(Ie.rightResizer)),a.ARIAUtils.markAsSlider(this.rightResizeElement);this.rightResizeElement.addEventListener("keydown",(e=>this.handleKeyboardResizing(e,!0))),this.rightResizeElement.addEventListener("focus",this.onRightResizeElementFocused.bind(this)),this.rightResizeElement.addEventListener("click",this.onResizerClicked),this.leftCurtainElement=e.createChild("div","window-curtain-left"),this.rightCurtainElement=e.createChild("div","window-curtain-right"),this.breadcrumbButtonContainerElement=e.createChild("div","create-breadcrumb-button-container"),this.createBreadcrumbButton=this.breadcrumbButtonContainerElement.createChild("div","create-breadcrumb-button"),this.createBreadcrumbButton.setAttribute("jslog",`${u.action("timeline.create-breadcrumb").track({click:!0})}`),this.reset()}enableCreateBreadcrumbsButton(){return this.curtainsRange=this.createBreadcrumbButton.createChild("div"),this.breadcrumbZoomIcon=new p.Icon.Icon,this.breadcrumbZoomIcon.data={iconName:"zoom-in",color:"var(--icon-default)",width:"20px",height:"20px"},this.createBreadcrumbButton.appendChild(this.breadcrumbZoomIcon),this.createBreadcrumbButton.addEventListener("click",(()=>{this.#Me()})),this.#Ie=!0,this.#He(this.parentElement),this.#He(this.rightResizeElement),this.#He(this.leftResizeElement),this.breadcrumbButtonContainerElement}set showingScreenshots(e){this.breadcrumbButtonContainerElement.classList.toggle("with-screenshots",e)}#He(e){this.#Ie&&(e.addEventListener("mouseover",(()=>{(this.windowLeft??0)<=0&&(this.windowRight??1)>=1?(this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!1),this.#Re=!1):(this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!0),this.#Re=!0)})),e.addEventListener("mouseout",(()=>{this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!1),this.#Re=!1})))}onResizerClicked(e){e.target&&e.target.focus()}onRightResizeElementFocused(){this.parentElement.scrollLeft=0}reset(){this.windowLeft=0,this.windowRight=1,this.setEnabled(!0),this.updateCurtains()}setEnabled(e){this.enabled=e,this.rightResizeElement.tabIndex=e?0:-1,this.leftResizeElement.tabIndex=e?0:-1}setClickHandler(e){this.clickHandler=e}resizerElementStartDragging(e){const t=e,i=e.target;return!!this.enabled&&(this.resizerParentOffsetLeft=t.pageX-t.offsetX-i.offsetLeft,e.stopPropagation(),!0)}leftResizeElementDragging(e){const t=e;this.resizeWindowLeft(t.pageX-(this.resizerParentOffsetLeft||0)),e.preventDefault()}rightResizeElementDragging(e){const t=e;this.resizeWindowRight(t.pageX-(this.resizerParentOffsetLeft||0)),e.preventDefault()}handleKeyboardResizing(e,t){const i=e,s=e.target;let r=!1;if("ArrowLeft"===i.key||"ArrowRight"===i.key){"ArrowRight"===i.key&&(r=!0);const n=this.getNewResizerPosition(s.offsetLeft,r,i.ctrlKey);t?this.resizeWindowRight(n):this.resizeWindowLeft(n),e.consume(!0)}}getNewResizerPosition(e,t,i){let s,r=i?10:2;r=t?r:-Math.abs(r);return s=e+5+r,t&&s<Be?s=Be:!t&&s>this.parentElement.clientWidth-Be&&(s=this.parentElement.clientWidth-Be),s}startWindowSelectorDragging(e){if(!this.enabled)return!1;const t=e;this.offsetLeft=this.parentElement.getBoundingClientRect().left;const i=t.x-this.offsetLeft;return this.overviewWindowSelector=new Ge(this.parentElement,i),!0}windowSelectorDragging(e){if(this.#Re=!0,!this.overviewWindowSelector)return;const t=e;this.overviewWindowSelector.updatePosition(t.x-this.offsetLeft),e.preventDefault()}endWindowSelectorDragging(e){if(!this.overviewWindowSelector)return;const t=e,i=this.overviewWindowSelector.close(t.x-this.offsetLeft);if(this.#Ie&&i.start===i.end)return;delete this.overviewWindowSelector;if(i.end-i.start<3){if(this.clickHandler&&this.clickHandler.call(null,e))return;const t=i.end;i.start=Math.max(0,t-7),i.end=Math.min(this.parentElement.clientWidth,t+7)}else i.end-i.start<Oe&&(this.parentElement.clientWidth-i.end>Oe?i.end=i.start+Oe:i.start=i.end-Oe);this.setWindowPosition(i.start,i.end)}startWindowDragging(e){const t=e;return this.dragStartPoint=t.pageX,this.dragStartLeft=this.windowLeft||0,this.dragStartRight=this.windowRight||0,e.stopPropagation(),!0}windowDragging(e){this.#Re=!0,this.#Ie&&this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!0);const t=e;t.preventDefault();let i=(t.pageX-this.dragStartPoint)/this.parentElement.clientWidth;this.dragStartLeft+i<0&&(i=-this.dragStartLeft),this.dragStartRight+i>1&&(i=1-this.dragStartRight),this.setWindow(this.dragStartLeft+i,this.dragStartRight+i)}resizeWindowLeft(e){this.#Re=!0,e<Be?e=0:e>this.rightResizeElement.offsetLeft-4&&(e=this.rightResizeElement.offsetLeft-4),this.setWindowPosition(e,null)}resizeWindowRight(e){this.#Re=!0,e>this.parentElement.clientWidth-Be?e=this.parentElement.clientWidth:e<this.leftResizeElement.offsetLeft+Oe&&(e=this.leftResizeElement.offsetLeft+Oe),this.setWindowPosition(null,e)}resizeWindowMaximum(){this.setWindowPosition(0,this.parentElement.clientWidth)}getRawSliderValue(e){if(!this.calculator)throw new Error("No calculator to calculate boundaries");const t=this.calculator.minimumBoundary(),i=this.calculator.maximumBoundary()-t;return e?t+i*(this.windowLeft||0):t+i*(this.windowRight||0)}updateResizeElementPositionValue(e,t){const i=e.toFixed(2),s=t.toFixed(2);a.ARIAUtils.setAriaValueNow(this.leftResizeElement,i),a.ARIAUtils.setAriaValueNow(this.rightResizeElement,s);const r=Number(s)-.5,n=Number(i)+.5;a.ARIAUtils.setAriaValueMinMax(this.leftResizeElement,"0",r.toString()),a.ARIAUtils.setAriaValueMinMax(this.rightResizeElement,n.toString(),"100")}updateResizeElementPositionLabels(){if(!this.calculator)return;const e=this.calculator.formatValue(d.Types.Timing.MilliSeconds(this.getRawSliderValue(!0))),t=this.calculator.formatValue(d.Types.Timing.MilliSeconds(this.getRawSliderValue(!1)));a.ARIAUtils.setAriaValueText(this.leftResizeElement,String(e)),a.ARIAUtils.setAriaValueText(this.rightResizeElement,String(t))}updateResizeElementPercentageLabels(e,t){a.ARIAUtils.setAriaValueText(this.leftResizeElement,e),a.ARIAUtils.setAriaValueText(this.rightResizeElement,t)}calculateWindowPosition(){return{rawStartValue:Number(this.getRawSliderValue(!0)),rawEndValue:Number(this.getRawSliderValue(!1))}}setWindow(e,t){this.windowLeft=e,this.windowRight=t,this.updateCurtains(),this.calculator&&this.dispatchEventToListeners("WindowChangedWithPosition",this.calculateWindowPosition()),this.dispatchEventToListeners("WindowChanged"),this.#Oe(e,t)}#Oe(e,t){this.#Ie&&(t>=1&&e<=0||!this.#Re?this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!1):this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!0))}#Me(){this.dispatchEventToListeners("BreadcrumbAdded",this.calculateWindowPosition())}updateCurtains(){const e=this.windowLeft||0,t=this.windowRight||0;let i=e,s=t;const r=s-i;if(0!==this.parentElement.clientWidth){const n=r*this.parentElement.clientWidth,o=7;if(n<o){const a=o/n;i=(t+e-r*a)/2,s=(t+e+r*a)/2}}const n=100*i,o=100*s,a=100-100*s,l=n+"%",h=o+"%";this.leftResizeElement.style.left=l,this.rightResizeElement.style.left=h,this.leftCurtainElement.style.width=l,this.rightCurtainElement.style.width=a+"%",this.breadcrumbButtonContainerElement.style.marginLeft=n>0?n+"%":"0%",this.breadcrumbButtonContainerElement.style.marginRight=a>0?a+"%":"0%",this.curtainsRange&&(this.curtainsRange.textContent=this.getWindowRange().toFixed(0)+" ms"),this.updateResizeElementPositionValue(n,o),this.calculator?this.updateResizeElementPositionLabels():this.updateResizeElementPercentageLabels(l,h),this.toggleZoomButtonDisplay()}toggleZoomButtonDisplay(){this.breadcrumbZoomIcon&&(this.getWindowRange()<4.5?(this.breadcrumbZoomIcon.style.display="none",this.breadcrumbButtonContainerElement.style.pointerEvents="none"):(this.breadcrumbZoomIcon.style.display="flex",this.breadcrumbButtonContainerElement.style.pointerEvents="auto"))}getWindowRange(){if(!this.calculator)throw new Error("No calculator to calculate window range");const e=this.windowLeft&&this.windowLeft>0?this.windowLeft:0,t=this.windowRight&&this.windowRight<1?this.windowRight:1;return this.calculator.boundarySpan()*(t-e)}setWindowPosition(e,t){const i=this.parentElement.clientWidth,s="number"==typeof e?e/i:this.windowLeft,r="number"==typeof t?t/i:this.windowRight;this.setWindow(s||0,r||0)}onMouseWheel(e){const t=e;if(this.enabled){if(t.deltaY){const e=1.1,i=1/53,s=t.offsetX/this.parentElement.clientWidth;this.zoom(Math.pow(e,t.deltaY*i),s)}if(t.deltaX){let e=Math.round(.3*t.deltaX);const i=this.leftResizeElement.offsetLeft+5,s=this.rightResizeElement.offsetLeft+5;i-e<0&&(e=i),s-e>this.parentElement.clientWidth&&(e=s-this.parentElement.clientWidth),this.setWindowPosition(i-e,s-e),t.preventDefault()}}}zoom(e,t){let i=this.windowLeft||0,s=this.windowRight||0;const n=s-i;let o=e*n;o>1&&(o=1,e=o/n),i=t+(i-t)*e,i=r.NumberUtilities.clamp(i,0,1-o),s=t+(s-t)*e,s=r.NumberUtilities.clamp(s,o,1),this.setWindow(i,s)}}class Ge{startPosition;width;windowSelector;constructor(e,t){this.startPosition=t,this.width=e.offsetWidth,this.windowSelector=document.createElement("div"),this.windowSelector.className="overview-grid-window-selector",this.windowSelector.style.left=this.startPosition+"px",this.windowSelector.style.right=this.width-this.startPosition+"px",e.appendChild(this.windowSelector)}close(e){return e=Math.max(0,Math.min(e,this.width)),this.windowSelector.remove(),this.startPosition<e?{start:this.startPosition,end:e}:{start:e,end:this.startPosition}}updatePosition(e){(e=Math.max(0,Math.min(e,this.width)))<this.startPosition?(this.windowSelector.style.left=e+"px",this.windowSelector.style.right=this.width-this.startPosition+"px"):(this.windowSelector.style.left=this.startPosition+"px",this.windowSelector.style.right=this.width-e+"px")}}var Fe=Object.freeze({__proto__:null,OverviewGrid:He,MinSelectableSize:Oe,WindowScrollSpeedFactor:.3,ResizerOffset:5,OffsetFromWindowEnds:Be,Window:Ae,WindowSelector:Ge});const ze=new CSSStyleSheet;ze.replaceSync(".root{align-items:flex-start;display:flex;min-width:fit-content;white-space:nowrap}.chart-root{position:relative;overflow:hidden}.pie-chart-foreground{position:absolute;width:100%;height:100%;z-index:10;top:0;display:flex;pointer-events:none}.pie-chart-total{margin:auto;padding:2px 5px;pointer-events:auto}:focus{outline-width:0}.pie-chart-total.selected{font-weight:bold}.chart-root .slice.selected{stroke:var(--sys-color-primary);stroke-opacity:1;stroke-width:0.04;stroke-linecap:round;stroke-linejoin:round}.pie-chart-legend{margin-left:30px}.pie-chart-legend-row{margin:5px 2px 5px auto;padding-right:25px}.pie-chart-legend-row.selected{font-weight:bold}.pie-chart-legend-row:focus-visible{box-shadow:0 0 0 2px var(--sys-color-state-focus-ring)!important}.pie-chart-swatch{display:inline-block;width:11px;height:11px;margin:0 6px;top:1px;position:relative;border:1px solid var(--sys-color-neutral-outline)}.pie-chart-name{display:inline-block}.pie-chart-size{display:inline-block;text-align:right;width:70px}@media (forced-colors: active){.pie-chart-swatch{forced-color-adjust:none;border-color:ButtonText}.pie-chart-total{forced-color-adjust:none;background-color:canvas}}\n/*# sourceURL=pieChart.css */\n");const{render:We,html:Ne,svg:Ue}=f,_e={total:"Total"},Ve=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/PieChart.ts",_e),Xe=e.i18n.getLocalizedString.bind(void 0,Ve);class $e extends HTMLElement{static litTagName=f.literal`devtools-perf-piechart`;shadow=this.attachShadow({mode:"open"});chartName="";size=0;formatter=e=>String(e);showLegend=!1;total=0;slices=[];totalSelected=!0;sliceSelected=-1;innerR=.618;lastAngle=-Math.PI/2;connectedCallback(){this.shadow.adoptedStyleSheets=[ze]}set data(e){this.chartName=e.chartName,this.size=e.size,this.formatter=e.formatter,this.showLegend=e.showLegend,this.total=e.total,this.slices=e.slices,this.render()}render(){this.lastAngle=-Math.PI/2;const e=Ne`
      <div class="root" role="group" @keydown=${this.onKeyDown} aria-label=${this.chartName}
          jslog=${u.pieChart().track({keydown:"ArrowUp|ArrowDown"})}>
        <div class="chart-root" style="width: ${this.size}px; height: ${this.size}px;">
          ${Ue`
          <svg>
          <g transform="scale(${this.size/2}) translate(1, 1) scale(0.99, 0.99)">
            <circle r="1" stroke="hsl(0, 0%, 80%)" fill="transparent" stroke-width=${1/this.size}></circle>
            <circle r=${this.innerR} stroke="hsl(0, 0%, 80%)" fill="transparent" stroke-width=${1/this.size}></circle>
            ${this.slices.map(((e,t)=>{const i=this.sliceSelected===t,s=i&&!this.showLegend?"0":"-1";return Ue`<path class="slice ${i?"selected":""}"
                  jslog=${u.pieChartSlice().track({click:!0})}
                  @click=${this.onSliceClicked(t)} tabIndex=${s}
                  fill=${e.color} d=${this.getPathStringForSlice(e)}
                  aria-label=${e.title} id=${i?"selectedSlice":""}></path>`}))}
            <!-- This is so that the selected slice is re-drawn on top, to avoid re-ordering slices
            just to render them properly. -->
            <use href="#selectedSlice" />
            </g>
          </svg>
          `}
          <div class="pie-chart-foreground">
            <div class="pie-chart-total ${this.totalSelected?"selected":""}" @click=${this.selectTotal}
                jslog=${u.pieChartTotal("select-total").track({click:!0})}
                tabIndex=${this.totalSelected&&!this.showLegend?"1":"-1"}>
              ${this.total?this.formatter(this.total):""}
            </div>
          </div>
        </div>
        ${this.showLegend?Ne`
        <div class="pie-chart-legend" jslog=${u.section("legend")}>
          ${this.slices.map(((e,t)=>{const i=this.sliceSelected===t;return Ne`
              <div class="pie-chart-legend-row ${i?"selected":""}"
                  jslog=${u.pieChartSlice().track({click:!0})}
                  @click=${this.onSliceClicked(t)} tabIndex=${i?"0":"-1"}>
                <div class="pie-chart-size">${this.formatter(e.value)}</div>
                <div class="pie-chart-swatch" style="background-color: ${e.color};"></div>
                <div class="pie-chart-name">${e.title}</div>
              </div>`}))}
          <div class="pie-chart-legend-row ${this.totalSelected?"selected":""}"
              jslog=${u.pieChartTotal("select-total").track({click:!0})}
              @click=${this.selectTotal} tabIndex=${this.totalSelected?"0":"-1"}>
            <div class="pie-chart-size">${this.formatter(this.total)}</div>
            <div class="pie-chart-swatch"></div>
            <div class="pie-chart-name">${Xe(_e.total)}</div>
          </div>
        </div>
        `:""}
    `;We(e,this.shadow,{host:this})}onSliceClicked(e){return()=>{this.selectSlice(e)}}selectSlice(e){this.totalSelected=!1,this.sliceSelected=e,this.render()}selectTotal(){this.totalSelected=!0,this.sliceSelected=-1,this.render()}selectAndFocusTotal(){this.selectTotal();const e=this.shadow.querySelector(".pie-chart-legend > :last-child");e&&e.focus()}selectAndFocusSlice(e){this.selectSlice(e);const t=this.shadow.querySelector(`.pie-chart-legend > :nth-child(${e+1})`);t&&t.focus()}focusNextElement(){this.totalSelected?this.selectAndFocusSlice(0):this.sliceSelected===this.slices.length-1?this.selectAndFocusTotal():this.selectAndFocusSlice(this.sliceSelected+1)}focusPreviousElement(){this.totalSelected?this.selectAndFocusSlice(this.slices.length-1):0===this.sliceSelected?this.selectAndFocusTotal():this.selectAndFocusSlice(this.sliceSelected-1)}onKeyDown(e){let t=!1;"ArrowDown"===e.key?(this.focusNextElement(),t=!0):"ArrowUp"===e.key&&(this.focusPreviousElement(),t=!0),t&&(e.stopImmediatePropagation(),e.preventDefault())}getPathStringForSlice(e){let t=e.value/this.total*2*Math.PI;if(!isFinite(t))return;t=Math.min(t,2*Math.PI*.9999);const i=Math.cos(this.lastAngle),s=Math.sin(this.lastAngle);this.lastAngle+=t;const r=Math.cos(this.lastAngle),n=Math.sin(this.lastAngle),o=this.innerR,a=r*o,l=n*o,h=i*o,d=s*o,c=t>Math.PI?1:0;return`M${i},${s} A1,1,0,${c},1,${r},${n} L${a},${l} A${o},${o},0,${c},0,${h},${d} Z`}}customElements.define("devtools-perf-piechart",$e);var Ke=Object.freeze({__proto__:null,PieChart:$e});class je{#Be=d.Types.Timing.MilliSeconds(0);#Ae=d.Types.Timing.MilliSeconds(100);workingArea;navStartTimes;computePosition(e){return(e-this.#Be)/this.boundarySpan()*this.workingArea}positionToTime(e){return e/this.workingArea*this.boundarySpan()+this.#Be}setBounds(e,t){this.#Be=e,this.#Ae=t}setNavStartTimes(e){this.navStartTimes=e}setDisplayWidth(e){this.workingArea=e}reset(){this.setBounds(d.Types.Timing.MilliSeconds(0),d.Types.Timing.MilliSeconds(100))}formatValue(t,i){if(this.navStartTimes)for(let e=this.navStartTimes.length-1;e>=0;e--){const i=d.Helpers.Timing.microSecondsToMilliseconds(this.navStartTimes[e].ts);if(t>i){t-=i-this.zeroTime();break}}return e.TimeUtilities.preciseMillisToString(t-this.zeroTime(),i)}maximumBoundary(){return this.#Ae}minimumBoundary(){return this.#Be}zeroTime(){return this.#Be}boundarySpan(){return d.Types.Timing.MilliSeconds(this.#Ae-this.#Be)}}var Ye=Object.freeze({__proto__:null,TimelineOverviewCalculator:je});const Ze=new CSSStyleSheet;Ze.replaceSync(".overview-info:not(:empty){display:flex;background:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow);padding:3px}.overview-info .frame .time{display:none}.overview-info .frame .thumbnail img{max-width:50vw;max-height:50vh}\n/*# sourceURL=timelineOverviewInfo.css */\n");class qe extends(s.ObjectWrapper.eventMixin(a.Widget.VBox)){overviewCalculator;overviewGrid;cursorArea;cursorElement;overviewControls;markers;overviewInfo;updateThrottler;cursorEnabled;cursorPosition;lastWidth;windowStartTime;windowEndTime;muteOnWindowChanged;constructor(e){super(),this.element.id=e+"-overview-pane",this.overviewCalculator=new je,this.overviewGrid=new He(e,this.overviewCalculator),this.element.appendChild(this.overviewGrid.element),this.cursorArea=this.overviewGrid.element.createChild("div","overview-grid-cursor-area"),this.cursorElement=this.overviewGrid.element.createChild("div","overview-grid-cursor-position"),this.cursorArea.addEventListener("mousemove",this.onMouseMove.bind(this),!0),this.cursorArea.addEventListener("mouseleave",this.hideCursor.bind(this),!0),this.overviewGrid.setResizeEnabled(!1),this.overviewGrid.addEventListener("WindowChangedWithPosition",this.onWindowChanged,this),this.overviewGrid.addEventListener("BreadcrumbAdded",this.onBreadcrumbAdded,this),this.overviewGrid.setClickHandler(this.onClick.bind(this)),this.overviewControls=[],this.markers=new Map,this.overviewInfo=new Je(this.cursorElement),this.updateThrottler=new s.Throttler.Throttler(100),this.cursorEnabled=!1,this.cursorPosition=0,this.lastWidth=0,this.windowStartTime=0,this.windowEndTime=1/0,this.muteOnWindowChanged=!1}enableCreateBreadcrumbsButton(){const e=this.overviewGrid.enableCreateBreadcrumbsButton();e.addEventListener("mousemove",this.onMouseMove.bind(this),!0),e.addEventListener("mouseleave",this.hideCursor.bind(this),!0)}onMouseMove(e){if(!this.cursorEnabled)return;const t=e,i=e.target.getBoundingClientRect().left-this.cursorArea.getBoundingClientRect().left;this.cursorPosition=t.offsetX+i,this.cursorElement.style.left=this.cursorPosition+"px",this.cursorElement.style.visibility="visible",this.overviewInfo.setContent(this.buildOverviewInfo())}async buildOverviewInfo(){const e=this.element.ownerDocument,t=this.cursorPosition,i=await Promise.all(this.overviewControls.map((e=>e.overviewInfoPromise(t)))),s=e.createDocumentFragment(),r=i.filter((e=>null!==e));return s.append(...r),s}hideCursor(){this.cursorElement.style.visibility="hidden",this.overviewInfo.hide()}wasShown(){this.update()}willHide(){this.overviewInfo.hide()}onResize(){const e=this.element.offsetWidth;e!==this.lastWidth&&(this.lastWidth=e,this.scheduleUpdate())}setOverviewControls(e){for(let e=0;e<this.overviewControls.length;++e)this.overviewControls[e].dispose();for(let t=0;t<e.length;++t)e[t].setCalculator(this.overviewCalculator),e[t].show(this.overviewGrid.element);this.overviewControls=e,this.update()}set showingScreenshots(e){this.overviewGrid.showingScreenshots=e}setBounds(e,t){e===this.overviewCalculator.minimumBoundary()&&t===this.overviewCalculator.maximumBoundary()||(this.overviewCalculator.setBounds(e,t),this.overviewGrid.setResizeEnabled(!0),this.cursorEnabled=!0,this.scheduleUpdate(e,t))}setNavStartTimes(e){this.overviewCalculator.setNavStartTimes(e)}scheduleUpdate(e,t){this.updateThrottler.schedule((async()=>{this.update(e,t)}))}update(e,t){if(this.isShowing()){this.overviewCalculator.setDisplayWidth(this.overviewGrid.clientWidth());for(let i=0;i<this.overviewControls.length;++i)this.overviewControls[i].update(e,t);this.overviewGrid.updateDividers(this.overviewCalculator),this.updateMarkers(),this.updateWindow()}}setMarkers(e){this.markers=e}getMarkers(){return this.markers}updateMarkers(){const e=new Map;for(const t of this.markers.keys()){const i=this.markers.get(t),s=Math.round(this.overviewCalculator.computePosition(d.Types.Timing.MilliSeconds(t)));e.has(s)||(e.set(s,i),i.style.left=s+"px")}this.overviewGrid.removeEventDividers(),this.overviewGrid.addEventDividers([...e.values()])}reset(){this.windowStartTime=0,this.windowEndTime=1/0,this.overviewCalculator.reset(),this.overviewGrid.reset(),this.overviewGrid.setResizeEnabled(!1),this.cursorEnabled=!1,this.hideCursor(),this.markers=new Map;for(const e of this.overviewControls)e.reset();this.overviewInfo.hide(),this.scheduleUpdate()}onClick(e){return this.overviewControls.some((t=>t.onClick(e)))}onBreadcrumbAdded(){this.dispatchEventToListeners("OverviewPaneBreadcrumbAdded",{startTime:d.Types.Timing.MilliSeconds(this.windowStartTime),endTime:d.Types.Timing.MilliSeconds(this.windowEndTime)})}onWindowChanged(e){if(this.muteOnWindowChanged)return;if(!this.overviewControls.length)return;this.windowStartTime=e.data.rawStartValue===this.overviewCalculator.minimumBoundary()?0:e.data.rawStartValue,this.windowEndTime=e.data.rawEndValue===this.overviewCalculator.maximumBoundary()?1/0:e.data.rawEndValue;const t={startTime:d.Types.Timing.MilliSeconds(this.windowStartTime),endTime:d.Types.Timing.MilliSeconds(this.windowEndTime)};this.dispatchEventToListeners("OverviewPaneWindowChanged",t)}setWindowTimes(e,t){e===this.windowStartTime&&t===this.windowEndTime||(this.windowStartTime=e,this.windowEndTime=t,this.updateWindow(),this.dispatchEventToListeners("OverviewPaneWindowChanged",{startTime:d.Types.Timing.MilliSeconds(e),endTime:d.Types.Timing.MilliSeconds(t)}))}updateWindow(){if(!this.overviewControls.length)return;const e=this.overviewCalculator.minimumBoundary(),t=this.overviewCalculator.maximumBoundary()-e,i=e>0,s=i&&this.windowStartTime?Math.min((this.windowStartTime-e)/t,1):0,r=i&&this.windowEndTime<1/0?(this.windowEndTime-e)/t:1;this.muteOnWindowChanged=!0,this.overviewGrid.setWindow(s,r),this.muteOnWindowChanged=!1}}class Qe extends a.Widget.VBox{calculatorInternal;canvas;contextInternal;constructor(){super(),this.calculatorInternal=null,this.canvas=this.element.createChild("canvas","fill"),this.contextInternal=this.canvas.getContext("2d")}width(){return this.canvas.width}height(){return this.canvas.height}context(){if(!this.contextInternal)throw new Error("Unable to retrieve canvas context");return this.contextInternal}calculator(){return this.calculatorInternal}update(){throw new Error("Not implemented")}dispose(){this.detach()}reset(){}async overviewInfoPromise(e){return null}setCalculator(e){this.calculatorInternal=e}onClick(e){return!1}resetCanvas(){this.element.clientWidth&&this.setCanvasSize(this.element.clientWidth,this.element.clientHeight)}setCanvasSize(e,t){this.canvas.width=e*window.devicePixelRatio,this.canvas.height=t*window.devicePixelRatio}}class Je{anchorElement;glassPane;visible;element;constructor(e){this.anchorElement=e,this.glassPane=new a.GlassPane.GlassPane,this.glassPane.setPointerEventsBehavior("PierceContents"),this.glassPane.setMarginBehavior("Arrow"),this.glassPane.setSizeBehavior("MeasureContent"),this.visible=!1,this.element=a.UIUtils.createShadowRootWithCoreStyles(this.glassPane.contentElement,{cssFile:[Ze],delegatesFocus:void 0}).createChild("div","overview-info")}async setContent(e){this.visible=!0;const t=await e;this.visible&&(this.element.removeChildren(),this.element.appendChild(t),this.glassPane.setContentAnchorBox(this.anchorElement.boxInWindow()),this.glassPane.isShowing()||this.glassPane.show(this.anchorElement.ownerDocument))}hide(){this.visible=!1,this.glassPane.hide()}}var et=Object.freeze({__proto__:null,TimelineOverviewPane:qe,TimelineOverviewBase:Qe,OverviewInfo:Je});export{C as BrickBreaker,se as ChartViewport,de as FilmStripView,ee as FlameChart,D as Font,ce as GCActionDelegate,ve as LineLevelProfile,Te as LiveHeapProfile,Pe as NetworkPriorities,Fe as OverviewGrid,Ke as PieChart,U as TimelineGrid,Ye as TimelineOverviewCalculator,et as TimelineOverviewPane};
