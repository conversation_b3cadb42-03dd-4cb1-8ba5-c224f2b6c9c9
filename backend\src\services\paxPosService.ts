/**
 * PAX POS Service
 * Handles integration with PAX A920 terminal hardware
 * Provides web-compatible interface for cloud deployment
 */

import { logger } from '../config/logger';
import { env } from '../config/env';

export interface PaxPaymentRequest {
  amount: number; // Amount in cents
  tenderType?: 'CREDIT' | 'DEBIT' | 'CASH';
  transType?: 'SALE' | 'REFUND' | 'VOID';
  referenceNumber?: string;
  items?: Array<{
    name: string;
    price: number;
    quantity: number;
  }>;
}

export interface PaxPaymentResponse {
  success: boolean;
  transactionId?: string;
  authCode?: string;
  resultCode?: string;
  message?: string;
  receiptData?: {
    customer: string;
    merchant: string;
  };
  cardInfo?: {
    last4?: string;
    brand?: string;
    entryMethod?: string;
  };
}

export interface PaxTerminalStatus {
  connected: boolean;
  ip?: string;
  port?: number;
  model?: string;
  serialNumber?: string;
  capabilities?: {
    contactless: boolean;
    emv: boolean;
    magneticStripe: boolean;
    printer: boolean;
  };
  lastResponse?: any;
}

export interface PaxConfiguration {
  terminal: {
    ip: string;
    port: number;
    timeout: number;
  };
  merchant: {
    id: string;
    name: string;
  };
  features: {
    receiptEnabled: boolean;
    signatureRequired: boolean;
  };
}

class PaxPosService {
  private isSimulationMode: boolean;
  private terminalConfig: PaxConfiguration;

  constructor() {
    // Determine if we're running in simulation mode
    // In cloud environments like Render, we can't access hardware directly
    this.isSimulationMode = this.shouldUseSimulation();
    
    this.terminalConfig = {
      terminal: {
        ip: env.PAX_TERMINAL_IP || '*************',
        port: parseInt(env.PAX_TERMINAL_PORT || '8080'),
        timeout: parseInt(env.PAX_TIMEOUT || '30000'),
      },
      merchant: {
        id: env.PAX_MERCHANT_ID || 'MERCHANT001',
        name: env.PAX_MERCHANT_NAME || 'PaxSoft POS',
      },
      features: {
        receiptEnabled: env.PAX_RECEIPT_ENABLED !== 'false',
        signatureRequired: env.PAX_SIGNATURE_REQUIRED === 'true',
      },
    };

    logger.info('PAX POS Service initialized', {
      simulationMode: this.isSimulationMode,
      terminalIp: this.terminalConfig.terminal.ip,
    });
  }

  /**
   * Determine if we should use simulation mode
   */
  private shouldUseSimulation(): boolean {
    // Use simulation in development or cloud environments
    if (env.NODE_ENV === 'development') return true;
    if (env.PAX_SIMULATION_MODE === 'true') return true;
    
    // Check if we're running in a cloud environment (Render, Heroku, etc.)
    const cloudIndicators = [
      'RENDER',
      'HEROKU',
      'VERCEL',
      'NETLIFY',
      'AWS_LAMBDA_FUNCTION_NAME',
      'GOOGLE_CLOUD_PROJECT',
    ];
    
    return cloudIndicators.some(indicator => process.env[indicator]);
  }

  /**
   * Process a payment transaction
   */
  async processPayment(request: PaxPaymentRequest): Promise<PaxPaymentResponse> {
    try {
      logger.info('Processing PAX payment', {
        amount: request.amount,
        tenderType: request.tenderType,
        simulationMode: this.isSimulationMode,
      });

      if (this.isSimulationMode) {
        return this.simulatePayment(request);
      }

      // In production with actual hardware, this would call the PAX integration
      return this.processHardwarePayment(request);

    } catch (error) {
      logger.error('PAX payment processing error', { error, request });
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Payment processing failed',
      };
    }
  }

  /**
   * Get terminal status
   */
  async getTerminalStatus(): Promise<PaxTerminalStatus> {
    try {
      if (this.isSimulationMode) {
        return this.getSimulatedStatus();
      }

      // In production with actual hardware, this would check real terminal status
      return this.getHardwareStatus();

    } catch (error) {
      logger.error('PAX status check error', { error });
      return {
        connected: false,
        model: 'PAX A920 (Error)',
      };
    }
  }

  /**
   * Test terminal connection
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      if (this.isSimulationMode) {
        // Simulate connection test
        await new Promise(resolve => setTimeout(resolve, 1000));
        return {
          success: true,
          message: 'Simulation mode - connection test successful',
        };
      }

      // In production, this would test actual hardware connection
      return this.testHardwareConnection();

    } catch (error) {
      logger.error('PAX connection test error', { error });
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Connection test failed',
      };
    }
  }

  /**
   * Cancel current transaction
   */
  async cancelTransaction(): Promise<{ success: boolean; message: string }> {
    try {
      if (this.isSimulationMode) {
        return {
          success: true,
          message: 'Simulation mode - transaction cancelled',
        };
      }

      // In production, this would cancel the actual transaction
      return this.cancelHardwareTransaction();

    } catch (error) {
      logger.error('PAX cancel transaction error', { error });
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Cancel failed',
      };
    }
  }

  /**
   * Get terminal configuration
   */
  getConfiguration(): PaxConfiguration {
    return this.terminalConfig;
  }

  /**
   * Simulate payment for development/cloud environments
   */
  private async simulatePayment(request: PaxPaymentRequest): Promise<PaxPaymentResponse> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000));

    // Simulate random success/failure for testing
    const success = Math.random() > 0.1; // 90% success rate

    if (success) {
      const transactionId = `SIM${Date.now()}`;
      const authCode = Math.floor(Math.random() * 999999).toString().padStart(6, '0');

      return {
        success: true,
        transactionId,
        authCode,
        resultCode: '00',
        message: 'Simulated payment successful',
        receiptData: {
          customer: this.generateSimulatedReceipt(request, transactionId, authCode, true),
          merchant: this.generateSimulatedReceipt(request, transactionId, authCode, false),
        },
        cardInfo: {
          last4: '1234',
          brand: 'VISA',
          entryMethod: 'CHIP',
        },
      };
    } else {
      return {
        success: false,
        message: 'Simulated payment declined - insufficient funds',
      };
    }
  }

  /**
   * Get simulated terminal status
   */
  private getSimulatedStatus(): PaxTerminalStatus {
    return {
      connected: true, // Always connected in simulation
      ip: this.terminalConfig.terminal.ip,
      port: this.terminalConfig.terminal.port,
      model: 'PAX A920 (Simulation)',
      serialNumber: 'SIM123456789',
      capabilities: {
        contactless: true,
        emv: true,
        magneticStripe: true,
        printer: true,
      },
    };
  }

  /**
   * Generate simulated receipt
   */
  private generateSimulatedReceipt(
    request: PaxPaymentRequest,
    transactionId: string,
    authCode: string,
    customerCopy: boolean
  ): string {
    const timestamp = new Date().toLocaleString();
    const copy = customerCopy ? 'CUSTOMER COPY' : 'MERCHANT COPY';
    const amount = (request.amount / 100).toFixed(2);

    return `
=============================
      ${this.terminalConfig.merchant.name}
        ${copy}
=============================
Date: ${timestamp}
Transaction ID: ${transactionId}
Auth Code: ${authCode}
=============================
Amount: $${amount}
Type: ${request.transType || 'SALE'}
Tender: ${request.tenderType || 'CREDIT'}
Card: VISA ****1234 (CHIP)
=============================
${request.items?.map(item => 
  `${item.name} x${item.quantity} - $${(item.price * item.quantity).toFixed(2)}`
).join('\n') || ''}
=============================
    SIMULATION MODE
    Thank you!
=============================
    `.trim();
  }

  /**
   * Process payment with actual hardware (placeholder)
   */
  private async processHardwarePayment(request: PaxPaymentRequest): Promise<PaxPaymentResponse> {
    // This would integrate with the actual PAX SDK/DLL
    // For now, return an error indicating hardware not available
    throw new Error('Hardware integration not available in cloud environment');
  }

  /**
   * Get actual hardware status (placeholder)
   */
  private async getHardwareStatus(): Promise<PaxTerminalStatus> {
    // This would check actual hardware status
    throw new Error('Hardware integration not available in cloud environment');
  }

  /**
   * Test actual hardware connection (placeholder)
   */
  private async testHardwareConnection(): Promise<{ success: boolean; message: string }> {
    // This would test actual hardware connection
    throw new Error('Hardware integration not available in cloud environment');
  }

  /**
   * Cancel actual hardware transaction (placeholder)
   */
  private async cancelHardwareTransaction(): Promise<{ success: boolean; message: string }> {
    // This would cancel actual hardware transaction
    throw new Error('Hardware integration not available in cloud environment');
  }
}

// Export singleton instance
export const paxPosService = new PaxPosService();
export default paxPosService;
