import React from 'react';
import PaxPaymentTerminal from '../components/PaxPaymentTerminal';

const PaxDemo: React.FC = () => {
  const handlePaymentComplete = (result: any) => {
    console.log('Payment completed:', result);
  };

  const handleReceiptPrinted = (success: boolean) => {
    console.log('Receipt printed:', success);
  };

  return (
    <div className="pax-demo-page">
      <div className="container">
        <header className="demo-header">
          <h1>PAX Hardware Integration Demo</h1>
          <p>
            This demo shows how the React app (deployed on Vercel) can communicate 
            with local PAX hardware for payment processing and receipt printing.
          </p>
        </header>

        <div className="demo-content">
          <div className="info-section">
            <h2>How It Works</h2>
            <div className="info-grid">
              <div className="info-card">
                <h3>🌐 Cloud Deployment</h3>
                <p>React app deployed on Vercel</p>
              </div>
              <div className="info-card">
                <h3>🖥️ Local Hardware</h3>
                <p>PAX A920PFG terminal (**************)</p>
              </div>
              <div className="info-card">
                <h3>🔗 Direct Integration</h3>
                <p>Frontend acts as server for hardware communication</p>
              </div>
              <div className="info-card">
                <h3>🧾 Receipt Printing</h3>
                <p>Trigger local receipt printing from cloud UI</p>
              </div>
            </div>
          </div>

          <div className="terminal-section">
            <PaxPaymentTerminal
              onPaymentComplete={handlePaymentComplete}
              onReceiptPrinted={handleReceiptPrinted}
            />
          </div>

          <div className="technical-details">
            <h2>Technical Details</h2>
            <div className="details-grid">
              <div className="detail-item">
                <strong>Architecture:</strong>
                <span>Frontend-integrated PAX service</span>
              </div>
              <div className="detail-item">
                <strong>Terminal Model:</strong>
                <span>PAX A920PFG</span>
              </div>
              <div className="detail-item">
                <strong>IP Address:</strong>
                <span>**************</span>
              </div>
              <div className="detail-item">
                <strong>Port:</strong>
                <span>10009</span>
              </div>
              <div className="detail-item">
                <strong>Software Version:</strong>
                <span>1.00.42.11303</span>
              </div>
              <div className="detail-item">
                <strong>Deployment:</strong>
                <span>Vercel (Cloud) + Local Hardware</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .pax-demo-page {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 20px;
        }

        .container {
          max-width: 1200px;
          margin: 0 auto;
        }

        .demo-header {
          text-align: center;
          color: white;
          margin-bottom: 40px;
        }

        .demo-header h1 {
          font-size: 2.5rem;
          margin-bottom: 10px;
          font-weight: bold;
        }

        .demo-header p {
          font-size: 1.1rem;
          opacity: 0.9;
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.6;
        }

        .demo-content {
          display: grid;
          gap: 30px;
        }

        .info-section {
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .info-section h2 {
          margin-bottom: 20px;
          color: #333;
          font-size: 1.8rem;
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
        }

        .info-card {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 20px;
          text-align: center;
          border: 2px solid #e9ecef;
          transition: transform 0.2s ease;
        }

        .info-card:hover {
          transform: translateY(-2px);
          border-color: #667eea;
        }

        .info-card h3 {
          margin-bottom: 10px;
          color: #495057;
          font-size: 1.1rem;
        }

        .info-card p {
          color: #6c757d;
          margin: 0;
        }

        .terminal-section {
          display: flex;
          justify-content: center;
        }

        .technical-details {
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .technical-details h2 {
          margin-bottom: 20px;
          color: #333;
          font-size: 1.8rem;
        }

        .details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 15px;
        }

        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px;
          background: #f8f9fa;
          border-radius: 6px;
          border-left: 4px solid #667eea;
        }

        .detail-item strong {
          color: #495057;
        }

        .detail-item span {
          color: #6c757d;
          font-family: 'Courier New', monospace;
        }

        @media (max-width: 768px) {
          .demo-header h1 {
            font-size: 2rem;
          }

          .info-grid {
            grid-template-columns: 1fr;
          }

          .details-grid {
            grid-template-columns: 1fr;
          }

          .detail-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
          }
        }
      `}</style>
    </div>
  );
};

export default PaxDemo;
