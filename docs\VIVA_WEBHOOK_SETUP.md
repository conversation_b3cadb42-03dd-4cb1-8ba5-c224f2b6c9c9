# Viva Wallet Webhook Setup Guide

This guide explains how to set up Viva Wallet webhooks for real-time payment notifications and receipt generation.

## Overview

The Viva webhook integration provides:
- Real-time payment status updates
- Automatic receipt generation
- WebSocket broadcasting to frontend
- Transaction status synchronization
- Support for multiple webhook event types

## Supported Webhook Events

| Event Type | Event ID | Description |
|------------|----------|-------------|
| Transaction Payment Created | 1796 | Customer payment successful |
| Transaction Failed | 1798 | Customer payment failed |
| Transaction Reversal Created | 1797 | Customer refund processed |
| Transaction POS ECR Session Created | 1802 | ECR session successful |
| Transaction POS ECR Session Failed | 1803 | ECR session failed |

## Setup Instructions

### 1. Environment Configuration

Copy the Viva environment configuration:
```bash
cp .env.viva.example .env.viva
```

Update the configuration with your Viva credentials:
```env
# Basic Auth Credentials
VIVA_MERCHANT_ID=your_merchant_id
VIVA_API_KEY=your_api_key

# OAuth2 Credentials
VIVA_CLIENT_ID=your_client_id
VIVA_CLIENT_SECRET=your_client_secret

# Configuration
VIVA_SOURCE_CODE=Default
VIVA_ENVIRONMENT=demo  # or 'production'
VIVA_ENABLED=true

# Webhook Configuration
VIVA_WEBHOOK_VERIFICATION_KEY=your_verification_key
```

### 2. Generate Webhook Verification Key

The webhook verification key is required by Viva to verify your webhook endpoint.

**Option A: Using the API endpoint**
```bash
curl -X GET "http://localhost:3001/api/v1/viva/webhook/verify" \
  -H "Content-Type: application/json"
```

**Option B: Direct Viva API call**
```bash
curl -X GET "https://demo.vivapayments.com/api/messages/config/token" \
  -H "Authorization: Basic $(echo -n 'MERCHANT_ID:API_KEY' | base64)"
```

The response will contain a verification key:
```json
{
  "Key": "B3248222FDCD1885AEAFE51CCC1B5607F00903F6"
}
```

Update your environment file with this key:
```env
VIVA_WEBHOOK_VERIFICATION_KEY=B3248222FDCD1885AEAFE51CCC1B5607F00903F6
```

### 3. Configure Webhook in Viva Self Care

1. **Log into Viva Banking App**
   - Demo: https://demo.vivapayments.com/
   - Production: https://www.vivapayments.com/

2. **Navigate to Webhook Settings**
   - Go to Settings > API Access > Webhooks
   - Click "Create Webhook"

3. **Configure Webhook**
   - **URL**: `https://your-domain.com/api/v1/viva/webhook`
   - **Event Type**: Select "Transaction Payment Created"
   - **Active**: Check the checkbox

4. **Verify Webhook**
   - Click "Verify" next to the URL field
   - Viva will make a GET request to your webhook verification endpoint
   - Your endpoint should return the verification key

5. **Save Configuration**
   - Click "Save" to activate the webhook

### 4. Webhook Endpoint Details

**Webhook URL**: `/api/v1/viva/webhook`
**Method**: POST
**Content-Type**: application/json

**Verification URL**: `/api/v1/viva/webhook/verify`
**Method**: GET
**Response**: JSON with verification key

**Health Check URL**: `/api/v1/viva/webhook/health`
**Method**: GET
**Response**: Health status

### 5. IP Whitelisting

Ensure your server accepts requests from Viva's IP addresses:

**Production IPs:**
- *************
- ************
- ************
- ***********
- **************/28
- **************/28
- ***********
- ***********
- ***********/28

**Demo IPs:**
- ************
- ***********
- ************
- ************
- ************
- *************
- *************
- ************

## Webhook Event Handling

### Transaction Payment Created (1796)

Triggered when a customer payment is successful:

```json
{
  "EventTypeId": 1796,
  "EventData": {
    "TransactionId": "997ab1e3-e6ce-45c9-970d-4d902f27ce71",
    "OrderCode": 2271655739472609,
    "Amount": 1000,
    "StatusId": "F",
    "MerchantId": "bdf4c6b3-c26d-4046-b5df-5c443ec39d09",
    "CardNumber": "414746XXXXXX0133",
    "AuthorizationId": "471543",
    "ResponseCode": "00"
  }
}
```

**Actions performed:**
- Update transaction status to 'success'
- Generate customer and merchant receipts
- Broadcast real-time updates via WebSocket
- Log event to system logs

### Transaction Failed (1798)

Triggered when a customer payment fails:

```json
{
  "EventTypeId": 1798,
  "EventData": {
    "TransactionId": "997ab1e3-e6ce-45c9-970d-4d902f27ce71",
    "OrderCode": 2271655739472609,
    "StatusId": "E",
    "ResponseCode": "05"
  }
}
```

**Actions performed:**
- Update transaction status to 'failed'
- Broadcast failure notification via WebSocket
- Log event to system logs

## Real-time Features

### WebSocket Integration

The webhook system broadcasts events to connected frontend clients via WebSocket:

**Connection URL**: `ws://localhost:3001/ws` (development)
**Connection URL**: `wss://your-domain.com/ws` (production)

**Event Types:**
- `viva_webhook_event`: Viva webhook notifications
- `transaction_update`: Transaction status changes
- `receipt_event`: Receipt generation notifications

### Frontend Integration

The frontend automatically connects to the WebSocket server and displays:
- Real-time payment notifications
- Transaction status updates
- Receipt generation alerts
- Connection status indicator

### Receipt Generation

Receipts are automatically generated for successful payments:
- **Customer Receipt**: Formatted for customer copy
- **Merchant Receipt**: Formatted for merchant records
- **Viva-specific Data**: Includes Viva transaction details
- **Real-time Delivery**: Broadcasted via WebSocket

## Testing

### 1. Test Webhook Endpoint

```bash
curl -X POST "http://localhost:3001/api/v1/viva/webhook" \
  -H "Content-Type: application/json" \
  -d '{
    "EventTypeId": 1796,
    "EventData": {
      "TransactionId": "test-transaction-id",
      "OrderCode": 123456789,
      "Amount": 1000,
      "StatusId": "F",
      "MerchantId": "test-merchant-id"
    },
    "Created": "2024-01-01T12:00:00Z",
    "CorrelationId": "test-correlation-id",
    "MessageId": "test-message-id",
    "RecipientId": "test-recipient-id",
    "RetryCount": 0,
    "MessageTypeId": 512
  }'
```

### 2. Test WebSocket Connection

Open browser console and test WebSocket connection:

```javascript
const ws = new WebSocket('ws://localhost:3001/ws');
ws.onopen = () => console.log('Connected');
ws.onmessage = (event) => console.log('Message:', JSON.parse(event.data));
```

### 3. Monitor Webhook Events

Use the built-in webhook monitor in the frontend:
- Click the "📡 Webhook Monitor" button in the bottom-right corner
- View real-time webhook events
- Monitor connection status
- Clear event history

## Troubleshooting

### Common Issues

1. **Webhook verification fails**
   - Ensure verification key is correctly set
   - Check that verification endpoint returns proper JSON
   - Verify TLS 1.2 support on your server

2. **Webhook events not received**
   - Check IP whitelisting configuration
   - Verify webhook URL is publicly accessible
   - Ensure webhook is active in Viva Self Care

3. **WebSocket connection fails**
   - Check firewall settings for WebSocket traffic
   - Verify WebSocket server is running
   - Check browser console for connection errors

### Debug Mode

Enable debug logging by setting log level to 'debug' in environment:

```env
LOG_LEVEL=debug
```

This will provide detailed logs for:
- Webhook event processing
- WebSocket connections
- Receipt generation
- Transaction updates

## Security Considerations

1. **HTTPS Required**: Webhooks must use HTTPS in production
2. **IP Whitelisting**: Restrict access to Viva's IP ranges
3. **Verification Key**: Keep webhook verification key secure
4. **Request Validation**: Validate all incoming webhook data
5. **Rate Limiting**: Implement rate limiting for webhook endpoints

## Support

For issues with Viva Wallet integration:
- Viva Developer Documentation: https://developer.viva.com/
- Viva Support: Contact through Viva Self Care
- Technical Issues: Check system logs and webhook monitor
