{"version": 3, "sources": ["../../../src/prebuild/resolveOptions.ts"], "sourcesContent": ["import { ModPlatform } from '@expo/config-plugins';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport * as Log from '../log';\nimport { CommandError } from '../utils/errors';\nimport { validateUrl } from '../utils/url';\n\nconst debug = require('debug')('expo:prebuild:resolveOptions') as typeof console.log;\n\nexport interface ResolvedTemplateOption {\n  type: 'file' | 'npm' | 'repository';\n  uri: string;\n}\n\nexport function resolvePackageManagerOptions(args: any) {\n  const managers: Record<string, boolean> = {\n    npm: args['--npm'],\n    yarn: args['--yarn'],\n    pnpm: args['--pnpm'],\n    bun: args['--bun'],\n  };\n\n  if (\n    [managers.npm, managers.pnpm, managers.yarn, managers.bun, !!args['--no-install']].filter(\n      Boolean\n    ).length > 1\n  ) {\n    throw new CommandError(\n      'BAD_ARGS',\n      'Specify at most one of: --no-install, --npm, --pnpm, --yarn, --bun'\n    );\n  }\n\n  return managers;\n}\n\n/** Resolves a template option as a URL or file path pointing to a tar file. */\nexport function resolveTemplateOption(template: string): ResolvedTemplateOption {\n  assert(template, 'template is required');\n\n  if (\n    // Expands github shorthand (owner/repo) to full URLs\n    template.includes('/') &&\n    !(\n      template.startsWith('@') || // Scoped package\n      template.startsWith('.') || // Relative path\n      template.startsWith(path.sep) || // Absolute path\n      // Contains a protocol\n      /^[a-z][-a-z0-9\\\\.\\\\+]*:/.test(template)\n    )\n  ) {\n    template = `https://github.com/${template}`;\n  }\n\n  if (template.startsWith('https://') || template.startsWith('http://')) {\n    if (!validateUrl(template)) {\n      throw new CommandError('BAD_ARGS', 'Invalid URL provided as a template');\n    }\n    debug('Resolved template to repository path:', template);\n    return { type: 'repository', uri: template };\n  }\n\n  if (\n    // Supports `file:./path/to/template.tgz`\n    template.startsWith('file:') ||\n    // Supports `../path/to/template.tgz`\n    template.startsWith('.') ||\n    // Supports `\\\\path\\\\to\\\\template.tgz`\n    template.startsWith(path.sep)\n  ) {\n    let resolvedUri = template;\n    if (resolvedUri.startsWith('file:')) {\n      resolvedUri = resolvedUri.substring(5);\n    }\n    const templatePath = path.resolve(resolvedUri);\n    assert(fs.existsSync(templatePath), 'template file does not exist: ' + templatePath);\n    assert(\n      fs.statSync(templatePath).isFile(),\n      'template must be a tar file created by running `npm pack` in a project: ' + templatePath\n    );\n\n    debug(`Resolved template to file path:`, templatePath);\n    return { type: 'file', uri: templatePath };\n  }\n\n  if (fs.existsSync(template)) {\n    // Backward compatible with the old local template argument, e.g. `--template dir/template.tgz`\n    const templatePath = path.resolve(template);\n    debug(`Resolved template to file path:`, templatePath);\n    return { type: 'file', uri: templatePath };\n  }\n\n  debug(`Resolved template to NPM package:`, template);\n  return { type: 'npm', uri: template };\n}\n\n/** Resolves dependencies to skip from a string joined by `,`. Example: `react-native,expo,lodash` */\nexport function resolveSkipDependencyUpdate(value: any) {\n  if (!value || typeof value !== 'string') {\n    return [];\n  }\n  return value.split(',');\n}\n\n/** Returns an array of platforms based on the input platform identifier and runtime constraints. */\nexport function resolvePlatformOption(\n  platform: string = 'all',\n  { loose }: { loose?: boolean } = {}\n): ModPlatform[] {\n  switch (platform) {\n    case 'ios':\n      return ['ios'];\n    case 'android':\n      return ['android'];\n    case 'all':\n      return loose || process.platform !== 'win32' ? ['android', 'ios'] : ['android'];\n    default:\n      return [platform as ModPlatform];\n  }\n}\n\n/** Warns and filters out unsupported platforms based on the runtime constraints. Essentially this means no iOS on Windows devices. */\nexport function ensureValidPlatforms(platforms: ModPlatform[]): ModPlatform[] {\n  // Skip prebuild for iOS on Windows\n  if (process.platform === 'win32' && platforms.includes('ios')) {\n    Log.warn(\n      chalk`⚠️  Skipping generating the iOS native project files. Run {bold npx expo prebuild} again from macOS or Linux to generate the iOS project.\\n`\n    );\n    return platforms.filter((platform) => platform !== 'ios');\n  }\n  return platforms;\n}\n\n/** Asserts platform length must be greater than zero. */\nexport function assertPlatforms(platforms: ModPlatform[]) {\n  if (!platforms?.length) {\n    throw new CommandError('At least one platform must be enabled when syncing');\n  }\n}\n"], "names": ["assertPlatforms", "ensureValidPlatforms", "resolvePackageManagerOptions", "resolvePlatformOption", "resolveSkipDependencyUpdate", "resolveTemplateOption", "debug", "require", "args", "managers", "npm", "yarn", "pnpm", "bun", "filter", "Boolean", "length", "CommandError", "template", "assert", "includes", "startsWith", "path", "sep", "test", "validateUrl", "type", "uri", "resolved<PERSON>ri", "substring", "templatePath", "resolve", "fs", "existsSync", "statSync", "isFile", "value", "split", "platform", "loose", "process", "platforms", "Log", "warn", "chalk"], "mappings": ";;;;;;;;;;;IAyIgBA,eAAe;eAAfA;;IAZAC,oBAAoB;eAApBA;;IA5GAC,4BAA4B;eAA5BA;;IA2FAC,qBAAqB;eAArBA;;IARAC,2BAA2B;eAA3BA;;IA5DAC,qBAAqB;eAArBA;;;;gEAvCG;;;;;;;gEACD;;;;;;;gEACH;;;;;;;gEACE;;;;;;6DAEI;wBACQ;qBACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5B,MAAMC,QAAQC,QAAQ,SAAS;AAOxB,SAASL,6BAA6BM,IAAS;IACpD,MAAMC,WAAoC;QACxCC,KAAKF,IAAI,CAAC,QAAQ;QAClBG,MAAMH,IAAI,CAAC,SAAS;QACpBI,MAAMJ,IAAI,CAAC,SAAS;QACpBK,KAAKL,IAAI,CAAC,QAAQ;IACpB;IAEA,IACE;QAACC,SAASC,GAAG;QAAED,SAASG,IAAI;QAAEH,SAASE,IAAI;QAAEF,SAASI,GAAG;QAAE,CAAC,CAACL,IAAI,CAAC,eAAe;KAAC,CAACM,MAAM,CACvFC,SACAC,MAAM,GAAG,GACX;QACA,MAAM,IAAIC,oBAAY,CACpB,YACA;IAEJ;IAEA,OAAOR;AACT;AAGO,SAASJ,sBAAsBa,QAAgB;IACpDC,IAAAA,iBAAM,EAACD,UAAU;IAEjB,IACE,qDAAqD;IACrDA,SAASE,QAAQ,CAAC,QAClB,CACEF,CAAAA,SAASG,UAAU,CAAC,QAAQ,iBAAiB;IAC7CH,SAASG,UAAU,CAAC,QAAQ,gBAAgB;IAC5CH,SAASG,UAAU,CAACC,eAAI,CAACC,GAAG,KAAK,gBAAgB;IACjD,sBAAsB;IACtB,0BAA0BC,IAAI,CAACN,SAAQ,GAEzC;QACAA,WAAW,CAAC,mBAAmB,EAAEA,UAAU;IAC7C;IAEA,IAAIA,SAASG,UAAU,CAAC,eAAeH,SAASG,UAAU,CAAC,YAAY;QACrE,IAAI,CAACI,IAAAA,gBAAW,EAACP,WAAW;YAC1B,MAAM,IAAID,oBAAY,CAAC,YAAY;QACrC;QACAX,MAAM,yCAAyCY;QAC/C,OAAO;YAAEQ,MAAM;YAAcC,KAAKT;QAAS;IAC7C;IAEA,IACE,yCAAyC;IACzCA,SAASG,UAAU,CAAC,YACpB,qCAAqC;IACrCH,SAASG,UAAU,CAAC,QACpB,sCAAsC;IACtCH,SAASG,UAAU,CAACC,eAAI,CAACC,GAAG,GAC5B;QACA,IAAIK,cAAcV;QAClB,IAAIU,YAAYP,UAAU,CAAC,UAAU;YACnCO,cAAcA,YAAYC,SAAS,CAAC;QACtC;QACA,MAAMC,eAAeR,eAAI,CAACS,OAAO,CAACH;QAClCT,IAAAA,iBAAM,EAACa,aAAE,CAACC,UAAU,CAACH,eAAe,mCAAmCA;QACvEX,IAAAA,iBAAM,EACJa,aAAE,CAACE,QAAQ,CAACJ,cAAcK,MAAM,IAChC,6EAA6EL;QAG/ExB,MAAM,CAAC,+BAA+B,CAAC,EAAEwB;QACzC,OAAO;YAAEJ,MAAM;YAAQC,KAAKG;QAAa;IAC3C;IAEA,IAAIE,aAAE,CAACC,UAAU,CAACf,WAAW;QAC3B,+FAA+F;QAC/F,MAAMY,eAAeR,eAAI,CAACS,OAAO,CAACb;QAClCZ,MAAM,CAAC,+BAA+B,CAAC,EAAEwB;QACzC,OAAO;YAAEJ,MAAM;YAAQC,KAAKG;QAAa;IAC3C;IAEAxB,MAAM,CAAC,iCAAiC,CAAC,EAAEY;IAC3C,OAAO;QAAEQ,MAAM;QAAOC,KAAKT;IAAS;AACtC;AAGO,SAASd,4BAA4BgC,KAAU;IACpD,IAAI,CAACA,SAAS,OAAOA,UAAU,UAAU;QACvC,OAAO,EAAE;IACX;IACA,OAAOA,MAAMC,KAAK,CAAC;AACrB;AAGO,SAASlC,sBACdmC,WAAmB,KAAK,EACxB,EAAEC,KAAK,EAAuB,GAAG,CAAC,CAAC;IAEnC,OAAQD;QACN,KAAK;YACH,OAAO;gBAAC;aAAM;QAChB,KAAK;YACH,OAAO;gBAAC;aAAU;QACpB,KAAK;YACH,OAAOC,SAASC,QAAQF,QAAQ,KAAK,UAAU;gBAAC;gBAAW;aAAM,GAAG;gBAAC;aAAU;QACjF;YACE,OAAO;gBAACA;aAAwB;IACpC;AACF;AAGO,SAASrC,qBAAqBwC,SAAwB;IAC3D,mCAAmC;IACnC,IAAID,QAAQF,QAAQ,KAAK,WAAWG,UAAUrB,QAAQ,CAAC,QAAQ;QAC7DsB,KAAIC,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC,2IAA2I,CAAC;QAEpJ,OAAOH,UAAU3B,MAAM,CAAC,CAACwB,WAAaA,aAAa;IACrD;IACA,OAAOG;AACT;AAGO,SAASzC,gBAAgByC,SAAwB;IACtD,IAAI,EAACA,6BAAAA,UAAWzB,MAAM,GAAE;QACtB,MAAM,IAAIC,oBAAY,CAAC;IACzB;AACF"}