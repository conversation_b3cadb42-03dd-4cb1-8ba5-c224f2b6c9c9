{"version": 3, "sources": ["../../../../src/run/android/resolveLaunchProps.ts"], "sourcesContent": ["import { AndroidConfig } from '@expo/config-plugins';\n\nimport { AndroidAppIdResolver } from '../../start/platforms/android/AndroidAppIdResolver';\nimport { CommandError } from '../../utils/errors';\n\nexport interface LaunchProps {\n  /**\n   * The \"common\" Android package name, configured through the app manifest.\n   * @see https://source.android.com/docs/core/architecture/hidl/code-style#package-names\n   */\n  packageName: string;\n  /**\n   * Optional customized application ID, used in product flavors.\n   * @see https://developer.android.com/build/build-variants#change-app-id\n   */\n  customAppId?: string;\n  /**\n   * The main activity to launch, by default this is `.MainActivity`.\n   * @see https://github.com/expo/expo/blob/c0aec226a43c0f186258a063a6145c3e52246f8a/templates/expo-template-bare-minimum/android/app/src/main/AndroidManifest.xml#L22\n   */\n  mainActivity: string;\n  /**\n   * The full launch activity reference used in the app intent to launch the app with `adb am start -n <launchActivity>`.\n   * Usually, this is structured as `<package-name>/.<activity-name>`.\n   * For product flavors, this is structured as `<custom-app-id>/<package-name>.<activity-name>`.\n   * @see https://developer.android.com/studio/command-line/adb#IntentSpec\n   */\n  launchActivity: string;\n}\n\nasync function getMainActivityAsync(projectRoot: string): Promise<string> {\n  const filePath = await AndroidConfig.Paths.getAndroidManifestAsync(projectRoot);\n  const androidManifest = await AndroidConfig.Manifest.readAndroidManifestAsync(filePath);\n\n  // Assert MainActivity defined.\n  const activity = await AndroidConfig.Manifest.getRunnableActivity(androidManifest);\n  if (!activity) {\n    throw new CommandError(\n      'ANDROID_MALFORMED',\n      `${filePath} is missing a runnable activity element.`\n    );\n  }\n  // Often this is \".MainActivity\"\n  return activity.$['android:name'];\n}\n\nexport async function resolveLaunchPropsAsync(\n  projectRoot: string,\n  options: { appId?: string }\n): Promise<LaunchProps> {\n  const mainActivity = await getMainActivityAsync(projectRoot);\n  const packageName = await new AndroidAppIdResolver(projectRoot).getAppIdFromNativeAsync();\n  const customAppId = options.appId;\n\n  const launchActivity =\n    customAppId && customAppId !== packageName\n      ? `${customAppId}/${packageName}${mainActivity}`\n      : `${packageName}/${mainActivity}`;\n\n  return {\n    mainActivity,\n    launchActivity,\n    packageName,\n    customAppId,\n  };\n}\n"], "names": ["resolveLaunchPropsAsync", "getMainActivityAsync", "projectRoot", "filePath", "AndroidConfig", "Paths", "getAndroidManifestAsync", "androidManifest", "Manifest", "readAndroidManifestAsync", "activity", "getRunnableActivity", "CommandError", "$", "options", "mainActivity", "packageName", "AndroidAppIdResolver", "getAppIdFromNativeAsync", "customAppId", "appId", "launchActivity"], "mappings": ";;;;+BA8<PERSON><PERSON>;;;eAAAA;;;;yBA9CQ;;;;;;sCAEO;wBACR;AA2B7B,eAAeC,qBAAqBC,WAAmB;IACrD,MAAMC,WAAW,MAAMC,8BAAa,CAACC,KAAK,CAACC,uBAAuB,CAACJ;IACnE,MAAMK,kBAAkB,MAAMH,8BAAa,CAACI,QAAQ,CAACC,wBAAwB,CAACN;IAE9E,+BAA+B;IAC/B,MAAMO,WAAW,MAAMN,8BAAa,CAACI,QAAQ,CAACG,mBAAmB,CAACJ;IAClE,IAAI,CAACG,UAAU;QACb,MAAM,IAAIE,oBAAY,CACpB,qBACA,GAAGT,SAAS,wCAAwC,CAAC;IAEzD;IACA,gCAAgC;IAChC,OAAOO,SAASG,CAAC,CAAC,eAAe;AACnC;AAEO,eAAeb,wBACpBE,WAAmB,EACnBY,OAA2B;IAE3B,MAAMC,eAAe,MAAMd,qBAAqBC;IAChD,MAAMc,cAAc,MAAM,IAAIC,0CAAoB,CAACf,aAAagB,uBAAuB;IACvF,MAAMC,cAAcL,QAAQM,KAAK;IAEjC,MAAMC,iBACJF,eAAeA,gBAAgBH,cAC3B,GAAGG,YAAY,CAAC,EAAEH,cAAcD,cAAc,GAC9C,GAAGC,YAAY,CAAC,EAAED,cAAc;IAEtC,OAAO;QACLA;QACAM;QACAL;QACAG;IACF;AACF"}