{"version": 3, "file": "AssetSourceResolver.js", "sourceRoot": "", "sources": ["../src/AssetSourceResolver.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAY1C,wDAAwD;AACxD,SAAS,kBAAkB,CAAC,KAAoC;IAC9D,MAAM,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;IAC5E,MAAM,WAAW,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC;IACzD,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;IACjD,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,KAAK,CAAC,kBAAkB,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,WAAW,GAAG,IAAI,CAAC;IAC1E,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,WAAW,GAAG,IAAI,CAAC;IAClG,CAAC;AACH,CAAC;AAED,MAAM,CAAC,OAAO,OAAO,mBAAmB;IACrB,SAAS,CAAS;IACnC,uCAAuC;IACvC,yCAAyC;IACzC,uCAAuC;IACtB,WAAW,CAA4B;IACxD,uBAAuB;IACP,KAAK,CAAgC;IAErD,YACE,SAAoC,EACpC,WAAsC,EACtC,KAAoB;QAEpB,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,kBAAkB,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAED,+BAA+B;IAC/B,kBAAkB;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gCAAgC;IAChC,sBAAsB;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACxE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,UAAU;QACpB,kBAAkB;QAClB,OAAO,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAC/C,CAAC;IACJ,CAAC;IAED,UAAU,CAAC,MAAc;QACvB,OAAO;YACL,gBAAgB,EAAE,IAAI;YACtB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,SAAS;YACpC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,SAAS;YACtC,GAAG,EAAE,MAAM;YACX,KAAK,EAAE,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC;SAC1E,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,MAAgB,EAAE,WAAmB;QACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;gBAC7B,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;CACF", "sourcesContent": ["import type { PackagerAsset } from '@react-native/assets-registry/registry';\nimport { Platform } from 'expo-modules-core';\nimport { PixelRatio } from 'react-native';\n\nimport type { AssetMetadata } from './AssetSources';\n\nexport type ResolvedAssetSource = {\n  __packager_asset: boolean;\n  width?: number;\n  height?: number;\n  uri: string;\n  scale: number;\n};\n\n// Returns the Metro dev server-specific asset location.\nfunction getScaledAssetPath(asset: PackagerAsset | AssetMetadata): string {\n  const scale = AssetSourceResolver.pickScale(asset.scales, PixelRatio.get());\n  const scaleSuffix = scale === 1 ? '' : '@' + scale + 'x';\n  const type = !asset.type ? '' : `.${asset.type}`;\n  if (__DEV__) {\n    return asset.httpServerLocation + '/' + asset.name + scaleSuffix + type;\n  } else {\n    return asset.httpServerLocation.replace(/\\.\\.\\//g, '_') + '/' + asset.name + scaleSuffix + type;\n  }\n}\n\nexport default class AssetSourceResolver {\n  private readonly serverUrl: string;\n  // where the jsbundle is being run from\n  // NOTE(EvanBacon): Never defined on web.\n  // @ts-expect-error: Never read locally\n  private readonly jsbundleUrl: string | undefined | null;\n  // the asset to resolve\n  public readonly asset: PackagerAsset | AssetMetadata;\n\n  constructor(\n    serverUrl: string | undefined | null,\n    jsbundleUrl: string | undefined | null,\n    asset: PackagerAsset\n  ) {\n    this.serverUrl = serverUrl || 'https://expo.dev';\n    this.jsbundleUrl = null;\n    this.asset = asset;\n  }\n\n  // Always true for web runtimes\n  isLoadedFromServer(): boolean {\n    return true;\n  }\n\n  // Always false for web runtimes\n  isLoadedFromFileSystem(): boolean {\n    return false;\n  }\n\n  defaultAsset(): ResolvedAssetSource {\n    return this.assetServerURL();\n  }\n\n  /**\n   * @returns absolute remote URL for the hosted asset.\n   */\n  assetServerURL(): ResolvedAssetSource {\n    const fromUrl = new URL(getScaledAssetPath(this.asset), this.serverUrl);\n    fromUrl.searchParams.set('platform', Platform.OS);\n    fromUrl.searchParams.set('hash', this.asset.hash);\n    return this.fromSource(\n      // Relative on web\n      fromUrl.toString().replace(fromUrl.origin, '')\n    );\n  }\n\n  fromSource(source: string): ResolvedAssetSource {\n    return {\n      __packager_asset: true,\n      width: this.asset.width ?? undefined,\n      height: this.asset.height ?? undefined,\n      uri: source,\n      scale: AssetSourceResolver.pickScale(this.asset.scales, PixelRatio.get()),\n    };\n  }\n\n  static pickScale(scales: number[], deviceScale: number): number {\n    for (let i = 0; i < scales.length; i++) {\n      if (scales[i] >= deviceScale) {\n        return scales[i];\n      }\n    }\n    return scales[scales.length - 1] || 1;\n  }\n}\n"]}