/**
 * Receipt Action Modal Component
 * 
 * Intelligent modal that appears automatically when a payment is completed
 * Provides options to print, email, or view receipts
 */

import React, { useState, useEffect } from 'react';
import { ReceiptEventData } from '../services/websocketService';

interface ReceiptActionModalProps {
  isOpen: boolean;
  receiptEvent: ReceiptEventData | null;
  onClose: () => void;
}

export function ReceiptActionModal({ isOpen, receiptEvent, onClose }: ReceiptActionModalProps) {
  const [selectedAction, setSelectedAction] = useState<'print' | 'email' | 'view' | null>(null);
  const [email, setEmail] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showEmailInput, setShowEmailInput] = useState(false);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedAction(null);
      setEmail('');
      setShowEmailInput(false);
      setIsProcessing(false);
    }
  }, [isOpen]);

  if (!isOpen || !receiptEvent) return null;

  const handlePrintReceipt = async () => {
    setIsProcessing(true);
    try {
      const response = await fetch('/api/v1/receipts/print', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: receiptEvent.transactionId,
          copies: 1,
          customerCopy: receiptEvent.receiptType === 'customer'
        }),
      });
      
      const data = await response.json();
      if (data.success) {
        alert('Receipt sent to printer!');
        onClose();
      } else {
        alert('Failed to print receipt: ' + data.error);
      }
    } catch (err) {
      alert('Failed to print receipt');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleEmailReceipt = async () => {
    if (!email.trim()) {
      alert('Please enter an email address');
      return;
    }

    setIsProcessing(true);
    try {
      const response = await fetch('/api/v1/receipts/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: receiptEvent.transactionId,
          email: email.trim(),
          customerCopy: receiptEvent.receiptType === 'customer'
        }),
      });
      
      const data = await response.json();
      if (data.success) {
        alert('Receipt email queued for delivery!');
        onClose();
      } else {
        alert('Failed to email receipt: ' + data.error);
      }
    } catch (err) {
      alert('Failed to email receipt');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleViewReceipt = () => {
    // Create a new window to display the receipt
    const receiptWindow = window.open('', '_blank', 'width=400,height=600,scrollbars=yes');
    if (receiptWindow) {
      receiptWindow.document.write(`
        <html>
          <head>
            <title>Receipt - ${receiptEvent.transactionId}</title>
            <style>
              body { 
                font-family: 'Courier New', monospace; 
                margin: 20px; 
                background: white;
                color: black;
              }
              pre { 
                white-space: pre-wrap; 
                word-wrap: break-word;
                font-size: 12px;
                line-height: 1.2;
              }
              .print-button {
                position: fixed;
                top: 10px;
                right: 10px;
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
              }
              @media print {
                .print-button { display: none; }
              }
            </style>
          </head>
          <body>
            <button class="print-button" onclick="window.print()">🖨️ Print</button>
            <pre>${receiptEvent.receiptContent}</pre>
          </body>
        </html>
      `);
      receiptWindow.document.close();
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">🧾</span>
              <div>
                <h3 className="text-xl font-bold">Receipt Ready!</h3>
                <p className="text-green-100 text-sm">
                  {receiptEvent.receiptType === 'customer' ? 'Customer' : 'Merchant'} receipt generated
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-green-200 transition-colors"
            >
              <span className="text-2xl">×</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          <div className="text-center">
            <p className="text-gray-600 mb-4">
              Transaction ID: <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                {receiptEvent.transactionId.slice(-8)}
              </span>
            </p>
            <p className="text-gray-700">What would you like to do with the receipt?</p>
          </div>

          {/* Action Buttons */}
          {!showEmailInput && (
            <div className="space-y-3">
              <button
                onClick={handlePrintReceipt}
                disabled={isProcessing}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl hover:bg-blue-700 disabled:bg-gray-400 transition-colors font-medium flex items-center justify-center space-x-2"
              >
                <span>🖨️</span>
                <span>{isProcessing ? 'Printing...' : 'Print Receipt'}</span>
              </button>

              <button
                onClick={() => setShowEmailInput(true)}
                disabled={isProcessing}
                className="w-full bg-purple-600 text-white py-3 px-4 rounded-xl hover:bg-purple-700 disabled:bg-gray-400 transition-colors font-medium flex items-center justify-center space-x-2"
              >
                <span>📧</span>
                <span>Email Receipt</span>
              </button>

              <button
                onClick={handleViewReceipt}
                disabled={isProcessing}
                className="w-full bg-green-600 text-white py-3 px-4 rounded-xl hover:bg-green-700 disabled:bg-gray-400 transition-colors font-medium flex items-center justify-center space-x-2"
              >
                <span>👁️</span>
                <span>View Receipt</span>
              </button>

              <button
                onClick={onClose}
                className="w-full bg-gray-300 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-400 transition-colors font-medium"
              >
                Skip for Now
              </button>
            </div>
          )}

          {/* Email Input */}
          {showEmailInput && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  autoFocus
                />
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={handleEmailReceipt}
                  disabled={isProcessing || !email.trim()}
                  className="flex-1 bg-purple-600 text-white py-3 px-4 rounded-xl hover:bg-purple-700 disabled:bg-gray-400 transition-colors font-medium"
                >
                  {isProcessing ? 'Sending...' : 'Send Email'}
                </button>
                <button
                  onClick={() => setShowEmailInput(false)}
                  disabled={isProcessing}
                  className="flex-1 bg-gray-300 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-400 transition-colors font-medium"
                >
                  Back
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 text-center">
          <p className="text-xs text-gray-500">
            Receipt will be available in transaction history
          </p>
        </div>
      </div>
    </div>
  );
}
