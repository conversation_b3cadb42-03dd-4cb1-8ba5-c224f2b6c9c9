{"name": "@expo/sdk-runtime-versions", "version": "1.0.0", "description": "Functions for converting Expo SDK versions to EAS runtime versions", "main": "build/index.js", "types": "build/index.d.ts", "files": ["build"], "scripts": {"clean": "rm -rf build", "format": "prettier --write src", "prepare": "rm -rf build && tsc", "test": "jest"}, "author": "Expo <<EMAIL>>", "license": "MIT", "jest": {"preset": "ts-jest", "rootDir": "src"}, "devDependencies": {"@types/jest": "^26.0.22", "jest": "^26.6.3", "prettier": "^2.2.1", "ts-jest": "^26.4.4", "typescript": "^4.2.3"}}