import*as e from"../../../core/common/common.js";import*as t from"../../../core/host/host.js";var o={cssContent:".webkit-css-property{color:var(--webkit-css-property-color,var(--sys-color-token-property-special))}.webkit-html-comment{color:var(--sys-color-token-comment)}.webkit-html-tag{color:var(--sys-color-token-tag)}.webkit-html-tag-name,\n.webkit-html-close-tag-name{color:var(--sys-color-token-tag)}.webkit-html-pseudo-element{color:var(--sys-color-token-pseudo-element)}.webkit-html-js-node,\n.webkit-html-css-node{color:var(--text-primary);white-space:pre-wrap}.webkit-html-text-node{color:var(--text-primary);unicode-bidi:-webkit-isolate}.webkit-html-entity-value{background-color:rgb(0 0 0/15%);unicode-bidi:-webkit-isolate}.webkit-html-doctype{color:var(--text-secondary)}.webkit-html-attribute-name{color:var(--sys-color-token-attribute);unicode-bidi:-webkit-isolate}.webkit-html-attribute-value{color:var(--sys-color-token-attribute-value);unicode-bidi:-webkit-isolate;word-break:break-all}.devtools-link{color:var(--text-link);text-decoration:underline;outline-offset:2px;.elements-disclosure &{color:var(--text-link)}devtools-icon{vertical-align:baseline;color:var(--sys-color-primary)}:focus .selected & devtools-icon{color:var(--sys-color-tonal-container)}&:focus-visible{outline-width:unset}&.invalid-link{color:var(--text-disabled);text-decoration:none}&:not(.devtools-link-prevent-click, .invalid-link){cursor:pointer}@media (forced-colors: active){&:not(.devtools-link-prevent-click){forced-color-adjust:none;color:linktext}&:focus-visible{background:Highlight;color:HighlightText}}}\n/*# sourceURL=inspectorSyntaxHighlight.css */\n"};let n;const s=new Map;class r extends EventTarget{setting;themeNameInternal="default";customSheets=new Set;computedStyleOfHTML=e.Lazy.lazy((()=>window.getComputedStyle(document.documentElement)));#e=new Set([document]);#t;#o;#n=()=>this.#s();#r=()=>this.fetchColorsAndApplyHostTheme();constructor(e){super(),this.setting=e,this.#t=window.matchMedia("(prefers-color-scheme: dark)"),this.#o=window.matchMedia("(forced-colors: active)"),this.#t.addEventListener("change",this.#n),this.#o.addEventListener("change",this.#n),e.addChangeListener(this.#n),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.ColorThemeChanged,this.#r)}#i(){this.#t.removeEventListener("change",this.#n),this.#o.removeEventListener("change",this.#n),this.setting.removeChangeListener(this.#n),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(t.InspectorFrontendHostAPI.Events.ColorThemeChanged,this.#r)}static hasInstance(){return void 0!==n}static instance(e={forceNew:null,setting:null}){const{forceNew:t,setting:o}=e;if(!n||t){if(!o)throw new Error(`Unable to create theme support: setting must be provided: ${(new Error).stack}`);n&&n.#i(),n=new r(o)}return n}addDocumentToTheme(e){this.#e.add(e),this.#a(e)}getComputedValue(e,t=null){let o=s.get(t);o||(o=new Map,s.set(t,o));let n=o.get(e);if(!n){const s=t?window.getComputedStyle(t):this.computedStyleOfHTML();if("symbol"==typeof s)throw new Error(`Computed value for property (${e}) could not be found on documentElement.`);n=s.getPropertyValue(e).trim(),n&&o.set(e,n)}return n}hasTheme(){return"default"!==this.themeNameInternal}themeName(){return this.themeNameInternal}injectHighlightStyleSheets(e){this.appendStyle(e,o)}appendStyle(e,{cssContent:t}){const o=document.createElement("style");o.textContent=t,e.appendChild(o)}injectCustomStyleSheets(e){for(const t of this.customSheets){const o=document.createElement("style");o.textContent=t,e.appendChild(o)}}addCustomStylesheet(e){this.customSheets.add(e)}#s(){for(const e of this.#e)this.#c(e)}#c(e){const t=window.matchMedia("(forced-colors: active)").matches,o=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"default",n="systemPreferred"===this.setting.get()||t;this.themeNameInternal=n?o:this.setting.get(),e.documentElement.classList.toggle("theme-with-dark-background","dark"===this.themeNameInternal),e.documentElement.classList.add("baseline-grayscale"),s.clear(),this.customSheets.clear(),this.dispatchEvent(new i)}static clearThemeCache(){s.clear()}fetchColorsAndApplyHostTheme(){for(const e of this.#e)this.#a(e)}#a(e){if(t.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode())return void this.#c(e);const o=e.querySelector("link[href*='//theme/colors.css']"),n=e.createElement("link");n.setAttribute("href",`devtools://theme/colors.css?sets=ui,chrome&version=${(new Date).getTime().toString()}`),n.setAttribute("rel","stylesheet"),n.setAttribute("type","text/css"),n.onload=()=>{o&&o.remove(),this.#c(e)},e.body.appendChild(n)}}class i extends Event{static eventName="themechange";constructor(){super(i.eventName,{bubbles:!0,composed:!0})}}export{i as ThemeChangeEvent,r as ThemeSupport};
