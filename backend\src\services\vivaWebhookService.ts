/**
 * Viva Wallet Webhook Service
 * 
 * Handles Viva Wallet webhook verification and event processing
 * Supports webhook verification key generation and signature validation
 */

import axios from 'axios';
import crypto from 'crypto';
import { z } from 'zod';
import { env } from '../config/env';
import { createChildLogger } from '../config/logger';

const vivaWebhookLogger = createChildLogger({ module: 'viva-webhook' });

// Viva Webhook Event Types
export const VIVA_WEBHOOK_EVENTS = {
  TRANSACTION_PAYMENT_CREATED: 1796,
  TRANSACTION_FAILED: 1798,
  TRANSACTION_PRICE_CALCULATED: 1799,
  TRANSACTION_REVERSAL_CREATED: 1797,
  ACCOUNT_TRANSACTION_CREATED: 2054,
  COMMAND_BANK_TRANSFER_CREATED: 768,
  COMMAND_BANK_TRANSFER_EXECUTED: 769,
  ACCOUNT_CONNECTED: 8193,
  ACCOUNT_VERIFICATION_STATUS_CHANGED: 8194,
  TRANSFER_CREATED: 8448,
  TRANSACTION_POS_ECR_SESSION_CREATED: 1802,
  TRANSACTION_POS_ECR_SESSION_FAILED: 1803,
} as const;

// Validation schemas
const vivaTransactionEventDataSchema = z.object({
  Moto: z.boolean().optional(),
  BinId: z.number().optional(),
  IsDcc: z.boolean().optional(),
  Ucaf: z.string().optional(),
  Email: z.string().email().optional(),
  Phone: z.string().optional(),
  BankId: z.string().optional(),
  Systemic: z.boolean().optional(),
  BatchId: z.string().optional(),
  Switching: z.boolean().optional(),
  ParentId: z.string().nullable().optional(),
  Amount: z.number(),
  ChannelId: z.string().optional(),
  TerminalId: z.number().optional(),
  MerchantId: z.string(),
  OrderCode: z.number(),
  ProductId: z.string().nullable().optional(),
  StatusId: z.string(),
  FullName: z.string().optional(),
  ResellerId: z.string().nullable().optional(),
  DualMessage: z.boolean().optional(),
  InsDate: z.string(),
  TotalFee: z.number().optional(),
  CardToken: z.string().optional(),
  CardNumber: z.string().optional(),
  Descriptor: z.string().nullable().optional(),
  TipAmount: z.number().optional(),
  SourceCode: z.string().optional(),
  SourceName: z.string().optional(),
  Latitude: z.number().nullable().optional(),
  Longitude: z.number().nullable().optional(),
  CompanyName: z.string().optional(),
  TransactionId: z.string(),
  CompanyTitle: z.string().optional(),
  PanEntryMode: z.string().optional(),
  ReferenceNumber: z.number().optional(),
  ResponseCode: z.string().optional(),
  CurrencyCode: z.string().optional(),
  OrderCulture: z.string().optional(),
  MerchantTrns: z.string().optional(),
  CustomerTrns: z.string().optional(),
  IsManualRefund: z.boolean().optional(),
  TargetPersonId: z.string().nullable().optional(),
  TargetWalletId: z.string().nullable().optional(),
  AcquirerApproved: z.boolean().optional(),
  LoyaltyTriggered: z.boolean().optional(),
  TransactionTypeId: z.number().optional(),
  AuthorizationId: z.string().optional(),
  TotalInstallments: z.number().optional(),
  CardCountryCode: z.string().optional(),
  CardIssuingBank: z.string().optional(),
  RedeemedAmount: z.number().optional(),
  ClearanceDate: z.string().nullable().optional(),
  ConversionRate: z.number().optional(),
  CurrentInstallment: z.number().optional(),
  OriginalAmount: z.number().optional(),
  Tags: z.array(z.string()).optional(),
  BillId: z.string().nullable().optional(),
  ConnectedAccountId: z.string().nullable().optional(),
  ResellerSourceCode: z.string().nullable().optional(),
  ResellerSourceName: z.string().nullable().optional(),
  MerchantCategoryCode: z.number().optional(),
  ResellerCompanyName: z.string().nullable().optional(),
  CardUniqueReference: z.string().optional(),
  OriginalCurrencyCode: z.string().optional(),
  ExternalTransactionId: z.string().nullable().optional(),
  ResellerSourceAddress: z.string().nullable().optional(),
  CardExpirationDate: z.string().optional(),
  ServiceId: z.string().nullable().optional(),
  RetrievalReferenceNumber: z.string().optional(),
  AssignedMerchantUsers: z.array(z.any()).optional(),
  AssignedResellerUsers: z.array(z.any()).optional(),
  CardTypeId: z.number().optional(),
  ResponseEventId: z.string().nullable().optional(),
  ElectronicCommerceIndicator: z.string().optional(),
  OrderServiceId: z.number().optional(),
  ApplicationIdentifierTerminal: z.string().nullable().optional(),
  IntegrationId: z.string().nullable().optional(),
  CardProductCategoryId: z.number().optional(),
  CardProductAccountTypeId: z.number().optional(),
  DigitalWalletId: z.number().optional(),
  DccSessionId: z.string().nullable().optional(),
  DccMarkup: z.number().nullable().optional(),
  DccDifferenceOverEcb: z.number().nullable().optional(),
});

const vivaWebhookEventSchema = z.object({
  Url: z.string().url(),
  EventData: vivaTransactionEventDataSchema,
  Created: z.string(),
  CorrelationId: z.string(),
  EventTypeId: z.number(),
  Delay: z.number().nullable().optional(),
  RetryCount: z.number(),
  RetryDelayInSeconds: z.number().nullable().optional(),
  MessageId: z.string(),
  RecipientId: z.string(),
  MessageTypeId: z.number(),
});

export type VivaWebhookEvent = z.infer<typeof vivaWebhookEventSchema>;
export type VivaTransactionEventData = z.infer<typeof vivaTransactionEventDataSchema>;

export interface VivaWebhookVerificationResponse {
  Key: string;
}

export interface VivaWebhookProcessingResult {
  processed: boolean;
  action?: string;
  transactionId?: string;
  error?: string;
}

class VivaWebhookService {
  private readonly merchantId: string;
  private readonly apiKey: string;
  private readonly environment: string;
  private readonly apiUrl: string;

  constructor() {
    this.merchantId = env.VIVA_MERCHANT_ID;
    this.apiKey = env.VIVA_API_KEY;
    this.environment = env.VIVA_ENVIRONMENT;
    this.apiUrl = env.VIVA_API_URL || (this.environment === 'production'
      ? 'https://www.vivapayments.com'
      : 'https://demo.vivapayments.com');

    vivaWebhookLogger.info('Viva Webhook Service initialized', {
      environment: this.environment,
      merchantId: this.merchantId,
      apiUrl: this.apiUrl,
    });
  }

  /**
   * Generate webhook verification key from Viva
   * This key is used to verify webhook authenticity
   */
  async generateWebhookVerificationKey(): Promise<VivaWebhookVerificationResponse> {
    try {
      vivaWebhookLogger.info('Generating Viva webhook verification key');

      const credentials = Buffer.from(`${this.merchantId}:${this.apiKey}`).toString('base64');
      
      const response = await axios.get(`${this.apiUrl}/api/messages/config/token`, {
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });

      vivaWebhookLogger.info('Webhook verification key generated successfully', {
        keyLength: response.data.Key?.length || 0,
      });

      return response.data;
    } catch (error) {
      vivaWebhookLogger.error('Failed to generate webhook verification key', error);
      throw new Error('Failed to generate webhook verification key');
    }
  }

  /**
   * Validate webhook event structure
   */
  validateWebhookEvent(payload: any): VivaWebhookEvent {
    try {
      return vivaWebhookEventSchema.parse(payload);
    } catch (error) {
      vivaWebhookLogger.error('Invalid webhook event structure', { error, payload });
      throw new Error('Invalid webhook event structure');
    }
  }

  /**
   * Verify webhook authenticity (placeholder for future implementation)
   * Viva doesn't provide signature verification in their documentation,
   * but we can implement IP whitelisting and other security measures
   */
  verifyWebhookAuthenticity(payload: string, headers: Record<string, any>): boolean {
    try {
      // For now, we'll implement basic verification
      // In production, you should implement IP whitelisting based on Viva's IP ranges
      
      // Check if the request comes from allowed IPs (implement IP whitelisting)
      const allowedIPs = this.environment === 'production' 
        ? [
            '*************', '************', '************',
            '***********', '**************/28', '**************/28',
            '***********', '***********', '***********/28'
          ]
        : [
            '************', '***********', '************',
            '************', '************', '*************',
            '*************', '************'
          ];

      // For development, we'll allow all requests
      // In production, implement proper IP validation
      if (this.environment === 'demo') {
        return true;
      }

      // TODO: Implement IP whitelisting validation
      return true;
    } catch (error) {
      vivaWebhookLogger.error('Webhook verification failed', error);
      return false;
    }
  }

  /**
   * Get event type description
   */
  getEventTypeDescription(eventTypeId: number): string {
    const eventDescriptions: Record<number, string> = {
      [VIVA_WEBHOOK_EVENTS.TRANSACTION_PAYMENT_CREATED]: 'Transaction Payment Created',
      [VIVA_WEBHOOK_EVENTS.TRANSACTION_FAILED]: 'Transaction Failed',
      [VIVA_WEBHOOK_EVENTS.TRANSACTION_PRICE_CALCULATED]: 'Transaction Price Calculated',
      [VIVA_WEBHOOK_EVENTS.TRANSACTION_REVERSAL_CREATED]: 'Transaction Reversal Created',
      [VIVA_WEBHOOK_EVENTS.ACCOUNT_TRANSACTION_CREATED]: 'Account Transaction Created',
      [VIVA_WEBHOOK_EVENTS.COMMAND_BANK_TRANSFER_CREATED]: 'Command Bank Transfer Created',
      [VIVA_WEBHOOK_EVENTS.COMMAND_BANK_TRANSFER_EXECUTED]: 'Command Bank Transfer Executed',
      [VIVA_WEBHOOK_EVENTS.ACCOUNT_CONNECTED]: 'Account Connected',
      [VIVA_WEBHOOK_EVENTS.ACCOUNT_VERIFICATION_STATUS_CHANGED]: 'Account Verification Status Changed',
      [VIVA_WEBHOOK_EVENTS.TRANSFER_CREATED]: 'Transfer Created',
      [VIVA_WEBHOOK_EVENTS.TRANSACTION_POS_ECR_SESSION_CREATED]: 'Transaction POS ECR Session Created',
      [VIVA_WEBHOOK_EVENTS.TRANSACTION_POS_ECR_SESSION_FAILED]: 'Transaction POS ECR Session Failed',
    };

    return eventDescriptions[eventTypeId] || `Unknown Event Type: ${eventTypeId}`;
  }
}

export const vivaWebhookService = new VivaWebhookService();
