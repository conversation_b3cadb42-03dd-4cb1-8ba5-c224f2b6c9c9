{"version": 3, "sources": ["asyncHookWrappers.ts"], "names": ["useState", "useEffect", "useMemo", "NativeEventEmitter", "NativeModules", "useOnMount", "asyncGetter", "initialResult", "response", "setResponse", "loading", "result", "getAsync", "deviceInfoEmitter", "RNDeviceInfo", "useOnEvent", "eventName", "initialValueAsyncGetter", "defaultValue", "setResult", "subscription", "addListener", "remove"], "mappings": "AAAA,SAASA,QAAT,EAAmBC,SAAnB,EAA8BC,OAA9B,QAA6C,OAA7C;AACA,SAASC,kBAAT,EAA6BC,aAA7B,QAAkD,cAAlD;;AAGA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAT,CAAuBC,WAAvB,EAAsDC,aAAtD,EAA4F;AACjG,QAAM,CAACC,QAAD,EAAWC,WAAX,IAA0BT,QAAQ,CAAqB;AAC3DU,IAAAA,OAAO,EAAE,IADkD;AAE3DC,IAAAA,MAAM,EAAEJ;AAFmD,GAArB,CAAxC;AAKAN,EAAAA,SAAS,CAAC,MAAM;AACd;AACA,UAAMW,QAAQ,GAAG,YAAY;AAC3B,YAAMD,MAAM,GAAG,MAAML,WAAW,EAAhC;AACAG,MAAAA,WAAW,CAAC;AAAEC,QAAAA,OAAO,EAAE,KAAX;AAAkBC,QAAAA;AAAlB,OAAD,CAAX;AACD,KAHD;;AAKAC,IAAAA,QAAQ;AACT,GARQ,EAQN,CAACN,WAAD,CARM,CAAT;AAUA,SAAOE,QAAP;AACD;AAED,OAAO,MAAMK,iBAAiB,GAAG,IAAIV,kBAAJ,CAAuBC,aAAa,CAACU,YAArC,CAA1B;AAEP;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,UAAT,CACLC,SADK,EAELC,uBAFK,EAGLC,YAHK,EAIe;AACpB,QAAM;AAAER,IAAAA,OAAF;AAAWC,IAAAA,MAAM,EAAEJ;AAAnB,MAAqCF,UAAU,CAACY,uBAAD,EAA0BC,YAA1B,CAArD;AACA,QAAM,CAACP,MAAD,EAASQ,SAAT,IAAsBnB,QAAQ,CAAIkB,YAAJ,CAApC,CAFoB,CAIpB;;AACAjB,EAAAA,SAAS,CAAC,MAAM;AACdkB,IAAAA,SAAS,CAACZ,aAAD,CAAT;AACD,GAFQ,EAEN,CAACA,aAAD,CAFM,CAAT,CALoB,CASpB;AACA;;AACAN,EAAAA,SAAS,CAAC,MAAM;AACd,UAAMmB,YAAY,GAAGP,iBAAiB,CAACQ,WAAlB,CAA8BL,SAA9B,EAAyCG,SAAzC,CAArB;AACA,WAAO,MAAMC,YAAY,CAACE,MAAb,EAAb;AACD,GAHQ,EAGN,CAACN,SAAD,CAHM,CAAT,CAXoB,CAgBpB;;AACA,SAAOd,OAAO,CAAC,OAAO;AAAEQ,IAAAA,OAAF;AAAWC,IAAAA;AAAX,GAAP,CAAD,EAA8B,CAACD,OAAD,EAAUC,MAAV,CAA9B,CAAd;AACD", "sourcesContent": ["import { useState, useEffect, useMemo } from 'react';\nimport { NativeEventEmitter, NativeModules } from 'react-native';\nimport type { AsyncHookResult } from './types';\n\n/**\n * simple hook wrapper for async functions for 'on-mount / componentDidMount' that only need to fired once\n * @param asyncGetter async function that 'gets' something\n * @param initialResult -1 | false | 'unknown'\n */\nexport function useOnMount<T>(asyncGetter: () => Promise<T>, initialResult: T): AsyncHookResult<T> {\n  const [response, setResponse] = useState<AsyncHookResult<T>>({\n    loading: true,\n    result: initialResult,\n  });\n\n  useEffect(() => {\n    // async function cuz react complains if useEffect's effect param is an async function\n    const getAsync = async () => {\n      const result = await asyncGetter();\n      setResponse({ loading: false, result });\n    };\n\n    getAsync();\n  }, [asyncGetter]);\n\n  return response;\n}\n\nexport const deviceInfoEmitter = new NativeEventEmitter(NativeModules.RNDeviceInfo);\n\n/**\n * simple hook wrapper for handling events\n * @param eventName\n * @param initialValueAsyncGetter\n * @param defaultValue\n */\nexport function useOnEvent<T>(\n  eventName: string,\n  initialValueAsyncGetter: () => Promise<T>,\n  defaultValue: T\n): AsyncHookResult<T> {\n  const { loading, result: initialResult } = useOnMount(initialValueAsyncGetter, defaultValue);\n  const [result, setResult] = useState<T>(defaultValue);\n\n  // sets the result to what the intial value is on mount\n  useEffect(() => {\n    setResult(initialResult);\n  }, [initialResult]);\n\n  // - set up the event listener to set the result\n  // - set up the clean up function to remove subscription on unmount\n  useEffect(() => {\n    const subscription = deviceInfoEmitter.addListener(eventName, setResult);\n    return () => subscription.remove();\n  }, [eventName]);\n\n  // loading will only be true while getting the inital value. After that, it will always be false, but a new result may occur\n  return useMemo(() => ({ loading, result }), [loading, result]);\n}\n"]}