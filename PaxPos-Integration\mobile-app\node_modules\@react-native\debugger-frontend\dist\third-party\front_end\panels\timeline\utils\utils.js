import*as e from"../../../core/common/common.js";import*as r from"../../../models/bindings/bindings.js";var s=Object.freeze({__proto__:null,TimelineNetworkRequest:class{#e;constructor(s){const o=s.args.data.url,t=e.ParsedURL.ParsedURL.urlWithoutHash(o),u=r.ResourceUtils.resourceForURL(o)||r.ResourceUtils.resourceForURL(t);this.#e=u?.request??null}get request(){return this.#e}}});export{s as NetworkRequest};
