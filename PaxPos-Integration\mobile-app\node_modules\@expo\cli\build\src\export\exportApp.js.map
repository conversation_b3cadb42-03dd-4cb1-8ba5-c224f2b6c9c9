{"version": 3, "sources": ["../../../src/export/exportApp.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport type { Platform } from '@expo/config';\nimport { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { type PlatformMetadata, createMetadataJson } from './createMetadataJson';\nimport { exportAssetsAsync } from './exportAssets';\nimport {\n  addDomBundleToMetadataAsync,\n  exportDomComponentAsync,\n  transformNativeBundleForMd5Filename,\n  transformDomEntryForMd5Filename,\n} from './exportDomComponents';\nimport { assertEngineMismatchAsync, isEnableHermesManaged } from './exportHermes';\nimport { exportApiRoutesStandaloneAsync, exportFromServerAsync } from './exportStaticAsync';\nimport { getVirtualFaviconAssetsAsync } from './favicon';\nimport { getPublicExpoManifestAsync } from './getPublicExpoManifest';\nimport { copyPublicFolderAsync } from './publicFolder';\nimport { Options } from './resolveOptions';\nimport {\n  ExportAssetMap,\n  BundleOutput,\n  getFilesFromSerialAssets,\n  persistMetroFilesAsync,\n  BundleAssetWithFileHashes,\n} from './saveAssets';\nimport { createAssetMap } from './writeContents';\nimport * as Log from '../log';\nimport { WebSupportProjectPrerequisite } from '../start/doctor/web/WebSupportProjectPrerequisite';\nimport { DevServerManager } from '../start/server/DevServerManager';\nimport { MetroBundlerDevServer } from '../start/server/metro/MetroBundlerDevServer';\nimport { getRouterDirectoryModuleIdWithManifest } from '../start/server/metro/router';\nimport { serializeHtmlWithAssets } from '../start/server/metro/serializeHtml';\nimport { getEntryWithServerRoot } from '../start/server/middleware/ManifestMiddleware';\nimport { getBaseUrlFromExpoConfig } from '../start/server/middleware/metroOptions';\nimport { createTemplateHtmlFromExpoConfigAsync } from '../start/server/webTemplate';\nimport { env } from '../utils/env';\nimport { CommandError } from '../utils/errors';\nimport { setNodeEnv } from '../utils/nodeEnv';\n\nexport async function exportAppAsync(\n  projectRoot: string,\n  {\n    platforms,\n    outputDir,\n    clear,\n    dev,\n    dumpAssetmap,\n    sourceMaps,\n    minify,\n    bytecode,\n    maxWorkers,\n    skipSSG,\n  }: Pick<\n    Options,\n    | 'dumpAssetmap'\n    | 'sourceMaps'\n    | 'dev'\n    | 'clear'\n    | 'outputDir'\n    | 'platforms'\n    | 'minify'\n    | 'bytecode'\n    | 'maxWorkers'\n    | 'skipSSG'\n  >\n): Promise<void> {\n  // Force the environment during export and do not allow overriding it.\n  const environment = dev ? 'development' : 'production';\n  process.env.NODE_ENV = environment;\n  setNodeEnv(environment);\n\n  require('@expo/env').load(projectRoot);\n\n  const projectConfig = getConfig(projectRoot);\n  const exp = await getPublicExpoManifestAsync(projectRoot, {\n    // Web doesn't require validation.\n    skipValidation: platforms.length === 1 && platforms[0] === 'web',\n  });\n\n  if (platforms.includes('web')) {\n    await new WebSupportProjectPrerequisite(projectRoot).assertAsync();\n  }\n\n  const useServerRendering = ['static', 'server'].includes(exp.web?.output ?? '');\n\n  if (skipSSG && exp.web?.output !== 'server') {\n    throw new CommandError('--no-ssg can only be used with `web.output: server`');\n  }\n\n  const baseUrl = getBaseUrlFromExpoConfig(exp);\n\n  if (!bytecode && (platforms.includes('ios') || platforms.includes('android'))) {\n    Log.warn(\n      `Bytecode makes the app startup faster, disabling bytecode is highly discouraged and should only be used for debugging purposes.`\n    );\n  }\n\n  // Print out logs\n  if (baseUrl) {\n    Log.log();\n    Log.log(chalk.gray`Using (experimental) base path: ${baseUrl}`);\n    // Warn if not using an absolute path.\n    if (!baseUrl.startsWith('/')) {\n      Log.log(\n        chalk.yellow`  Base path does not start with a slash. Requests will not be absolute.`\n      );\n    }\n  }\n\n  const mode = dev ? 'development' : 'production';\n  const publicPath = path.resolve(projectRoot, env.EXPO_PUBLIC_FOLDER);\n  const outputPath = path.resolve(projectRoot, outputDir);\n\n  // Write the JS bundles to disk, and get the bundle file names (this could change with async chunk loading support).\n\n  const files: ExportAssetMap = new Map();\n\n  const devServerManager = await DevServerManager.startMetroAsync(projectRoot, {\n    minify,\n    mode,\n    port: 8081,\n    isExporting: true,\n    location: {},\n    resetDevServer: clear,\n    maxWorkers,\n  });\n\n  const devServer = devServerManager.getDefaultDevServer();\n  assert(devServer instanceof MetroBundlerDevServer);\n\n  const bundles: Partial<Record<Platform, BundleOutput>> = {};\n  const domComponentAssetsMetadata: Partial<Record<Platform, PlatformMetadata['assets']>> = {};\n\n  const spaPlatforms =\n    // TODO: Support server and static rendering for server component exports.\n    useServerRendering && !devServer.isReactServerComponentsEnabled\n      ? platforms.filter((platform) => platform !== 'web')\n      : platforms;\n\n  try {\n    if (devServer.isReactServerComponentsEnabled) {\n      // In RSC mode, we only need these to be in the client dir.\n      // TODO: Merge back with other copy after we add SSR.\n      try {\n        await copyPublicFolderAsync(publicPath, path.join(outputPath, 'client'));\n      } catch (error) {\n        Log.error('Failed to copy public directory to dist directory');\n        throw error;\n      }\n    } else {\n      // NOTE(kitten): The public folder is currently always copied, regardless of targetDomain\n      // split. Hence, there's another separate `copyPublicFolderAsync` call below for `web`\n      await copyPublicFolderAsync(publicPath, outputPath);\n    }\n\n    let templateHtml: string | undefined;\n    // Can be empty during web-only SSG.\n    if (spaPlatforms.length) {\n      await Promise.all(\n        spaPlatforms.map(async (platform) => {\n          // Assert early so the user doesn't have to wait until bundling is complete to find out that\n          // Hermes won't be available.\n          const isHermes = isEnableHermesManaged(exp, platform);\n          if (isHermes) {\n            await assertEngineMismatchAsync(projectRoot, exp, platform);\n          }\n\n          let bundle: {\n            artifacts: SerialAsset[];\n            assets: readonly BundleAssetWithFileHashes[];\n            files?: ExportAssetMap;\n          };\n\n          try {\n            // Run metro bundler and create the JS bundles/source maps.\n            bundle = await devServer.nativeExportBundleAsync(\n              exp,\n              {\n                platform,\n                splitChunks:\n                  !env.EXPO_NO_BUNDLE_SPLITTING &&\n                  ((devServer.isReactServerComponentsEnabled && !bytecode) || platform === 'web'),\n                mainModuleName: getEntryWithServerRoot(projectRoot, {\n                  platform,\n                  pkg: projectConfig.pkg,\n                }),\n                mode: dev ? 'development' : 'production',\n                engine: isHermes ? 'hermes' : undefined,\n                serializerIncludeMaps: sourceMaps,\n                bytecode: bytecode && isHermes,\n                reactCompiler: !!exp.experiments?.reactCompiler,\n              },\n              files\n            );\n          } catch (error) {\n            Log.log('');\n            if (error instanceof Error) {\n              Log.exception(error);\n            } else {\n              Log.error('Failed to bundle the app');\n              Log.log(error as any);\n            }\n            process.exit(1);\n          }\n\n          bundles[platform] = bundle;\n\n          getFilesFromSerialAssets(bundle.artifacts, {\n            includeSourceMaps: sourceMaps,\n            files,\n            isServerHosted: devServer.isReactServerComponentsEnabled,\n          });\n\n          // TODO: Remove duplicates...\n          const expoDomComponentReferences = bundle.artifacts\n            .map((artifact) =>\n              Array.isArray(artifact.metadata.expoDomComponentReferences)\n                ? artifact.metadata.expoDomComponentReferences\n                : []\n            )\n            .flat();\n          await Promise.all(\n            // TODO: Make a version of this which uses `this.metro.getBundler().buildGraphForEntries([])` to bundle all the DOM components at once.\n            expoDomComponentReferences.map(async (filePath) => {\n              const { bundle: platformDomComponentsBundle, htmlOutputName } =\n                await exportDomComponentAsync({\n                  filePath,\n                  projectRoot,\n                  dev,\n                  devServer,\n                  isHermes,\n                  includeSourceMaps: sourceMaps,\n                  exp,\n                  files,\n                  useMd5Filename: true,\n                });\n\n              // Merge the assets from the DOM component into the output assets.\n              // @ts-expect-error: mutate assets\n              bundle.assets.push(...platformDomComponentsBundle.assets);\n\n              transformNativeBundleForMd5Filename({\n                domComponentReference: filePath,\n                nativeBundle: bundle,\n                files,\n                htmlOutputName,\n              });\n              domComponentAssetsMetadata[platform] = [\n                ...(await addDomBundleToMetadataAsync(platformDomComponentsBundle)),\n                ...transformDomEntryForMd5Filename({\n                  files,\n                  htmlOutputName,\n                }),\n              ];\n            })\n          );\n\n          if (platform === 'web') {\n            // TODO: Unify with exportStaticAsync\n            // TODO: Maybe move to the serializer.\n            let html = await serializeHtmlWithAssets({\n              isExporting: true,\n              resources: bundle.artifacts,\n              template: await createTemplateHtmlFromExpoConfigAsync(projectRoot, {\n                scripts: [],\n                cssLinks: [],\n                exp: projectConfig.exp,\n              }),\n              baseUrl,\n            });\n\n            // Add the favicon assets to the HTML.\n            const modifyHtml = await getVirtualFaviconAssetsAsync(projectRoot, {\n              outputDir,\n              baseUrl,\n              files,\n              exp: projectConfig.exp,\n            });\n            if (modifyHtml) {\n              html = modifyHtml(html);\n            }\n\n            // HACK: This is used for adding SSR shims in React Server Components.\n            templateHtml = html;\n\n            // Generate SPA-styled HTML file.\n            // If web exists, then write the template HTML file.\n            files.set('index.html', {\n              contents: html,\n              targetDomain: devServer.isReactServerComponentsEnabled ? 'server' : 'client',\n            });\n          }\n        })\n      );\n\n      if (devServer.isReactServerComponentsEnabled) {\n        const isWeb = platforms.includes('web');\n\n        await exportApiRoutesStandaloneAsync(devServer, {\n          files,\n          platform: 'web',\n          apiRoutesOnly: !isWeb,\n          templateHtml,\n        });\n      }\n\n      // TODO: Use same asset system across platforms again.\n      const { assets, embeddedHashSet } = await exportAssetsAsync(projectRoot, {\n        files,\n        exp,\n        outputDir: outputPath,\n        bundles,\n        baseUrl,\n      });\n\n      if (dumpAssetmap) {\n        Log.log('Creating asset map');\n        files.set('assetmap.json', { contents: JSON.stringify(createAssetMap({ assets })) });\n      }\n\n      const targetDomain = devServer.isReactServerComponentsEnabled ? 'client/' : '';\n      const fileNames = Object.fromEntries(\n        Object.entries(bundles).map(([platform, bundle]) => [\n          platform,\n          bundle.artifacts\n            .filter((asset) => asset.type === 'js')\n            .map((asset) => targetDomain + asset.filename),\n        ])\n      );\n\n      // Generate a `metadata.json` for EAS Update.\n      const contents = createMetadataJson({\n        bundles,\n        fileNames,\n        embeddedHashSet,\n        domComponentAssetsMetadata,\n      });\n      files.set('metadata.json', { contents: JSON.stringify(contents) });\n    }\n\n    // Additional web-only steps...\n\n    if (platforms.includes('web') && useServerRendering) {\n      const exportServer = exp.web?.output === 'server';\n\n      if (exportServer) {\n        // TODO: Remove when this is abstracted into the files map\n        await copyPublicFolderAsync(publicPath, path.resolve(outputPath, 'client'));\n      }\n\n      if (skipSSG) {\n        Log.log('Skipping static site generation');\n        await exportApiRoutesStandaloneAsync(devServer, {\n          files,\n          platform: 'web',\n          apiRoutesOnly: true,\n        });\n\n        // Output a placeholder index.html if one doesn't exist in the public directory.\n        // This ensures native + API routes have some content at the root URL.\n        const placeholderIndex = path.resolve(outputPath, 'client/index.html');\n        if (!fs.existsSync(placeholderIndex)) {\n          files.set('index.html', {\n            contents: `<html><body></body></html>`,\n            targetDomain: 'client',\n          });\n        }\n      } else if (\n        // TODO: Support static export with RSC.\n        !devServer.isReactServerComponentsEnabled\n      ) {\n        await exportFromServerAsync(projectRoot, devServer, {\n          mode,\n          files,\n          clear: !!clear,\n          outputDir: outputPath,\n          minify,\n          baseUrl,\n          includeSourceMaps: sourceMaps,\n          routerRoot: getRouterDirectoryModuleIdWithManifest(projectRoot, exp),\n          reactCompiler: !!exp.experiments?.reactCompiler,\n          exportServer,\n          maxWorkers,\n          isExporting: true,\n          exp: projectConfig.exp,\n        });\n      }\n    }\n  } finally {\n    await devServerManager.stopAsync();\n  }\n\n  // Write all files at the end for unified logging.\n  await persistMetroFilesAsync(files, outputPath);\n}\n"], "names": ["exportAppAsync", "projectRoot", "platforms", "outputDir", "clear", "dev", "dumpAssetmap", "sourceMaps", "minify", "bytecode", "maxWorkers", "skipSSG", "exp", "environment", "process", "env", "NODE_ENV", "setNodeEnv", "require", "load", "projectConfig", "getConfig", "getPublicExpoManifestAsync", "skipValidation", "length", "includes", "WebSupportProjectPrerequisite", "assertAsync", "useServerRendering", "web", "output", "CommandError", "baseUrl", "getBaseUrlFromExpoConfig", "Log", "warn", "log", "chalk", "gray", "startsWith", "yellow", "mode", "publicPath", "path", "resolve", "EXPO_PUBLIC_FOLDER", "outputPath", "files", "Map", "devServerManager", "DevServerManager", "startMetroAsync", "port", "isExporting", "location", "resetDevServer", "devServer", "getDefaultDevServer", "assert", "MetroBundlerDevServer", "bundles", "domComponentAssetsMetadata", "spaPlatforms", "isReactServerComponentsEnabled", "filter", "platform", "copyPublicFolderAsync", "join", "error", "templateHtml", "Promise", "all", "map", "isHermes", "isEnableHermesManaged", "assertEngineMismatchAsync", "bundle", "nativeExportBundleAsync", "splitChunks", "EXPO_NO_BUNDLE_SPLITTING", "mainModuleName", "getEntryWithServerRoot", "pkg", "engine", "undefined", "serializerIncludeMaps", "reactCompiler", "experiments", "Error", "exception", "exit", "getFilesFromSerialAssets", "artifacts", "includeSourceMaps", "isServerHosted", "expoDomComponentReferences", "artifact", "Array", "isArray", "metadata", "flat", "filePath", "platformDomComponentsBundle", "htmlOutputName", "exportDomComponentAsync", "useMd5Filename", "assets", "push", "transformNativeBundleForMd5Filename", "domComponentReference", "nativeBundle", "addDomBundleToMetadataAsync", "transformDomEntryForMd5Filename", "html", "serializeHtmlWithAssets", "resources", "template", "createTemplateHtmlFromExpoConfigAsync", "scripts", "cssLinks", "modifyHtml", "getVirtualFaviconAssetsAsync", "set", "contents", "targetDomain", "isWeb", "exportApiRoutesStandaloneAsync", "apiRoutesOnly", "embeddedHashSet", "exportAssetsAsync", "JSON", "stringify", "createAssetMap", "fileNames", "Object", "fromEntries", "entries", "asset", "type", "filename", "createMetadataJson", "exportServer", "placeholderIndex", "fs", "existsSync", "exportFromServerAsync", "routerRoot", "getRouterDirectoryModuleIdWithManifest", "stopAsync", "persistMetroFilesAsync"], "mappings": ";;;;+BA2CsBA;;;eAAAA;;;;yBA3CI;;;;;;;gEAGP;;;;;;;gEACD;;;;;;;gEACH;;;;;;;gEACE;;;;;;oCAEyC;8BACxB;qCAM3B;8BAC0D;mCACK;yBACzB;uCACF;8BACL;4BAQ/B;+BACwB;6DACV;+CACyB;kCACb;uCACK;wBACiB;+BACf;oCACD;8BACE;6BACa;qBAClC;wBACS;yBACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB,eAAeA,eACpBC,WAAmB,EACnB,EACEC,SAAS,EACTC,SAAS,EACTC,KAAK,EACLC,GAAG,EACHC,YAAY,EACZC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,OAAO,EAaR;QAmBwDC,UAE1CA;IAnBf,sEAAsE;IACtE,MAAMC,cAAcR,MAAM,gBAAgB;IAC1CS,QAAQC,GAAG,CAACC,QAAQ,GAAGH;IACvBI,IAAAA,mBAAU,EAACJ;IAEXK,QAAQ,aAAaC,IAAI,CAAClB;IAE1B,MAAMmB,gBAAgBC,IAAAA,mBAAS,EAACpB;IAChC,MAAMW,MAAM,MAAMU,IAAAA,iDAA0B,EAACrB,aAAa;QACxD,kCAAkC;QAClCsB,gBAAgBrB,UAAUsB,MAAM,KAAK,KAAKtB,SAAS,CAAC,EAAE,KAAK;IAC7D;IAEA,IAAIA,UAAUuB,QAAQ,CAAC,QAAQ;QAC7B,MAAM,IAAIC,4DAA6B,CAACzB,aAAa0B,WAAW;IAClE;IAEA,MAAMC,qBAAqB;QAAC;QAAU;KAAS,CAACH,QAAQ,CAACb,EAAAA,WAAAA,IAAIiB,GAAG,qBAAPjB,SAASkB,MAAM,KAAI;IAE5E,IAAInB,WAAWC,EAAAA,YAAAA,IAAIiB,GAAG,qBAAPjB,UAASkB,MAAM,MAAK,UAAU;QAC3C,MAAM,IAAIC,oBAAY,CAAC;IACzB;IAEA,MAAMC,UAAUC,IAAAA,sCAAwB,EAACrB;IAEzC,IAAI,CAACH,YAAaP,CAAAA,UAAUuB,QAAQ,CAAC,UAAUvB,UAAUuB,QAAQ,CAAC,UAAS,GAAI;QAC7ES,KAAIC,IAAI,CACN,CAAC,+HAA+H,CAAC;IAErI;IAEA,iBAAiB;IACjB,IAAIH,SAAS;QACXE,KAAIE,GAAG;QACPF,KAAIE,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAC,gCAAgC,EAAEN,QAAQ,CAAC;QAC9D,sCAAsC;QACtC,IAAI,CAACA,QAAQO,UAAU,CAAC,MAAM;YAC5BL,KAAIE,GAAG,CACLC,gBAAK,CAACG,MAAM,CAAC,uEAAuE,CAAC;QAEzF;IACF;IAEA,MAAMC,OAAOpC,MAAM,gBAAgB;IACnC,MAAMqC,aAAaC,eAAI,CAACC,OAAO,CAAC3C,aAAac,QAAG,CAAC8B,kBAAkB;IACnE,MAAMC,aAAaH,eAAI,CAACC,OAAO,CAAC3C,aAAaE;IAE7C,oHAAoH;IAEpH,MAAM4C,QAAwB,IAAIC;IAElC,MAAMC,mBAAmB,MAAMC,kCAAgB,CAACC,eAAe,CAAClD,aAAa;QAC3EO;QACAiC;QACAW,MAAM;QACNC,aAAa;QACbC,UAAU,CAAC;QACXC,gBAAgBnD;QAChBM;IACF;IAEA,MAAM8C,YAAYP,iBAAiBQ,mBAAmB;IACtDC,IAAAA,iBAAM,EAACF,qBAAqBG,4CAAqB;IAEjD,MAAMC,UAAmD,CAAC;IAC1D,MAAMC,6BAAoF,CAAC;IAE3F,MAAMC,eACJ,0EAA0E;IAC1ElC,sBAAsB,CAAC4B,UAAUO,8BAA8B,GAC3D7D,UAAU8D,MAAM,CAAC,CAACC,WAAaA,aAAa,SAC5C/D;IAEN,IAAI;QACF,IAAIsD,UAAUO,8BAA8B,EAAE;YAC5C,2DAA2D;YAC3D,qDAAqD;YACrD,IAAI;gBACF,MAAMG,IAAAA,mCAAqB,EAACxB,YAAYC,eAAI,CAACwB,IAAI,CAACrB,YAAY;YAChE,EAAE,OAAOsB,OAAO;gBACdlC,KAAIkC,KAAK,CAAC;gBACV,MAAMA;YACR;QACF,OAAO;YACL,yFAAyF;YACzF,sFAAsF;YACtF,MAAMF,IAAAA,mCAAqB,EAACxB,YAAYI;QAC1C;QAEA,IAAIuB;QACJ,oCAAoC;QACpC,IAAIP,aAAatC,MAAM,EAAE;YACvB,MAAM8C,QAAQC,GAAG,CACfT,aAAaU,GAAG,CAAC,OAAOP;gBACtB,4FAA4F;gBAC5F,6BAA6B;gBAC7B,MAAMQ,WAAWC,IAAAA,mCAAqB,EAAC9D,KAAKqD;gBAC5C,IAAIQ,UAAU;oBACZ,MAAME,IAAAA,uCAAyB,EAAC1E,aAAaW,KAAKqD;gBACpD;gBAEA,IAAIW;gBAMJ,IAAI;wBAiBmBhE;oBAhBrB,2DAA2D;oBAC3DgE,SAAS,MAAMpB,UAAUqB,uBAAuB,CAC9CjE,KACA;wBACEqD;wBACAa,aACE,CAAC/D,QAAG,CAACgE,wBAAwB,IAC5B,CAAA,AAACvB,UAAUO,8BAA8B,IAAI,CAACtD,YAAawD,aAAa,KAAI;wBAC/Ee,gBAAgBC,IAAAA,0CAAsB,EAAChF,aAAa;4BAClDgE;4BACAiB,KAAK9D,cAAc8D,GAAG;wBACxB;wBACAzC,MAAMpC,MAAM,gBAAgB;wBAC5B8E,QAAQV,WAAW,WAAWW;wBAC9BC,uBAAuB9E;wBACvBE,UAAUA,YAAYgE;wBACtBa,eAAe,CAAC,GAAC1E,mBAAAA,IAAI2E,WAAW,qBAAf3E,iBAAiB0E,aAAa;oBACjD,GACAvC;gBAEJ,EAAE,OAAOqB,OAAO;oBACdlC,KAAIE,GAAG,CAAC;oBACR,IAAIgC,iBAAiBoB,OAAO;wBAC1BtD,KAAIuD,SAAS,CAACrB;oBAChB,OAAO;wBACLlC,KAAIkC,KAAK,CAAC;wBACVlC,KAAIE,GAAG,CAACgC;oBACV;oBACAtD,QAAQ4E,IAAI,CAAC;gBACf;gBAEA9B,OAAO,CAACK,SAAS,GAAGW;gBAEpBe,IAAAA,oCAAwB,EAACf,OAAOgB,SAAS,EAAE;oBACzCC,mBAAmBtF;oBACnBwC;oBACA+C,gBAAgBtC,UAAUO,8BAA8B;gBAC1D;gBAEA,6BAA6B;gBAC7B,MAAMgC,6BAA6BnB,OAAOgB,SAAS,CAChDpB,GAAG,CAAC,CAACwB,WACJC,MAAMC,OAAO,CAACF,SAASG,QAAQ,CAACJ,0BAA0B,IACtDC,SAASG,QAAQ,CAACJ,0BAA0B,GAC5C,EAAE,EAEPK,IAAI;gBACP,MAAM9B,QAAQC,GAAG,CACf,uIAAuI;gBACvIwB,2BAA2BvB,GAAG,CAAC,OAAO6B;oBACpC,MAAM,EAAEzB,QAAQ0B,2BAA2B,EAAEC,cAAc,EAAE,GAC3D,MAAMC,IAAAA,4CAAuB,EAAC;wBAC5BH;wBACApG;wBACAI;wBACAmD;wBACAiB;wBACAoB,mBAAmBtF;wBACnBK;wBACAmC;wBACA0D,gBAAgB;oBAClB;oBAEF,kEAAkE;oBAClE,kCAAkC;oBAClC7B,OAAO8B,MAAM,CAACC,IAAI,IAAIL,4BAA4BI,MAAM;oBAExDE,IAAAA,wDAAmC,EAAC;wBAClCC,uBAAuBR;wBACvBS,cAAclC;wBACd7B;wBACAwD;oBACF;oBACA1C,0BAA0B,CAACI,SAAS,GAAG;2BACjC,MAAM8C,IAAAA,gDAA2B,EAACT;2BACnCU,IAAAA,oDAA+B,EAAC;4BACjCjE;4BACAwD;wBACF;qBACD;gBACH;gBAGF,IAAItC,aAAa,OAAO;oBACtB,qCAAqC;oBACrC,sCAAsC;oBACtC,IAAIgD,OAAO,MAAMC,IAAAA,sCAAuB,EAAC;wBACvC7D,aAAa;wBACb8D,WAAWvC,OAAOgB,SAAS;wBAC3BwB,UAAU,MAAMC,IAAAA,kDAAqC,EAACpH,aAAa;4BACjEqH,SAAS,EAAE;4BACXC,UAAU,EAAE;4BACZ3G,KAAKQ,cAAcR,GAAG;wBACxB;wBACAoB;oBACF;oBAEA,sCAAsC;oBACtC,MAAMwF,aAAa,MAAMC,IAAAA,qCAA4B,EAACxH,aAAa;wBACjEE;wBACA6B;wBACAe;wBACAnC,KAAKQ,cAAcR,GAAG;oBACxB;oBACA,IAAI4G,YAAY;wBACdP,OAAOO,WAAWP;oBACpB;oBAEA,sEAAsE;oBACtE5C,eAAe4C;oBAEf,iCAAiC;oBACjC,oDAAoD;oBACpDlE,MAAM2E,GAAG,CAAC,cAAc;wBACtBC,UAAUV;wBACVW,cAAcpE,UAAUO,8BAA8B,GAAG,WAAW;oBACtE;gBACF;YACF;YAGF,IAAIP,UAAUO,8BAA8B,EAAE;gBAC5C,MAAM8D,QAAQ3H,UAAUuB,QAAQ,CAAC;gBAEjC,MAAMqG,IAAAA,iDAA8B,EAACtE,WAAW;oBAC9CT;oBACAkB,UAAU;oBACV8D,eAAe,CAACF;oBAChBxD;gBACF;YACF;YAEA,sDAAsD;YACtD,MAAM,EAAEqC,MAAM,EAAEsB,eAAe,EAAE,GAAG,MAAMC,IAAAA,+BAAiB,EAAChI,aAAa;gBACvE8C;gBACAnC;gBACAT,WAAW2C;gBACXc;gBACA5B;YACF;YAEA,IAAI1B,cAAc;gBAChB4B,KAAIE,GAAG,CAAC;gBACRW,MAAM2E,GAAG,CAAC,iBAAiB;oBAAEC,UAAUO,KAAKC,SAAS,CAACC,IAAAA,6BAAc,EAAC;wBAAE1B;oBAAO;gBAAI;YACpF;YAEA,MAAMkB,eAAepE,UAAUO,8BAA8B,GAAG,YAAY;YAC5E,MAAMsE,YAAYC,OAAOC,WAAW,CAClCD,OAAOE,OAAO,CAAC5E,SAASY,GAAG,CAAC,CAAC,CAACP,UAAUW,OAAO,GAAK;oBAClDX;oBACAW,OAAOgB,SAAS,CACb5B,MAAM,CAAC,CAACyE,QAAUA,MAAMC,IAAI,KAAK,MACjClE,GAAG,CAAC,CAACiE,QAAUb,eAAea,MAAME,QAAQ;iBAChD;YAGH,6CAA6C;YAC7C,MAAMhB,WAAWiB,IAAAA,sCAAkB,EAAC;gBAClChF;gBACAyE;gBACAL;gBACAnE;YACF;YACAd,MAAM2E,GAAG,CAAC,iBAAiB;gBAAEC,UAAUO,KAAKC,SAAS,CAACR;YAAU;QAClE;QAEA,+BAA+B;QAE/B,IAAIzH,UAAUuB,QAAQ,CAAC,UAAUG,oBAAoB;gBAC9BhB;YAArB,MAAMiI,eAAejI,EAAAA,YAAAA,IAAIiB,GAAG,qBAAPjB,UAASkB,MAAM,MAAK;YAEzC,IAAI+G,cAAc;gBAChB,0DAA0D;gBAC1D,MAAM3E,IAAAA,mCAAqB,EAACxB,YAAYC,eAAI,CAACC,OAAO,CAACE,YAAY;YACnE;YAEA,IAAInC,SAAS;gBACXuB,KAAIE,GAAG,CAAC;gBACR,MAAM0F,IAAAA,iDAA8B,EAACtE,WAAW;oBAC9CT;oBACAkB,UAAU;oBACV8D,eAAe;gBACjB;gBAEA,gFAAgF;gBAChF,sEAAsE;gBACtE,MAAMe,mBAAmBnG,eAAI,CAACC,OAAO,CAACE,YAAY;gBAClD,IAAI,CAACiG,aAAE,CAACC,UAAU,CAACF,mBAAmB;oBACpC/F,MAAM2E,GAAG,CAAC,cAAc;wBACtBC,UAAU,CAAC,0BAA0B,CAAC;wBACtCC,cAAc;oBAChB;gBACF;YACF,OAAO,IACL,wCAAwC;YACxC,CAACpE,UAAUO,8BAA8B,EACzC;oBAUmBnD;gBATnB,MAAMqI,IAAAA,wCAAqB,EAAChJ,aAAauD,WAAW;oBAClDf;oBACAM;oBACA3C,OAAO,CAAC,CAACA;oBACTD,WAAW2C;oBACXtC;oBACAwB;oBACA6D,mBAAmBtF;oBACnB2I,YAAYC,IAAAA,8CAAsC,EAAClJ,aAAaW;oBAChE0E,eAAe,CAAC,GAAC1E,mBAAAA,IAAI2E,WAAW,qBAAf3E,iBAAiB0E,aAAa;oBAC/CuD;oBACAnI;oBACA2C,aAAa;oBACbzC,KAAKQ,cAAcR,GAAG;gBACxB;YACF;QACF;IACF,SAAU;QACR,MAAMqC,iBAAiBmG,SAAS;IAClC;IAEA,kDAAkD;IAClD,MAAMC,IAAAA,kCAAsB,EAACtG,OAAOD;AACtC"}