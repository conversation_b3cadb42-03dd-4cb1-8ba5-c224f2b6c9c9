{"version": 3, "sources": ["index.ts"], "names": ["_configuration", "DEFAULT_CONFIGURATION", "_state", "createState", "State", "isRequestInProgress", "requestQueue", "configure", "configuration", "tearDown", "Platform", "OS", "NativeInterface", "fetch", "requestedInterface", "latest", "refresh", "Promise", "resolve", "push", "_fetchCurrentState", "then", "result", "for<PERSON>ach", "finally", "addEventListener", "listener", "add", "remove", "useNetInfo", "netInfo", "setNetInfo", "type", "Types", "NetInfoStateType", "unknown", "isConnected", "isInternetReachable", "details", "unsubscribe", "useNetInfoInstance", "isPaused", "networkInfoManager", "setNetworkInfoManager", "config", "state"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AASA;;AACA;;AACA;;AACA;;AACA;;AACA;;AA4LA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AA1MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA,IAAIA,cAAc,GAAGC,6BAArB,C,CAEA;;AACA,IAAIC,MAAoB,GAAG,IAA3B;;AACA,MAAMC,WAAW,GAAG,MAAa;AAC/B,SAAO,IAAIC,eAAJ,CAAUJ,cAAV,CAAP;AACD,CAFD,C,CAIA;;;AACA,IAAIK,mBAAmB,GAAG,KAA1B;AACA,IAAIC,YAAqD,GAAG,EAA5D;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,SAAT,CACLC,aADK,EAEC;AACNR,EAAAA,cAAc,GAAG,EACf,GAAGC,6BADY;AAEf,OAAGO;AAFY,GAAjB;;AAKA,MAAIN,MAAJ,EAAY;AACVA,IAAAA,MAAM,CAACO,QAAP;;AACAP,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AAED,MAAIO,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzBC,6BAAgBL,SAAhB,CAA0BC,aAA1B;AACD;AACF;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASK,KAAT,CACLC,kBADK,EAEwB;AAC7B,MAAI,CAACZ,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AACD,SAAOD,MAAM,CAACa,MAAP,CAAcD,kBAAd,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;;AACO,SAASE,OAAT,GAAgD;AACrD,MAAI,CAACd,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD,GAHoD,CAKtD;;;AACC,MAAIE,mBAAJ,EAAyB;AACvB,WAAO,IAAIY,OAAJ,CAAaC,OAAD,IAAa;AAC9BZ,MAAAA,YAAY,CAACa,IAAb,CAAkBD,OAAlB;AACD,KAFM,CAAP;AAGD;;AAEDb,EAAAA,mBAAmB,GAAG,IAAtB;AAEA,SAAOH,MAAM,CAACkB,kBAAP,GAA4BC,IAA5B,CAAkCC,MAAD,IAAY;AAClDhB,IAAAA,YAAY,CAACiB,OAAb,CAAsBL,OAAD,IAAaA,OAAO,CAACI,MAAD,CAAzC;AACAhB,IAAAA,YAAY,GAAG,EAAf;AACA,WAAOgB,MAAP;AACD,GAJM,EAIJE,OAJI,CAII,MAAM;AACfnB,IAAAA,mBAAmB,GAAG,KAAtB;AACD,GANM,CAAP;AAOD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASoB,gBAAT,CACLC,QADK,EAEsB;AAC3B,MAAI,CAACxB,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AAEDD,EAAAA,MAAM,CAACyB,GAAP,CAAWD,QAAX;;AACA,SAAO,MAAY;AACjBxB,IAAAA,MAAM,IAAIA,MAAM,CAAC0B,MAAP,CAAcF,QAAd,CAAV;AACD,GAFD;AAGD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASG,UAAT,CACLrB,aADK,EAEe;AACpB,MAAIA,aAAJ,EAAmB;AACjBD,IAAAA,SAAS,CAACC,aAAD,CAAT;AACD;;AAED,QAAM,CAACsB,OAAD,EAAUC,UAAV,IAAwB,qBAA6B;AACzDC,IAAAA,IAAI,EAAEC,KAAK,CAACC,gBAAN,CAAuBC,OAD4B;AAEzDC,IAAAA,WAAW,EAAE,IAF4C;AAGzDC,IAAAA,mBAAmB,EAAE,IAHoC;AAIzDC,IAAAA,OAAO,EAAE;AAJgD,GAA7B,CAA9B;AAOA,wBAAU,MAAoB;AAC5B,UAAMC,WAAW,GAAGd,gBAAgB,CAACM,UAAD,CAApC;AACA,WAAO,MAAMQ,WAAW,EAAxB;AACD,GAHD,EAGG,EAHH;AAKA,SAAOT,OAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASU,kBAAT,CACLC,QAAQ,GAAG,KADN,EAELjC,aAFK,EAGL;AACA,QAAM,CAACkC,kBAAD,EAAqBC,qBAArB,IAA8C,sBAApD;AACA,QAAM,CAACb,OAAD,EAAUC,UAAV,IAAwB,qBAA6B;AACzDC,IAAAA,IAAI,EAAEC,KAAK,CAACC,gBAAN,CAAuBC,OAD4B;AAEzDC,IAAAA,WAAW,EAAE,IAF4C;AAGzDC,IAAAA,mBAAmB,EAAE,IAHoC;AAIzDC,IAAAA,OAAO,EAAE;AAJgD,GAA7B,CAA9B;AAOA,wBAAU,MAAM;AACd,QAAIG,QAAJ,EAAc;AACZ;AACD;;AACD,UAAMG,MAAM,GAAG,EACb,GAAG3C,6BADU;AAEb,SAAGO;AAFU,KAAf;AAIA,UAAMqC,KAAK,GAAG,IAAIzC,eAAJ,CAAUwC,MAAV,CAAd;AACAD,IAAAA,qBAAqB,CAACE,KAAD,CAArB;AACAA,IAAAA,KAAK,CAAClB,GAAN,CAAUI,UAAV;AACA,WAAOc,KAAK,CAACpC,QAAb;AACD,GAZD,EAYG,CAACgC,QAAD,EAAWjC,aAAX,CAZH;AAcA,QAAMQ,OAAO,GAAG,wBAAY,MAAM;AAChC,QAAI0B,kBAAkB,IAAI,CAACrC,mBAA3B,EAAgD;AAC9CA,MAAAA,mBAAmB,GAAG,IAAtB;;AACAqC,MAAAA,kBAAkB,CAACtB,kBAAnB,GAAwCI,OAAxC,CAAgD,MAAM;AACpDnB,QAAAA,mBAAmB,GAAG,KAAtB;AACD,OAFD;AAGD;AACF,GAPe,EAOb,CAACqC,kBAAD,CAPa,CAAhB;AASA,SAAO;AACLZ,IAAAA,OADK;AAELd,IAAAA;AAFK,GAAP;AAID;;eAIc;AACbT,EAAAA,SADa;AAEbM,EAAAA,KAFa;AAGbG,EAAAA,OAHa;AAIbS,EAAAA,gBAJa;AAKbI,EAAAA,UALa;AAMbW,EAAAA;AANa,C", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {useState, useEffect, useCallback} from 'react';\nimport {Platform} from 'react-native';\nimport DEFAULT_CONFIGURATION from './internal/defaultConfiguration';\nimport NativeInterface from './internal/nativeInterface';\nimport State from './internal/state';\nimport * as Types from './internal/types';\n\n// Stores the currently used configuration\nlet _configuration = DEFAULT_CONFIGURATION;\n\n// Stores the singleton reference to the state manager\nlet _state: State | null = null;\nconst createState = (): State => {\n  return new State(_configuration);\n};\n\n// Track ongoing requests\nlet isRequestInProgress = false;\nlet requestQueue: ((state: Types.NetInfoState) => void)[] = [];\n\n/**\n * Configures the library with the given configuration. Note that calling this will stop all\n * previously added listeners from being called again. It is best to call this right when your\n * application is started to avoid issues. The configuration sets up a global singleton instance.\n *\n * @param configuration The new configuration to set.\n */\nexport function configure(\n  configuration: Partial<Types.NetInfoConfiguration>,\n): void {\n  _configuration = {\n    ...DEFAULT_CONFIGURATION,\n    ...configuration,\n  };\n\n  if (_state) {\n    _state.tearDown();\n    _state = createState();\n  }\n\n  if (Platform.OS === 'ios') {\n    NativeInterface.configure(configuration);\n  }\n}\n\n/**\n * Returns a `Promise` that resolves to a `NetInfoState` object.\n * This function operates on the global singleton instance configured using `configure()`\n *\n * @param [requestedInterface] interface from which to obtain the information\n *\n * @returns A Promise which contains the current connection state.\n */\nexport function fetch(\n  requestedInterface?: string,\n): Promise<Types.NetInfoState> {\n  if (!_state) {\n    _state = createState();\n  }\n  return _state.latest(requestedInterface);\n}\n\n/**\n * Force-refreshes the internal state of the global singleton managed by this library.\n *\n * @returns A Promise which contains the updated connection state.\n */\nexport function refresh(): Promise<Types.NetInfoState> {\n  if (!_state) {\n    _state = createState();\n  }\n\n // If a request is already in progress, return a promise that will resolve when the current request finishes\n  if (isRequestInProgress) {\n    return new Promise((resolve) => {\n      requestQueue.push(resolve);\n    });\n  }\n\n  isRequestInProgress = true;\n\n  return _state._fetchCurrentState().then((result) => {\n    requestQueue.forEach((resolve) => resolve(result));\n    requestQueue = [];\n    return result;\n  }).finally(() => {\n    isRequestInProgress = false;\n  });\n}\n\n/**\n * Subscribe to the global singleton's connection information. The callback is called with a parameter of type\n * [`NetInfoState`](README.md#netinfostate) whenever the connection state changes. Your listener\n * will be called with the latest information soon after you subscribe and then with any\n * subsequent changes afterwards. You should not assume that the listener is called in the same\n * way across devices or platforms.\n *\n * @param listener The listener which is called when the network state changes.\n *\n * @returns A function which can be called to unsubscribe.\n */\nexport function addEventListener(\n  listener: Types.NetInfoChangeHandler,\n): Types.NetInfoSubscription {\n  if (!_state) {\n    _state = createState();\n  }\n\n  _state.add(listener);\n  return (): void => {\n    _state && _state.remove(listener);\n  };\n}\n\n/**\n * A React Hook into this library's singleton which updates when the connection state changes.\n *\n * @param {Partial<Types.NetInfoConfiguration>} configuration - Configure the isolated network checker managed by this hook\n *\n * @returns The connection state.\n */\nexport function useNetInfo(\n  configuration?: Partial<Types.NetInfoConfiguration>,\n): Types.NetInfoState {\n  if (configuration) {\n    configure(configuration);\n  }\n\n  const [netInfo, setNetInfo] = useState<Types.NetInfoState>({\n    type: Types.NetInfoStateType.unknown,\n    isConnected: null,\n    isInternetReachable: null,\n    details: null,\n  });\n\n  useEffect((): (() => void) => {\n    const unsubscribe = addEventListener(setNetInfo);\n    return () => unsubscribe();\n  }, []);\n\n  return netInfo;\n}\n\n/**\n * A React Hook which manages an isolated instance of the network info manager.\n * This is not a hook into a singleton shared state. NetInfo.configure, NetInfo.addEventListener,\n * NetInfo.fetch, NetInfo.refresh are performed on a global singleton and have no affect on this hook.\n * @param {boolean} isPaused - Pause the internal network checks.\n * @param {Partial<Types.NetInfoConfiguration>} configuration - Configure the isolated network checker managed by this hook\n *\n * @returns the netInfo state and a refresh function\n */\nexport function useNetInfoInstance(\n  isPaused = false,\n  configuration?: Partial<Types.NetInfoConfiguration>,\n) {\n  const [networkInfoManager, setNetworkInfoManager] = useState<State>();\n  const [netInfo, setNetInfo] = useState<Types.NetInfoState>({\n    type: Types.NetInfoStateType.unknown,\n    isConnected: null,\n    isInternetReachable: null,\n    details: null,\n  });\n\n  useEffect(() => {\n    if (isPaused) {\n      return;\n    }\n    const config = {\n      ...DEFAULT_CONFIGURATION,\n      ...configuration,\n    };\n    const state = new State(config);\n    setNetworkInfoManager(state);\n    state.add(setNetInfo);\n    return state.tearDown;\n  }, [isPaused, configuration]);\n\n  const refresh = useCallback(() => {\n    if (networkInfoManager && !isRequestInProgress) {\n      isRequestInProgress = true;\n      networkInfoManager._fetchCurrentState().finally(() => {\n        isRequestInProgress = false;\n      });\n    }\n  }, [networkInfoManager]);\n\n  return {\n    netInfo,\n    refresh,\n  };\n}\n\nexport * from './internal/types';\n\nexport default {\n  configure,\n  fetch,\n  refresh,\n  addEventListener,\n  useNetInfo,\n  useNetInfoInstance,\n};\n"]}