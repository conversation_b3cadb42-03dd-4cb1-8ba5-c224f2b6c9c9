declare const _default: import("./createIconSet").Icon<"number" | "link" | "search" | "image" | "alert" | "plus" | "info" | "check" | "book" | "question" | "mail" | "home" | "star" | "filter" | "inbox" | "lock" | "eye" | "heart" | "upload" | "download" | "unlock" | "play" | "tag" | "calendar" | "database" | "hourglass" | "key" | "gift" | "sync" | "table" | "archive" | "arrow-down" | "arrow-left" | "arrow-right" | "arrow-up" | "bell" | "bookmark" | "briefcase" | "browser" | "bug" | "chevron-down" | "chevron-left" | "chevron-right" | "chevron-up" | "circle" | "clock" | "code" | "copy" | "credit-card" | "globe" | "infinity" | "light-bulb" | "location" | "megaphone" | "moon" | "note" | "pencil" | "pin" | "quote" | "reply" | "rocket" | "rss" | "share" | "shield" | "stopwatch" | "tools" | "trash" | "triangle-down" | "triangle-left" | "triangle-right" | "triangle-up" | "video" | "comment" | "gear" | "bold" | "check-circle" | "columns" | "cpu" | "file" | "git-branch" | "git-commit" | "git-merge" | "git-pull-request" | "hash" | "italic" | "package" | "plus-circle" | "server" | "square" | "sun" | "terminal" | "x" | "x-circle" | "zap" | "stop" | "sign-out" | "sign-in" | "strikethrough" | "sort-desc" | "sort-asc" | "paste" | "mortar-board" | "history" | "plug" | "bell-slash" | "diamond" | "id-badge" | "meter" | "ruby" | "markdown" | "file-code" | "heading" | "ellipsis" | "paintbrush" | "person" | "smiley" | "pulse" | "accessibility" | "apps" | "beaker" | "duplicate" | "flame" | "git-compare" | "logo-github" | "people" | "person-add" | "telescope" | "broadcast" | "graph" | "mirror" | "shield-check" | "shield-lock" | "webhook" | "checklist" | "horizontal-rule" | "report" | "verified" | "arrow-both" | "arrow-switch" | "bell-fill" | "blocked" | "bookmark-slash" | "check-circle-fill" | "circle-slash" | "code-of-conduct" | "code-review" | "code-square" | "codescan" | "codescan-checkmark" | "codespaces" | "comment-discussion" | "container" | "cross-reference" | "dash" | "dependabot" | "desktop-download" | "device-camera" | "device-camera-video" | "device-desktop" | "device-mobile" | "diff" | "diff-added" | "diff-ignored" | "diff-modified" | "diff-removed" | "diff-renamed" | "dot" | "dot-fill" | "eye-closed" | "feed-discussion" | "feed-heart" | "feed-person" | "feed-repo" | "feed-rocket" | "feed-star" | "feed-tag" | "file-badge" | "file-binary" | "file-diff" | "file-directory" | "file-submodule" | "file-symlink-file" | "file-zip" | "fold" | "fold-down" | "fold-up" | "git-pull-request-closed" | "git-pull-request-draft" | "grabber" | "heart-fill" | "hubot" | "issue-closed" | "issue-draft" | "issue-opened" | "issue-reopened" | "iterations" | "kebab-horizontal" | "key-asterisk" | "law" | "link-external" | "list-ordered" | "list-unordered" | "log" | "logo-gist" | "mark-github" | "mention" | "milestone" | "multi-select" | "mute" | "no-entry" | "north-star" | "organization" | "package-dependencies" | "package-dependents" | "paper-airplane" | "person-fill" | "project" | "repo" | "repo-clone" | "repo-deleted" | "repo-forked" | "repo-pull" | "repo-push" | "repo-template" | "rows" | "screen-full" | "screen-normal" | "share-android" | "shield-x" | "sidebar-collapse" | "sidebar-expand" | "single-select" | "skip" | "square-fill" | "squirrel" | "stack" | "star-fill" | "tab-external" | "tasklist" | "telescope-fill" | "three-bars" | "thumbsdown" | "thumbsup" | "typography" | "unfold" | "unmute" | "unverified" | "versions" | "workflow" | "x-circle-fill", "octicons">;
export default _default;
//# sourceMappingURL=Octicons.d.ts.map