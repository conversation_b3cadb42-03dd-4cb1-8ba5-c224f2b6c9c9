/**
 * Frontend WebSocket Service
 * 
 * Handles real-time communication with the backend WebSocket server
 * Manages payment events, transaction updates, and receipt notifications
 */

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export interface PaymentEventData {
  transactionId: string;
  orderCode?: string | number;
  status: string;
  amount?: number;
  currency?: string;
  paymentProvider: string;
  eventType: string;
  metadata?: Record<string, any>;
}

export interface ReceiptEventData {
  transactionId: string;
  receiptType: 'customer' | 'merchant';
  receiptContent: string;
  format: 'text' | 'html' | 'pdf';
}

export interface VivaWebhookEventData {
  eventType: number;
  eventDescription: string;
  transactionId: string;
  orderCode: number;
  amount: number;
  statusId: string;
  processed: boolean;
}

export interface TransactionUpdateData {
  transactionId: string;
  status: string;
  amount: number;
  currency?: string;
  paymentMethod?: string;
  paymentProvider?: string;
  metadata?: Record<string, any>;
  updatedAt: string;
}

type EventHandler = (data: any) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds
  private eventHandlers: Map<string, EventHandler[]> = new Map();
  private isConnecting = false;
  private shouldReconnect = true;

  /**
   * Connect to WebSocket server
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Connection already in progress'));
        return;
      }

      this.isConnecting = true;
      
      try {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.hostname;
        const port = process.env.NODE_ENV === 'development' ? '3001' : window.location.port;
        const wsUrl = `${protocol}//${host}:${port}/ws`;

        console.log('Connecting to WebSocket:', wsUrl);
        
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected successfully');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.reconnectDelay = 1000;
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket connection closed:', event.code, event.reason);
          this.isConnecting = false;
          this.ws = null;
          
          if (this.shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    this.shouldReconnect = false;
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * Send message to WebSocket server
   */
  send(message: WebSocketMessage): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  /**
   * Send ping to server
   */
  ping(): void {
    this.send({
      type: 'ping',
      data: { timestamp: new Date().toISOString() },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Subscribe to specific events
   */
  subscribe(events: string[]): void {
    this.send({
      type: 'subscribe',
      data: { events },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Unsubscribe from specific events
   */
  unsubscribe(events: string[]): void {
    this.send({
      type: 'unsubscribe',
      data: { events },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Add event handler
   */
  on(eventType: string, handler: EventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  /**
   * Remove event handler
   */
  off(eventType: string, handler: EventHandler): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(message: WebSocketMessage): void {
    console.log('Received WebSocket message:', message.type, message.data);

    // Emit to specific event handlers
    const handlers = this.eventHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message.data);
        } catch (error) {
          console.error('Error in event handler:', error);
        }
      });
    }

    // Handle built-in message types
    switch (message.type) {
      case 'connection':
        console.log('WebSocket connection established:', message.data);
        break;

      case 'pong':
        console.log('Received pong from server');
        break;

      case 'payment_event':
        this.handlePaymentEvent(message.data as PaymentEventData);
        break;

      case 'transaction_update':
        this.handleTransactionUpdate(message.data as TransactionUpdateData);
        break;

      case 'receipt_event':
        this.handleReceiptEvent(message.data as ReceiptEventData);
        break;

      case 'viva_webhook_event':
        this.handleVivaWebhookEvent(message.data as VivaWebhookEventData);
        break;

      default:
        console.log('Unhandled message type:', message.type);
    }
  }

  /**
   * Handle payment events
   */
  private handlePaymentEvent(data: PaymentEventData): void {
    console.log('Payment event received:', data);
    
    // Show notification
    this.showNotification(`Payment ${data.status}`, {
      body: `Transaction ${data.transactionId} - ${data.paymentProvider}`,
      icon: '/favicon.ico'
    });
  }

  /**
   * Handle transaction updates
   */
  private handleTransactionUpdate(data: TransactionUpdateData): void {
    console.log('Transaction update received:', data);
    
    // Show notification for status changes
    if (data.status === 'success') {
      this.showNotification('Payment Successful', {
        body: `Transaction completed successfully`,
        icon: '/favicon.ico'
      });
    } else if (data.status === 'failed') {
      this.showNotification('Payment Failed', {
        body: `Transaction failed`,
        icon: '/favicon.ico'
      });
    }
  }

  /**
   * Handle receipt events
   */
  private handleReceiptEvent(data: ReceiptEventData): void {
    console.log('Receipt event received:', data);
    
    // Show notification
    this.showNotification('Receipt Generated', {
      body: `${data.receiptType} receipt for transaction ${data.transactionId}`,
      icon: '/favicon.ico'
    });
  }

  /**
   * Handle Viva webhook events
   */
  private handleVivaWebhookEvent(data: VivaWebhookEventData): void {
    console.log('Viva webhook event received:', data);
    
    // Show notification
    this.showNotification('Viva Payment Update', {
      body: `${data.eventDescription} - Order ${data.orderCode}`,
      icon: '/favicon.ico'
    });
  }

  /**
   * Show browser notification
   */
  private showNotification(title: string, options: NotificationOptions): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, options);
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }
    }, delay);
  }

  /**
   * Get connection status
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  }
}

export const websocketService = new WebSocketService();
