{"version": 3, "sources": ["../../../../../src/start/server/middleware/CorsMiddleware.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config';\n\nimport type { ServerRequest, ServerResponse } from './server.types';\n\nconst DEFAULT_ALLOWED_CORS_HOSTNAMES = [\n  'localhost',\n  'chrome-devtools-frontend.appspot.com', // Support remote Chrome DevTools frontend\n  'devtools', // Support local Chrome DevTools `devtools://devtools`\n];\n\nexport function createCorsMiddleware(exp: ExpoConfig) {\n  const allowedHostnames = [...DEFAULT_ALLOWED_CORS_HOSTNAMES];\n  // Support for expo-router API routes\n  if (exp.extra?.router?.headOrigin) {\n    allowedHostnames.push(new URL(exp.extra.router.headOrigin).hostname);\n  }\n  if (exp.extra?.router?.origin) {\n    allowedHostnames.push(new URL(exp.extra.router.origin).hostname);\n  }\n\n  return (req: ServerRequest, res: ServerResponse, next: (err?: Error) => void) => {\n    if (typeof req.headers.origin === 'string') {\n      const { host, hostname } = new URL(req.headers.origin);\n      const isSameOrigin = host === req.headers.host;\n      if (!isSameOrigin && !allowedHostnames.includes(hostname)) {\n        next(\n          new Error(\n            `Unauthorized request from ${req.headers.origin}. ` +\n              'This may happen because of a conflicting browser extension to intercept HTTP requests. ' +\n              'Disable browser extensions or use incognito mode and try again.'\n          )\n        );\n        return;\n      }\n\n      res.setHeader('Access-Control-Allow-Origin', req.headers.origin);\n      maybePreventMetroResetCorsHeader(req, res);\n    }\n\n    // Block MIME-type sniffing.\n    res.setHeader('X-Content-Type-Options', 'nosniff');\n\n    next();\n  };\n}\n\n// When accessing source maps,\n// metro will overwrite the `Access-Control-Allow-Origin` header with hardcoded `devtools://devtools` value.\n// https://github.com/facebook/metro/blob/a7f8955e6d2424b0d5f73d4bcdaf22560e1d5f27/packages/metro/src/Server.js#L540\n// This is a workaround to prevent this behavior.\nfunction maybePreventMetroResetCorsHeader(req: ServerRequest, res: ServerResponse) {\n  const pathname = req.url ? new URL(req.url, `http://${req.headers.host}`).pathname : '';\n  if (pathname.endsWith('.map')) {\n    const setHeader = res.setHeader.bind(res);\n    res.setHeader = (key, ...args) => {\n      if (key !== 'Access-Control-Allow-Origin') {\n        setHeader(key, ...args);\n      }\n      return res;\n    };\n  }\n}\n"], "names": ["createCorsMiddleware", "DEFAULT_ALLOWED_CORS_HOSTNAMES", "exp", "allowedHostnames", "extra", "router", "<PERSON><PERSON><PERSON><PERSON>", "push", "URL", "hostname", "origin", "req", "res", "next", "headers", "host", "isSameOrigin", "includes", "Error", "<PERSON><PERSON><PERSON><PERSON>", "maybePreventMetroResetCorsHeader", "pathname", "url", "endsWith", "bind", "key", "args"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;AANhB,MAAMC,iCAAiC;IACrC;IACA;IACA;CACD;AAEM,SAASD,qBAAqBE,GAAe;QAG9CA,mBAAAA,YAGAA,oBAAAA;IALJ,MAAMC,mBAAmB;WAAIF;KAA+B;IAC5D,qCAAqC;IACrC,KAAIC,aAAAA,IAAIE,KAAK,sBAATF,oBAAAA,WAAWG,MAAM,qBAAjBH,kBAAmBI,UAAU,EAAE;QACjCH,iBAAiBI,IAAI,CAAC,IAAIC,IAAIN,IAAIE,KAAK,CAACC,MAAM,CAACC,UAAU,EAAEG,QAAQ;IACrE;IACA,KAAIP,cAAAA,IAAIE,KAAK,sBAATF,qBAAAA,YAAWG,MAAM,qBAAjBH,mBAAmBQ,MAAM,EAAE;QAC7BP,iBAAiBI,IAAI,CAAC,IAAIC,IAAIN,IAAIE,KAAK,CAACC,MAAM,CAACK,MAAM,EAAED,QAAQ;IACjE;IAEA,OAAO,CAACE,KAAoBC,KAAqBC;QAC/C,IAAI,OAAOF,IAAIG,OAAO,CAACJ,MAAM,KAAK,UAAU;YAC1C,MAAM,EAAEK,IAAI,EAAEN,QAAQ,EAAE,GAAG,IAAID,IAAIG,IAAIG,OAAO,CAACJ,MAAM;YACrD,MAAMM,eAAeD,SAASJ,IAAIG,OAAO,CAACC,IAAI;YAC9C,IAAI,CAACC,gBAAgB,CAACb,iBAAiBc,QAAQ,CAACR,WAAW;gBACzDI,KACE,IAAIK,MACF,CAAC,0BAA0B,EAAEP,IAAIG,OAAO,CAACJ,MAAM,CAAC,EAAE,CAAC,GACjD,4FACA;gBAGN;YACF;YAEAE,IAAIO,SAAS,CAAC,+BAA+BR,IAAIG,OAAO,CAACJ,MAAM;YAC/DU,iCAAiCT,KAAKC;QACxC;QAEA,4BAA4B;QAC5BA,IAAIO,SAAS,CAAC,0BAA0B;QAExCN;IACF;AACF;AAEA,8BAA8B;AAC9B,4GAA4G;AAC5G,oHAAoH;AACpH,iDAAiD;AACjD,SAASO,iCAAiCT,GAAkB,EAAEC,GAAmB;IAC/E,MAAMS,WAAWV,IAAIW,GAAG,GAAG,IAAId,IAAIG,IAAIW,GAAG,EAAE,CAAC,OAAO,EAAEX,IAAIG,OAAO,CAACC,IAAI,EAAE,EAAEM,QAAQ,GAAG;IACrF,IAAIA,SAASE,QAAQ,CAAC,SAAS;QAC7B,MAAMJ,YAAYP,IAAIO,SAAS,CAACK,IAAI,CAACZ;QACrCA,IAAIO,SAAS,GAAG,CAACM,KAAK,GAAGC;YACvB,IAAID,QAAQ,+BAA+B;gBACzCN,UAAUM,QAAQC;YACpB;YACA,OAAOd;QACT;IACF;AACF"}