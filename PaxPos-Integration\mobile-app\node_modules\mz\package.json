{"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "twitter": "https://twitter.com/jongleberry"}, "license": "MIT", "repository": "normalize/mz", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}, "devDependencies": {"istanbul": "^0.4.0", "bluebird": "^3.0.0", "mocha": "^3.0.0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js", "child_process.js", "crypto.js", "dns.js", "fs.js", "readline.js", "zlib.js"]}