{"version": 3, "file": "Config.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_deepmerge", "_fs", "_glob", "_path", "_resolveFrom", "_semver", "_slugify", "_getConfig", "_getExpoSDKVersion", "_withConfigPlugins", "_withInternal", "_resolvePackageJson", "_Config", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "e", "__esModule", "default", "hasWarnedAboutRootConfig", "reduceExpoObject", "config", "expo", "filter", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "str", "<PERSON><PERSON><PERSON><PERSON>", "ansi<PERSON>old", "plural", "console", "warn", "map", "join", "mods", "getSupportedPlatforms", "projectRoot", "platforms", "resolveFrom", "silent", "push", "getConfig", "options", "paths", "getConfigFilePaths", "rawStaticConfig", "staticConfigPath", "getStaticConfig", "rootConfig", "staticConfig", "packageJson", "packageJsonPath", "getPackageJsonAndPath", "fillAndReturnConfig", "dynamicConfigObjectType", "mayHaveUnusedStaticConfig", "configWithDefaultValues", "ensureConfigHasDefaultValues", "exp", "pkg", "skipSDKVersionRequirement", "dynamicConfigPath", "hasUnusedStaticConfig", "isModdedConfig", "withConfigPlugins", "skip<PERSON>lug<PERSON>", "isPublicConfig", "_internal", "hooks", "ios", "android", "updates", "codeSigningCertificate", "codeSigningMetadata", "getContextConfig", "exportedObjectType", "rawDynamicConfig", "getDynamicConfig", "dynamicConfig", "getPackageJson", "getRootPackageJsonPath", "JsonFile", "read", "getDynamicConfigFilePath", "getStaticConfigFilePath", "fileName", "config<PERSON><PERSON>", "path", "fs", "existsSync", "modifyConfigAsync", "modifications", "readOptions", "writeOptions", "is<PERSON>ry<PERSON>un", "dryRun", "outputConfig", "mergeConfigModifications", "writeAsync", "json5", "type", "message", "relative", "newConfig", "newConfighasModifications", "isMatchingObject", "plugins", "modifiedExpoConfig", "deepMerge", "existingPlugins", "fromEntries", "definition", "undefined", "plugin", "pluginName", "pluginProps", "Array", "isArray", "existingPlugin", "existingPluginName", "finalizedConfig", "expectedV<PERSON>ues", "actualValues", "withInternal", "pkgName", "name", "basename", "pkgVersion", "version", "pkgWithDefaults", "slug", "slugify", "toLowerCase", "description", "expWithDefaults", "sdkVersion", "getExpoSDKVersion", "error", "DEFAULT_BUILD_PATH", "getWebOutputPath", "process", "env", "WEBPACK_BUILD_OUTPUT_PATH", "web", "build", "output", "getNameFromConfig", "appManifest", "appName", "displayName", "webName", "getDefaultTarget", "semver", "lt", "isBareWorkflowProject", "dependencies", "expokit", "xcodeprojFiles", "globSync", "absolute", "cwd", "gradleFiles", "getProjectConfigDescription", "getProjectConfigDescriptionWithPaths", "projectConfig", "relativeDynamicConfigPath"], "sources": ["../src/Config.ts"], "sourcesContent": ["import { ModConfig } from '@expo/config-plugins';\nimport Json<PERSON><PERSON>, { JSONObject } from '@expo/json-file';\nimport deepMerge from 'deepmerge';\nimport fs from 'fs';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\nimport slugify from 'slugify';\n\nimport {\n  AppJSONConfig,\n  ConfigFilePaths,\n  ExpoConfig,\n  GetConfigOptions,\n  PackageJSONConfig,\n  Platform,\n  ProjectConfig,\n  ProjectTarget,\n  WriteConfigOptions,\n} from './Config.types';\nimport { getDynamicConfig, getStaticConfig } from './getConfig';\nimport { getExpoSDKVersion } from './getExpoSDKVersion';\nimport { withConfigPlugins } from './plugins/withConfigPlugins';\nimport { withInternal } from './plugins/withInternal';\nimport { getRootPackageJsonPath } from './resolvePackageJson';\n\ntype SplitConfigs = { expo?: ExpoConfig; mods?: ModConfig };\n\nlet hasWarnedAboutRootConfig = false;\n\n/**\n * If a config has an `expo` object then that will be used as the config.\n * This method reduces out other top level values if an `expo` object exists.\n *\n * @param config Input config object to reduce\n */\nfunction reduceExpoObject(config?: any): SplitConfigs | null {\n  if (!config) return config || null;\n\n  if (config.expo && !hasWarnedAboutRootConfig) {\n    const keys = Object.keys(config).filter((key) => key !== 'expo');\n    if (keys.length) {\n      hasWarnedAboutRootConfig = true;\n      const ansiYellow = (str: string) => `\\u001B[33m${str}\\u001B[0m`;\n      const ansiGray = (str: string) => `\\u001B[90m${str}\\u001B[0m`;\n      const ansiBold = (str: string) => `\\u001B[1m${str}\\u001B[22m`;\n      const plural = keys.length > 1;\n      console.warn(\n        ansiYellow(\n          ansiBold('Warning: ') +\n            `Root-level ${ansiBold(`\"expo\"`)} object found. Ignoring extra key${plural ? 's' : ''} in Expo config: ${keys\n              .map((key) => `\"${key}\"`)\n              .join(', ')}\\n` +\n            ansiGray(`Learn more: https://expo.fyi/root-expo-object`)\n        )\n      );\n    }\n  }\n\n  const { mods, ...expo } = config.expo ?? config;\n\n  return {\n    expo,\n    mods,\n  };\n}\n\n/**\n * Get all platforms that a project is currently capable of running.\n *\n * @param projectRoot\n * @param exp\n */\nfunction getSupportedPlatforms(projectRoot: string): Platform[] {\n  const platforms: Platform[] = [];\n  if (resolveFrom.silent(projectRoot, 'react-native')) {\n    platforms.push('ios', 'android');\n  }\n  if (resolveFrom.silent(projectRoot, 'react-dom')) {\n    platforms.push('web');\n  }\n  return platforms;\n}\n\n/**\n * Evaluate the config for an Expo project.\n * If a function is exported from the `app.config.js` then a partial config will be passed as an argument.\n * The partial config is composed from any existing app.json, and certain fields from the `package.json` like name and description.\n *\n * If options.isPublicConfig is true, the Expo config will include only public-facing options (omitting private keys).\n * The resulting config should be suitable for hosting or embedding in a publicly readable location.\n *\n * **Example**\n * ```js\n * module.exports = function({ config }) {\n *   // mutate the config before returning it.\n *   config.slug = 'new slug'\n *   return { expo: config };\n * }\n * ```\n *\n * **Supports**\n * - `app.config.ts`\n * - `app.config.js`\n * - `app.config.json`\n * - `app.json`\n *\n * @param projectRoot the root folder containing all of your application code\n * @param options enforce criteria for a project config\n */\nexport function getConfig(projectRoot: string, options: GetConfigOptions = {}): ProjectConfig {\n  const paths = getConfigFilePaths(projectRoot);\n\n  const rawStaticConfig = paths.staticConfigPath ? getStaticConfig(paths.staticConfigPath) : null;\n  // For legacy reasons, always return an object.\n  const rootConfig = (rawStaticConfig || {}) as AppJSONConfig;\n  const staticConfig = reduceExpoObject(rawStaticConfig) || {};\n\n  // Can only change the package.json location if an app.json or app.config.json exists\n  const [packageJson, packageJsonPath] = getPackageJsonAndPath(projectRoot);\n\n  function fillAndReturnConfig(\n    config: SplitConfigs,\n    dynamicConfigObjectType: string | null,\n    mayHaveUnusedStaticConfig: boolean = false\n  ) {\n    const configWithDefaultValues = {\n      ...ensureConfigHasDefaultValues({\n        projectRoot,\n        exp: config.expo || {},\n        pkg: packageJson,\n        skipSDKVersionRequirement: options.skipSDKVersionRequirement,\n        paths,\n        packageJsonPath,\n      }),\n      mods: config.mods,\n      dynamicConfigObjectType,\n      rootConfig,\n      dynamicConfigPath: paths.dynamicConfigPath,\n      staticConfigPath: paths.staticConfigPath,\n      hasUnusedStaticConfig:\n        !!paths.staticConfigPath && !!paths.dynamicConfigPath && mayHaveUnusedStaticConfig,\n    };\n\n    if (options.isModdedConfig) {\n      // @ts-ignore: Add the mods back to the object.\n      configWithDefaultValues.exp.mods = config.mods ?? null;\n    }\n\n    // Apply static json plugins, should be done after _internal\n    configWithDefaultValues.exp = withConfigPlugins(\n      configWithDefaultValues.exp,\n      !!options.skipPlugins\n    );\n\n    if (!options.isModdedConfig) {\n      // @ts-ignore: Delete mods added by static plugins when they won't have a chance to be evaluated\n      delete configWithDefaultValues.exp.mods;\n    }\n\n    if (options.isPublicConfig) {\n      // TODD(EvanBacon): Drop plugins array after it's been resolved.\n\n      // Remove internal values with references to user's file paths from the public config.\n      delete configWithDefaultValues.exp._internal;\n\n      // hooks no longer exists in the typescript type but should still be removed\n      if ('hooks' in configWithDefaultValues.exp) {\n        delete configWithDefaultValues.exp.hooks;\n      }\n      if (configWithDefaultValues.exp.ios?.config) {\n        delete configWithDefaultValues.exp.ios.config;\n      }\n      if (configWithDefaultValues.exp.android?.config) {\n        delete configWithDefaultValues.exp.android.config;\n      }\n\n      delete configWithDefaultValues.exp.updates?.codeSigningCertificate;\n      delete configWithDefaultValues.exp.updates?.codeSigningMetadata;\n    }\n\n    return configWithDefaultValues;\n  }\n\n  // Fill in the static config\n  function getContextConfig(config: SplitConfigs) {\n    return ensureConfigHasDefaultValues({\n      projectRoot,\n      exp: config.expo || {},\n      pkg: packageJson,\n      skipSDKVersionRequirement: true,\n      paths,\n      packageJsonPath,\n    }).exp;\n  }\n\n  if (paths.dynamicConfigPath) {\n    // No app.config.json or app.json but app.config.js\n    const {\n      exportedObjectType,\n      config: rawDynamicConfig,\n      mayHaveUnusedStaticConfig,\n    } = getDynamicConfig(paths.dynamicConfigPath, {\n      projectRoot,\n      staticConfigPath: paths.staticConfigPath,\n      packageJsonPath,\n      config: getContextConfig(staticConfig),\n    });\n    // Allow for the app.config.js to `export default null;`\n    // Use `dynamicConfigPath` to detect if a dynamic config exists.\n    const dynamicConfig = reduceExpoObject(rawDynamicConfig) || {};\n    return fillAndReturnConfig(dynamicConfig, exportedObjectType, mayHaveUnusedStaticConfig);\n  }\n\n  // No app.config.js but json or no config\n  return fillAndReturnConfig(staticConfig || {}, null);\n}\n\nexport function getPackageJson(projectRoot: string): PackageJSONConfig {\n  const [pkg] = getPackageJsonAndPath(projectRoot);\n  return pkg;\n}\n\nfunction getPackageJsonAndPath(projectRoot: string): [PackageJSONConfig, string] {\n  const packageJsonPath = getRootPackageJsonPath(projectRoot);\n  return [JsonFile.read(packageJsonPath), packageJsonPath];\n}\n\n/**\n * Get the static and dynamic config paths for a project. Also accounts for custom paths.\n *\n * @param projectRoot\n */\nexport function getConfigFilePaths(projectRoot: string): ConfigFilePaths {\n  return {\n    dynamicConfigPath: getDynamicConfigFilePath(projectRoot),\n    staticConfigPath: getStaticConfigFilePath(projectRoot),\n  };\n}\n\nfunction getDynamicConfigFilePath(projectRoot: string): string | null {\n  for (const fileName of ['app.config.ts', 'app.config.js']) {\n    const configPath = path.join(projectRoot, fileName);\n    if (fs.existsSync(configPath)) {\n      return configPath;\n    }\n  }\n  return null;\n}\n\nfunction getStaticConfigFilePath(projectRoot: string): string | null {\n  for (const fileName of ['app.config.json', 'app.json']) {\n    const configPath = path.join(projectRoot, fileName);\n    if (fs.existsSync(configPath)) {\n      return configPath;\n    }\n  }\n  return null;\n}\n\n/**\n * Attempt to modify an Expo project config.\n * This will only fully work if the project is using static configs only.\n * Otherwise 'warn' | 'fail' will return with a message about why the config couldn't be updated.\n * The potentially modified config object will be returned for testing purposes.\n *\n * @param projectRoot\n * @param modifications modifications to make to an existing config\n * @param readOptions options for reading the current config file\n * @param writeOptions If true, the static config file will not be rewritten\n */\nexport async function modifyConfigAsync(\n  projectRoot: string,\n  modifications: Partial<ExpoConfig>,\n  readOptions: GetConfigOptions = {},\n  writeOptions: WriteConfigOptions = {}\n): Promise<{\n  type: 'success' | 'warn' | 'fail';\n  message?: string;\n  config: ExpoConfig | null;\n}> {\n  const config = getConfig(projectRoot, readOptions);\n  const isDryRun = writeOptions.dryRun;\n\n  // Create or modify the static config, when not using dynamic config\n  if (!config.dynamicConfigPath) {\n    const outputConfig = mergeConfigModifications(config, modifications);\n\n    if (!isDryRun) {\n      const configPath = config.staticConfigPath ?? path.join(projectRoot, 'app.json');\n      await JsonFile.writeAsync(configPath, outputConfig, { json5: false });\n    }\n\n    return { type: 'success', config: outputConfig.expo ?? outputConfig };\n  }\n\n  // Attempt to write to a function-like dynamic config, when used with a static config\n  if (\n    config.staticConfigPath &&\n    config.dynamicConfigObjectType === 'function' &&\n    !modifications.hasOwnProperty('plugins') // We don't know what plugins are in dynamic configs\n  ) {\n    const outputConfig = mergeConfigModifications(config, modifications);\n\n    if (isDryRun) {\n      return {\n        type: 'warn',\n        message: `Cannot verify config modifications in dry-run mode for config at: ${path.relative(projectRoot, config.dynamicConfigPath)}`,\n        config: null,\n      };\n    }\n\n    // Attempt to write the static config with the config modifications\n    await JsonFile.writeAsync(config.staticConfigPath, outputConfig, { json5: false });\n\n    // Verify that the dynamic config is using the static config\n    const newConfig = getConfig(projectRoot, readOptions);\n    const newConfighasModifications = isMatchingObject(modifications, newConfig.exp);\n    if (newConfighasModifications) {\n      return {\n        type: 'success',\n        config: newConfig.exp,\n      };\n    }\n\n    // Rollback the changes when the reloaded config did not include the modifications\n    await JsonFile.writeAsync(config.staticConfigPath, config.rootConfig, { json5: false });\n  }\n\n  // We cannot automatically write to a dynamic config\n  return {\n    type: 'warn',\n    message: `Cannot automatically write to dynamic config at: ${path.relative(\n      projectRoot,\n      config.dynamicConfigPath\n    )}`,\n    config: null,\n  };\n}\n\n/**\n * Merge the config modifications, using an optional possible top-level `expo` object.\n * Note, changes in the plugins are merged differently to avoid duplicate entries.\n */\nfunction mergeConfigModifications(\n  config: ProjectConfig,\n  { plugins, ...modifications }: Partial<ExpoConfig>\n): AppJSONConfig {\n  const modifiedExpoConfig: ExpoConfig = !config.rootConfig.expo\n    ? deepMerge(config.rootConfig, modifications)\n    : deepMerge(config.rootConfig.expo, modifications);\n\n  if (plugins?.length) {\n    // When adding plugins, ensure the config has a plugin list\n    if (!modifiedExpoConfig.plugins) {\n      modifiedExpoConfig.plugins = [];\n    }\n\n    // Create a plugin lookup map\n    const existingPlugins: Record<string, any> = Object.fromEntries(\n      modifiedExpoConfig.plugins.map((definition) =>\n        typeof definition === 'string' ? [definition, undefined] : definition\n      )\n    );\n\n    for (const plugin of plugins) {\n      // Unpack the plugin definition, using either the short (string) or normal (array) notation\n      const [pluginName, pluginProps] = Array.isArray(plugin) ? plugin : [plugin];\n      // Abort if the plugin definition is empty\n      if (!pluginName) continue;\n\n      // Add the plugin if it doesn't exist yet, including its properties\n      if (!(pluginName in existingPlugins)) {\n        modifiedExpoConfig.plugins.push(plugin);\n        continue;\n      }\n\n      // If the plugin has properties, and it exists, merge the properties\n      if (pluginProps) {\n        modifiedExpoConfig.plugins = modifiedExpoConfig.plugins.map((existingPlugin) => {\n          const [existingPluginName] = Array.isArray(existingPlugin)\n            ? existingPlugin\n            : [existingPlugin];\n\n          // Do not modify other plugins\n          if (existingPluginName !== pluginName) {\n            return existingPlugin;\n          }\n\n          // Add the props to the existing plugin entry\n          if (typeof existingPlugin === 'string') {\n            return [existingPlugin, pluginProps];\n          }\n\n          // Merge the props to the existing plugin properties\n          if (Array.isArray(existingPlugin) && existingPlugin[0]) {\n            return [existingPlugin[0], deepMerge(existingPlugin[1] ?? {}, pluginProps)];\n          }\n\n          return existingPlugin;\n        });\n        continue;\n      }\n\n      // If the same plugin exists with properties, and the modification does not contain properties, ignore\n    }\n  }\n\n  const finalizedConfig = !config.rootConfig.expo\n    ? modifiedExpoConfig\n    : { ...config.rootConfig, expo: modifiedExpoConfig };\n\n  return finalizedConfig as AppJSONConfig;\n}\n\nfunction isMatchingObject<T extends Record<string, any>>(\n  expectedValues: T,\n  actualValues: T\n): boolean {\n  for (const key in expectedValues) {\n    if (!expectedValues.hasOwnProperty(key)) {\n      continue;\n    }\n\n    if (typeof expectedValues[key] === 'object' && actualValues[key] !== null) {\n      if (!isMatchingObject(expectedValues[key], actualValues[key])) {\n        return false;\n      }\n    } else {\n      if (expectedValues[key] !== actualValues[key]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nfunction ensureConfigHasDefaultValues({\n  projectRoot,\n  exp,\n  pkg,\n  paths,\n  packageJsonPath,\n  skipSDKVersionRequirement = false,\n}: {\n  projectRoot: string;\n  exp: Partial<ExpoConfig> | null;\n  pkg: JSONObject;\n  skipSDKVersionRequirement?: boolean;\n  paths?: ConfigFilePaths;\n  packageJsonPath?: string;\n}): { exp: ExpoConfig; pkg: PackageJSONConfig } {\n  if (!exp) {\n    exp = {};\n  }\n  exp = withInternal(exp as any, {\n    projectRoot,\n    ...(paths ?? {}),\n    packageJsonPath,\n  });\n  // Defaults for package.json fields\n  const pkgName = typeof pkg.name === 'string' ? pkg.name : path.basename(projectRoot);\n  const pkgVersion = typeof pkg.version === 'string' ? pkg.version : '1.0.0';\n\n  const pkgWithDefaults = { ...pkg, name: pkgName, version: pkgVersion };\n\n  // Defaults for app.json/app.config.js fields\n  const name = exp.name ?? pkgName;\n  const slug = exp.slug ?? slugify(name.toLowerCase());\n  const version = exp.version ?? pkgVersion;\n  let description = exp.description;\n  if (!description && typeof pkg.description === 'string') {\n    description = pkg.description;\n  }\n\n  const expWithDefaults = { ...exp, name, slug, version, description };\n\n  let sdkVersion;\n  try {\n    sdkVersion = getExpoSDKVersion(projectRoot, expWithDefaults);\n  } catch (error) {\n    if (!skipSDKVersionRequirement) throw error;\n  }\n\n  let platforms = exp.platforms;\n  if (!platforms) {\n    platforms = getSupportedPlatforms(projectRoot);\n  }\n\n  return {\n    exp: { ...expWithDefaults, sdkVersion, platforms },\n    pkg: pkgWithDefaults,\n  };\n}\n\nconst DEFAULT_BUILD_PATH = `web-build`;\n\nexport function getWebOutputPath(config: { [key: string]: any } = {}): string {\n  if (process.env.WEBPACK_BUILD_OUTPUT_PATH) {\n    return process.env.WEBPACK_BUILD_OUTPUT_PATH;\n  }\n  const expo = config.expo || config || {};\n  return expo?.web?.build?.output || DEFAULT_BUILD_PATH;\n}\n\nexport function getNameFromConfig(exp: Record<string, any> = {}): {\n  appName?: string;\n  webName?: string;\n} {\n  // For RN CLI support\n  const appManifest = exp.expo || exp;\n  const { web = {} } = appManifest;\n\n  // rn-cli apps use a displayName value as well.\n  const appName = exp.displayName || appManifest.displayName || appManifest.name;\n  const webName = web.name || appName;\n\n  return {\n    appName,\n    webName,\n  };\n}\n\nexport function getDefaultTarget(\n  projectRoot: string,\n  exp?: Pick<ExpoConfig, 'sdkVersion'>\n): ProjectTarget {\n  exp ??= getConfig(projectRoot, { skipSDKVersionRequirement: true }).exp;\n\n  // before SDK 37, always default to managed to preserve previous behavior\n  if (exp.sdkVersion && exp.sdkVersion !== 'UNVERSIONED' && semver.lt(exp.sdkVersion, '37.0.0')) {\n    return 'managed';\n  }\n  return isBareWorkflowProject(projectRoot) ? 'bare' : 'managed';\n}\n\nfunction isBareWorkflowProject(projectRoot: string): boolean {\n  const [pkg] = getPackageJsonAndPath(projectRoot);\n\n  // TODO: Drop this\n  if (pkg.dependencies && pkg.dependencies.expokit) {\n    return false;\n  }\n\n  const xcodeprojFiles = globSync('ios/**/*.xcodeproj', {\n    absolute: true,\n    cwd: projectRoot,\n  });\n  if (xcodeprojFiles.length) {\n    return true;\n  }\n  const gradleFiles = globSync('android/**/*.gradle', {\n    absolute: true,\n    cwd: projectRoot,\n  });\n  if (gradleFiles.length) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Return a useful name describing the project config.\n * - dynamic: app.config.js\n * - static: app.json\n * - custom path app config relative to root folder\n * - both: app.config.js or app.json\n */\nexport function getProjectConfigDescription(projectRoot: string): string {\n  const paths = getConfigFilePaths(projectRoot);\n  return getProjectConfigDescriptionWithPaths(projectRoot, paths);\n}\n\n/**\n * Returns a string describing the configurations used for the given project root.\n * Will return null if no config is found.\n *\n * @param projectRoot\n * @param projectConfig\n */\nexport function getProjectConfigDescriptionWithPaths(\n  projectRoot: string,\n  projectConfig: ConfigFilePaths\n): string {\n  if (projectConfig.dynamicConfigPath) {\n    const relativeDynamicConfigPath = path.relative(projectRoot, projectConfig.dynamicConfigPath);\n    if (projectConfig.staticConfigPath) {\n      return `${relativeDynamicConfigPath} or ${path.relative(\n        projectRoot,\n        projectConfig.staticConfigPath\n      )}`;\n    }\n    return relativeDynamicConfigPath;\n  } else if (projectConfig.staticConfigPath) {\n    return path.relative(projectRoot, projectConfig.staticConfigPath);\n  }\n  // If a config doesn't exist, our tooling will generate a static app.json\n  return 'app.json';\n}\n\nexport * from './Config.types';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,WAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,MAAA;EAAA,MAAAN,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,aAAA;EAAA,MAAAP,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAK,YAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,QAAA;EAAA,MAAAR,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,SAAA;EAAA,MAAAT,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAO,QAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAaA,SAAAU,WAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,UAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAW,mBAAA;EAAA,MAAAX,IAAA,GAAAE,OAAA;EAAAS,kBAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAY,mBAAA;EAAA,MAAAZ,IAAA,GAAAE,OAAA;EAAAU,kBAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAa,cAAA;EAAA,MAAAb,IAAA,GAAAE,OAAA;EAAAW,aAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAc,oBAAA;EAAA,MAAAd,IAAA,GAAAE,OAAA;EAAAY,mBAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAikBA,IAAAe,OAAA,GAAAb,OAAA;AAAAc,MAAA,CAAAC,IAAA,CAAAF,OAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,OAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,OAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAA+B,SAAAlB,uBAAA2B,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AA7jB/B,IAAIG,wBAAwB,GAAG,KAAK;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,MAAY,EAAuB;EAC3D,IAAI,CAACA,MAAM,EAAE,OAAOA,MAAM,IAAI,IAAI;EAElC,IAAIA,MAAM,CAACC,IAAI,IAAI,CAACH,wBAAwB,EAAE;IAC5C,MAAMd,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACgB,MAAM,CAAC,CAACE,MAAM,CAAEhB,GAAG,IAAKA,GAAG,KAAK,MAAM,CAAC;IAChE,IAAIF,IAAI,CAACmB,MAAM,EAAE;MACfL,wBAAwB,GAAG,IAAI;MAC/B,MAAMM,UAAU,GAAIC,GAAW,IAAK,aAAaA,GAAG,WAAW;MAC/D,MAAMC,QAAQ,GAAID,GAAW,IAAK,aAAaA,GAAG,WAAW;MAC7D,MAAME,QAAQ,GAAIF,GAAW,IAAK,YAAYA,GAAG,YAAY;MAC7D,MAAMG,MAAM,GAAGxB,IAAI,CAACmB,MAAM,GAAG,CAAC;MAC9BM,OAAO,CAACC,IAAI,CACVN,UAAU,CACRG,QAAQ,CAAC,WAAW,CAAC,GACnB,cAAcA,QAAQ,CAAC,QAAQ,CAAC,oCAAoCC,MAAM,GAAG,GAAG,GAAG,EAAE,oBAAoBxB,IAAI,CAC1G2B,GAAG,CAAEzB,GAAG,IAAK,IAAIA,GAAG,GAAG,CAAC,CACxB0B,IAAI,CAAC,IAAI,CAAC,IAAI,GACjBN,QAAQ,CAAC,+CAA+C,CAC5D,CACF,CAAC;IACH;EACF;EAEA,MAAM;IAAEO,IAAI;IAAE,GAAGZ;EAAK,CAAC,GAAGD,MAAM,CAACC,IAAI,IAAID,MAAM;EAE/C,OAAO;IACLC,IAAI;IACJY;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,WAAmB,EAAc;EAC9D,MAAMC,SAAqB,GAAG,EAAE;EAChC,IAAIC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,cAAc,CAAC,EAAE;IACnDC,SAAS,CAACG,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC;EAClC;EACA,IAAIF,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,WAAW,CAAC,EAAE;IAChDC,SAAS,CAACG,IAAI,CAAC,KAAK,CAAC;EACvB;EACA,OAAOH,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,SAASA,CAACL,WAAmB,EAAEM,OAAyB,GAAG,CAAC,CAAC,EAAiB;EAC5F,MAAMC,KAAK,GAAGC,kBAAkB,CAACR,WAAW,CAAC;EAE7C,MAAMS,eAAe,GAAGF,KAAK,CAACG,gBAAgB,GAAG,IAAAC,4BAAe,EAACJ,KAAK,CAACG,gBAAgB,CAAC,GAAG,IAAI;EAC/F;EACA,MAAME,UAAU,GAAIH,eAAe,IAAI,CAAC,CAAmB;EAC3D,MAAMI,YAAY,GAAG7B,gBAAgB,CAACyB,eAAe,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAM,CAACK,WAAW,EAAEC,eAAe,CAAC,GAAGC,qBAAqB,CAAChB,WAAW,CAAC;EAEzE,SAASiB,mBAAmBA,CAC1BhC,MAAoB,EACpBiC,uBAAsC,EACtCC,yBAAkC,GAAG,KAAK,EAC1C;IACA,MAAMC,uBAAuB,GAAG;MAC9B,GAAGC,4BAA4B,CAAC;QAC9BrB,WAAW;QACXsB,GAAG,EAAErC,MAAM,CAACC,IAAI,IAAI,CAAC,CAAC;QACtBqC,GAAG,EAAET,WAAW;QAChBU,yBAAyB,EAAElB,OAAO,CAACkB,yBAAyB;QAC5DjB,KAAK;QACLQ;MACF,CAAC,CAAC;MACFjB,IAAI,EAAEb,MAAM,CAACa,IAAI;MACjBoB,uBAAuB;MACvBN,UAAU;MACVa,iBAAiB,EAAElB,KAAK,CAACkB,iBAAiB;MAC1Cf,gBAAgB,EAAEH,KAAK,CAACG,gBAAgB;MACxCgB,qBAAqB,EACnB,CAAC,CAACnB,KAAK,CAACG,gBAAgB,IAAI,CAAC,CAACH,KAAK,CAACkB,iBAAiB,IAAIN;IAC7D,CAAC;IAED,IAAIb,OAAO,CAACqB,cAAc,EAAE;MAC1B;MACAP,uBAAuB,CAACE,GAAG,CAACxB,IAAI,GAAGb,MAAM,CAACa,IAAI,IAAI,IAAI;IACxD;;IAEA;IACAsB,uBAAuB,CAACE,GAAG,GAAG,IAAAM,sCAAiB,EAC7CR,uBAAuB,CAACE,GAAG,EAC3B,CAAC,CAAChB,OAAO,CAACuB,WACZ,CAAC;IAED,IAAI,CAACvB,OAAO,CAACqB,cAAc,EAAE;MAC3B;MACA,OAAOP,uBAAuB,CAACE,GAAG,CAACxB,IAAI;IACzC;IAEA,IAAIQ,OAAO,CAACwB,cAAc,EAAE;MAC1B;;MAEA;MACA,OAAOV,uBAAuB,CAACE,GAAG,CAACS,SAAS;;MAE5C;MACA,IAAI,OAAO,IAAIX,uBAAuB,CAACE,GAAG,EAAE;QAC1C,OAAOF,uBAAuB,CAACE,GAAG,CAACU,KAAK;MAC1C;MACA,IAAIZ,uBAAuB,CAACE,GAAG,CAACW,GAAG,EAAEhD,MAAM,EAAE;QAC3C,OAAOmC,uBAAuB,CAACE,GAAG,CAACW,GAAG,CAAChD,MAAM;MAC/C;MACA,IAAImC,uBAAuB,CAACE,GAAG,CAACY,OAAO,EAAEjD,MAAM,EAAE;QAC/C,OAAOmC,uBAAuB,CAACE,GAAG,CAACY,OAAO,CAACjD,MAAM;MACnD;MAEA,OAAOmC,uBAAuB,CAACE,GAAG,CAACa,OAAO,EAAEC,sBAAsB;MAClE,OAAOhB,uBAAuB,CAACE,GAAG,CAACa,OAAO,EAAEE,mBAAmB;IACjE;IAEA,OAAOjB,uBAAuB;EAChC;;EAEA;EACA,SAASkB,gBAAgBA,CAACrD,MAAoB,EAAE;IAC9C,OAAOoC,4BAA4B,CAAC;MAClCrB,WAAW;MACXsB,GAAG,EAAErC,MAAM,CAACC,IAAI,IAAI,CAAC,CAAC;MACtBqC,GAAG,EAAET,WAAW;MAChBU,yBAAyB,EAAE,IAAI;MAC/BjB,KAAK;MACLQ;IACF,CAAC,CAAC,CAACO,GAAG;EACR;EAEA,IAAIf,KAAK,CAACkB,iBAAiB,EAAE;IAC3B;IACA,MAAM;MACJc,kBAAkB;MAClBtD,MAAM,EAAEuD,gBAAgB;MACxBrB;IACF,CAAC,GAAG,IAAAsB,6BAAgB,EAAClC,KAAK,CAACkB,iBAAiB,EAAE;MAC5CzB,WAAW;MACXU,gBAAgB,EAAEH,KAAK,CAACG,gBAAgB;MACxCK,eAAe;MACf9B,MAAM,EAAEqD,gBAAgB,CAACzB,YAAY;IACvC,CAAC,CAAC;IACF;IACA;IACA,MAAM6B,aAAa,GAAG1D,gBAAgB,CAACwD,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC9D,OAAOvB,mBAAmB,CAACyB,aAAa,EAAEH,kBAAkB,EAAEpB,yBAAyB,CAAC;EAC1F;;EAEA;EACA,OAAOF,mBAAmB,CAACJ,YAAY,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;AACtD;AAEO,SAAS8B,cAAcA,CAAC3C,WAAmB,EAAqB;EACrE,MAAM,CAACuB,GAAG,CAAC,GAAGP,qBAAqB,CAAChB,WAAW,CAAC;EAChD,OAAOuB,GAAG;AACZ;AAEA,SAASP,qBAAqBA,CAAChB,WAAmB,EAA+B;EAC/E,MAAMe,eAAe,GAAG,IAAA6B,4CAAsB,EAAC5C,WAAW,CAAC;EAC3D,OAAO,CAAC6C,mBAAQ,CAACC,IAAI,CAAC/B,eAAe,CAAC,EAAEA,eAAe,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASP,kBAAkBA,CAACR,WAAmB,EAAmB;EACvE,OAAO;IACLyB,iBAAiB,EAAEsB,wBAAwB,CAAC/C,WAAW,CAAC;IACxDU,gBAAgB,EAAEsC,uBAAuB,CAAChD,WAAW;EACvD,CAAC;AACH;AAEA,SAAS+C,wBAAwBA,CAAC/C,WAAmB,EAAiB;EACpE,KAAK,MAAMiD,QAAQ,IAAI,CAAC,eAAe,EAAE,eAAe,CAAC,EAAE;IACzD,MAAMC,UAAU,GAAGC,eAAI,CAACtD,IAAI,CAACG,WAAW,EAAEiD,QAAQ,CAAC;IACnD,IAAIG,aAAE,CAACC,UAAU,CAACH,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU;IACnB;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASF,uBAAuBA,CAAChD,WAAmB,EAAiB;EACnE,KAAK,MAAMiD,QAAQ,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE;IACtD,MAAMC,UAAU,GAAGC,eAAI,CAACtD,IAAI,CAACG,WAAW,EAAEiD,QAAQ,CAAC;IACnD,IAAIG,aAAE,CAACC,UAAU,CAACH,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU;IACnB;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,eAAeI,iBAAiBA,CACrCtD,WAAmB,EACnBuD,aAAkC,EAClCC,WAA6B,GAAG,CAAC,CAAC,EAClCC,YAAgC,GAAG,CAAC,CAAC,EAKpC;EACD,MAAMxE,MAAM,GAAGoB,SAAS,CAACL,WAAW,EAAEwD,WAAW,CAAC;EAClD,MAAME,QAAQ,GAAGD,YAAY,CAACE,MAAM;;EAEpC;EACA,IAAI,CAAC1E,MAAM,CAACwC,iBAAiB,EAAE;IAC7B,MAAMmC,YAAY,GAAGC,wBAAwB,CAAC5E,MAAM,EAAEsE,aAAa,CAAC;IAEpE,IAAI,CAACG,QAAQ,EAAE;MACb,MAAMR,UAAU,GAAGjE,MAAM,CAACyB,gBAAgB,IAAIyC,eAAI,CAACtD,IAAI,CAACG,WAAW,EAAE,UAAU,CAAC;MAChF,MAAM6C,mBAAQ,CAACiB,UAAU,CAACZ,UAAU,EAAEU,YAAY,EAAE;QAAEG,KAAK,EAAE;MAAM,CAAC,CAAC;IACvE;IAEA,OAAO;MAAEC,IAAI,EAAE,SAAS;MAAE/E,MAAM,EAAE2E,YAAY,CAAC1E,IAAI,IAAI0E;IAAa,CAAC;EACvE;;EAEA;EACA,IACE3E,MAAM,CAACyB,gBAAgB,IACvBzB,MAAM,CAACiC,uBAAuB,KAAK,UAAU,IAC7C,CAACqC,aAAa,CAAClF,cAAc,CAAC,SAAS,CAAC,CAAC;EAAA,EACzC;IACA,MAAMuF,YAAY,GAAGC,wBAAwB,CAAC5E,MAAM,EAAEsE,aAAa,CAAC;IAEpE,IAAIG,QAAQ,EAAE;MACZ,OAAO;QACLM,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,qEAAqEd,eAAI,CAACe,QAAQ,CAAClE,WAAW,EAAEf,MAAM,CAACwC,iBAAiB,CAAC,EAAE;QACpIxC,MAAM,EAAE;MACV,CAAC;IACH;;IAEA;IACA,MAAM4D,mBAAQ,CAACiB,UAAU,CAAC7E,MAAM,CAACyB,gBAAgB,EAAEkD,YAAY,EAAE;MAAEG,KAAK,EAAE;IAAM,CAAC,CAAC;;IAElF;IACA,MAAMI,SAAS,GAAG9D,SAAS,CAACL,WAAW,EAAEwD,WAAW,CAAC;IACrD,MAAMY,yBAAyB,GAAGC,gBAAgB,CAACd,aAAa,EAAEY,SAAS,CAAC7C,GAAG,CAAC;IAChF,IAAI8C,yBAAyB,EAAE;MAC7B,OAAO;QACLJ,IAAI,EAAE,SAAS;QACf/E,MAAM,EAAEkF,SAAS,CAAC7C;MACpB,CAAC;IACH;;IAEA;IACA,MAAMuB,mBAAQ,CAACiB,UAAU,CAAC7E,MAAM,CAACyB,gBAAgB,EAAEzB,MAAM,CAAC2B,UAAU,EAAE;MAAEmD,KAAK,EAAE;IAAM,CAAC,CAAC;EACzF;;EAEA;EACA,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,oDAAoDd,eAAI,CAACe,QAAQ,CACxElE,WAAW,EACXf,MAAM,CAACwC,iBACT,CAAC,EAAE;IACHxC,MAAM,EAAE;EACV,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAAS4E,wBAAwBA,CAC/B5E,MAAqB,EACrB;EAAEqF,OAAO;EAAE,GAAGf;AAAmC,CAAC,EACnC;EACf,MAAMgB,kBAA8B,GAAG,CAACtF,MAAM,CAAC2B,UAAU,CAAC1B,IAAI,GAC1D,IAAAsF,oBAAS,EAACvF,MAAM,CAAC2B,UAAU,EAAE2C,aAAa,CAAC,GAC3C,IAAAiB,oBAAS,EAACvF,MAAM,CAAC2B,UAAU,CAAC1B,IAAI,EAAEqE,aAAa,CAAC;EAEpD,IAAIe,OAAO,EAAElF,MAAM,EAAE;IACnB;IACA,IAAI,CAACmF,kBAAkB,CAACD,OAAO,EAAE;MAC/BC,kBAAkB,CAACD,OAAO,GAAG,EAAE;IACjC;;IAEA;IACA,MAAMG,eAAoC,GAAGzG,MAAM,CAAC0G,WAAW,CAC7DH,kBAAkB,CAACD,OAAO,CAAC1E,GAAG,CAAE+E,UAAU,IACxC,OAAOA,UAAU,KAAK,QAAQ,GAAG,CAACA,UAAU,EAAEC,SAAS,CAAC,GAAGD,UAC7D,CACF,CAAC;IAED,KAAK,MAAME,MAAM,IAAIP,OAAO,EAAE;MAC5B;MACA,MAAM,CAACQ,UAAU,EAAEC,WAAW,CAAC,GAAGC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;MAC3E;MACA,IAAI,CAACC,UAAU,EAAE;;MAEjB;MACA,IAAI,EAAEA,UAAU,IAAIL,eAAe,CAAC,EAAE;QACpCF,kBAAkB,CAACD,OAAO,CAAClE,IAAI,CAACyE,MAAM,CAAC;QACvC;MACF;;MAEA;MACA,IAAIE,WAAW,EAAE;QACfR,kBAAkB,CAACD,OAAO,GAAGC,kBAAkB,CAACD,OAAO,CAAC1E,GAAG,CAAEsF,cAAc,IAAK;UAC9E,MAAM,CAACC,kBAAkB,CAAC,GAAGH,KAAK,CAACC,OAAO,CAACC,cAAc,CAAC,GACtDA,cAAc,GACd,CAACA,cAAc,CAAC;;UAEpB;UACA,IAAIC,kBAAkB,KAAKL,UAAU,EAAE;YACrC,OAAOI,cAAc;UACvB;;UAEA;UACA,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;YACtC,OAAO,CAACA,cAAc,EAAEH,WAAW,CAAC;UACtC;;UAEA;UACA,IAAIC,KAAK,CAACC,OAAO,CAACC,cAAc,CAAC,IAAIA,cAAc,CAAC,CAAC,CAAC,EAAE;YACtD,OAAO,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE,IAAAV,oBAAS,EAACU,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEH,WAAW,CAAC,CAAC;UAC7E;UAEA,OAAOG,cAAc;QACvB,CAAC,CAAC;QACF;MACF;;MAEA;IACF;EACF;EAEA,MAAME,eAAe,GAAG,CAACnG,MAAM,CAAC2B,UAAU,CAAC1B,IAAI,GAC3CqF,kBAAkB,GAClB;IAAE,GAAGtF,MAAM,CAAC2B,UAAU;IAAE1B,IAAI,EAAEqF;EAAmB,CAAC;EAEtD,OAAOa,eAAe;AACxB;AAEA,SAASf,gBAAgBA,CACvBgB,cAAiB,EACjBC,YAAe,EACN;EACT,KAAK,MAAMnH,GAAG,IAAIkH,cAAc,EAAE;IAChC,IAAI,CAACA,cAAc,CAAChH,cAAc,CAACF,GAAG,CAAC,EAAE;MACvC;IACF;IAEA,IAAI,OAAOkH,cAAc,CAAClH,GAAG,CAAC,KAAK,QAAQ,IAAImH,YAAY,CAACnH,GAAG,CAAC,KAAK,IAAI,EAAE;MACzE,IAAI,CAACkG,gBAAgB,CAACgB,cAAc,CAAClH,GAAG,CAAC,EAAEmH,YAAY,CAACnH,GAAG,CAAC,CAAC,EAAE;QAC7D,OAAO,KAAK;MACd;IACF,CAAC,MAAM;MACL,IAAIkH,cAAc,CAAClH,GAAG,CAAC,KAAKmH,YAAY,CAACnH,GAAG,CAAC,EAAE;QAC7C,OAAO,KAAK;MACd;IACF;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASkD,4BAA4BA,CAAC;EACpCrB,WAAW;EACXsB,GAAG;EACHC,GAAG;EACHhB,KAAK;EACLQ,eAAe;EACfS,yBAAyB,GAAG;AAQ9B,CAAC,EAA+C;EAC9C,IAAI,CAACF,GAAG,EAAE;IACRA,GAAG,GAAG,CAAC,CAAC;EACV;EACAA,GAAG,GAAG,IAAAiE,4BAAY,EAACjE,GAAG,EAAS;IAC7BtB,WAAW;IACX,IAAIO,KAAK,IAAI,CAAC,CAAC,CAAC;IAChBQ;EACF,CAAC,CAAC;EACF;EACA,MAAMyE,OAAO,GAAG,OAAOjE,GAAG,CAACkE,IAAI,KAAK,QAAQ,GAAGlE,GAAG,CAACkE,IAAI,GAAGtC,eAAI,CAACuC,QAAQ,CAAC1F,WAAW,CAAC;EACpF,MAAM2F,UAAU,GAAG,OAAOpE,GAAG,CAACqE,OAAO,KAAK,QAAQ,GAAGrE,GAAG,CAACqE,OAAO,GAAG,OAAO;EAE1E,MAAMC,eAAe,GAAG;IAAE,GAAGtE,GAAG;IAAEkE,IAAI,EAAED,OAAO;IAAEI,OAAO,EAAED;EAAW,CAAC;;EAEtE;EACA,MAAMF,IAAI,GAAGnE,GAAG,CAACmE,IAAI,IAAID,OAAO;EAChC,MAAMM,IAAI,GAAGxE,GAAG,CAACwE,IAAI,IAAI,IAAAC,kBAAO,EAACN,IAAI,CAACO,WAAW,CAAC,CAAC,CAAC;EACpD,MAAMJ,OAAO,GAAGtE,GAAG,CAACsE,OAAO,IAAID,UAAU;EACzC,IAAIM,WAAW,GAAG3E,GAAG,CAAC2E,WAAW;EACjC,IAAI,CAACA,WAAW,IAAI,OAAO1E,GAAG,CAAC0E,WAAW,KAAK,QAAQ,EAAE;IACvDA,WAAW,GAAG1E,GAAG,CAAC0E,WAAW;EAC/B;EAEA,MAAMC,eAAe,GAAG;IAAE,GAAG5E,GAAG;IAAEmE,IAAI;IAAEK,IAAI;IAAEF,OAAO;IAAEK;EAAY,CAAC;EAEpE,IAAIE,UAAU;EACd,IAAI;IACFA,UAAU,GAAG,IAAAC,sCAAiB,EAACpG,WAAW,EAAEkG,eAAe,CAAC;EAC9D,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAI,CAAC7E,yBAAyB,EAAE,MAAM6E,KAAK;EAC7C;EAEA,IAAIpG,SAAS,GAAGqB,GAAG,CAACrB,SAAS;EAC7B,IAAI,CAACA,SAAS,EAAE;IACdA,SAAS,GAAGF,qBAAqB,CAACC,WAAW,CAAC;EAChD;EAEA,OAAO;IACLsB,GAAG,EAAE;MAAE,GAAG4E,eAAe;MAAEC,UAAU;MAAElG;IAAU,CAAC;IAClDsB,GAAG,EAAEsE;EACP,CAAC;AACH;AAEA,MAAMS,kBAAkB,GAAG,WAAW;AAE/B,SAASC,gBAAgBA,CAACtH,MAA8B,GAAG,CAAC,CAAC,EAAU;EAC5E,IAAIuH,OAAO,CAACC,GAAG,CAACC,yBAAyB,EAAE;IACzC,OAAOF,OAAO,CAACC,GAAG,CAACC,yBAAyB;EAC9C;EACA,MAAMxH,IAAI,GAAGD,MAAM,CAACC,IAAI,IAAID,MAAM,IAAI,CAAC,CAAC;EACxC,OAAOC,IAAI,EAAEyH,GAAG,EAAEC,KAAK,EAAEC,MAAM,IAAIP,kBAAkB;AACvD;AAEO,SAASQ,iBAAiBA,CAACxF,GAAwB,GAAG,CAAC,CAAC,EAG7D;EACA;EACA,MAAMyF,WAAW,GAAGzF,GAAG,CAACpC,IAAI,IAAIoC,GAAG;EACnC,MAAM;IAAEqF,GAAG,GAAG,CAAC;EAAE,CAAC,GAAGI,WAAW;;EAEhC;EACA,MAAMC,OAAO,GAAG1F,GAAG,CAAC2F,WAAW,IAAIF,WAAW,CAACE,WAAW,IAAIF,WAAW,CAACtB,IAAI;EAC9E,MAAMyB,OAAO,GAAGP,GAAG,CAAClB,IAAI,IAAIuB,OAAO;EAEnC,OAAO;IACLA,OAAO;IACPE;EACF,CAAC;AACH;AAEO,SAASC,gBAAgBA,CAC9BnH,WAAmB,EACnBsB,GAAoC,EACrB;EACfA,GAAG,KAAKjB,SAAS,CAACL,WAAW,EAAE;IAAEwB,yBAAyB,EAAE;EAAK,CAAC,CAAC,CAACF,GAAG;;EAEvE;EACA,IAAIA,GAAG,CAAC6E,UAAU,IAAI7E,GAAG,CAAC6E,UAAU,KAAK,aAAa,IAAIiB,iBAAM,CAACC,EAAE,CAAC/F,GAAG,CAAC6E,UAAU,EAAE,QAAQ,CAAC,EAAE;IAC7F,OAAO,SAAS;EAClB;EACA,OAAOmB,qBAAqB,CAACtH,WAAW,CAAC,GAAG,MAAM,GAAG,SAAS;AAChE;AAEA,SAASsH,qBAAqBA,CAACtH,WAAmB,EAAW;EAC3D,MAAM,CAACuB,GAAG,CAAC,GAAGP,qBAAqB,CAAChB,WAAW,CAAC;;EAEhD;EACA,IAAIuB,GAAG,CAACgG,YAAY,IAAIhG,GAAG,CAACgG,YAAY,CAACC,OAAO,EAAE;IAChD,OAAO,KAAK;EACd;EAEA,MAAMC,cAAc,GAAG,IAAAC,YAAQ,EAAC,oBAAoB,EAAE;IACpDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE5H;EACP,CAAC,CAAC;EACF,IAAIyH,cAAc,CAACrI,MAAM,EAAE;IACzB,OAAO,IAAI;EACb;EACA,MAAMyI,WAAW,GAAG,IAAAH,YAAQ,EAAC,qBAAqB,EAAE;IAClDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE5H;EACP,CAAC,CAAC;EACF,IAAI6H,WAAW,CAACzI,MAAM,EAAE;IACtB,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS0I,2BAA2BA,CAAC9H,WAAmB,EAAU;EACvE,MAAMO,KAAK,GAAGC,kBAAkB,CAACR,WAAW,CAAC;EAC7C,OAAO+H,oCAAoC,CAAC/H,WAAW,EAAEO,KAAK,CAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASwH,oCAAoCA,CAClD/H,WAAmB,EACnBgI,aAA8B,EACtB;EACR,IAAIA,aAAa,CAACvG,iBAAiB,EAAE;IACnC,MAAMwG,yBAAyB,GAAG9E,eAAI,CAACe,QAAQ,CAAClE,WAAW,EAAEgI,aAAa,CAACvG,iBAAiB,CAAC;IAC7F,IAAIuG,aAAa,CAACtH,gBAAgB,EAAE;MAClC,OAAO,GAAGuH,yBAAyB,OAAO9E,eAAI,CAACe,QAAQ,CACrDlE,WAAW,EACXgI,aAAa,CAACtH,gBAChB,CAAC,EAAE;IACL;IACA,OAAOuH,yBAAyB;EAClC,CAAC,MAAM,IAAID,aAAa,CAACtH,gBAAgB,EAAE;IACzC,OAAOyC,eAAI,CAACe,QAAQ,CAAClE,WAAW,EAAEgI,aAAa,CAACtH,gBAAgB,CAAC;EACnE;EACA;EACA,OAAO,UAAU;AACnB", "ignoreList": []}