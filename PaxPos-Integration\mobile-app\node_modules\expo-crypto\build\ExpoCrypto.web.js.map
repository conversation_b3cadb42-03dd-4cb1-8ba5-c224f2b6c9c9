{"version": 3, "file": "ExpoCrypto.web.js", "sourceRoot": "", "sources": ["../src/ExpoCrypto.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAc,MAAM,mBAAmB,CAAC;AAE3D,OAAO,EAAyB,cAAc,EAAuB,MAAM,gBAAgB,CAAC;AAE5F,MAAM,SAAS,GAAG,GAAW,EAAE,CAAC,MAAM,CAAC,MAAM,IAAK,MAAc,CAAC,QAAQ,CAAC;AAE1E,eAAe;IACb,KAAK,CAAC,iBAAiB,CACrB,SAAgC,EAChC,IAAY,EACZ,OAA4B;QAE5B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,UAAU,CAClB,wBAAwB,EACxB,gFAAgF,CACjF,CAAC;QACJ,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACjE,IAAI,OAAO,CAAC,QAAQ,KAAK,cAAc,CAAC,GAAG,EAAE,CAAC;YAC5C,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,cAAc,CAAC,MAAM,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC;QACD,MAAM,IAAI,UAAU,CAAC,mBAAmB,EAAE,iCAAiC,CAAC,CAAC;IAC/E,CAAC;IACD,cAAc,CAAC,MAAc;QAC3B,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QACrC,OAAO,SAAS,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IACD,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QACrC,OAAO,SAAS,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IACD,eAAe,CAAC,UAAsB;QACpC,OAAO,SAAS,EAAE,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IACD,UAAU;QACR,OAAO,SAAS,EAAE,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IACD,WAAW,CAAC,SAA8B,EAAE,IAAiB;QAC3D,OAAO,SAAS,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;CACF,CAAC;AAEF,SAAS,SAAS,CAAC,MAAmB;IACpC,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAEzC,MAAM,QAAQ,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACnC,MAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC/C,OAAO,aAAa,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC", "sourcesContent": ["import { CodedError, TypedArray } from 'expo-modules-core';\n\nimport { CryptoDigestAlgorithm, CryptoEncoding, CryptoDigestOptions } from './Crypto.types';\n\nconst getCrypto = (): Crypto => window.crypto ?? (window as any).msCrypto;\n\nexport default {\n  async digestStringAsync(\n    algorithm: CryptoDigestAlgorithm,\n    data: string,\n    options: CryptoDigestOptions\n  ): Promise<string> {\n    if (!crypto.subtle) {\n      throw new CodedError(\n        'ERR_CRYPTO_UNAVAILABLE',\n        'Access to the WebCrypto API is restricted to secure origins (localhost/https).'\n      );\n    }\n    const encoder = new TextEncoder();\n    const buffer = encoder.encode(data);\n    const hashedData = await crypto.subtle.digest(algorithm, buffer);\n    if (options.encoding === CryptoEncoding.HEX) {\n      return hexString(hashedData);\n    } else if (options.encoding === CryptoEncoding.BASE64) {\n      return btoa(String.fromCharCode(...new Uint8Array(hashedData)));\n    }\n    throw new CodedError('ERR_CRYPTO_DIGEST', 'Invalid encoding type provided.');\n  },\n  getRandomBytes(length: number): Uint8Array {\n    const array = new Uint8Array(length);\n    return getCrypto().getRandomValues(array);\n  },\n  async getRandomBytesAsync(length: number): Promise<Uint8Array> {\n    const array = new Uint8Array(length);\n    return getCrypto().getRandomValues(array);\n  },\n  getRandomValues(typedArray: TypedArray) {\n    return getCrypto().getRandomValues(typedArray);\n  },\n  randomUUID() {\n    return getCrypto().randomUUID();\n  },\n  digestAsync(algorithm: AlgorithmIdentifier, data: ArrayBuffer): Promise<ArrayBuffer> {\n    return getCrypto().subtle.digest(algorithm, data);\n  },\n};\n\nfunction hexString(buffer: ArrayBuffer): string {\n  const byteArray = new Uint8Array(buffer);\n\n  const hexCodes = [...byteArray].map((value) => {\n    const hexCode = value.toString(16);\n    const paddedHexCode = hexCode.padStart(2, '0');\n    return paddedHexCode;\n  });\n\n  return hexCodes.join('');\n}\n"]}