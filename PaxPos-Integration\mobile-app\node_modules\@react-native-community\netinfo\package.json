{"name": "@react-native-community/netinfo", "version": "11.4.1", "description": "React Native Network Info API for iOS & Android", "react-native": "src/index.ts", "types": "lib/typescript/src/index.d.ts", "main": "lib/commonjs/index.js", "module": "lib/module/index.js", "files": ["/android", "!/android/build", "/ios", "/windows", "/macos", "/src", "/lib", "/*.podspec", "/jest"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> (Salakar) <<EMAIL>>"], "homepage": "https://github.com/react-native-netinfo/react-native-netinfo#readme", "license": "MIT", "scripts": {"start": "react-native start", "start:android": "react-native run-android --appId com.reactnativecommunity.netinfo.example", "start:ios": "react-native run-ios", "start:macos": "react-native run-macos --scheme NetInfoExample --project-path example/macos", "start:web": "webpack-dev-server --config example/webpack.config.js", "start:windows": "install-windows-test-app --project-directory example/windows && react-native run-windows --root example --arch x64", "test": "yarn validate:eslint && yarn validate:typescript && yarn test:jest", "validate:eslint": "eslint \"src/**/*.{js,ts,tsx}\" \"example/**/*.{js,ts,tsx}\"", "validate:typescript": "tsc --project ./ --noEmit", "test:jest": "jest \"/src/\"", "build:e2e:macos": "scripts/run_macos_e2e.sh 'build'", "test:e2e:macos": "scripts/run_macos_e2e.sh 'test'", "ci:publish": "yarn semantic-release", "semantic-release": "semantic-release", "prepare": "bob build"}, "keywords": ["react-native", "react native", "netinfo", "networking", "network info"], "peerDependencies": {"react-native": ">=0.59"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/bob": "^0.17.1", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "9.0.0", "@types/jest": "^29.2.1", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "babel-jest": "^29.2.1", "babel-loader": "^8.0.6", "eslint": "8.19.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "3.1.2", "html-webpack-plugin": "^3.2.0", "husky": "^2.7.0", "jest": "^29.2.1", "jest-expo": "^49.0.0", "jest-fetch-mock": "^3.0.3", "lint-staged": "^8.2.1", "metro-react-native-babel-preset": "^0.76.8", "prettier": "^2.4.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "^0.72.6", "react-native-macos": "^0.72.5", "react-native-test-app": "^2.5.32", "react-native-web": "^0.17.7", "react-native-windows": "^0.72.18", "react-test-renderer": "18.2.0", "rimraf": "^2.6.3", "semantic-release": "^17.4.7", "source-map-loader": "^0.2.4", "ts-jest": "^29.1.1", "ts-loader": "^6.2.1", "typescript": "^5.2.2", "webpack": "^4.47.0", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.11.3"}, "repository": {"type": "git", "url": "https://github.com/react-native-netinfo/react-native-netinfo.git"}, "@react-native-community/bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn test"}}, "lint-staged": {"*.{js,ts,tsx}": ["yarn eslint --fix", "git add"]}}