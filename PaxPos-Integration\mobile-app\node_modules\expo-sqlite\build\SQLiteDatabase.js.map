{"version": 3, "file": "SQLiteDatabase.js", "sourceRoot": "", "sources": ["../src/SQLiteDatabase.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,kBAAkB,EAAqC,MAAM,kBAAkB,CAAC;AACzF,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAKL,eAAe,GAEhB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAIjD;;GAEG;AACH,MAAM,OAAO,cAAc;IAEP;IACA;IACA;IAHlB,YACkB,YAAoB,EACpB,OAA0B,EAC1B,cAA8B;QAF9B,iBAAY,GAAZ,YAAY,CAAQ;QACpB,YAAO,GAAP,OAAO,CAAmB;QAC1B,mBAAc,GAAd,cAAc,CAAgB;IAC7C,CAAC;IAEJ;;OAEG;IACI,oBAAoB;QACzB,OAAO,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,UAAU;QACf,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,MAAc;QAC7B,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,eAAuB,MAAM;QACjD,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAC1D,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,YAAY,CAAC,MAAc;QACtC,MAAM,eAAe,GAAG,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;QACzD,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QAChE,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,kBAAkB,CAAC,SAAiB,MAAM;QACrD,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;QACrD,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACpE,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACI,KAAK,CAAC,oBAAoB,CAAC,IAAyB;QACzD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC9B,MAAM,IAAI,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACjC,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACI,KAAK,CAAC,6BAA6B,CACxC,IAAyC;QAEzC,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QACD,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,KAAK,CAAC;QACV,IAAI,CAAC;YACH,MAAM,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC;YACxB,MAAM,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACxC,KAAK,GAAG,CAAC,CAAC;QACZ,CAAC;gBAAS,CAAC;YACT,MAAM,WAAW,CAAC,UAAU,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,SAAS;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;IACzC,CAAC;IAED;;;;;;;;OAQG;IACI,QAAQ,CAAC,MAAc;QAC5B,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAAC,eAAuB,MAAM;QAChD,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,MAAc;QAC/B,MAAM,eAAe,GAAG,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;QACzD,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QACzD,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;OAOG;IACI,iBAAiB,CAAC,SAAiB,MAAM;QAC9C,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;QACrD,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAC7D,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,IAAgB;QACzC,IAAI,CAAC;YACH,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvB,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC1B,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAeM,KAAK,CAAC,QAAQ,CAAC,MAAc,EAAE,GAAG,MAAa;QACpD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,MAAyC,CAAC;QAC9C,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC,CAAC;QACnD,CAAC;gBAAS,CAAC;YACT,MAAM,SAAS,CAAC,aAAa,EAAE,CAAC;QAClC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAYM,KAAK,CAAC,aAAa,CAAI,MAAc,EAAE,GAAG,MAAa;QAC5D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,QAAkB,CAAC;QACvB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAI,GAAG,MAAM,CAAC,CAAC;YAC1D,QAAQ,GAAG,MAAM,MAAM,CAAC,aAAa,EAAE,CAAC;QAC1C,CAAC;gBAAS,CAAC;YACT,MAAM,SAAS,CAAC,aAAa,EAAE,CAAC;QAClC,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAgBM,KAAK,CAAC,CAAC,YAAY,CAAI,MAAc,EAAE,GAAG,MAAa;QAC5D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAI,GAAG,MAAM,CAAC,CAAC;YAC1D,IAAI,KAAK,EAAE,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;gBAC/B,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,MAAM,SAAS,CAAC,aAAa,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAuBM,KAAK,CAAC,WAAW,CAAI,MAAc,EAAE,GAAG,MAAa;QAC1D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,OAAO,CAAC;QACZ,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAI,GAAG,MAAM,CAAC,CAAC;YAC1D,OAAO,GAAG,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;QACvC,CAAC;gBAAS,CAAC;YACT,MAAM,SAAS,CAAC,aAAa,EAAE,CAAC;QAClC,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAaM,OAAO,CAAC,MAAc,EAAE,GAAG,MAAa;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,MAAwC,CAAC;QAC7C,IAAI,CAAC;YACH,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,CAAC;QAC5C,CAAC;gBAAS,CAAC;YACT,SAAS,CAAC,YAAY,EAAE,CAAC;QAC3B,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAaM,YAAY,CAAI,MAAc,EAAE,GAAG,MAAa;QACrD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,QAAkB,CAAC;QACvB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,SAAS,CAAC,WAAW,CAAI,GAAG,MAAM,CAAC,CAAC;YACnD,QAAQ,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QACnC,CAAC;gBAAS,CAAC;YACT,SAAS,CAAC,YAAY,EAAE,CAAC;QAC3B,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAcM,CAAC,WAAW,CAAI,MAAc,EAAE,GAAG,MAAa;QACrD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,SAAS,CAAC,WAAW,CAAI,GAAG,MAAM,CAAC,CAAC;YACnD,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;gBACzB,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,SAAS,CAAC,YAAY,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAaM,UAAU,CAAI,MAAc,EAAE,GAAG,MAAa;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,OAAO,CAAC;QACZ,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,SAAS,CAAC,WAAW,CAAI,GAAG,MAAM,CAAC,CAAC;YACnD,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QAChC,CAAC;gBAAS,CAAC;YACT,SAAS,CAAC,YAAY,EAAE,CAAC;QAC3B,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;IAC1C,CAAC;CAGF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,UAAU,CAAC,wBAAwB,CAAC;AAE5E;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,YAAoB,EACpB,OAA2B,EAC3B,SAAkB;IAElB,MAAM,WAAW,GAAG,OAAO,IAAI,EAAE,CAAC;IAClC,MAAM,YAAY,GAAG,kBAAkB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACjE,MAAM,UAAU,CAAC,6BAA6B,CAAC,YAAY,CAAC,CAAC;IAC7D,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,cAAc,CAClD,YAAY,EACZ,kBAAkB,CAAC,WAAW,CAAC,CAChC,CAAC;IACF,MAAM,cAAc,CAAC,SAAS,EAAE,CAAC;IACjC,OAAO,IAAI,cAAc,CAAC,YAAY,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AACvE,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,gBAAgB,CAC9B,YAAoB,EACpB,OAA2B,EAC3B,SAAkB;IAElB,MAAM,WAAW,GAAG,OAAO,IAAI,EAAE,CAAC;IAClC,MAAM,YAAY,GAAG,kBAAkB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACjE,UAAU,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAC;IACtD,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,cAAc,CAClD,YAAY,EACZ,kBAAkB,CAAC,WAAW,CAAC,CAChC,CAAC;IACF,cAAc,CAAC,QAAQ,EAAE,CAAC;IAC1B,OAAO,IAAI,cAAc,CAAC,YAAY,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AACvE,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAC5C,cAA0B,EAC1B,OAA2B;IAE3B,MAAM,WAAW,GAAG,OAAO,IAAI,EAAE,CAAC;IAClC,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,cAAc,CAClD,UAAU,EACV,kBAAkB,CAAC,WAAW,CAAC,EAC/B,cAAc,CACf,CAAC;IACF,MAAM,cAAc,CAAC,SAAS,EAAE,CAAC;IACjC,OAAO,IAAI,cAAc,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AACrE,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,uBAAuB,CACrC,cAA0B,EAC1B,OAA2B;IAE3B,MAAM,WAAW,GAAG,OAAO,IAAI,EAAE,CAAC;IAClC,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,cAAc,CAClD,UAAU,EACV,kBAAkB,CAAC,WAAW,CAAC,EAC/B,cAAc,CACf,CAAC;IACF,cAAc,CAAC,QAAQ,EAAE,CAAC;IAC1B,OAAO,IAAI,cAAc,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AACrE,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,YAAoB,EAAE,SAAkB;IAChF,MAAM,YAAY,GAAG,kBAAkB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACjE,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,kBAAkB,CAAC,YAAoB,EAAE,SAAkB;IACzE,MAAM,YAAY,GAAG,kBAAkB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACjE,OAAO,UAAU,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,mBAAmB,CAAC,EAClC,cAAc,EACd,kBAAkB,EAClB,YAAY,EACZ,gBAAgB,GAMjB;IACC,OAAO,UAAU,CAAC,mBAAmB,CACnC,YAAY,CAAC,cAAc,EAC3B,gBAAgB,IAAI,MAAM,EAC1B,cAAc,CAAC,cAAc,EAC7B,kBAAkB,IAAI,MAAM,CAC7B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,kBAAkB,CAAC,EACjC,cAAc,EACd,kBAAkB,EAClB,YAAY,EACZ,gBAAgB,GAMjB;IACC,OAAO,UAAU,CAAC,kBAAkB,CAClC,YAAY,CAAC,cAAc,EAC3B,gBAAgB,IAAI,MAAM,EAC1B,cAAc,CAAC,cAAc,EAC7B,kBAAkB,IAAI,MAAM,CAC7B,CAAC;AACJ,CAAC;AAmBD;;;;;;GAMG;AACH,MAAM,UAAU,yBAAyB,CACvC,QAA8C;IAE9C,OAAO,UAAU,CAAC,WAAW,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;AAC9D,CAAC;AAED;;;GAGG;AACH,MAAM,WAAY,SAAQ,cAAc;IAC/B,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,EAAkB;QAChD,MAAM,OAAO,GAAG,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC;QAC1D,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,cAAc,CAClD,EAAE,CAAC,YAAY,EACf,kBAAkB,CAAC,OAAO,CAAC,CAC5B,CAAC;QACF,MAAM,cAAc,CAAC,SAAS,EAAE,CAAC;QACjC,OAAO,IAAI,WAAW,CAAC,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;IACnE,CAAC;CACF", "sourcesContent": ["import { type EventSubscription } from 'expo-modules-core';\nimport { Platform } from 'react-native';\n\nimport ExpoSQLite from './ExpoSQLite';\nimport { flattenOpenOptions, NativeDatabase, SQLiteOpenOptions } from './NativeDatabase';\nimport { SQLiteSession } from './SQLiteSession';\nimport {\n  SQLiteBindParams,\n  SQLiteExecuteAsyncResult,\n  SQLiteExecuteSyncResult,\n  SQLiteRunResult,\n  SQLiteStatement,\n  SQLiteVariadicBindParams,\n} from './SQLiteStatement';\nimport { createDatabasePath } from './pathUtils';\n\nexport { SQLiteOpenOptions };\n\n/**\n * A SQLite database.\n */\nexport class SQLiteDatabase {\n  constructor(\n    public readonly databasePath: string,\n    public readonly options: SQLiteOpenOptions,\n    public readonly nativeDatabase: NativeDatabase\n  ) {}\n\n  /**\n   * Asynchronous call to return whether the database is currently in a transaction.\n   */\n  public isInTransactionAsync(): Promise<boolean> {\n    return this.nativeDatabase.isInTransactionAsync();\n  }\n\n  /**\n   * Close the database.\n   */\n  public closeAsync(): Promise<void> {\n    return this.nativeDatabase.closeAsync();\n  }\n\n  /**\n   * Execute all SQL queries in the supplied string.\n   * > Note: The queries are not escaped for you! Be careful when constructing your queries.\n   *\n   * @param source A string containing all the SQL queries.\n   */\n  public execAsync(source: string): Promise<void> {\n    return this.nativeDatabase.execAsync(source);\n  }\n\n  /**\n   * [Serialize the database](https://sqlite.org/c3ref/serialize.html) as `Uint8Array`.\n   *\n   * @param databaseName The name of the current attached databases. The default value is `main` which is the default database name.\n   */\n  public serializeAsync(databaseName: string = 'main'): Promise<Uint8Array> {\n    return this.nativeDatabase.serializeAsync(databaseName);\n  }\n\n  /**\n   * Create a [prepared SQLite statement](https://www.sqlite.org/c3ref/prepare.html).\n   *\n   * @param source A string containing the SQL query.\n   */\n  public async prepareAsync(source: string): Promise<SQLiteStatement> {\n    const nativeStatement = new ExpoSQLite.NativeStatement();\n    await this.nativeDatabase.prepareAsync(nativeStatement, source);\n    return new SQLiteStatement(this.nativeDatabase, nativeStatement);\n  }\n\n  /**\n   * Create a new session for the database.\n   * @see [`sqlite3session_create`](https://www.sqlite.org/session/sqlite3session_create.html)\n   * @param dbName The name of the database to create a session for. The default value is `main`.\n   */\n  public async createSessionAsync(dbName: string = 'main'): Promise<SQLiteSession> {\n    const nativeSession = new ExpoSQLite.NativeSession();\n    await this.nativeDatabase.createSessionAsync(nativeSession, dbName);\n    return new SQLiteSession(this.nativeDatabase, nativeSession);\n  }\n\n  /**\n   * Execute a transaction and automatically commit/rollback based on the `task` result.\n   *\n   * > **Note:** This transaction is not exclusive and can be interrupted by other async queries.\n   *\n   * @example\n   * ```ts\n   * db.withTransactionAsync(async () => {\n   *   await db.execAsync('UPDATE test SET name = \"aaa\"');\n   *\n   *   //\n   *   // We cannot control the order of async/await order, so order of execution is not guaranteed.\n   *   // The following UPDATE query out of transaction may be executed here and break the expectation.\n   *   //\n   *\n   *   const result = await db.getFirstAsync<{ name: string }>('SELECT name FROM Users');\n   *   expect(result?.name).toBe('aaa');\n   * });\n   * db.execAsync('UPDATE test SET name = \"bbb\"');\n   * ```\n   * If you worry about the order of execution, use `withExclusiveTransactionAsync` instead.\n   *\n   * @param task An async function to execute within a transaction.\n   */\n  public async withTransactionAsync(task: () => Promise<void>): Promise<void> {\n    try {\n      await this.execAsync('BEGIN');\n      await task();\n      await this.execAsync('COMMIT');\n    } catch (e) {\n      await this.execAsync('ROLLBACK');\n      throw e;\n    }\n  }\n\n  /**\n   * Execute a transaction and automatically commit/rollback based on the `task` result.\n   *\n   * The transaction may be exclusive.\n   * As long as the transaction is converted into a write transaction,\n   * the other async write queries will abort with `database is locked` error.\n   *\n   * > **Note:** This function is not supported on web.\n   *\n   * @param task An async function to execute within a transaction. Any queries inside the transaction must be executed on the `txn` object.\n   * The `txn` object has the same interfaces as the [`SQLiteDatabase`](#sqlitedatabase) object. You can use `txn` like a [`SQLiteDatabase`](#sqlitedatabase) object.\n   *\n   * @platform android\n   * @platform ios\n   * @platform macos\n   *\n   * @example\n   * ```ts\n   * db.withExclusiveTransactionAsync(async (txn) => {\n   *   await txn.execAsync('UPDATE test SET name = \"aaa\"');\n   * });\n   * ```\n   */\n  public async withExclusiveTransactionAsync(\n    task: (txn: Transaction) => Promise<void>\n  ): Promise<void> {\n    if (Platform.OS === 'web') {\n      throw new Error('withExclusiveTransactionAsync is not supported on web');\n    }\n    const transaction = await Transaction.createAsync(this);\n    let error;\n    try {\n      await transaction.execAsync('BEGIN');\n      await task(transaction);\n      await transaction.execAsync('COMMIT');\n    } catch (e) {\n      await transaction.execAsync('ROLLBACK');\n      error = e;\n    } finally {\n      await transaction.closeAsync();\n    }\n    if (error) {\n      throw error;\n    }\n  }\n\n  /**\n   * Synchronous call to return whether the database is currently in a transaction.\n   */\n  public isInTransactionSync(): boolean {\n    return this.nativeDatabase.isInTransactionSync();\n  }\n\n  /**\n   * Close the database.\n   */\n  public closeSync(): void {\n    return this.nativeDatabase.closeSync();\n  }\n\n  /**\n   * Execute all SQL queries in the supplied string.\n   *\n   * > **Note:** The queries are not escaped for you! Be careful when constructing your queries.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @param source A string containing all the SQL queries.\n   */\n  public execSync(source: string): void {\n    return this.nativeDatabase.execSync(source);\n  }\n\n  /**\n   * [Serialize the database](https://sqlite.org/c3ref/serialize.html) as `Uint8Array`.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @param databaseName The name of the current attached databases. The default value is `main` which is the default database name.\n   */\n  public serializeSync(databaseName: string = 'main'): Uint8Array {\n    return this.nativeDatabase.serializeSync(databaseName);\n  }\n\n  /**\n   * Create a [prepared SQLite statement](https://www.sqlite.org/c3ref/prepare.html).\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @param source A string containing the SQL query.\n   */\n  public prepareSync(source: string): SQLiteStatement {\n    const nativeStatement = new ExpoSQLite.NativeStatement();\n    this.nativeDatabase.prepareSync(nativeStatement, source);\n    return new SQLiteStatement(this.nativeDatabase, nativeStatement);\n  }\n\n  /**\n   * Create a new session for the database.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @see [`sqlite3session_create`](https://www.sqlite.org/session/sqlite3session_create.html)\n   * @param dbName The name of the database to create a session for. The default value is `main`.\n   */\n  public createSessionSync(dbName: string = 'main'): SQLiteSession {\n    const nativeSession = new ExpoSQLite.NativeSession();\n    this.nativeDatabase.createSessionSync(nativeSession, dbName);\n    return new SQLiteSession(this.nativeDatabase, nativeSession);\n  }\n\n  /**\n   * Execute a transaction and automatically commit/rollback based on the `task` result.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @param task An async function to execute within a transaction.\n   */\n  public withTransactionSync(task: () => void): void {\n    try {\n      this.execSync('BEGIN');\n      task();\n      this.execSync('COMMIT');\n    } catch (e) {\n      this.execSync('ROLLBACK');\n      throw e;\n    }\n  }\n\n  //#region Statement API shorthands\n\n  /**\n   * A convenience wrapper around [`SQLiteDatabase.prepareAsync()`](#prepareasyncsource), [`SQLiteStatement.executeAsync()`](#executeasyncparams), and [`SQLiteStatement.finalizeAsync()`](#finalizeasync).\n   * @param source A string containing the SQL query.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   */\n  public runAsync(source: string, params: SQLiteBindParams): Promise<SQLiteRunResult>;\n\n  /**\n   * @hidden\n   */\n  public runAsync(source: string, ...params: SQLiteVariadicBindParams): Promise<SQLiteRunResult>;\n  public async runAsync(source: string, ...params: any[]): Promise<SQLiteRunResult> {\n    const statement = await this.prepareAsync(source);\n    let result: SQLiteExecuteAsyncResult<unknown>;\n    try {\n      result = await statement.executeAsync(...params);\n    } finally {\n      await statement.finalizeAsync();\n    }\n    return result;\n  }\n\n  /**\n   * A convenience wrapper around [`SQLiteDatabase.prepareAsync()`](#prepareasyncsource), [`SQLiteStatement.executeAsync()`](#executeasyncparams), [`SQLiteExecuteAsyncResult.getFirstAsync()`](#getfirstasync), and [`SQLiteStatement.finalizeAsync()`](#finalizeasync).\n   * @param source A string containing the SQL query.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   */\n  public getFirstAsync<T>(source: string, params: SQLiteBindParams): Promise<T | null>;\n  /**\n   * @hidden\n   */\n  public getFirstAsync<T>(source: string, ...params: SQLiteVariadicBindParams): Promise<T | null>;\n  public async getFirstAsync<T>(source: string, ...params: any[]): Promise<T | null> {\n    const statement = await this.prepareAsync(source);\n    let firstRow: T | null;\n    try {\n      const result = await statement.executeAsync<T>(...params);\n      firstRow = await result.getFirstAsync();\n    } finally {\n      await statement.finalizeAsync();\n    }\n    return firstRow;\n  }\n\n  /**\n   * A convenience wrapper around [`SQLiteDatabase.prepareAsync()`](#prepareasyncsource), [`SQLiteStatement.executeAsync()`](#executeasyncparams), [`SQLiteExecuteAsyncResult`](#sqliteexecuteasyncresult) `AsyncIterator`, and [`SQLiteStatement.finalizeAsync()`](#finalizeasync).\n   * @param source A string containing the SQL query.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   * @returns Rather than returning Promise, this function returns an [`AsyncIterableIterator`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/AsyncIterator). You can use `for await...of` to iterate over the rows from the SQLite query result.\n   */\n  public getEachAsync<T>(source: string, params: SQLiteBindParams): AsyncIterableIterator<T>;\n  /**\n   * @hidden\n   */\n  public getEachAsync<T>(\n    source: string,\n    ...params: SQLiteVariadicBindParams\n  ): AsyncIterableIterator<T>;\n  public async *getEachAsync<T>(source: string, ...params: any[]): AsyncIterableIterator<T> {\n    const statement = await this.prepareAsync(source);\n    try {\n      const result = await statement.executeAsync<T>(...params);\n      for await (const row of result) {\n        yield row;\n      }\n    } finally {\n      await statement.finalizeAsync();\n    }\n  }\n\n  /**\n   * A convenience wrapper around [`SQLiteDatabase.prepareAsync()`](#prepareasyncsource), [`SQLiteStatement.executeAsync()`](#executeasyncparams), [`SQLiteExecuteAsyncResult.getAllAsync()`](#getallasync), and [`SQLiteStatement.finalizeAsync()`](#finalizeasync).\n   * @param source A string containing the SQL query.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   * @example\n   * ```ts\n   * // For unnamed parameters, you pass values in an array.\n   * db.getAllAsync('SELECT * FROM test WHERE intValue = ? AND name = ?', [1, 'Hello']);\n   *\n   * // For unnamed parameters, you pass values in variadic arguments.\n   * db.getAllAsync('SELECT * FROM test WHERE intValue = ? AND name = ?', 1, 'Hello');\n   *\n   * // For named parameters, you should pass values in object.\n   * db.getAllAsync('SELECT * FROM test WHERE intValue = $intValue AND name = $name', { $intValue: 1, $name: 'Hello' });\n   * ```\n   */\n  public getAllAsync<T>(source: string, params: SQLiteBindParams): Promise<T[]>;\n  /**\n   * @hidden\n   */\n  public getAllAsync<T>(source: string, ...params: SQLiteVariadicBindParams): Promise<T[]>;\n  public async getAllAsync<T>(source: string, ...params: any[]): Promise<T[]> {\n    const statement = await this.prepareAsync(source);\n    let allRows;\n    try {\n      const result = await statement.executeAsync<T>(...params);\n      allRows = await result.getAllAsync();\n    } finally {\n      await statement.finalizeAsync();\n    }\n    return allRows;\n  }\n\n  /**\n   * A convenience wrapper around [`SQLiteDatabase.prepareSync()`](#preparesyncsource), [`SQLiteStatement.executeSync()`](#executesyncparams), and [`SQLiteStatement.finalizeSync()`](#finalizesync).\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   * @param source A string containing the SQL query.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   */\n  public runSync(source: string, params: SQLiteBindParams): SQLiteRunResult;\n  /**\n   * @hidden\n   */\n  public runSync(source: string, ...params: SQLiteVariadicBindParams): SQLiteRunResult;\n  public runSync(source: string, ...params: any[]): SQLiteRunResult {\n    const statement = this.prepareSync(source);\n    let result: SQLiteExecuteSyncResult<unknown>;\n    try {\n      result = statement.executeSync(...params);\n    } finally {\n      statement.finalizeSync();\n    }\n    return result;\n  }\n\n  /**\n   * A convenience wrapper around [`SQLiteDatabase.prepareSync()`](#preparesyncsource), [`SQLiteStatement.executeSync()`](#executesyncparams), [`SQLiteExecuteSyncResult.getFirstSync()`](#getfirstsync), and [`SQLiteStatement.finalizeSync()`](#finalizesync).\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   * @param source A string containing the SQL query.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   */\n  public getFirstSync<T>(source: string, params: SQLiteBindParams): T | null;\n  /**\n   * @hidden\n   */\n  public getFirstSync<T>(source: string, ...params: SQLiteVariadicBindParams): T | null;\n  public getFirstSync<T>(source: string, ...params: any[]): T | null {\n    const statement = this.prepareSync(source);\n    let firstRow: T | null;\n    try {\n      const result = statement.executeSync<T>(...params);\n      firstRow = result.getFirstSync();\n    } finally {\n      statement.finalizeSync();\n    }\n    return firstRow;\n  }\n\n  /**\n   * A convenience wrapper around [`SQLiteDatabase.prepareSync()`](#preparesyncsource), [`SQLiteStatement.executeSync()`](#executesyncparams), [`SQLiteExecuteSyncResult`](#sqliteexecutesyncresult) `Iterator`, and [`SQLiteStatement.finalizeSync()`](#finalizesync).\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   * @param source A string containing the SQL query.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   * @returns This function returns an [`IterableIterator`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Iterator). You can use `for...of` to iterate over the rows from the SQLite query result.\n   */\n  public getEachSync<T>(source: string, params: SQLiteBindParams): IterableIterator<T>;\n  /**\n   * @hidden\n   */\n  public getEachSync<T>(source: string, ...params: SQLiteVariadicBindParams): IterableIterator<T>;\n  public *getEachSync<T>(source: string, ...params: any[]): IterableIterator<T> {\n    const statement = this.prepareSync(source);\n    try {\n      const result = statement.executeSync<T>(...params);\n      for (const row of result) {\n        yield row;\n      }\n    } finally {\n      statement.finalizeSync();\n    }\n  }\n\n  /**\n   * A convenience wrapper around [`SQLiteDatabase.prepareSync()`](#preparesyncsource), [`SQLiteStatement.executeSync()`](#executesyncparams), [`SQLiteExecuteSyncResult.getAllSync()`](#getallsync), and [`SQLiteStatement.finalizeSync()`](#finalizesync).\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   * @param source A string containing the SQL query.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   */\n  public getAllSync<T>(source: string, params: SQLiteBindParams): T[];\n  /**\n   * @hidden\n   */\n  public getAllSync<T>(source: string, ...params: SQLiteVariadicBindParams): T[];\n  public getAllSync<T>(source: string, ...params: any[]): T[] {\n    const statement = this.prepareSync(source);\n    let allRows;\n    try {\n      const result = statement.executeSync<T>(...params);\n      allRows = result.getAllSync();\n    } finally {\n      statement.finalizeSync();\n    }\n    return allRows;\n  }\n\n  /**\n   * Synchronize the local database with the remote libSQL server.\n   * This method is only available from libSQL integration.\n   */\n  public syncLibSQL(): Promise<void> {\n    if (typeof this.nativeDatabase.syncLibSQL !== 'function') {\n      throw new Error('syncLibSQL is not supported in the current environment');\n    }\n    return this.nativeDatabase.syncLibSQL();\n  }\n\n  //#endregion\n}\n\n/**\n * The default directory for SQLite databases.\n */\nexport const defaultDatabaseDirectory = ExpoSQLite.defaultDatabaseDirectory;\n\n/**\n * Open a database.\n *\n * @param databaseName The name of the database file to open.\n * @param options Open options.\n * @param directory The directory where the database file is located. The default value is `defaultDatabaseDirectory`. This parameter is not supported on web.\n */\nexport async function openDatabaseAsync(\n  databaseName: string,\n  options?: SQLiteOpenOptions,\n  directory?: string\n): Promise<SQLiteDatabase> {\n  const openOptions = options ?? {};\n  const databasePath = createDatabasePath(databaseName, directory);\n  await ExpoSQLite.ensureDatabasePathExistsAsync(databasePath);\n  const nativeDatabase = new ExpoSQLite.NativeDatabase(\n    databasePath,\n    flattenOpenOptions(openOptions)\n  );\n  await nativeDatabase.initAsync();\n  return new SQLiteDatabase(databasePath, openOptions, nativeDatabase);\n}\n\n/**\n * Open a database.\n *\n * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n *\n * @param databaseName The name of the database file to open.\n * @param options Open options.\n * @param directory The directory where the database file is located. The default value is `defaultDatabaseDirectory`. This parameter is not supported on web.\n */\nexport function openDatabaseSync(\n  databaseName: string,\n  options?: SQLiteOpenOptions,\n  directory?: string\n): SQLiteDatabase {\n  const openOptions = options ?? {};\n  const databasePath = createDatabasePath(databaseName, directory);\n  ExpoSQLite.ensureDatabasePathExistsSync(databasePath);\n  const nativeDatabase = new ExpoSQLite.NativeDatabase(\n    databasePath,\n    flattenOpenOptions(openOptions)\n  );\n  nativeDatabase.initSync();\n  return new SQLiteDatabase(databasePath, openOptions, nativeDatabase);\n}\n\n/**\n * Given a `Uint8Array` data and [deserialize to memory database](https://sqlite.org/c3ref/deserialize.html).\n *\n * @param serializedData The binary array to deserialize from [`SQLiteDatabase.serializeAsync()`](#serializeasyncdatabasename).\n * @param options Open options.\n */\nexport async function deserializeDatabaseAsync(\n  serializedData: Uint8Array,\n  options?: SQLiteOpenOptions\n): Promise<SQLiteDatabase> {\n  const openOptions = options ?? {};\n  const nativeDatabase = new ExpoSQLite.NativeDatabase(\n    ':memory:',\n    flattenOpenOptions(openOptions),\n    serializedData\n  );\n  await nativeDatabase.initAsync();\n  return new SQLiteDatabase(':memory:', openOptions, nativeDatabase);\n}\n\n/**\n * Given a `Uint8Array` data and [deserialize to memory database](https://sqlite.org/c3ref/deserialize.html).\n *\n * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n *\n * @param serializedData The binary array to deserialize from [`SQLiteDatabase.serializeSync()`](#serializesyncdatabasename)\n * @param options Open options.\n */\nexport function deserializeDatabaseSync(\n  serializedData: Uint8Array,\n  options?: SQLiteOpenOptions\n): SQLiteDatabase {\n  const openOptions = options ?? {};\n  const nativeDatabase = new ExpoSQLite.NativeDatabase(\n    ':memory:',\n    flattenOpenOptions(openOptions),\n    serializedData\n  );\n  nativeDatabase.initSync();\n  return new SQLiteDatabase(':memory:', openOptions, nativeDatabase);\n}\n\n/**\n * Delete a database file.\n *\n * @param databaseName The name of the database file to delete.\n * @param directory The directory where the database file is located. The default value is `defaultDatabaseDirectory`.\n */\nexport async function deleteDatabaseAsync(databaseName: string, directory?: string): Promise<void> {\n  const databasePath = createDatabasePath(databaseName, directory);\n  return await ExpoSQLite.deleteDatabaseAsync(databasePath);\n}\n\n/**\n * Delete a database file.\n *\n * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n *\n * @param databaseName The name of the database file to delete.\n * @param directory The directory where the database file is located. The default value is `defaultDatabaseDirectory`.\n */\nexport function deleteDatabaseSync(databaseName: string, directory?: string): void {\n  const databasePath = createDatabasePath(databaseName, directory);\n  return ExpoSQLite.deleteDatabaseSync(databasePath);\n}\n\n/**\n * Backup a database to another database.\n *\n * @see https://www.sqlite.org/c3ref/backup_finish.html\n *\n * @param options - The backup options\n * @param options.sourceDatabase - The source database to backup from\n * @param options.sourceDatabaseName - The name of the source database. The default value is `main`\n * @param options.destDatabase - The destination database to backup to\n * @param options.destDatabaseName - The name of the destination database. The default value is `m\n */\nexport function backupDatabaseAsync({\n  sourceDatabase,\n  sourceDatabaseName,\n  destDatabase,\n  destDatabaseName,\n}: {\n  sourceDatabase: SQLiteDatabase;\n  sourceDatabaseName?: string;\n  destDatabase: SQLiteDatabase;\n  destDatabaseName?: string;\n}): Promise<void> {\n  return ExpoSQLite.backupDatabaseAsync(\n    destDatabase.nativeDatabase,\n    destDatabaseName ?? 'main',\n    sourceDatabase.nativeDatabase,\n    sourceDatabaseName ?? 'main'\n  );\n}\n\n/**\n * Backup a database to another database.\n *\n * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n *\n * @see https://www.sqlite.org/c3ref/backup_finish.html\n *\n * @param options - The backup options\n * @param options.sourceDatabase - The source database to backup from\n * @param options.sourceDatabaseName - The name of the source database. The default value is `main`\n * @param options.destDatabase - The destination database to backup to\n * @param options.destDatabaseName - The name of the destination database. The default value is `m\n */\nexport function backupDatabaseSync({\n  sourceDatabase,\n  sourceDatabaseName,\n  destDatabase,\n  destDatabaseName,\n}: {\n  sourceDatabase: SQLiteDatabase;\n  sourceDatabaseName?: string;\n  destDatabase: SQLiteDatabase;\n  destDatabaseName?: string;\n}): void {\n  return ExpoSQLite.backupDatabaseSync(\n    destDatabase.nativeDatabase,\n    destDatabaseName ?? 'main',\n    sourceDatabase.nativeDatabase,\n    sourceDatabaseName ?? 'main'\n  );\n}\n\n/**\n * The event payload for the listener of [`addDatabaseChangeListener`](#sqliteadddatabasechangelistenerlistener)\n */\nexport type DatabaseChangeEvent = {\n  /** The database name. The value would be `main` by default and other database names if you use `ATTACH DATABASE` statement. */\n  databaseName: string;\n\n  /** The absolute file path to the database. */\n  databaseFilePath: string;\n\n  /** The table name. */\n  tableName: string;\n\n  /** The changed row ID. */\n  rowId: number;\n};\n\n/**\n * Add a listener for database changes.\n * > Note: to enable this feature, you must set [`enableChangeListener` to `true`](#sqliteopenoptions) when opening the database.\n *\n * @param listener A function that receives the `databaseName`, `databaseFilePath`, `tableName` and `rowId` of the modified data.\n * @returns A `Subscription` object that you can call `remove()` on when you would like to unsubscribe the listener.\n */\nexport function addDatabaseChangeListener(\n  listener: (event: DatabaseChangeEvent) => void\n): EventSubscription {\n  return ExpoSQLite.addListener('onDatabaseChange', listener);\n}\n\n/**\n * A new connection specific used for [`withExclusiveTransactionAsync`](#withexclusivetransactionasynctask).\n * @hidden not going to pull all the database methods to the document.\n */\nclass Transaction extends SQLiteDatabase {\n  public static async createAsync(db: SQLiteDatabase): Promise<Transaction> {\n    const options = { ...db.options, useNewConnection: true };\n    const nativeDatabase = new ExpoSQLite.NativeDatabase(\n      db.databasePath,\n      flattenOpenOptions(options)\n    );\n    await nativeDatabase.initAsync();\n    return new Transaction(db.databasePath, options, nativeDatabase);\n  }\n}\n"]}