{"name": "@expo/package-manager", "version": "1.8.6", "description": "A library for installing and finding packages in a project", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "prepare": "expo-module clean && expo-module build", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/package-manager"}, "keywords": ["react-native", "package-manager", "package-json", "node", "yarn", "yarnpkg", "pnpm", "bun"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/package-manager#readme", "files": ["build"], "dependencies": {"@expo/json-file": "^9.1.5", "@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "resolve-workspace-root": "^2.0.0"}, "devDependencies": {"@types/micromatch": "^4.0.2", "@types/npm-package-arg": "^6.1.0", "expo-module-scripts": "^4.1.9"}, "publishConfig": {"access": "public"}, "gitHead": "1c4a89b0c0adebb53ef84b4a6ac25864e4652917"}