{"version": 3, "sources": ["../../../../src/utils/telemetry/index.ts"], "sourcesContent": ["import process from 'node:process';\n\nimport type { Telemetry } from './Telemetry';\nimport { commandEvent } from './events';\nimport type { TelemetryRecord } from './types';\nimport { getUserAsync } from '../../api/user/user';\nimport { env } from '../env';\n\n/** The singleton telemetry manager to use */\nlet telemetry: Telemetry | null = null;\n\nexport function getTelemetry(): Telemetry | null {\n  if (env.EXPO_NO_TELEMETRY || env.EXPO_OFFLINE) return null;\n\n  if (!telemetry) {\n    // Lazy load the telemetry client, only when enabled\n    const { Telemetry } = require('./Telemetry') as typeof import('./Telemetry');\n    telemetry = new Telemetry();\n\n    // Flush any pending events on exit\n    process.once('SIGINT', () => telemetry?.flushOnExit());\n    process.once('SIGTERM', () => telemetry?.flushOnExit());\n    process.once('beforeExit', () => telemetry?.flushOnExit());\n\n    // Initialize the telemetry\n    getUserAsync()\n      .then((actor) => telemetry?.initialize({ userId: actor?.id ?? null }))\n      .catch(() => telemetry?.initialize({ userId: null }));\n  }\n\n  return telemetry;\n}\n\n/**\n * Record a single telemetry event, or multiple in a single batch.\n * The event does not need to be awaited, its:\n *   - Not sent when using `EXPO_NO_TELEMETRY` or `EXPO_OFFLINE`, and returns `null`\n *   - Sent immediately for long running commands, returns the `fetch` promise\n *   - Queued and sent in background, returns `undefined`\n */\nexport function record(records: TelemetryRecord | TelemetryRecord[]) {\n  return getTelemetry()?.record(records);\n}\n\n/**\n * Record a command invocation, and the name of the command.\n * This can be disabled with the $EXPO_NO_TELEMETRY environment variable.\n * We do this to determine how well deprecations are going before remove a command.\n */\nexport function recordCommand(command: string) {\n  if (isLongRunningCommand(command)) {\n    getTelemetry()?.setStrategy('instant');\n  }\n\n  return record(commandEvent(command));\n}\n\n/** Determine if the command is a long-running command, based on the command name */\nfunction isLongRunningCommand(command: string) {\n  return command === 'start' || command.startsWith('run') || command.startsWith('export');\n}\n"], "names": ["getTelemetry", "record", "recordCommand", "telemetry", "env", "EXPO_NO_TELEMETRY", "EXPO_OFFLINE", "Telemetry", "require", "process", "once", "flushOnExit", "getUserAsync", "then", "actor", "initialize", "userId", "id", "catch", "records", "command", "isLongRunningCommand", "setStrategy", "commandEvent", "startsWith"], "mappings": ";;;;;;;;;;;IAWgBA,YAAY;eAAZA;;IA6BAC,MAAM;eAANA;;IASAC,aAAa;eAAbA;;;;gEAjDI;;;;;;wBAGS;sBAEA;qBACT;;;;;;AAEpB,2CAA2C,GAC3C,IAAIC,YAA8B;AAE3B,SAASH;IACd,IAAII,QAAG,CAACC,iBAAiB,IAAID,QAAG,CAACE,YAAY,EAAE,OAAO;IAEtD,IAAI,CAACH,WAAW;QACd,oDAAoD;QACpD,MAAM,EAAEI,SAAS,EAAE,GAAGC,QAAQ;QAC9BL,YAAY,IAAII;QAEhB,mCAAmC;QACnCE,sBAAO,CAACC,IAAI,CAAC,UAAU,IAAMP,6BAAAA,UAAWQ,WAAW;QACnDF,sBAAO,CAACC,IAAI,CAAC,WAAW,IAAMP,6BAAAA,UAAWQ,WAAW;QACpDF,sBAAO,CAACC,IAAI,CAAC,cAAc,IAAMP,6BAAAA,UAAWQ,WAAW;QAEvD,2BAA2B;QAC3BC,IAAAA,kBAAY,IACTC,IAAI,CAAC,CAACC,QAAUX,6BAAAA,UAAWY,UAAU,CAAC;gBAAEC,QAAQF,CAAAA,yBAAAA,MAAOG,EAAE,KAAI;YAAK,IAClEC,KAAK,CAAC,IAAMf,6BAAAA,UAAWY,UAAU,CAAC;gBAAEC,QAAQ;YAAK;IACtD;IAEA,OAAOb;AACT;AASO,SAASF,OAAOkB,OAA4C;QAC1DnB;IAAP,QAAOA,gBAAAA,mCAAAA,cAAgBC,MAAM,CAACkB;AAChC;AAOO,SAASjB,cAAckB,OAAe;IAC3C,IAAIC,qBAAqBD,UAAU;YACjCpB;SAAAA,gBAAAA,mCAAAA,cAAgBsB,WAAW,CAAC;IAC9B;IAEA,OAAOrB,OAAOsB,IAAAA,oBAAY,EAACH;AAC7B;AAEA,kFAAkF,GAClF,SAASC,qBAAqBD,OAAe;IAC3C,OAAOA,YAAY,WAAWA,QAAQI,UAAU,CAAC,UAAUJ,QAAQI,UAAU,CAAC;AAChF"}