{"version": 3, "file": "reactNativeConfig.types.js", "sourceRoot": "", "sources": ["../../src/reactNativeConfig/reactNativeConfig.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { SupportedPlatform } from '../types';\n\n/**\n * Options for 'react-native-config' command.\n */\nexport interface RNConfigCommandOptions {\n  platform: SupportedPlatform;\n  projectRoot: string;\n  searchPaths: string[];\n  transitiveLinkingDependencies: string[];\n}\n\n/**\n * Dependency configuration for Android platform.\n */\nexport interface RNConfigDependencyAndroid {\n  sourceDir: string;\n  packageImportPath: string;\n  packageInstance: string;\n  dependencyConfiguration?: string;\n  buildTypes: string[];\n  libraryName?: string | null;\n  componentDescriptors?: string[] | null;\n  cmakeListsPath?: string | null;\n  cxxModuleCMakeListsModuleName?: string | null;\n  cxxModuleCMakeListsPath?: string | null;\n  cxxModuleHeaderName?: string | null;\n}\n\n/**\n * Dependency configuration for iOS platform.\n */\nexport interface RNConfigDependencyIos {\n  podspecPath: string;\n  version: string;\n  configurations: string[];\n  scriptPhases: any[];\n}\n\n/**\n * Dependency configuration.\n */\nexport interface RNConfigDependency {\n  root: string;\n  name: string;\n  platforms: {\n    android?: RNConfigDependencyAndroid;\n    ios?: RNConfigDependencyIos;\n  };\n}\n\n/**\n * Result of 'react-native-config' command.\n */\nexport interface RNConfigResult {\n  root: string;\n  reactNativePath: string;\n  dependencies: Record<string, RNConfigDependency>;\n  project: {\n    ios?: {\n      sourceDir: string;\n    };\n  };\n}\n\nexport type RNConfigReactNativePlatformsConfigAndroid = any;\nexport type RNConfigReactNativePlatformsConfigIos = any;\n\ninterface RNConfigReactNativePlatformsConfig {\n  root?: string;\n  platforms?: {\n    android?: RNConfigReactNativePlatformsConfigAndroid;\n    ios?: RNConfigReactNativePlatformsConfigIos;\n  };\n}\n\n/**\n * The `react-native.config.js` config from projectRoot.\n */\nexport interface RNConfigReactNativeProjectConfig {\n  dependencies?: Record<string, RNConfigReactNativePlatformsConfig>;\n}\n\n/**\n * The `react-native.config.js` config from library packageRoot.\n */\nexport interface RNConfigReactNativeLibraryConfig {\n  dependency?: RNConfigReactNativePlatformsConfig;\n  platforms?: any;\n}\n\nexport type RNConfigReactNativeConfig =\n  | RNConfigReactNativeProjectConfig\n  | RNConfigReactNativeLibraryConfig;\n\n/**\n * The `project` config represents the app project configuration.\n */\nexport interface RNConfigReactNativeAppProjectConfig {\n  android?: {\n    sourceDir: string;\n    packageName: string;\n  };\n  ios?: {\n    sourceDir: string;\n  };\n}\n"]}