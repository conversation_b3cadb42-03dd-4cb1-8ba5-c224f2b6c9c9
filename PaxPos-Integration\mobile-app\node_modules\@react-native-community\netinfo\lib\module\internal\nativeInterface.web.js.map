{"version": 3, "sources": ["nativeInterface.web.ts"], "names": ["NativeEventEmitter", "RNCNetInfo", "DEVICE_CONNECTIVITY_EVENT", "nativeEventEmitter", "addListener", "event", "emit", "eventEmitter"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,SAAQA,kBAAR,QAAiC,cAAjC;AACA,OAAOC,UAAP,MAAuB,gBAAvB;AACA,SAAQC,yBAAR,QAAwC,gBAAxC;AAEA,MAAMC,kBAAkB,GAAG,IAAIH,kBAAJ,EAA3B,C,CAEA;;AACAC,UAAU,CAACG,WAAX,CACEF,yBADF,EAEGG,KAAD,IAAiB;AACfF,EAAAA,kBAAkB,CAACG,IAAnB,CAAwBJ,yBAAxB,EAAmDG,KAAnD;AACD,CAJH;AAOA,eAAe,EACb,GAAGJ,UADU;AAEbM,EAAAA,YAAY,EAAEJ;AAFD,CAAf", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {NativeEventEmitter} from 'react-native';\nimport RNCNetInfo from './nativeModule';\nimport {DEVICE_CONNECTIVITY_EVENT} from './privateTypes';\n\nconst nativeEventEmitter = new NativeEventEmitter();\n\n// Listen to connectivity events\nRNCNetInfo.addListener(\n  DEVICE_CONNECTIVITY_EVENT,\n  (event): void => {\n    nativeEventEmitter.emit(DEVICE_CONNECTIVITY_EVENT, event);\n  },\n);\n\nexport default {\n  ...RNCNetInfo,\n  eventEmitter: nativeEventEmitter,\n};\n"]}