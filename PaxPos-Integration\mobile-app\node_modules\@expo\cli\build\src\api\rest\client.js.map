{"version": 3, "sources": ["../../../../src/api/rest/client.ts"], "sourcesContent": ["import type { J<PERSON><PERSON>Val<PERSON> } from '@expo/json-file';\nimport path from 'path';\n\nimport { wrapFetchWithCache } from './cache/wrapFetchWithCache';\nimport type { FetchLike } from './client.types';\nimport { wrapFetchWithBaseUrl } from './wrapFetchWithBaseUrl';\nimport { wrapFetchWithOffline } from './wrapFetchWithOffline';\nimport { wrapFetchWithProgress } from './wrapFetchWithProgress';\nimport { wrapFetchWithProxy } from './wrapFetchWithProxy';\nimport { wrapFetchWithUserAgent } from './wrapFetchWithUserAgent';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { fetch } from '../../utils/fetch';\nimport { getExpoApiBaseUrl } from '../endpoint';\nimport { disableNetwork } from '../settings';\nimport { getAccessToken, getExpoHomeDirectory, getSession } from '../user/UserSettings';\n\nexport class ApiV2Error extends Error {\n  readonly name = 'ApiV2Error';\n  readonly code: string;\n  readonly expoApiV2ErrorCode: string;\n  readonly expoApiV2ErrorDetails?: JSONValue;\n  readonly expoApiV2ErrorServerStack?: string;\n  readonly expoApiV2ErrorMetadata?: object;\n  readonly expoApiV2RequestId?: string;\n\n  constructor(response: {\n    message: string;\n    code: string;\n    stack?: string;\n    details?: JSONValue;\n    metadata?: object;\n    requestId: string;\n  }) {\n    super(response.message);\n    this.code = response.code;\n    this.expoApiV2ErrorCode = response.code;\n    this.expoApiV2ErrorDetails = response.details;\n    this.expoApiV2ErrorServerStack = response.stack;\n    this.expoApiV2ErrorMetadata = response.metadata;\n    this.expoApiV2RequestId = response.requestId;\n  }\n\n  toString() {\n    return `${super.toString()}${env.EXPO_DEBUG && this.expoApiV2RequestId ? ` (Request Id: ${this.expoApiV2RequestId})` : ''}`;\n  }\n}\n\n/**\n * An Expo server error that didn't return the expected error JSON information.\n * The only 'expected' place for this is in testing, all other cases are bugs with the server.\n */\nexport class UnexpectedServerError extends Error {\n  readonly name = 'UnexpectedServerError';\n}\n\n/**\n * An error defining that the server didn't return the expected error JSON information.\n * The only 'expected' place for this is in testing, all other cases are bugs with the client.\n */\nexport class UnexpectedServerData extends Error {\n  readonly name = 'UnexpectedServerData';\n}\n\n/** Validate the response json contains `.data` property, or throw an unexpected server data error */\nexport function getResponseDataOrThrow<T = any>(json: unknown): T {\n  if (!!json && typeof json === 'object' && 'data' in json) {\n    return json.data as T;\n  }\n\n  throw new UnexpectedServerData(\n    !!json && typeof json === 'object' ? JSON.stringify(json) : 'Unknown data received from server.'\n  );\n}\n\n/**\n * @returns a `fetch` function that will inject user authentication information and handle errors from the Expo API.\n */\nexport function wrapFetchWithCredentials(fetchFunction: FetchLike): FetchLike {\n  return async function fetchWithCredentials(url, options = {}) {\n    if (Array.isArray(options.headers)) {\n      throw new Error('request headers must be in object form');\n    }\n\n    const resolvedHeaders = options.headers ?? ({} as any);\n\n    const token = getAccessToken();\n    if (token) {\n      resolvedHeaders.authorization = `Bearer ${token}`;\n    } else {\n      const sessionSecret = getSession()?.sessionSecret;\n      if (sessionSecret) {\n        resolvedHeaders['expo-session'] = sessionSecret;\n      }\n    }\n\n    try {\n      const response = await fetchFunction(url, {\n        ...options,\n        headers: resolvedHeaders,\n      });\n\n      // Handle expected API errors (4xx)\n      if (response.status >= 400 && response.status < 500) {\n        const body = await response.text();\n        try {\n          const data = JSON.parse(body);\n          if (data?.errors?.length) {\n            throw new ApiV2Error(data.errors[0]);\n          }\n        } catch (error: any) {\n          // Server returned non-json response.\n          if (error.message.includes('in JSON at position')) {\n            throw new UnexpectedServerError(body);\n          }\n          throw error;\n        }\n      }\n\n      return response;\n    } catch (error: any) {\n      // When running `expo start`, but wifi or internet has issues\n      if (\n        isNetworkError(error) || // node-fetch error handling\n        ('cause' in error && isNetworkError(error.cause)) // undici error handling\n      ) {\n        disableNetwork();\n\n        throw new CommandError(\n          'OFFLINE',\n          'Network connection is unreliable. Try again with the environment variable `EXPO_OFFLINE=1` to skip network requests.'\n        );\n      }\n\n      throw error;\n    }\n  };\n}\n\n/**\n * Determine if the provided error is related to a network issue.\n * When this returns true, offline mode should be enabled.\n *   - `ENOTFOUND` is thrown when the DNS lookup failed\n *   - `EAI_AGAIN` is thrown when DNS lookup failed due to a server-side error\n *   - `UND_ERR_CONNECT_TIMEOUT` is thrown after DNS is resolved, but server can't be reached\n *\n * @see https://nodejs.org/api/errors.html\n * @see https://github.com/nodejs/undici#network-address-family-autoselection\n */\nfunction isNetworkError(error: Error & { code?: string }) {\n  return (\n    'code' in error &&\n    error.code &&\n    ['ENOTFOUND', 'EAI_AGAIN', 'UND_ERR_CONNECT_TIMEOUT'].includes(error.code)\n  );\n}\n\nconst fetchWithOffline = wrapFetchWithOffline(wrapFetchWithUserAgent(fetch));\n\nconst fetchWithBaseUrl = wrapFetchWithBaseUrl(fetchWithOffline, getExpoApiBaseUrl() + '/v2/');\n\nconst fetchWithProxy = wrapFetchWithProxy(fetchWithBaseUrl);\n\nconst fetchWithCredentials = wrapFetchWithProgress(wrapFetchWithCredentials(fetchWithProxy));\n\n/**\n * Create an instance of the fully qualified fetch command (auto authentication and api) but with caching in the '~/.expo' directory.\n * Caching is disabled automatically if the EXPO_NO_CACHE or EXPO_BETA environment variables are enabled.\n */\nexport function createCachedFetch({\n  fetch = fetchWithCredentials,\n  cacheDirectory,\n  ttl,\n  skipCache,\n}: {\n  fetch?: FetchLike;\n  cacheDirectory: string;\n  ttl?: number;\n  skipCache?: boolean;\n}): FetchLike {\n  // Disable all caching in EXPO_BETA.\n  if (skipCache || env.EXPO_BETA || env.EXPO_NO_CACHE) {\n    return fetch;\n  }\n\n  const { FileSystemResponseCache } =\n    require('./cache/FileSystemResponseCache') as typeof import('./cache/FileSystemResponseCache');\n\n  return wrapFetchWithCache(\n    fetch,\n    new FileSystemResponseCache({\n      cacheDirectory: path.join(getExpoHomeDirectory(), cacheDirectory),\n      ttl,\n    })\n  );\n}\n\n/** Instance of fetch with automatic base URL pointing to the Expo API, user credential injection, and API error handling. Caching not included.  */\nexport const fetchAsync = wrapFetchWithProgress(wrapFetchWithCredentials(fetchWithProxy));\n"], "names": ["ApiV2Error", "UnexpectedServerData", "UnexpectedServerError", "createCachedFetch", "fetchAsync", "getResponseDataOrThrow", "wrapFetchWithCredentials", "Error", "constructor", "response", "message", "name", "code", "expoApiV2ErrorCode", "expoApiV2ErrorDetails", "details", "expoApiV2ErrorServerStack", "stack", "expoApiV2ErrorMetadata", "metadata", "expoApiV2RequestId", "requestId", "toString", "env", "EXPO_DEBUG", "json", "data", "JSON", "stringify", "fetchFunction", "fetchWithCredentials", "url", "options", "Array", "isArray", "headers", "resolvedHeaders", "token", "getAccessToken", "authorization", "getSession", "sessionSecret", "status", "body", "text", "parse", "errors", "length", "error", "includes", "isNetworkError", "cause", "disableNetwork", "CommandError", "fetchWithOffline", "wrapFetchWithOffline", "wrapFetchWithUserAgent", "fetch", "fetchWithBaseUrl", "wrapFetchWithBaseUrl", "getExpoApiBaseUrl", "fetchWithProxy", "wrapFetchWithProxy", "wrapFetchWithProgress", "cacheDirectory", "ttl", "<PERSON><PERSON><PERSON>", "EXPO_BETA", "EXPO_NO_CACHE", "FileSystemResponseCache", "require", "wrapFetchWithCache", "path", "join", "getExpoHomeDirectory"], "mappings": ";;;;;;;;;;;IAiBaA,UAAU;eAAVA;;IA2CAC,oBAAoB;eAApBA;;IARAC,qBAAqB;eAArBA;;IAqHGC,iBAAiB;eAAjBA;;IA6BHC,UAAU;eAAVA;;IArIGC,sBAAsB;eAAtBA;;IAaAC,wBAAwB;eAAxBA;;;;gEA7EC;;;;;;oCAEkB;sCAEE;sCACA;uCACC;oCACH;wCACI;qBACnB;wBACS;uBACP;0BACY;0BACH;8BACkC;;;;;;AAE1D,MAAMN,mBAAmBO;IAS9BC,YAAYC,QAOX,CAAE;QACD,KAAK,CAACA,SAASC,OAAO,QAhBfC,OAAO;QAiBd,IAAI,CAACC,IAAI,GAAGH,SAASG,IAAI;QACzB,IAAI,CAACC,kBAAkB,GAAGJ,SAASG,IAAI;QACvC,IAAI,CAACE,qBAAqB,GAAGL,SAASM,OAAO;QAC7C,IAAI,CAACC,yBAAyB,GAAGP,SAASQ,KAAK;QAC/C,IAAI,CAACC,sBAAsB,GAAGT,SAASU,QAAQ;QAC/C,IAAI,CAACC,kBAAkB,GAAGX,SAASY,SAAS;IAC9C;IAEAC,WAAW;QACT,OAAO,GAAG,KAAK,CAACA,aAAaC,QAAG,CAACC,UAAU,IAAI,IAAI,CAACJ,kBAAkB,GAAG,CAAC,cAAc,EAAE,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI;IAC7H;AACF;AAMO,MAAMlB,8BAA8BK;;QAApC,qBACII,OAAO;;AAClB;AAMO,MAAMV,6BAA6BM;;QAAnC,qBACII,OAAO;;AAClB;AAGO,SAASN,uBAAgCoB,IAAa;IAC3D,IAAI,CAAC,CAACA,QAAQ,OAAOA,SAAS,YAAY,UAAUA,MAAM;QACxD,OAAOA,KAAKC,IAAI;IAClB;IAEA,MAAM,IAAIzB,qBACR,CAAC,CAACwB,QAAQ,OAAOA,SAAS,WAAWE,KAAKC,SAAS,CAACH,QAAQ;AAEhE;AAKO,SAASnB,yBAAyBuB,aAAwB;IAC/D,OAAO,eAAeC,qBAAqBC,GAAG,EAAEC,UAAU,CAAC,CAAC;QAC1D,IAAIC,MAAMC,OAAO,CAACF,QAAQG,OAAO,GAAG;YAClC,MAAM,IAAI5B,MAAM;QAClB;QAEA,MAAM6B,kBAAkBJ,QAAQG,OAAO,IAAK,CAAC;QAE7C,MAAME,QAAQC,IAAAA,4BAAc;QAC5B,IAAID,OAAO;YACTD,gBAAgBG,aAAa,GAAG,CAAC,OAAO,EAAEF,OAAO;QACnD,OAAO;gBACiBG;YAAtB,MAAMC,iBAAgBD,cAAAA,IAAAA,wBAAU,wBAAVA,YAAcC,aAAa;YACjD,IAAIA,eAAe;gBACjBL,eAAe,CAAC,eAAe,GAAGK;YACpC;QACF;QAEA,IAAI;YACF,MAAMhC,WAAW,MAAMoB,cAAcE,KAAK;gBACxC,GAAGC,OAAO;gBACVG,SAASC;YACX;YAEA,mCAAmC;YACnC,IAAI3B,SAASiC,MAAM,IAAI,OAAOjC,SAASiC,MAAM,GAAG,KAAK;gBACnD,MAAMC,OAAO,MAAMlC,SAASmC,IAAI;gBAChC,IAAI;wBAEElB;oBADJ,MAAMA,OAAOC,KAAKkB,KAAK,CAACF;oBACxB,IAAIjB,yBAAAA,eAAAA,KAAMoB,MAAM,qBAAZpB,aAAcqB,MAAM,EAAE;wBACxB,MAAM,IAAI/C,WAAW0B,KAAKoB,MAAM,CAAC,EAAE;oBACrC;gBACF,EAAE,OAAOE,OAAY;oBACnB,qCAAqC;oBACrC,IAAIA,MAAMtC,OAAO,CAACuC,QAAQ,CAAC,wBAAwB;wBACjD,MAAM,IAAI/C,sBAAsByC;oBAClC;oBACA,MAAMK;gBACR;YACF;YAEA,OAAOvC;QACT,EAAE,OAAOuC,OAAY;YACnB,6DAA6D;YAC7D,IACEE,eAAeF,UAAU,4BAA4B;YACpD,WAAWA,SAASE,eAAeF,MAAMG,KAAK,EAAG,wBAAwB;cAC1E;gBACAC,IAAAA,wBAAc;gBAEd,MAAM,IAAIC,oBAAY,CACpB,WACA;YAEJ;YAEA,MAAML;QACR;IACF;AACF;AAEA;;;;;;;;;CASC,GACD,SAASE,eAAeF,KAAgC;IACtD,OACE,UAAUA,SACVA,MAAMpC,IAAI,IACV;QAAC;QAAa;QAAa;KAA0B,CAACqC,QAAQ,CAACD,MAAMpC,IAAI;AAE7E;AAEA,MAAM0C,mBAAmBC,IAAAA,0CAAoB,EAACC,IAAAA,8CAAsB,EAACC,YAAK;AAE1E,MAAMC,mBAAmBC,IAAAA,0CAAoB,EAACL,kBAAkBM,IAAAA,2BAAiB,MAAK;AAEtF,MAAMC,iBAAiBC,IAAAA,sCAAkB,EAACJ;AAE1C,MAAM5B,uBAAuBiC,IAAAA,4CAAqB,EAACzD,yBAAyBuD;AAMrE,SAAS1D,kBAAkB,EAChCsD,QAAQ3B,oBAAoB,EAC5BkC,cAAc,EACdC,GAAG,EACHC,SAAS,EAMV;IACC,oCAAoC;IACpC,IAAIA,aAAa3C,QAAG,CAAC4C,SAAS,IAAI5C,QAAG,CAAC6C,aAAa,EAAE;QACnD,OAAOX;IACT;IAEA,MAAM,EAAEY,uBAAuB,EAAE,GAC/BC,QAAQ;IAEV,OAAOC,IAAAA,sCAAkB,EACvBd,OACA,IAAIY,wBAAwB;QAC1BL,gBAAgBQ,eAAI,CAACC,IAAI,CAACC,IAAAA,kCAAoB,KAAIV;QAClDC;IACF;AAEJ;AAGO,MAAM7D,aAAa2D,IAAAA,4CAAqB,EAACzD,yBAAyBuD"}