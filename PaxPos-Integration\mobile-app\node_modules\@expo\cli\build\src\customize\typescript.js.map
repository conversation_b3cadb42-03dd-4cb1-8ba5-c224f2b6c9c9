{"version": 3, "sources": ["../../../src/customize/typescript.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\n\nimport { Log } from '../log';\n\nexport async function typescript(projectRoot: string) {\n  const { TypeScriptProjectPrerequisite } = await import(\n    '../start/doctor/typescript/TypeScriptProjectPrerequisite.js'\n  );\n  const { MetroBundlerDevServer } = await import('../start/server/metro/MetroBundlerDevServer.js');\n  const { getPlatformBundlers } = await import('../start/server/platformBundlers.js');\n\n  try {\n    await new TypeScriptProjectPrerequisite(projectRoot).bootstrapAsync();\n  } catch (error: any) {\n    // Ensure the process doesn't fail if the TypeScript check fails.\n    // This could happen during the install.\n    Log.log();\n    Log.exception(error);\n    return;\n  }\n\n  const { exp } = getConfig(projectRoot, { skipSDKVersionRequirement: true });\n  await new MetroBundlerDevServer(projectRoot, getPlatformBundlers(projectRoot, exp), {\n    isDevClient: true,\n  }).startTypeScriptServices();\n}\n"], "names": ["typescript", "projectRoot", "TypeScriptProjectPrerequisite", "MetroBundlerDevServer", "getPlatformBundlers", "bootstrapAsync", "error", "Log", "log", "exception", "exp", "getConfig", "skipSDKVersionRequirement", "isDevClient", "startTypeScriptServices"], "mappings": ";;;;+BAIsBA;;;eAAAA;;;;yBAJI;;;;;;qBAEN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,eAAeA,WAAWC,WAAmB;IAClD,MAAM,EAAEC,6BAA6B,EAAE,GAAG,MAAM,mEAAA,QAC9C;IAEF,MAAM,EAAEC,qBAAqB,EAAE,GAAG,MAAM,mEAAA,QAAO;IAC/C,MAAM,EAAEC,mBAAmB,EAAE,GAAG,MAAM,mEAAA,QAAO;IAE7C,IAAI;QACF,MAAM,IAAIF,8BAA8BD,aAAaI,cAAc;IACrE,EAAE,OAAOC,OAAY;QACnB,iEAAiE;QACjE,wCAAwC;QACxCC,QAAG,CAACC,GAAG;QACPD,QAAG,CAACE,SAAS,CAACH;QACd;IACF;IAEA,MAAM,EAAEI,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAACV,aAAa;QAAEW,2BAA2B;IAAK;IACzE,MAAM,IAAIT,sBAAsBF,aAAaG,oBAAoBH,aAAaS,MAAM;QAClFG,aAAa;IACf,GAAGC,uBAAuB;AAC5B"}