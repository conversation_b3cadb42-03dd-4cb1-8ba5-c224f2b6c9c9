import { MongoClient, Db, Collection, ObjectId } from 'mongodb';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { LoggingService } from './LoggingService';

// MongoDB Models
export interface PaxTransaction {
  _id?: ObjectId;
  id: string;
  amount: number;
  transType: 'SALE' | 'REFUND' | 'VOID' | 'AUTH';
  tenderType: 'CREDIT' | 'DEBIT' | 'CASH' | 'CHECK';
  status: 'success' | 'failed' | 'pending' | 'voided';
  authCode?: string;
  referenceNumber?: string;
  resultCode?: string;
  receiptData?: string;
  customerData?: any;
  terminalId: string;
  merchantId: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
  syncedAt?: Date;
}

export interface InventoryItem {
  _id?: ObjectId;
  id: string;
  sku: string;
  name: string;
  description?: string;
  price: number;
  cost: number;
  quantity: number;
  category: string;
  barcode?: string;
  imageUrl?: string;
  isActive: boolean;
  storeId: string;
  createdAt: Date;
  updatedAt: Date;
  syncedAt?: Date;
}

export interface User {
  _id?: ObjectId;
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'manager' | 'cashier';
  storeId: string;
  permissions: string[];
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Store {
  _id?: ObjectId;
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  taxRate: number;
  currency: string;
  timezone: string;
  settings: any;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SystemLog {
  _id?: ObjectId;
  id: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  category: string;
  storeId: string;
  terminalId: string;
  metadata?: any;
  timestamp: Date;
}

export class MongoDBService {
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private isConnected: boolean = false;
  private logger: LoggingService;
  private connectionString: string;
  private storeId: string = '';
  private terminalId: string = '';

  // Collections
  private transactions: Collection<PaxTransaction> | null = null;
  private inventory: Collection<InventoryItem> | null = null;
  private users: Collection<User> | null = null;
  private stores: Collection<Store> | null = null;
  private logs: Collection<SystemLog> | null = null;

  constructor() {
    this.logger = new LoggingService();
    this.connectionString = "mongodb+srv://inspiredolajosh:<EMAIL>/PaxPOSHq?retryWrites=true&w=majority";
  }

  async initialize(): Promise<void> {
    try {
      // Get store and terminal IDs from storage
      this.storeId = await AsyncStorage.getItem('storeId') || 'store_default';
      this.terminalId = await AsyncStorage.getItem('terminalId') || `terminal_${Date.now()}`;

      // Save terminal ID if it's new
      await AsyncStorage.setItem('terminalId', this.terminalId);

      // Connect to MongoDB
      await this.connect();
      
      // Initialize collections
      this.initializeCollections();
      
      // Create indexes for performance
      await this.createIndexes();
      
      // Set up offline sync
      this.setupOfflineSync();
      
      this.logger.info('MongoDB service initialized successfully', {
        storeId: this.storeId,
        terminalId: this.terminalId
      });

    } catch (error) {
      this.logger.error('MongoDB initialization failed', error);
      throw error;
    }
  }

  private async connect(): Promise<void> {
    try {
      this.client = new MongoClient(this.connectionString, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      });

      await this.client.connect();
      this.db = this.client.db('PaxPOSHq');
      this.isConnected = true;

      this.logger.info('Connected to MongoDB successfully');
    } catch (error) {
      this.logger.error('MongoDB connection failed', error);
      this.isConnected = false;
      throw error;
    }
  }

  private initializeCollections(): void {
    if (!this.db) throw new Error('Database not connected');

    this.transactions = this.db.collection<PaxTransaction>('transactions');
    this.inventory = this.db.collection<InventoryItem>('inventory');
    this.users = this.db.collection<User>('users');
    this.stores = this.db.collection<Store>('stores');
    this.logs = this.db.collection<SystemLog>('logs');
  }

  private async createIndexes(): Promise<void> {
    try {
      if (!this.transactions || !this.inventory || !this.users || !this.stores || !this.logs) {
        throw new Error('Collections not initialized');
      }

      // Transaction indexes
      await this.transactions.createIndex({ id: 1 }, { unique: true });
      await this.transactions.createIndex({ storeId: 1, createdAt: -1 });
      await this.transactions.createIndex({ status: 1 });
      await this.transactions.createIndex({ terminalId: 1 });

      // Inventory indexes
      await this.inventory.createIndex({ id: 1 }, { unique: true });
      await this.inventory.createIndex({ sku: 1, storeId: 1 }, { unique: true });
      await this.inventory.createIndex({ storeId: 1, category: 1 });
      await this.inventory.createIndex({ barcode: 1 });

      // User indexes
      await this.users.createIndex({ id: 1 }, { unique: true });
      await this.users.createIndex({ username: 1 }, { unique: true });
      await this.users.createIndex({ email: 1 }, { unique: true });
      await this.users.createIndex({ storeId: 1 });

      // Store indexes
      await this.stores.createIndex({ id: 1 }, { unique: true });

      // Log indexes
      await this.logs.createIndex({ storeId: 1, timestamp: -1 });
      await this.logs.createIndex({ level: 1 });
      await this.logs.createIndex({ category: 1 });

      this.logger.info('Database indexes created successfully');
    } catch (error) {
      this.logger.error('Failed to create database indexes', error);
    }
  }

  private setupOfflineSync(): void {
    // Monitor network connectivity
    NetInfo.addEventListener(state => {
      if (state.isConnected && !this.isConnected) {
        this.reconnect();
      }
    });

    // Set up periodic sync
    setInterval(() => {
      this.syncOfflineData();
    }, 30000); // Sync every 30 seconds
  }

  private async reconnect(): Promise<void> {
    try {
      await this.connect();
      await this.syncOfflineData();
    } catch (error) {
      this.logger.error('Reconnection failed', error);
    }
  }

  private async syncOfflineData(): Promise<void> {
    if (!this.isConnected) return;

    try {
      // Sync pending transactions from local storage
      const pendingTransactions = await AsyncStorage.getItem('pendingTransactions');
      if (pendingTransactions) {
        const transactions = JSON.parse(pendingTransactions);
        for (const transaction of transactions) {
          await this.createTransaction(transaction);
        }
        await AsyncStorage.removeItem('pendingTransactions');
      }

      this.logger.info('Offline data synced successfully');
    } catch (error) {
      this.logger.error('Offline sync failed', error);
    }
  }

  // Transaction Operations
  async createTransaction(transaction: Omit<PaxTransaction, '_id' | 'createdAt' | 'updatedAt' | 'syncedAt'>): Promise<PaxTransaction> {
    try {
      const now = new Date();
      const newTransaction: PaxTransaction = {
        ...transaction,
        terminalId: this.terminalId,
        merchantId: this.storeId,
        createdAt: now,
        updatedAt: now,
        syncedAt: now
      };

      if (this.isConnected && this.transactions) {
        const result = await this.transactions.insertOne(newTransaction);
        newTransaction._id = result.insertedId;
        this.logger.info('Transaction created in MongoDB', { id: transaction.id });
      } else {
        // Store offline for later sync
        await this.storeOfflineTransaction(newTransaction);
        this.logger.info('Transaction stored offline', { id: transaction.id });
      }

      return newTransaction;
    } catch (error) {
      this.logger.error('Failed to create transaction', error);
      // Store offline as fallback
      await this.storeOfflineTransaction(transaction as PaxTransaction);
      throw error;
    }
  }

  private async storeOfflineTransaction(transaction: PaxTransaction): Promise<void> {
    try {
      const pendingTransactions = await AsyncStorage.getItem('pendingTransactions');
      const transactions = pendingTransactions ? JSON.parse(pendingTransactions) : [];
      transactions.push(transaction);
      await AsyncStorage.setItem('pendingTransactions', JSON.stringify(transactions));
    } catch (error) {
      this.logger.error('Failed to store offline transaction', error);
    }
  }

  async getTransactions(limit: number = 50, skip: number = 0): Promise<PaxTransaction[]> {
    try {
      if (!this.isConnected || !this.transactions) {
        return await this.getOfflineTransactions(limit, skip);
      }

      const transactions = await this.transactions
        .find({ terminalId: this.terminalId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
        .toArray();

      return transactions;
    } catch (error) {
      this.logger.error('Failed to get transactions', error);
      return await this.getOfflineTransactions(limit, skip);
    }
  }

  private async getOfflineTransactions(limit: number, skip: number): Promise<PaxTransaction[]> {
    try {
      const offlineTransactions = await AsyncStorage.getItem('offlineTransactions');
      if (!offlineTransactions) return [];

      const transactions = JSON.parse(offlineTransactions);
      return transactions.slice(skip, skip + limit);
    } catch (error) {
      this.logger.error('Failed to get offline transactions', error);
      return [];
    }
  }

  async getTransaction(id: string): Promise<PaxTransaction | null> {
    try {
      if (!this.isConnected || !this.transactions) {
        return null;
      }

      const transaction = await this.transactions.findOne({ id });
      return transaction;
    } catch (error) {
      this.logger.error('Failed to get transaction', error);
      return null;
    }
  }

  async updateTransaction(id: string, updates: Partial<PaxTransaction>): Promise<boolean> {
    try {
      if (!this.isConnected || !this.transactions) {
        return false;
      }

      const result = await this.transactions.updateOne(
        { id },
        { 
          $set: { 
            ...updates, 
            updatedAt: new Date(),
            syncedAt: new Date()
          } 
        }
      );

      return result.modifiedCount > 0;
    } catch (error) {
      this.logger.error('Failed to update transaction', error);
      return false;
    }
  }

  // Inventory Operations
  async createInventoryItem(item: Omit<InventoryItem, '_id' | 'createdAt' | 'updatedAt' | 'syncedAt'>): Promise<InventoryItem> {
    try {
      const now = new Date();
      const newItem: InventoryItem = {
        ...item,
        storeId: this.storeId,
        createdAt: now,
        updatedAt: now,
        syncedAt: now
      };

      if (this.isConnected && this.inventory) {
        const result = await this.inventory.insertOne(newItem);
        newItem._id = result.insertedId;
      }

      return newItem;
    } catch (error) {
      this.logger.error('Failed to create inventory item', error);
      throw error;
    }
  }

  async getInventory(category?: string): Promise<InventoryItem[]> {
    try {
      if (!this.isConnected || !this.inventory) {
        return [];
      }

      const filter: any = { storeId: this.storeId, isActive: true };
      if (category) {
        filter.category = category;
      }

      const items = await this.inventory
        .find(filter)
        .sort({ name: 1 })
        .toArray();

      return items;
    } catch (error) {
      this.logger.error('Failed to get inventory', error);
      return [];
    }
  }

  async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<boolean> {
    try {
      if (!this.isConnected || !this.inventory) {
        return false;
      }

      const result = await this.inventory.updateOne(
        { id, storeId: this.storeId },
        { 
          $set: { 
            ...updates, 
            updatedAt: new Date(),
            syncedAt: new Date()
          } 
        }
      );

      return result.modifiedCount > 0;
    } catch (error) {
      this.logger.error('Failed to update inventory item', error);
      return false;
    }
  }

  // User Operations
  async createUser(user: Omit<User, '_id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    try {
      const now = new Date();
      const newUser: User = {
        ...user,
        storeId: this.storeId,
        createdAt: now,
        updatedAt: now
      };

      if (this.isConnected && this.users) {
        const result = await this.users.insertOne(newUser);
        newUser._id = result.insertedId;
      }

      return newUser;
    } catch (error) {
      this.logger.error('Failed to create user', error);
      throw error;
    }
  }

  async getUserByUsername(username: string): Promise<User | null> {
    try {
      if (!this.isConnected || !this.users) {
        return null;
      }

      const user = await this.users.findOne({ 
        username, 
        storeId: this.storeId, 
        isActive: true 
      });

      return user;
    } catch (error) {
      this.logger.error('Failed to get user', error);
      return null;
    }
  }

  // Store Operations
  async getStore(): Promise<Store | null> {
    try {
      if (!this.isConnected || !this.stores) {
        return null;
      }

      const store = await this.stores.findOne({ id: this.storeId });
      return store;
    } catch (error) {
      this.logger.error('Failed to get store', error);
      return null;
    }
  }

  async updateStore(updates: Partial<Store>): Promise<boolean> {
    try {
      if (!this.isConnected || !this.stores) {
        return false;
      }

      const result = await this.stores.updateOne(
        { id: this.storeId },
        { 
          $set: { 
            ...updates, 
            updatedAt: new Date()
          } 
        }
      );

      return result.modifiedCount > 0;
    } catch (error) {
      this.logger.error('Failed to update store', error);
      return false;
    }
  }

  // Logging Operations
  async addLog(log: Omit<SystemLog, '_id' | 'id' | 'timestamp' | 'storeId' | 'terminalId'>): Promise<void> {
    try {
      const newLog: SystemLog = {
        ...log,
        id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        storeId: this.storeId,
        terminalId: this.terminalId,
        timestamp: new Date()
      };

      if (this.isConnected && this.logs) {
        await this.logs.insertOne(newLog);
      }
    } catch (error) {
      console.error('Failed to add log to MongoDB:', error);
    }
  }

  async getLogs(level?: string, limit: number = 100): Promise<SystemLog[]> {
    try {
      if (!this.isConnected || !this.logs) {
        return [];
      }

      const filter: any = { storeId: this.storeId };
      if (level) {
        filter.level = level;
      }

      const logs = await this.logs
        .find(filter)
        .sort({ timestamp: -1 })
        .limit(limit)
        .toArray();

      return logs;
    } catch (error) {
      this.logger.error('Failed to get logs', error);
      return [];
    }
  }

  // Analytics and Reporting
  async getSalesReport(startDate: Date, endDate: Date): Promise<any> {
    try {
      if (!this.isConnected || !this.transactions) {
        return null;
      }

      const pipeline = [
        {
          $match: {
            storeId: this.storeId,
            status: 'success',
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: null,
            totalSales: { $sum: '$amount' },
            totalTransactions: { $sum: 1 },
            averageTransaction: { $avg: '$amount' },
            salesByType: {
              $push: {
                type: '$transType',
                amount: '$amount'
              }
            }
          }
        }
      ];

      const result = await this.transactions.aggregate(pipeline).toArray();
      return result[0] || null;
    } catch (error) {
      this.logger.error('Failed to get sales report', error);
      return null;
    }
  }

  // Health Check
  async healthCheck(): Promise<{ connected: boolean; latency?: number }> {
    try {
      const start = Date.now();
      
      if (this.isConnected && this.db) {
        await this.db.admin().ping();
        const latency = Date.now() - start;
        return { connected: true, latency };
      }
      
      return { connected: false };
    } catch (error) {
      this.logger.error('Health check failed', error);
      return { connected: false };
    }
  }

  // Cleanup
  async close(): Promise<void> {
    try {
      if (this.client) {
        await this.client.close();
        this.isConnected = false;
        this.logger.info('MongoDB connection closed');
      }
    } catch (error) {
      this.logger.error('Failed to close MongoDB connection', error);
    }
  }

  // Getters
  get connected(): boolean {
    return this.isConnected;
  }

  get storeIdentifier(): string {
    return this.storeId;
  }

  get terminalIdentifier(): string {
    return this.terminalId;
  }
}
