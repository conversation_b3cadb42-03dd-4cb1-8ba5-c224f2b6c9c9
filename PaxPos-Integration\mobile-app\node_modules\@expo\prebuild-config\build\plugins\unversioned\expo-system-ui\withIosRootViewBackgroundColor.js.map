{"version": 3, "file": "withIosRootViewBackgroundColor.js", "names": ["_configPlugins", "data", "require", "_normalizeColors", "_interopRequireDefault", "_semver", "e", "__esModule", "default", "BACKGROUND_COLOR_KEY", "debug", "withIosRootViewBackgroundColor", "config", "withInfoPlist", "shouldUseLegacyBehavior", "modResults", "setRootViewBackgroundColor", "warnSystemUIMissing", "exports", "sdkVersion", "semver", "lt", "backgroundColor", "getRootViewBackgroundColor", "WarningAggregator", "addWarningIOS", "infoPlist", "color", "normalizeColor", "Error", "ios"], "sources": ["../../../../src/plugins/unversioned/expo-system-ui/withIosRootViewBackgroundColor.ts"], "sourcesContent": ["import { ConfigPlugin, InfoPlist, WarningAggregator, withInfoPlist } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n// @ts-ignore: uses flow\nimport normalizeColor from '@react-native/normalize-colors';\nimport semver from 'semver';\n\n// Maps to the template AppDelegate.m\nconst BACKGROUND_COLOR_KEY = 'RCTRootViewBackgroundColor';\n\nconst debug = require('debug')('expo:system-ui:plugin:ios');\n\nexport const withIosRootViewBackgroundColor: ConfigPlugin = (config) => {\n  config = withInfoPlist(config, (config) => {\n    if (shouldUseLegacyBehavior(config)) {\n      config.modResults = setRootViewBackgroundColor(config, config.modResults);\n    } else {\n      warnSystemUIMissing(config);\n    }\n    return config;\n  });\n  return config;\n};\n\n/** The template was changed in SDK 43 to move the background color logic to the `expo-system-ui` module */\nexport function shouldUseLegacyBehavior(config: Pick<ExpoConfig, 'sdkVersion'>): boolean {\n  try {\n    return !!(config.sdkVersion && semver.lt(config.sdkVersion, '44.0.0'));\n  } catch {}\n  return false;\n}\n\nexport function warnSystemUIMissing(\n  config: Pick<ExpoConfig, 'sdkVersion' | 'backgroundColor' | 'ios'>\n) {\n  const backgroundColor = getRootViewBackgroundColor(config);\n\n  if (backgroundColor) {\n    // Background color needs to be set programmatically\n    WarningAggregator.addWarningIOS(\n      'ios.backgroundColor',\n      'Install expo-system-ui to enable this feature',\n      'https://docs.expo.dev/build-reference/migrating/#expo-config--backgroundcolor--depends-on'\n    );\n  }\n}\n\nexport function setRootViewBackgroundColor(\n  config: Pick<ExpoConfig, 'backgroundColor' | 'ios'>,\n  infoPlist: InfoPlist\n): InfoPlist {\n  const backgroundColor = getRootViewBackgroundColor(config);\n  if (!backgroundColor) {\n    delete infoPlist[BACKGROUND_COLOR_KEY];\n  } else {\n    let color = normalizeColor(backgroundColor);\n    if (!color) {\n      throw new Error('Invalid background color on iOS');\n    }\n    color = ((color << 24) | (color >>> 8)) >>> 0;\n    infoPlist[BACKGROUND_COLOR_KEY] = color;\n\n    debug(`Convert color: ${backgroundColor} -> ${color}`);\n  }\n  return infoPlist;\n}\n\nexport function getRootViewBackgroundColor(config: Pick<ExpoConfig, 'ios' | 'backgroundColor'>) {\n  return config.ios?.backgroundColor || config.backgroundColor || null;\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAE,iBAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,gBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAG,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAF5B;;AAIA;AACA,MAAMG,oBAAoB,GAAG,4BAA4B;AAEzD,MAAMC,KAAK,GAAGR,OAAO,CAAC,OAAO,CAAC,CAAC,2BAA2B,CAAC;AAEpD,MAAMS,8BAA4C,GAAIC,MAAM,IAAK;EACtEA,MAAM,GAAG,IAAAC,8BAAa,EAACD,MAAM,EAAGA,MAAM,IAAK;IACzC,IAAIE,uBAAuB,CAACF,MAAM,CAAC,EAAE;MACnCA,MAAM,CAACG,UAAU,GAAGC,0BAA0B,CAACJ,MAAM,EAAEA,MAAM,CAACG,UAAU,CAAC;IAC3E,CAAC,MAAM;MACLE,mBAAmB,CAACL,MAAM,CAAC;IAC7B;IACA,OAAOA,MAAM;EACf,CAAC,CAAC;EACF,OAAOA,MAAM;AACf,CAAC;;AAED;AAAAM,OAAA,CAAAP,8BAAA,GAAAA,8BAAA;AACO,SAASG,uBAAuBA,CAACF,MAAsC,EAAW;EACvF,IAAI;IACF,OAAO,CAAC,EAAEA,MAAM,CAACO,UAAU,IAAIC,iBAAM,CAACC,EAAE,CAACT,MAAM,CAACO,UAAU,EAAE,QAAQ,CAAC,CAAC;EACxE,CAAC,CAAC,MAAM,CAAC;EACT,OAAO,KAAK;AACd;AAEO,SAASF,mBAAmBA,CACjCL,MAAkE,EAClE;EACA,MAAMU,eAAe,GAAGC,0BAA0B,CAACX,MAAM,CAAC;EAE1D,IAAIU,eAAe,EAAE;IACnB;IACAE,kCAAiB,CAACC,aAAa,CAC7B,qBAAqB,EACrB,+CAA+C,EAC/C,2FACF,CAAC;EACH;AACF;AAEO,SAAST,0BAA0BA,CACxCJ,MAAmD,EACnDc,SAAoB,EACT;EACX,MAAMJ,eAAe,GAAGC,0BAA0B,CAACX,MAAM,CAAC;EAC1D,IAAI,CAACU,eAAe,EAAE;IACpB,OAAOI,SAAS,CAACjB,oBAAoB,CAAC;EACxC,CAAC,MAAM;IACL,IAAIkB,KAAK,GAAG,IAAAC,0BAAc,EAACN,eAAe,CAAC;IAC3C,IAAI,CAACK,KAAK,EAAE;MACV,MAAM,IAAIE,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACAF,KAAK,GAAG,CAAEA,KAAK,IAAI,EAAE,GAAKA,KAAK,KAAK,CAAE,MAAM,CAAC;IAC7CD,SAAS,CAACjB,oBAAoB,CAAC,GAAGkB,KAAK;IAEvCjB,KAAK,CAAC,kBAAkBY,eAAe,OAAOK,KAAK,EAAE,CAAC;EACxD;EACA,OAAOD,SAAS;AAClB;AAEO,SAASH,0BAA0BA,CAACX,MAAmD,EAAE;EAC9F,OAAOA,MAAM,CAACkB,GAAG,EAAER,eAAe,IAAIV,MAAM,CAACU,eAAe,IAAI,IAAI;AACtE", "ignoreList": []}