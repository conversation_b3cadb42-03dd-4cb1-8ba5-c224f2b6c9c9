import*as e from"../../../ui/legacy/theme_support/theme_support.js";var r=Object.freeze({__proto__:null,extensionEntryColor:function(r){let a="--ref-palette-primary70";switch(r.args.color){case"primary":a="--ref-palette-primary70";break;case"primary-light":a="--ref-palette-primary80";break;case"primary-dark":a="--ref-palette-primary60";break;case"secondary":a="--ref-palette-secondary70";break;case"secondary-light":a="--ref-palette-secondary80";break;case"secondary-dark":a="--ref-palette-secondary60";break;case"tertiary":a="--ref-palette-tertiary70";break;case"tertiary-light":a="--ref-palette-tertiary80";break;case"tertiary-dark":a="--ref-palette-tertiary60";break;case"error":a="--ref-palette-error50"}return e.ThemeSupport.instance().getComputedValue(a)}});export{r as ExtensionUI};
