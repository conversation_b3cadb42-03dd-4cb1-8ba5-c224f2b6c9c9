{"version": 3, "sources": ["../../../src/prebuild/ensureConfigAsync.ts"], "sourcesContent": ["import { ExpoConfig, getConfig, PackageJSONConfig } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\n\nimport {\n  getOrPromptForBundleIdentifierAsync,\n  getOrPromptForPackageAsync,\n} from '../utils/getOrPromptApplicationId';\n\n/** Ensure config is written, and prompts for application identifiers. */\nexport async function ensureConfigAsync(\n  projectRoot: string,\n  {\n    platforms,\n  }: {\n    platforms: ModPlatform[];\n  }\n): Promise<{ exp: ExpoConfig; pkg: PackageJSONConfig }> {\n  // Prompt for the Android package first because it's more strict than the bundle identifier\n  // this means you'll have a better chance at matching the bundle identifier with the package name.\n  if (platforms.includes('android')) {\n    await getOrPromptForPackageAsync(projectRoot);\n  }\n\n  if (platforms.includes('ios')) {\n    await getOrPromptForBundleIdentifierAsync(projectRoot);\n  }\n\n  // Read config again because prompting for bundle id or package name may have mutated the results.\n  return getConfig(projectRoot);\n}\n"], "names": ["ensureConfigAsync", "projectRoot", "platforms", "includes", "getOrPromptForPackageAsync", "getOrPromptForBundleIdentifierAsync", "getConfig"], "mappings": ";;;;+BASsBA;;;eAAAA;;;;yBATmC;;;;;;0CAMlD;AAGA,eAAeA,kBACpBC,WAAmB,EACnB,EACEC,SAAS,EAGV;IAED,2FAA2F;IAC3F,kGAAkG;IAClG,IAAIA,UAAUC,QAAQ,CAAC,YAAY;QACjC,MAAMC,IAAAA,oDAA0B,EAACH;IACnC;IAEA,IAAIC,UAAUC,QAAQ,CAAC,QAAQ;QAC7B,MAAME,IAAAA,6DAAmC,EAACJ;IAC5C;IAEA,kGAAkG;IAClG,OAAOK,IAAAA,mBAAS,EAACL;AACnB"}