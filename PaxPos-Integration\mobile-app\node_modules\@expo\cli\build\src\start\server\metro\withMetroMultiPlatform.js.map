{"version": 3, "sources": ["../../../../../src/start/server/metro/withMetroMultiPlatform.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ExpoConfig, Platform } from '@expo/config';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport Bundler from 'metro/src/Bundler';\nimport { ConfigT } from 'metro-config';\nimport { Resolution, ResolutionContext, CustomResolutionContext } from 'metro-resolver';\nimport * as metroResolver from 'metro-resolver';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { createFallbackModuleResolver } from './createExpoFallbackResolver';\nimport { createFastResolver, FailedToResolvePathError } from './createExpoMetroResolver';\nimport { isNodeExternal, shouldCreateVirtualCanary, shouldCreateVirtualShim } from './externals';\nimport { isFailedToResolveNameError, isFailedToResolvePathError } from './metroErrors';\nimport { getMetroBundlerWithVirtualModules } from './metroVirtualModules';\nimport {\n  withMetroErrorReportingResolver,\n  withMetroMutatedResolverContext,\n  withMetroResolvers,\n} from './withMetroResolvers';\nimport { Log } from '../../../log';\nimport { FileNotifier } from '../../../utils/FileNotifier';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\nimport { isInteractive } from '../../../utils/interactive';\nimport { loadTsConfigPathsAsync, TsConfigPaths } from '../../../utils/tsconfig/loadTsConfigPaths';\nimport { resolveWithTsConfigPaths } from '../../../utils/tsconfig/resolveWithTsConfigPaths';\nimport { isServerEnvironment } from '../middleware/metroOptions';\nimport { PlatformBundlers } from '../platformBundlers';\n\ntype Mutable<T> = { -readonly [K in keyof T]: T[K] };\n\nexport type StrictResolver = (moduleName: string) => Resolution;\nexport type StrictResolverFactory = (\n  context: ResolutionContext,\n  platform: string | null\n) => StrictResolver;\n\nconst ASSET_REGISTRY_SRC = `const assets=[];module.exports={registerAsset:s=>assets.push(s),getAssetByID:s=>assets[s-1]};`;\n\nconst debug = require('debug')('expo:start:server:metro:multi-platform') as typeof console.log;\n\nfunction withWebPolyfills(\n  config: ConfigT,\n  {\n    getMetroBundler,\n  }: {\n    getMetroBundler: () => Bundler;\n  }\n): ConfigT {\n  const originalGetPolyfills = config.serializer.getPolyfills\n    ? config.serializer.getPolyfills.bind(config.serializer)\n    : () => [];\n\n  const getPolyfills = (ctx: { platform?: string | null }): readonly string[] => {\n    const virtualEnvVarId = `\\0polyfill:environment-variables`;\n\n    getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n      virtualEnvVarId,\n      (() => {\n        return `//`;\n      })()\n    );\n\n    const virtualModuleId = `\\0polyfill:external-require`;\n\n    getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n      virtualModuleId,\n      (() => {\n        if (ctx.platform === 'web') {\n          return `global.$$require_external = typeof require !== \"undefined\" ? require : () => null;`;\n        } else {\n          // Wrap in try/catch to support Android.\n          return 'try { global.$$require_external = typeof expo === \"undefined\" ? require : (moduleId) => { throw new Error(`Node.js standard library module ${moduleId} is not available in this JavaScript environment`);} } catch { global.$$require_external = (moduleId) => { throw new Error(`Node.js standard library module ${moduleId} is not available in this JavaScript environment`);} }';\n        }\n      })()\n    );\n\n    if (ctx.platform === 'web') {\n      return [\n        virtualModuleId,\n        virtualEnvVarId,\n        // Ensure that the error-guard polyfill is included in the web polyfills to\n        // make metro-runtime work correctly.\n        // TODO: This module is pretty big for a function that simply re-throws an error that doesn't need to be caught.\n        require.resolve('@react-native/js-polyfills/error-guard'),\n      ];\n    }\n\n    // Generally uses `rn-get-polyfills`\n    const polyfills = originalGetPolyfills(ctx);\n    return [\n      ...polyfills,\n      virtualModuleId,\n      virtualEnvVarId,\n      // Removed on server platforms during the transform.\n      require.resolve('expo/virtual/streams.js'),\n    ];\n  };\n\n  return {\n    ...config,\n    serializer: {\n      ...config.serializer,\n      getPolyfills,\n    },\n  };\n}\n\nfunction normalizeSlashes(p: string) {\n  return p.replace(/\\\\/g, '/');\n}\n\nexport function getNodejsExtensions(srcExts: readonly string[]): string[] {\n  const mjsExts = srcExts.filter((ext) => /mjs$/.test(ext));\n  const nodejsSourceExtensions = srcExts.filter((ext) => !/mjs$/.test(ext));\n  // find index of last `*.js` extension\n  const jsIndex = nodejsSourceExtensions.reduce((index, ext, i) => {\n    return /jsx?$/.test(ext) ? i : index;\n  }, -1);\n\n  // insert `*.mjs` extensions after `*.js` extensions\n  nodejsSourceExtensions.splice(jsIndex + 1, 0, ...mjsExts);\n\n  return nodejsSourceExtensions;\n}\n\n/**\n * Apply custom resolvers to do the following:\n * - Disable `.native.js` extensions on web.\n * - Alias `react-native` to `react-native-web` on web.\n * - Redirect `react-native-web/dist/modules/AssetRegistry/index.js` to `@react-native/assets/registry.js` on web.\n * - Add support for `tsconfig.json`/`jsconfig.json` aliases via `compilerOptions.paths`.\n * - Alias react-native renderer code to a vendored React canary build on native.\n */\nexport function withExtendedResolver(\n  config: ConfigT,\n  {\n    tsconfig,\n    isTsconfigPathsEnabled,\n    isFastResolverEnabled,\n    isExporting,\n    isReactCanaryEnabled,\n    isReactServerComponentsEnabled,\n    getMetroBundler,\n  }: {\n    tsconfig: TsConfigPaths | null;\n    isTsconfigPathsEnabled?: boolean;\n    isFastResolverEnabled?: boolean;\n    isExporting?: boolean;\n    isReactCanaryEnabled?: boolean;\n    isReactServerComponentsEnabled?: boolean;\n    getMetroBundler: () => Bundler;\n  }\n) {\n  if (isReactServerComponentsEnabled) {\n    Log.warn(`React Server Components (beta) is enabled.`);\n  }\n  if (isReactCanaryEnabled) {\n    Log.warn(`Experimental React 19 canary is enabled.`);\n  }\n  if (isFastResolverEnabled) {\n    Log.log(chalk.dim`Fast resolver is enabled.`);\n  }\n\n  const defaultResolver = metroResolver.resolve;\n  const resolver = isFastResolverEnabled\n    ? createFastResolver({\n        preserveSymlinks: true,\n        blockList: !config.resolver?.blockList\n          ? []\n          : Array.isArray(config.resolver?.blockList)\n            ? config.resolver?.blockList\n            : [config.resolver?.blockList],\n      })\n    : defaultResolver;\n\n  const aliases: { [key: string]: Record<string, string> } = {\n    web: {\n      'react-native': 'react-native-web',\n      'react-native/index': 'react-native-web',\n      'react-native/Libraries/Image/resolveAssetSource': 'expo-asset/build/resolveAssetSource',\n    },\n  };\n\n  // The vendored canary modules live inside /static/canary-full/node_modules\n  // Adding the `index.js` allows us to add this path as `originModulePath` to\n  // resolve the nested `node_modules` folder properly.\n  const canaryModulesPath = path.join(\n    require.resolve('@expo/cli/package.json'),\n    '../static/canary-full/index.js'\n  );\n\n  let _universalAliases: [RegExp, string][] | null;\n\n  function getUniversalAliases() {\n    if (_universalAliases) {\n      return _universalAliases;\n    }\n\n    _universalAliases = [];\n\n    // This package is currently always installed as it is included in the `expo` package.\n    if (resolveFrom.silent(config.projectRoot, '@expo/vector-icons')) {\n      debug('Enabling alias: react-native-vector-icons -> @expo/vector-icons');\n      _universalAliases.push([/^react-native-vector-icons(\\/.*)?/, '@expo/vector-icons$1']);\n    }\n    if (isReactServerComponentsEnabled) {\n      if (resolveFrom.silent(config.projectRoot, 'expo-router/rsc')) {\n        debug('Enabling bridge alias: expo-router -> expo-router/rsc');\n        _universalAliases.push([/^expo-router$/, 'expo-router/rsc']);\n        // Bridge the internal entry point which is a standalone import to ensure package.json resolution works as expected.\n        _universalAliases.push([/^expo-router\\/entry-classic$/, 'expo-router/rsc/entry']);\n      }\n    }\n    return _universalAliases;\n  }\n\n  const preferredMainFields: { [key: string]: string[] } = {\n    // Defaults from Expo Webpack. Most packages using `react-native` don't support web\n    // in the `react-native` field, so we should prefer the `browser` field.\n    // https://github.com/expo/router/issues/37\n    web: ['browser', 'module', 'main'],\n  };\n\n  let tsConfigResolve =\n    isTsconfigPathsEnabled && (tsconfig?.paths || tsconfig?.baseUrl != null)\n      ? resolveWithTsConfigPaths.bind(resolveWithTsConfigPaths, {\n          paths: tsconfig.paths ?? {},\n          baseUrl: tsconfig.baseUrl ?? config.projectRoot,\n          hasBaseUrl: !!tsconfig.baseUrl,\n        })\n      : null;\n\n  // TODO: Move this to be a transform key for invalidation.\n  if (!isExporting && isInteractive()) {\n    if (isTsconfigPathsEnabled) {\n      // TODO: We should track all the files that used imports and invalidate them\n      // currently the user will need to save all the files that use imports to\n      // use the new aliases.\n      const configWatcher = new FileNotifier(config.projectRoot, [\n        './tsconfig.json',\n        './jsconfig.json',\n      ]);\n      configWatcher.startObserving(() => {\n        debug('Reloading tsconfig.json');\n        loadTsConfigPathsAsync(config.projectRoot).then((tsConfigPaths) => {\n          if (tsConfigPaths?.paths && !!Object.keys(tsConfigPaths.paths).length) {\n            debug('Enabling tsconfig.json paths support');\n            tsConfigResolve = resolveWithTsConfigPaths.bind(resolveWithTsConfigPaths, {\n              paths: tsConfigPaths.paths ?? {},\n              baseUrl: tsConfigPaths.baseUrl ?? config.projectRoot,\n              hasBaseUrl: !!tsConfigPaths.baseUrl,\n            });\n          } else {\n            debug('Disabling tsconfig.json paths support');\n            tsConfigResolve = null;\n          }\n        });\n      });\n\n      // TODO: This probably prevents the process from exiting.\n      installExitHooks(() => {\n        configWatcher.stopObserving();\n      });\n    } else {\n      debug('Skipping tsconfig.json paths support');\n    }\n  }\n\n  let nodejsSourceExtensions: string[] | null = null;\n\n  const getStrictResolver: StrictResolverFactory = (\n    { resolveRequest, ...context },\n    platform\n  ): StrictResolver => {\n    return function doResolve(moduleName: string): Resolution {\n      return resolver(context, moduleName, platform);\n    };\n  };\n\n  function getOptionalResolver(context: ResolutionContext, platform: string | null) {\n    const doResolve = getStrictResolver(context, platform);\n    return function optionalResolve(moduleName: string): Resolution | null {\n      try {\n        return doResolve(moduleName);\n      } catch (error) {\n        // If the error is directly related to a resolver not being able to resolve a module, then\n        // we can ignore the error and try the next resolver. Otherwise, we should throw the error.\n        const isResolutionError =\n          isFailedToResolveNameError(error) || isFailedToResolvePathError(error);\n        if (!isResolutionError) {\n          throw error;\n        }\n      }\n      return null;\n    };\n  }\n\n  // TODO: This is a hack to get resolveWeak working.\n  const idFactory = (config.serializer?.createModuleIdFactory?.() ??\n    ((id: number | string, context: { platform: string; environment?: string }): number | string =>\n      id)) as (\n    id: number | string,\n    context: { platform: string; environment?: string }\n  ) => number | string;\n\n  const getAssetRegistryModule = () => {\n    const virtualModuleId = `\\0polyfill:assets-registry`;\n    getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n      virtualModuleId,\n      ASSET_REGISTRY_SRC\n    );\n    return {\n      type: 'sourceFile',\n      filePath: virtualModuleId,\n    } as const;\n  };\n\n  // If Node.js pass-through, then remap to a module like `module.exports = $$require_external(<module>)`.\n  // If module should be shimmed, remap to an empty module.\n  const externals: {\n    match: (context: ResolutionContext, moduleName: string, platform: string | null) => boolean;\n    replace: 'empty' | 'node' | 'weak';\n  }[] = [\n    {\n      match: (context: ResolutionContext, moduleName: string) => {\n        if (\n          // Disable internal externals when exporting for production.\n          context.customResolverOptions.exporting ||\n          // These externals are only for Node.js environments.\n          !isServerEnvironment(context.customResolverOptions?.environment)\n        ) {\n          return false;\n        }\n\n        if (context.customResolverOptions?.environment === 'react-server') {\n          // Ensure these non-react-server modules are excluded when bundling for React Server Components in development.\n          return /^(source-map-support(\\/.*)?|@babel\\/runtime\\/.+|debug|metro-runtime\\/src\\/modules\\/HMRClient|metro|acorn-loose|acorn|chalk|ws|ansi-styles|supports-color|color-convert|has-flag|utf-8-validate|color-name|react-refresh\\/runtime|@remix-run\\/node\\/.+)$/.test(\n            moduleName\n          );\n        }\n\n        // TODO: Windows doesn't support externals somehow.\n        if (process.platform === 'win32') {\n          return /^(source-map-support(\\/.*)?)$/.test(moduleName);\n        }\n\n        // Extern these modules in standard Node.js environments in development to prevent API routes side-effects\n        // from leaking into the dev server process.\n        return /^(source-map-support(\\/.*)?|react|@radix-ui\\/.+|@babel\\/runtime\\/.+|react-dom(\\/.+)?|debug|acorn-loose|acorn|css-in-js-utils\\/lib\\/.+|hyphenate-style-name|color|color-string|color-convert|color-name|fontfaceobserver|fast-deep-equal|query-string|escape-string-regexp|invariant|postcss-value-parser|memoize-one|nullthrows|strict-uri-encode|decode-uri-component|split-on-first|filter-obj|warn-once|simple-swizzle|is-arrayish|inline-style-prefixer\\/.+)$/.test(\n          moduleName\n        );\n      },\n      replace: 'node',\n    },\n    // Externals to speed up async split chunks by extern-ing common packages that appear in the root client chunk.\n    {\n      match: (context: ResolutionContext, moduleName: string, platform: string | null) => {\n        if (\n          // Disable internal externals when exporting for production.\n          context.customResolverOptions.exporting ||\n          // These externals are only for client environments.\n          isServerEnvironment(context.customResolverOptions?.environment) ||\n          // Only enable for client boundaries\n          !context.customResolverOptions.clientboundary\n        ) {\n          return false;\n        }\n\n        // We don't support this in the resolver at the moment.\n        if (moduleName.endsWith('/package.json')) {\n          return false;\n        }\n\n        const isExternal = // Extern these modules in standard Node.js environments.\n          /^(deprecated-react-native-prop-types|react|react\\/jsx-dev-runtime|scheduler|react-native|react-dom(\\/.+)?|metro-runtime(\\/.+)?)$/.test(\n            moduleName\n          ) ||\n          // TODO: Add more\n          /^@babel\\/runtime\\/helpers\\/(wrapNativeSuper)$/.test(moduleName);\n\n        return isExternal;\n      },\n      replace: 'weak',\n    },\n  ];\n\n  const metroConfigWithCustomResolver = withMetroResolvers(config, [\n    // Mock out production react imports in development.\n    function requestDevMockProdReact(\n      context: ResolutionContext,\n      moduleName: string,\n      platform: string | null\n    ) {\n      // This resolution is dev-only to prevent bundling the production React packages in development.\n      if (!context.dev) return null;\n\n      if (\n        // Match react-native renderers.\n        (platform !== 'web' &&\n          context.originModulePath.match(/[\\\\/]node_modules[\\\\/]react-native[\\\\/]/) &&\n          moduleName.match(/([\\\\/]ReactFabric|ReactNativeRenderer)-prod/)) ||\n        // Match react production imports.\n        (moduleName.match(/\\.production(\\.min)?\\.js$/) &&\n          // Match if the import originated from a react package.\n          context.originModulePath.match(/[\\\\/]node_modules[\\\\/](react[-\\\\/]|scheduler[\\\\/])/))\n      ) {\n        debug(`Skipping production module: ${moduleName}`);\n        // /Users/<USER>/to/expo/node_modules/react/index.js ./cjs/react.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react/jsx-dev-runtime.js ./cjs/react-jsx-dev-runtime.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-is/index.js ./cjs/react-is.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-refresh/runtime.js ./cjs/react-refresh-runtime.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-native/node_modules/scheduler/index.native.js ./cjs/scheduler.native.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-native/node_modules/react-is/index.js ./cjs/react-is.production.min.js\n        return {\n          type: 'empty',\n        };\n      }\n      return null;\n    },\n    // tsconfig paths\n    function requestTsconfigPaths(\n      context: ResolutionContext,\n      moduleName: string,\n      platform: string | null\n    ) {\n      return (\n        tsConfigResolve?.(\n          {\n            originModulePath: context.originModulePath,\n            moduleName,\n          },\n          getOptionalResolver(context, platform)\n        ) ?? null\n      );\n    },\n\n    // Node.js externals support\n    function requestNodeExternals(\n      context: ResolutionContext,\n      moduleName: string,\n      platform: string | null\n    ) {\n      const isServer =\n        context.customResolverOptions?.environment === 'node' ||\n        context.customResolverOptions?.environment === 'react-server';\n\n      const moduleId = isNodeExternal(moduleName);\n      if (!moduleId) {\n        return null;\n      }\n\n      if (\n        // In browser runtimes, we want to either resolve a local node module by the same name, or shim the module to\n        // prevent crashing when Node.js built-ins are imported.\n        !isServer\n      ) {\n        // Perform optional resolve first. If the module doesn't exist (no module in the node_modules)\n        // then we can mock the file to use an empty module.\n        const result = getOptionalResolver(context, platform)(moduleName);\n\n        if (!result && platform !== 'web') {\n          // Preserve previous behavior where native throws an error on node.js internals.\n          return null;\n        }\n\n        return (\n          result ?? {\n            // In this case, mock the file to use an empty module.\n            type: 'empty',\n          }\n        );\n      }\n      const contents = `module.exports=$$require_external('node:${moduleId}');`;\n      debug(`Virtualizing Node.js \"${moduleId}\"`);\n      const virtualModuleId = `\\0node:${moduleId}`;\n      getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n        virtualModuleId,\n        contents\n      );\n      return {\n        type: 'sourceFile',\n        filePath: virtualModuleId,\n      };\n    },\n\n    // Custom externals support\n    function requestCustomExternals(\n      context: ResolutionContext,\n      moduleName: string,\n      platform: string | null\n    ) {\n      // We don't support this in the resolver at the moment.\n      if (moduleName.endsWith('/package.json')) {\n        return null;\n      }\n      // Skip applying JS externals for CSS files.\n      if (/\\.(s?css|sass)$/.test(context.originModulePath)) {\n        return null;\n      }\n\n      const environment = context.customResolverOptions?.environment;\n\n      const strictResolve = getStrictResolver(context, platform);\n\n      for (const external of externals) {\n        if (external.match(context, moduleName, platform)) {\n          if (external.replace === 'empty') {\n            debug(`Redirecting external \"${moduleName}\" to \"${external.replace}\"`);\n            return {\n              type: external.replace,\n            };\n          } else if (external.replace === 'weak') {\n            // TODO: Make this use require.resolveWeak again. Previously this was just resolving to the same path.\n            const realModule = strictResolve(moduleName);\n            const realPath = realModule.type === 'sourceFile' ? realModule.filePath : moduleName;\n            const opaqueId = idFactory(realPath, {\n              platform: platform!,\n              environment,\n            });\n\n            const contents =\n              typeof opaqueId === 'number'\n                ? `module.exports=/*${moduleName}*/__r(${opaqueId})`\n                : `module.exports=/*${moduleName}*/__r(${JSON.stringify(opaqueId)})`;\n            // const contents = `module.exports=/*${moduleName}*/__r(require.resolveWeak('${moduleName}'))`;\n            // const generatedModuleId = fastHashMemoized(contents);\n            const virtualModuleId = `\\0weak:${opaqueId}`;\n            debug('Virtualizing module:', moduleName, '->', virtualModuleId);\n            getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n              virtualModuleId,\n              contents\n            );\n            return {\n              type: 'sourceFile',\n              filePath: virtualModuleId,\n            };\n          } else if (external.replace === 'node') {\n            const contents = `module.exports=$$require_external('${moduleName}')`;\n            const virtualModuleId = `\\0node:${moduleName}`;\n            debug('Virtualizing Node.js (custom):', moduleName, '->', virtualModuleId);\n            getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n              virtualModuleId,\n              contents\n            );\n            return {\n              type: 'sourceFile',\n              filePath: virtualModuleId,\n            };\n          } else {\n            throw new CommandError(\n              `Invalid external alias type: \"${external.replace}\" for module \"${moduleName}\" (platform: ${platform}, originModulePath: ${context.originModulePath})`\n            );\n          }\n        }\n      }\n      return null;\n    },\n\n    // Basic moduleId aliases\n    function requestAlias(context: ResolutionContext, moduleName: string, platform: string | null) {\n      // Conditionally remap `react-native` to `react-native-web` on web in\n      // a way that doesn't require Babel to resolve the alias.\n      if (platform && platform in aliases && aliases[platform][moduleName]) {\n        const redirectedModuleName = aliases[platform][moduleName];\n        return getStrictResolver(context, platform)(redirectedModuleName);\n      }\n\n      for (const [matcher, alias] of getUniversalAliases()) {\n        const match = moduleName.match(matcher);\n        if (match) {\n          const aliasedModule = alias.replace(\n            /\\$(\\d+)/g,\n            (_, index) => match[parseInt(index, 10)] ?? ''\n          );\n          const doResolve = getStrictResolver(context, platform);\n          debug(`Alias \"${moduleName}\" to \"${aliasedModule}\"`);\n          return doResolve(aliasedModule);\n        }\n      }\n\n      return null;\n    },\n\n    // Polyfill for asset registry\n    function requestStableAssetRegistry(\n      context: ResolutionContext,\n      moduleName: string,\n      platform: string | null\n    ) {\n      if (/^@react-native\\/assets-registry\\/registry(\\.js)?$/.test(moduleName)) {\n        return getAssetRegistryModule();\n      }\n\n      if (\n        platform === 'web' &&\n        context.originModulePath.match(/node_modules[\\\\/]react-native-web[\\\\/]/) &&\n        moduleName.includes('/modules/AssetRegistry')\n      ) {\n        return getAssetRegistryModule();\n      }\n\n      return null;\n    },\n\n    // TODO: Reduce these as much as possible in the future.\n    // Complex post-resolution rewrites.\n    function requestPostRewrites(\n      context: ResolutionContext,\n      moduleName: string,\n      platform: string | null\n    ) {\n      const doResolve = getStrictResolver(context, platform);\n\n      const result = doResolve(moduleName);\n\n      if (result.type !== 'sourceFile') {\n        return result;\n      }\n\n      if (platform === 'web') {\n        if (result.filePath.includes('node_modules')) {\n          // // Disallow importing confusing native modules on web\n          if (moduleName.includes('react-native/Libraries/Utilities/codegenNativeCommands')) {\n            throw new FailedToResolvePathError(\n              `Importing native-only module \"${moduleName}\" on web from: ${context.originModulePath}`\n            );\n          }\n\n          // Replace with static shims\n\n          const normalName = normalizeSlashes(result.filePath)\n            // Drop everything up until the `node_modules` folder.\n            .replace(/.*node_modules\\//, '');\n\n          const shimFile = shouldCreateVirtualShim(normalName);\n          if (shimFile) {\n            const virtualId = `\\0shim:${normalName}`;\n            const bundler = getMetroBundlerWithVirtualModules(getMetroBundler());\n            if (!bundler.hasVirtualModule(virtualId)) {\n              bundler.setVirtualModule(virtualId, fs.readFileSync(shimFile, 'utf8'));\n            }\n            debug(`Redirecting module \"${result.filePath}\" to shim`);\n\n            return {\n              ...result,\n              filePath: virtualId,\n            };\n          }\n        }\n      } else {\n        const isServer =\n          context.customResolverOptions?.environment === 'node' ||\n          context.customResolverOptions?.environment === 'react-server';\n\n        // react-native/Libraries/Core/InitializeCore\n        const normal = normalizeSlashes(result.filePath);\n\n        // Shim out React Native native runtime globals in server mode for native.\n        if (isServer) {\n          if (normal.endsWith('react-native/Libraries/Core/InitializeCore.js')) {\n            debug('Shimming out InitializeCore for React Native in native SSR bundle');\n            return {\n              type: 'empty',\n            };\n          }\n        }\n\n        // When server components are enabled, redirect React Native's renderer to the canary build\n        // this will enable the use hook and other requisite features from React 19.\n        if (isReactCanaryEnabled && result.filePath.includes('node_modules')) {\n          const normalName = normalizeSlashes(result.filePath)\n            // Drop everything up until the `node_modules` folder.\n            .replace(/.*node_modules\\//, '');\n\n          const canaryFile = shouldCreateVirtualCanary(normalName);\n          if (canaryFile) {\n            debug(`Redirecting React Native module \"${result.filePath}\" to canary build`);\n            return {\n              ...result,\n              filePath: canaryFile,\n            };\n          }\n        }\n      }\n\n      return result;\n    },\n\n    // If at this point, we haven't resolved a module yet, if it's a module specifier for a known dependency\n    // of either `expo` or `expo-router`, attempt to resolve it from these origin modules instead\n    createFallbackModuleResolver({\n      projectRoot: config.projectRoot,\n      originModuleNames: ['expo', 'expo-router'],\n      getStrictResolver,\n    }),\n  ]);\n\n  // Ensure we mutate the resolution context to include the custom resolver options for server and web.\n  const metroConfigWithCustomContext = withMetroMutatedResolverContext(\n    metroConfigWithCustomResolver,\n    (\n      immutableContext: CustomResolutionContext,\n      moduleName: string,\n      platform: string | null\n    ): CustomResolutionContext => {\n      const context: Mutable<CustomResolutionContext> = {\n        ...immutableContext,\n        preferNativePlatform: platform !== 'web',\n      };\n\n      // TODO: Remove this when we have React 19 in the expo/expo monorepo.\n      if (\n        isReactCanaryEnabled &&\n        // Change the node modules path for react and react-dom to use the vendor in Expo CLI.\n        /^(react|react\\/.*|react-dom|react-dom\\/.*)$/.test(moduleName)\n      ) {\n        // Modifying the origin module path changes the starting Node module resolution path to this folder\n        context.originModulePath = canaryModulesPath;\n        // Hierarchical lookup has to be enabled for this to work\n        context.disableHierarchicalLookup = false;\n      }\n\n      if (isServerEnvironment(context.customResolverOptions?.environment)) {\n        // Adjust nodejs source extensions to sort mjs after js, including platform variants.\n        if (nodejsSourceExtensions === null) {\n          nodejsSourceExtensions = getNodejsExtensions(context.sourceExts);\n        }\n        context.sourceExts = nodejsSourceExtensions;\n\n        context.unstable_enablePackageExports = true;\n        context.unstable_conditionsByPlatform = {};\n\n        const isReactServerComponents =\n          context.customResolverOptions?.environment === 'react-server';\n\n        if (isReactServerComponents) {\n          // NOTE: Align the behavior across server and client. This is a breaking change so we'll just roll it out with React Server Components.\n          // This ensures that react-server and client code both resolve `module` and `main` in the same order.\n          if (platform === 'web') {\n            // Node.js runtimes should only be importing main at the moment.\n            // This is a temporary fix until we can support the package.json exports.\n            context.mainFields = ['module', 'main'];\n          } else {\n            // In Node.js + native, use the standard main fields.\n            context.mainFields = ['react-native', 'module', 'main'];\n          }\n        } else {\n          if (platform === 'web') {\n            // Node.js runtimes should only be importing main at the moment.\n            // This is a temporary fix until we can support the package.json exports.\n            context.mainFields = ['main', 'module'];\n          } else {\n            // In Node.js + native, use the standard main fields.\n            context.mainFields = ['react-native', 'main', 'module'];\n          }\n        }\n\n        // Enable react-server import conditions.\n        if (context.customResolverOptions?.environment === 'react-server') {\n          context.unstable_conditionNames = ['node', 'react-server', 'workerd'];\n        } else {\n          context.unstable_conditionNames = ['node'];\n        }\n      } else {\n        // Non-server changes\n\n        if (!env.EXPO_METRO_NO_MAIN_FIELD_OVERRIDE && platform && platform in preferredMainFields) {\n          context.mainFields = preferredMainFields[platform];\n        }\n      }\n\n      return context;\n    }\n  );\n\n  return withMetroErrorReportingResolver(metroConfigWithCustomContext);\n}\n\n/** @returns `true` if the incoming resolution should be swapped. */\nexport function shouldAliasModule(\n  input: {\n    platform: string | null;\n    result: Resolution;\n  },\n  alias: { platform: string; output: string }\n): boolean {\n  return (\n    input.platform === alias.platform &&\n    input.result?.type === 'sourceFile' &&\n    typeof input.result?.filePath === 'string' &&\n    normalizeSlashes(input.result.filePath).endsWith(alias.output)\n  );\n}\n\n/** Add support for `react-native-web` and the Web platform. */\nexport async function withMetroMultiPlatformAsync(\n  projectRoot: string,\n  {\n    config,\n    exp,\n    platformBundlers,\n    isTsconfigPathsEnabled,\n    isFastResolverEnabled,\n    isExporting,\n    isReactCanaryEnabled,\n    isNamedRequiresEnabled,\n    isReactServerComponentsEnabled,\n    getMetroBundler,\n  }: {\n    config: ConfigT;\n    exp: ExpoConfig;\n    isTsconfigPathsEnabled: boolean;\n    platformBundlers: PlatformBundlers;\n    isFastResolverEnabled?: boolean;\n    isExporting?: boolean;\n    isReactCanaryEnabled: boolean;\n    isReactServerComponentsEnabled: boolean;\n    isNamedRequiresEnabled: boolean;\n    getMetroBundler: () => Bundler;\n  }\n) {\n  if (isNamedRequiresEnabled) {\n    debug('Using Expo metro require runtime.');\n    // Change the default metro-runtime to a custom one that supports bundle splitting.\n    require('metro-config/src/defaults/defaults').moduleSystem = require.resolve(\n      '@expo/cli/build/metro-require/require'\n    );\n  }\n\n  if (!config.projectRoot) {\n    // @ts-expect-error: read-only types\n    config.projectRoot = projectRoot;\n  }\n\n  // Required for @expo/metro-runtime to format paths in the web LogBox.\n  process.env.EXPO_PUBLIC_PROJECT_ROOT = process.env.EXPO_PUBLIC_PROJECT_ROOT ?? projectRoot;\n\n  // This is used for running Expo CLI in development against projects outside the monorepo.\n  if (!isDirectoryIn(__dirname, projectRoot)) {\n    if (!config.watchFolders) {\n      // @ts-expect-error: watchFolders is readonly\n      config.watchFolders = [];\n    }\n    // @ts-expect-error: watchFolders is readonly\n    config.watchFolders.push(path.join(require.resolve('metro-runtime/package.json'), '../..'));\n    // @ts-expect-error: watchFolders is readonly\n    config.watchFolders.push(\n      path.join(require.resolve('@expo/metro-config/package.json'), '../..'),\n      // For virtual modules\n      path.join(require.resolve('expo/package.json'), '..')\n    );\n    if (isReactCanaryEnabled) {\n      // @ts-expect-error: watchFolders is readonly\n      config.watchFolders.push(path.join(require.resolve('@expo/cli/package.json'), '..'));\n    }\n  }\n\n  // TODO: Remove this\n  // @ts-expect-error: Invalidate the cache when the location of expo-router changes on-disk.\n  config.transformer._expoRouterPath = resolveFrom.silent(projectRoot, 'expo-router');\n\n  let tsconfig: null | TsConfigPaths = null;\n\n  if (isTsconfigPathsEnabled) {\n    tsconfig = await loadTsConfigPathsAsync(projectRoot);\n  }\n\n  let expoConfigPlatforms = Object.entries(platformBundlers)\n    .filter(\n      ([platform, bundler]) => bundler === 'metro' && exp.platforms?.includes(platform as Platform)\n    )\n    .map(([platform]) => platform);\n\n  if (Array.isArray(config.resolver.platforms)) {\n    expoConfigPlatforms = [...new Set(expoConfigPlatforms.concat(config.resolver.platforms))];\n  }\n\n  // @ts-expect-error: typed as `readonly`.\n  config.resolver.platforms = expoConfigPlatforms;\n\n  config = withWebPolyfills(config, { getMetroBundler });\n\n  return withExtendedResolver(config, {\n    tsconfig,\n    isExporting,\n    isTsconfigPathsEnabled,\n    isFastResolverEnabled,\n    isReactCanaryEnabled,\n    isReactServerComponentsEnabled,\n    getMetroBundler,\n  });\n}\n\nfunction isDirectoryIn(targetPath: string, rootPath: string) {\n  return targetPath.startsWith(rootPath) && targetPath.length >= rootPath.length;\n}\n"], "names": ["getNodejsExtensions", "shouldAliasModule", "withExtendedResolver", "withMetroMultiPlatformAsync", "ASSET_REGISTRY_SRC", "debug", "require", "withWebPolyfills", "config", "getMetroBundler", "originalGetPolyfills", "serializer", "getPolyfills", "bind", "ctx", "virtualEnvVarId", "getMetroBundlerWithVirtualModules", "setVirtualModule", "virtualModuleId", "platform", "resolve", "polyfills", "normalizeSlashes", "p", "replace", "srcExts", "mjsExts", "filter", "ext", "test", "nodejsSourceExtensions", "jsIndex", "reduce", "index", "i", "splice", "tsconfig", "isTsconfigPathsEnabled", "isFastResolverEnabled", "isExporting", "isReactCanaryEnabled", "isReactServerComponentsEnabled", "Log", "warn", "log", "chalk", "dim", "defaultResolver", "metroResolver", "resolver", "createFastResolver", "preserveSymlinks", "blockList", "Array", "isArray", "aliases", "web", "canaryModulesPath", "path", "join", "_universalAliases", "getUniversalAliases", "resolveFrom", "silent", "projectRoot", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsConfigResolve", "paths", "baseUrl", "resolveWithTsConfigPaths", "hasBaseUrl", "isInteractive", "config<PERSON><PERSON><PERSON>", "FileNotifier", "startObserving", "loadTsConfigPathsAsync", "then", "tsConfigPaths", "Object", "keys", "length", "installExitHooks", "stopObserving", "getStrictResolver", "resolveRequest", "context", "doResolve", "moduleName", "getOptionalResolver", "optionalResolve", "error", "isResolutionError", "isFailedToResolveNameError", "isFailedToResolvePathError", "idFactory", "createModuleIdFactory", "id", "getAssetRegistryModule", "type", "filePath", "externals", "match", "customResolverOptions", "exporting", "isServerEnvironment", "environment", "process", "clientboundary", "endsWith", "isExternal", "metroConfigWithCustomResolver", "withMetroResolvers", "requestDevMockProdReact", "dev", "originModulePath", "requestTsconfigPaths", "requestNodeExternals", "isServer", "moduleId", "isNodeExternal", "result", "contents", "requestCustomExternals", "strictResolve", "external", "realModule", "realPath", "opaqueId", "JSON", "stringify", "CommandError", "request<PERSON>lias", "redirectedModuleName", "matcher", "alias", "aliasedModule", "_", "parseInt", "requestStableAssetRegistry", "includes", "requestPostRewrites", "FailedToResolvePathError", "normalName", "shimFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virtualId", "bundler", "hasVirtualModule", "fs", "readFileSync", "normal", "canaryFile", "shouldCreateVirtualCanary", "createFallbackModuleResolver", "originModuleNames", "metroConfigWithCustomContext", "withMetroMutatedResolverContext", "immutableContext", "preferNativePlatform", "disableHierarchicalLookup", "sourceExts", "unstable_enablePackageExports", "unstable_conditionsByPlatform", "isReactServerComponents", "mainFields", "unstable_conditionNames", "env", "EXPO_METRO_NO_MAIN_FIELD_OVERRIDE", "withMetroErrorReportingResolver", "input", "output", "exp", "platformBundlers", "isNamedRequiresEnabled", "moduleSystem", "EXPO_PUBLIC_PROJECT_ROOT", "isDirectoryIn", "__dirname", "watchFolders", "transformer", "_expoRouterPath", "expoConfigPlatforms", "entries", "platforms", "map", "Set", "concat", "targetPath", "rootPath", "startsWith"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAmHeA,mBAAmB;eAAnBA;;IA4pBAC,iBAAiB;eAAjBA;;IAtoBAC,oBAAoB;eAApBA;;IAspBMC,2BAA2B;eAA3BA;;;;gEA7xBJ;;;;;;;gEACH;;;;;;;iEAIgB;;;;;;;gEACd;;;;;;;gEACO;;;;;;4CAEqB;yCACgB;2BACsB;6BACZ;qCACrB;oCAK3C;qBACa;8BACS;qBACT;wBACS;sBACI;6BACH;mCACwB;0CACb;8BACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpC,MAAMC,qBAAqB,CAAC,6FAA6F,CAAC;AAE1H,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,SAASC,iBACPC,MAAe,EACf,EACEC,eAAe,EAGhB;IAED,MAAMC,uBAAuBF,OAAOG,UAAU,CAACC,YAAY,GACvDJ,OAAOG,UAAU,CAACC,YAAY,CAACC,IAAI,CAACL,OAAOG,UAAU,IACrD,IAAM,EAAE;IAEZ,MAAMC,eAAe,CAACE;QACpB,MAAMC,kBAAkB,CAAC,gCAAgC,CAAC;QAE1DC,IAAAA,sDAAiC,EAACP,mBAAmBQ,gBAAgB,CACnEF,iBACA,AAAC,CAAA;YACC,OAAO,CAAC,EAAE,CAAC;QACb,CAAA;QAGF,MAAMG,kBAAkB,CAAC,2BAA2B,CAAC;QAErDF,IAAAA,sDAAiC,EAACP,mBAAmBQ,gBAAgB,CACnEC,iBACA,AAAC,CAAA;YACC,IAAIJ,IAAIK,QAAQ,KAAK,OAAO;gBAC1B,OAAO,CAAC,kFAAkF,CAAC;YAC7F,OAAO;gBACL,wCAAwC;gBACxC,OAAO;YACT;QACF,CAAA;QAGF,IAAIL,IAAIK,QAAQ,KAAK,OAAO;YAC1B,OAAO;gBACLD;gBACAH;gBACA,2EAA2E;gBAC3E,qCAAqC;gBACrC,gHAAgH;gBAChHT,QAAQc,OAAO,CAAC;aACjB;QACH;QAEA,oCAAoC;QACpC,MAAMC,YAAYX,qBAAqBI;QACvC,OAAO;eACFO;YACHH;YACAH;YACA,oDAAoD;YACpDT,QAAQc,OAAO,CAAC;SACjB;IACH;IAEA,OAAO;QACL,GAAGZ,MAAM;QACTG,YAAY;YACV,GAAGH,OAAOG,UAAU;YACpBC;QACF;IACF;AACF;AAEA,SAASU,iBAAiBC,CAAS;IACjC,OAAOA,EAAEC,OAAO,CAAC,OAAO;AAC1B;AAEO,SAASxB,oBAAoByB,OAA0B;IAC5D,MAAMC,UAAUD,QAAQE,MAAM,CAAC,CAACC,MAAQ,OAAOC,IAAI,CAACD;IACpD,MAAME,yBAAyBL,QAAQE,MAAM,CAAC,CAACC,MAAQ,CAAC,OAAOC,IAAI,CAACD;IACpE,sCAAsC;IACtC,MAAMG,UAAUD,uBAAuBE,MAAM,CAAC,CAACC,OAAOL,KAAKM;QACzD,OAAO,QAAQL,IAAI,CAACD,OAAOM,IAAID;IACjC,GAAG,CAAC;IAEJ,oDAAoD;IACpDH,uBAAuBK,MAAM,CAACJ,UAAU,GAAG,MAAML;IAEjD,OAAOI;AACT;AAUO,SAAS5B,qBACdM,MAAe,EACf,EACE4B,QAAQ,EACRC,sBAAsB,EACtBC,qBAAqB,EACrBC,WAAW,EACXC,oBAAoB,EACpBC,8BAA8B,EAC9BhC,eAAe,EAShB;QAgBiBD,kBAEMA,mBACZA,mBACCA,mBA+HMA,0CAAAA;IAjJnB,IAAIiC,gCAAgC;QAClCC,QAAG,CAACC,IAAI,CAAC,CAAC,0CAA0C,CAAC;IACvD;IACA,IAAIH,sBAAsB;QACxBE,QAAG,CAACC,IAAI,CAAC,CAAC,wCAAwC,CAAC;IACrD;IACA,IAAIL,uBAAuB;QACzBI,QAAG,CAACE,GAAG,CAACC,gBAAK,CAACC,GAAG,CAAC,yBAAyB,CAAC;IAC9C;IAEA,MAAMC,kBAAkBC,iBAAc5B,OAAO;IAC7C,MAAM6B,WAAWX,wBACbY,IAAAA,2CAAkB,EAAC;QACjBC,kBAAkB;QAClBC,WAAW,GAAC5C,mBAAAA,OAAOyC,QAAQ,qBAAfzC,iBAAiB4C,SAAS,IAClC,EAAE,GACFC,MAAMC,OAAO,EAAC9C,oBAAAA,OAAOyC,QAAQ,qBAAfzC,kBAAiB4C,SAAS,KACtC5C,oBAAAA,OAAOyC,QAAQ,qBAAfzC,kBAAiB4C,SAAS,GAC1B;aAAC5C,oBAAAA,OAAOyC,QAAQ,qBAAfzC,kBAAiB4C,SAAS;SAAC;IACpC,KACAL;IAEJ,MAAMQ,UAAqD;QACzDC,KAAK;YACH,gBAAgB;YAChB,sBAAsB;YACtB,mDAAmD;QACrD;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,qDAAqD;IACrD,MAAMC,oBAAoBC,eAAI,CAACC,IAAI,CACjCrD,QAAQc,OAAO,CAAC,2BAChB;IAGF,IAAIwC;IAEJ,SAASC;QACP,IAAID,mBAAmB;YACrB,OAAOA;QACT;QAEAA,oBAAoB,EAAE;QAEtB,sFAAsF;QACtF,IAAIE,sBAAW,CAACC,MAAM,CAACvD,OAAOwD,WAAW,EAAE,uBAAuB;YAChE3D,MAAM;YACNuD,kBAAkBK,IAAI,CAAC;gBAAC;gBAAqC;aAAuB;QACtF;QACA,IAAIxB,gCAAgC;YAClC,IAAIqB,sBAAW,CAACC,MAAM,CAACvD,OAAOwD,WAAW,EAAE,oBAAoB;gBAC7D3D,MAAM;gBACNuD,kBAAkBK,IAAI,CAAC;oBAAC;oBAAiB;iBAAkB;gBAC3D,oHAAoH;gBACpHL,kBAAkBK,IAAI,CAAC;oBAAC;oBAAgC;iBAAwB;YAClF;QACF;QACA,OAAOL;IACT;IAEA,MAAMM,sBAAmD;QACvD,mFAAmF;QACnF,wEAAwE;QACxE,2CAA2C;QAC3CV,KAAK;YAAC;YAAW;YAAU;SAAO;IACpC;IAEA,IAAIW,kBACF9B,0BAA2BD,CAAAA,CAAAA,4BAAAA,SAAUgC,KAAK,KAAIhC,CAAAA,4BAAAA,SAAUiC,OAAO,KAAI,IAAG,IAClEC,kDAAwB,CAACzD,IAAI,CAACyD,kDAAwB,EAAE;QACtDF,OAAOhC,SAASgC,KAAK,IAAI,CAAC;QAC1BC,SAASjC,SAASiC,OAAO,IAAI7D,OAAOwD,WAAW;QAC/CO,YAAY,CAAC,CAACnC,SAASiC,OAAO;IAChC,KACA;IAEN,0DAA0D;IAC1D,IAAI,CAAC9B,eAAeiC,IAAAA,0BAAa,KAAI;QACnC,IAAInC,wBAAwB;YAC1B,4EAA4E;YAC5E,yEAAyE;YACzE,uBAAuB;YACvB,MAAMoC,gBAAgB,IAAIC,0BAAY,CAAClE,OAAOwD,WAAW,EAAE;gBACzD;gBACA;aACD;YACDS,cAAcE,cAAc,CAAC;gBAC3BtE,MAAM;gBACNuE,IAAAA,yCAAsB,EAACpE,OAAOwD,WAAW,EAAEa,IAAI,CAAC,CAACC;oBAC/C,IAAIA,CAAAA,iCAAAA,cAAeV,KAAK,KAAI,CAAC,CAACW,OAAOC,IAAI,CAACF,cAAcV,KAAK,EAAEa,MAAM,EAAE;wBACrE5E,MAAM;wBACN8D,kBAAkBG,kDAAwB,CAACzD,IAAI,CAACyD,kDAAwB,EAAE;4BACxEF,OAAOU,cAAcV,KAAK,IAAI,CAAC;4BAC/BC,SAASS,cAAcT,OAAO,IAAI7D,OAAOwD,WAAW;4BACpDO,YAAY,CAAC,CAACO,cAAcT,OAAO;wBACrC;oBACF,OAAO;wBACLhE,MAAM;wBACN8D,kBAAkB;oBACpB;gBACF;YACF;YAEA,yDAAyD;YACzDe,IAAAA,sBAAgB,EAAC;gBACfT,cAAcU,aAAa;YAC7B;QACF,OAAO;YACL9E,MAAM;QACR;IACF;IAEA,IAAIyB,yBAA0C;IAE9C,MAAMsD,oBAA2C,CAC/C,EAAEC,cAAc,EAAE,GAAGC,SAAS,EAC9BnE;QAEA,OAAO,SAASoE,UAAUC,UAAkB;YAC1C,OAAOvC,SAASqC,SAASE,YAAYrE;QACvC;IACF;IAEA,SAASsE,oBAAoBH,OAA0B,EAAEnE,QAAuB;QAC9E,MAAMoE,YAAYH,kBAAkBE,SAASnE;QAC7C,OAAO,SAASuE,gBAAgBF,UAAkB;YAChD,IAAI;gBACF,OAAOD,UAAUC;YACnB,EAAE,OAAOG,OAAO;gBACd,0FAA0F;gBAC1F,2FAA2F;gBAC3F,MAAMC,oBACJC,IAAAA,uCAA0B,EAACF,UAAUG,IAAAA,uCAA0B,EAACH;gBAClE,IAAI,CAACC,mBAAmB;oBACtB,MAAMD;gBACR;YACF;YACA,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAMI,YAAavF,EAAAA,qBAAAA,OAAOG,UAAU,sBAAjBH,2CAAAA,mBAAmBwF,qBAAqB,qBAAxCxF,8CAAAA,wBAChB,CAAA,CAACyF,IAAqBX,UACrBW,EAAC;IAKL,MAAMC,yBAAyB;QAC7B,MAAMhF,kBAAkB,CAAC,0BAA0B,CAAC;QACpDF,IAAAA,sDAAiC,EAACP,mBAAmBQ,gBAAgB,CACnEC,iBACAd;QAEF,OAAO;YACL+F,MAAM;YACNC,UAAUlF;QACZ;IACF;IAEA,wGAAwG;IACxG,yDAAyD;IACzD,MAAMmF,YAGA;QACJ;YACEC,OAAO,CAAChB,SAA4BE;oBAKXF,gCAKnBA;gBATJ,IACE,4DAA4D;gBAC5DA,QAAQiB,qBAAqB,CAACC,SAAS,IACvC,qDAAqD;gBACrD,CAACC,IAAAA,iCAAmB,GAACnB,iCAAAA,QAAQiB,qBAAqB,qBAA7BjB,+BAA+BoB,WAAW,GAC/D;oBACA,OAAO;gBACT;gBAEA,IAAIpB,EAAAA,kCAAAA,QAAQiB,qBAAqB,qBAA7BjB,gCAA+BoB,WAAW,MAAK,gBAAgB;oBACjE,+GAA+G;oBAC/G,OAAO,0PAA0P7E,IAAI,CACnQ2D;gBAEJ;gBAEA,mDAAmD;gBACnD,IAAImB,QAAQxF,QAAQ,KAAK,SAAS;oBAChC,OAAO,gCAAgCU,IAAI,CAAC2D;gBAC9C;gBAEA,0GAA0G;gBAC1G,4CAA4C;gBAC5C,OAAO,ocAAoc3D,IAAI,CAC7c2D;YAEJ;YACAhE,SAAS;QACX;QACA,+GAA+G;QAC/G;YACE8E,OAAO,CAAChB,SAA4BE,YAAoBrE;oBAKhCmE;gBAJtB,IACE,4DAA4D;gBAC5DA,QAAQiB,qBAAqB,CAACC,SAAS,IACvC,oDAAoD;gBACpDC,IAAAA,iCAAmB,GAACnB,iCAAAA,QAAQiB,qBAAqB,qBAA7BjB,+BAA+BoB,WAAW,KAC9D,oCAAoC;gBACpC,CAACpB,QAAQiB,qBAAqB,CAACK,cAAc,EAC7C;oBACA,OAAO;gBACT;gBAEA,uDAAuD;gBACvD,IAAIpB,WAAWqB,QAAQ,CAAC,kBAAkB;oBACxC,OAAO;gBACT;gBAEA,MAAMC,aACJ,mIAAmIjF,IAAI,CACrI2D,eAEF,iBAAiB;gBACjB,gDAAgD3D,IAAI,CAAC2D;gBAEvD,OAAOsB;YACT;YACAtF,SAAS;QACX;KACD;IAED,MAAMuF,gCAAgCC,IAAAA,sCAAkB,EAACxG,QAAQ;QAC/D,oDAAoD;QACpD,SAASyG,wBACP3B,OAA0B,EAC1BE,UAAkB,EAClBrE,QAAuB;YAEvB,gGAAgG;YAChG,IAAI,CAACmE,QAAQ4B,GAAG,EAAE,OAAO;YAEzB,IAEE,AADA,gCAAgC;YAC/B/F,aAAa,SACZmE,QAAQ6B,gBAAgB,CAACb,KAAK,CAAC,8CAC/Bd,WAAWc,KAAK,CAAC,kDACnB,kCAAkC;YACjCd,WAAWc,KAAK,CAAC,gCAChB,uDAAuD;YACvDhB,QAAQ6B,gBAAgB,CAACb,KAAK,CAAC,uDACjC;gBACAjG,MAAM,CAAC,4BAA4B,EAAEmF,YAAY;gBACjD,gFAAgF;gBAChF,0GAA0G;gBAC1G,sFAAsF;gBACtF,0GAA0G;gBAC1G,gIAAgI;gBAChI,gHAAgH;gBAChH,OAAO;oBACLW,MAAM;gBACR;YACF;YACA,OAAO;QACT;QACA,iBAAiB;QACjB,SAASiB,qBACP9B,OAA0B,EAC1BE,UAAkB,EAClBrE,QAAuB;YAEvB,OACEgD,CAAAA,mCAAAA,gBACE;gBACEgD,kBAAkB7B,QAAQ6B,gBAAgB;gBAC1C3B;YACF,GACAC,oBAAoBH,SAASnE,eAC1B;QAET;QAEA,4BAA4B;QAC5B,SAASkG,qBACP/B,OAA0B,EAC1BE,UAAkB,EAClBrE,QAAuB;gBAGrBmE,gCACAA;YAFF,MAAMgC,WACJhC,EAAAA,iCAAAA,QAAQiB,qBAAqB,qBAA7BjB,+BAA+BoB,WAAW,MAAK,UAC/CpB,EAAAA,kCAAAA,QAAQiB,qBAAqB,qBAA7BjB,gCAA+BoB,WAAW,MAAK;YAEjD,MAAMa,WAAWC,IAAAA,yBAAc,EAAChC;YAChC,IAAI,CAAC+B,UAAU;gBACb,OAAO;YACT;YAEA,IACE,6GAA6G;YAC7G,wDAAwD;YACxD,CAACD,UACD;gBACA,8FAA8F;gBAC9F,oDAAoD;gBACpD,MAAMG,SAAShC,oBAAoBH,SAASnE,UAAUqE;gBAEtD,IAAI,CAACiC,UAAUtG,aAAa,OAAO;oBACjC,gFAAgF;oBAChF,OAAO;gBACT;gBAEA,OACEsG,UAAU;oBACR,sDAAsD;oBACtDtB,MAAM;gBACR;YAEJ;YACA,MAAMuB,WAAW,CAAC,wCAAwC,EAAEH,SAAS,GAAG,CAAC;YACzElH,MAAM,CAAC,sBAAsB,EAAEkH,SAAS,CAAC,CAAC;YAC1C,MAAMrG,kBAAkB,CAAC,OAAO,EAAEqG,UAAU;YAC5CvG,IAAAA,sDAAiC,EAACP,mBAAmBQ,gBAAgB,CACnEC,iBACAwG;YAEF,OAAO;gBACLvB,MAAM;gBACNC,UAAUlF;YACZ;QACF;QAEA,2BAA2B;QAC3B,SAASyG,uBACPrC,OAA0B,EAC1BE,UAAkB,EAClBrE,QAAuB;gBAWHmE;YATpB,uDAAuD;YACvD,IAAIE,WAAWqB,QAAQ,CAAC,kBAAkB;gBACxC,OAAO;YACT;YACA,4CAA4C;YAC5C,IAAI,kBAAkBhF,IAAI,CAACyD,QAAQ6B,gBAAgB,GAAG;gBACpD,OAAO;YACT;YAEA,MAAMT,eAAcpB,iCAAAA,QAAQiB,qBAAqB,qBAA7BjB,+BAA+BoB,WAAW;YAE9D,MAAMkB,gBAAgBxC,kBAAkBE,SAASnE;YAEjD,KAAK,MAAM0G,YAAYxB,UAAW;gBAChC,IAAIwB,SAASvB,KAAK,CAAChB,SAASE,YAAYrE,WAAW;oBACjD,IAAI0G,SAASrG,OAAO,KAAK,SAAS;wBAChCnB,MAAM,CAAC,sBAAsB,EAAEmF,WAAW,MAAM,EAAEqC,SAASrG,OAAO,CAAC,CAAC,CAAC;wBACrE,OAAO;4BACL2E,MAAM0B,SAASrG,OAAO;wBACxB;oBACF,OAAO,IAAIqG,SAASrG,OAAO,KAAK,QAAQ;wBACtC,sGAAsG;wBACtG,MAAMsG,aAAaF,cAAcpC;wBACjC,MAAMuC,WAAWD,WAAW3B,IAAI,KAAK,eAAe2B,WAAW1B,QAAQ,GAAGZ;wBAC1E,MAAMwC,WAAWjC,UAAUgC,UAAU;4BACnC5G,UAAUA;4BACVuF;wBACF;wBAEA,MAAMgB,WACJ,OAAOM,aAAa,WAChB,CAAC,iBAAiB,EAAExC,WAAW,MAAM,EAAEwC,SAAS,CAAC,CAAC,GAClD,CAAC,iBAAiB,EAAExC,WAAW,MAAM,EAAEyC,KAAKC,SAAS,CAACF,UAAU,CAAC,CAAC;wBACxE,gGAAgG;wBAChG,wDAAwD;wBACxD,MAAM9G,kBAAkB,CAAC,OAAO,EAAE8G,UAAU;wBAC5C3H,MAAM,wBAAwBmF,YAAY,MAAMtE;wBAChDF,IAAAA,sDAAiC,EAACP,mBAAmBQ,gBAAgB,CACnEC,iBACAwG;wBAEF,OAAO;4BACLvB,MAAM;4BACNC,UAAUlF;wBACZ;oBACF,OAAO,IAAI2G,SAASrG,OAAO,KAAK,QAAQ;wBACtC,MAAMkG,WAAW,CAAC,mCAAmC,EAAElC,WAAW,EAAE,CAAC;wBACrE,MAAMtE,kBAAkB,CAAC,OAAO,EAAEsE,YAAY;wBAC9CnF,MAAM,kCAAkCmF,YAAY,MAAMtE;wBAC1DF,IAAAA,sDAAiC,EAACP,mBAAmBQ,gBAAgB,CACnEC,iBACAwG;wBAEF,OAAO;4BACLvB,MAAM;4BACNC,UAAUlF;wBACZ;oBACF,OAAO;wBACL,MAAM,IAAIiH,oBAAY,CACpB,CAAC,8BAA8B,EAAEN,SAASrG,OAAO,CAAC,cAAc,EAAEgE,WAAW,aAAa,EAAErE,SAAS,oBAAoB,EAAEmE,QAAQ6B,gBAAgB,CAAC,CAAC,CAAC;oBAE1J;gBACF;YACF;YACA,OAAO;QACT;QAEA,yBAAyB;QACzB,SAASiB,aAAa9C,OAA0B,EAAEE,UAAkB,EAAErE,QAAuB;YAC3F,qEAAqE;YACrE,yDAAyD;YACzD,IAAIA,YAAYA,YAAYoC,WAAWA,OAAO,CAACpC,SAAS,CAACqE,WAAW,EAAE;gBACpE,MAAM6C,uBAAuB9E,OAAO,CAACpC,SAAS,CAACqE,WAAW;gBAC1D,OAAOJ,kBAAkBE,SAASnE,UAAUkH;YAC9C;YAEA,KAAK,MAAM,CAACC,SAASC,MAAM,IAAI1E,sBAAuB;gBACpD,MAAMyC,QAAQd,WAAWc,KAAK,CAACgC;gBAC/B,IAAIhC,OAAO;oBACT,MAAMkC,gBAAgBD,MAAM/G,OAAO,CACjC,YACA,CAACiH,GAAGxG,QAAUqE,KAAK,CAACoC,SAASzG,OAAO,IAAI,IAAI;oBAE9C,MAAMsD,YAAYH,kBAAkBE,SAASnE;oBAC7Cd,MAAM,CAAC,OAAO,EAAEmF,WAAW,MAAM,EAAEgD,cAAc,CAAC,CAAC;oBACnD,OAAOjD,UAAUiD;gBACnB;YACF;YAEA,OAAO;QACT;QAEA,8BAA8B;QAC9B,SAASG,2BACPrD,OAA0B,EAC1BE,UAAkB,EAClBrE,QAAuB;YAEvB,IAAI,oDAAoDU,IAAI,CAAC2D,aAAa;gBACxE,OAAOU;YACT;YAEA,IACE/E,aAAa,SACbmE,QAAQ6B,gBAAgB,CAACb,KAAK,CAAC,6CAC/Bd,WAAWoD,QAAQ,CAAC,2BACpB;gBACA,OAAO1C;YACT;YAEA,OAAO;QACT;QAEA,wDAAwD;QACxD,oCAAoC;QACpC,SAAS2C,oBACPvD,OAA0B,EAC1BE,UAAkB,EAClBrE,QAAuB;YAEvB,MAAMoE,YAAYH,kBAAkBE,SAASnE;YAE7C,MAAMsG,SAASlC,UAAUC;YAEzB,IAAIiC,OAAOtB,IAAI,KAAK,cAAc;gBAChC,OAAOsB;YACT;YAEA,IAAItG,aAAa,OAAO;gBACtB,IAAIsG,OAAOrB,QAAQ,CAACwC,QAAQ,CAAC,iBAAiB;oBAC5C,wDAAwD;oBACxD,IAAIpD,WAAWoD,QAAQ,CAAC,2DAA2D;wBACjF,MAAM,IAAIE,iDAAwB,CAChC,CAAC,8BAA8B,EAAEtD,WAAW,eAAe,EAAEF,QAAQ6B,gBAAgB,EAAE;oBAE3F;oBAEA,4BAA4B;oBAE5B,MAAM4B,aAAazH,iBAAiBmG,OAAOrB,QAAQ,CACjD,sDAAsD;qBACrD5E,OAAO,CAAC,oBAAoB;oBAE/B,MAAMwH,WAAWC,IAAAA,kCAAuB,EAACF;oBACzC,IAAIC,UAAU;wBACZ,MAAME,YAAY,CAAC,OAAO,EAAEH,YAAY;wBACxC,MAAMI,UAAUnI,IAAAA,sDAAiC,EAACP;wBAClD,IAAI,CAAC0I,QAAQC,gBAAgB,CAACF,YAAY;4BACxCC,QAAQlI,gBAAgB,CAACiI,WAAWG,aAAE,CAACC,YAAY,CAACN,UAAU;wBAChE;wBACA3I,MAAM,CAAC,oBAAoB,EAAEoH,OAAOrB,QAAQ,CAAC,SAAS,CAAC;wBAEvD,OAAO;4BACL,GAAGqB,MAAM;4BACTrB,UAAU8C;wBACZ;oBACF;gBACF;YACF,OAAO;oBAEH5D,gCACAA;gBAFF,MAAMgC,WACJhC,EAAAA,iCAAAA,QAAQiB,qBAAqB,qBAA7BjB,+BAA+BoB,WAAW,MAAK,UAC/CpB,EAAAA,kCAAAA,QAAQiB,qBAAqB,qBAA7BjB,gCAA+BoB,WAAW,MAAK;gBAEjD,6CAA6C;gBAC7C,MAAM6C,SAASjI,iBAAiBmG,OAAOrB,QAAQ;gBAE/C,0EAA0E;gBAC1E,IAAIkB,UAAU;oBACZ,IAAIiC,OAAO1C,QAAQ,CAAC,kDAAkD;wBACpExG,MAAM;wBACN,OAAO;4BACL8F,MAAM;wBACR;oBACF;gBACF;gBAEA,2FAA2F;gBAC3F,4EAA4E;gBAC5E,IAAI3D,wBAAwBiF,OAAOrB,QAAQ,CAACwC,QAAQ,CAAC,iBAAiB;oBACpE,MAAMG,aAAazH,iBAAiBmG,OAAOrB,QAAQ,CACjD,sDAAsD;qBACrD5E,OAAO,CAAC,oBAAoB;oBAE/B,MAAMgI,aAAaC,IAAAA,oCAAyB,EAACV;oBAC7C,IAAIS,YAAY;wBACdnJ,MAAM,CAAC,iCAAiC,EAAEoH,OAAOrB,QAAQ,CAAC,iBAAiB,CAAC;wBAC5E,OAAO;4BACL,GAAGqB,MAAM;4BACTrB,UAAUoD;wBACZ;oBACF;gBACF;YACF;YAEA,OAAO/B;QACT;QAEA,wGAAwG;QACxG,6FAA6F;QAC7FiC,IAAAA,wDAA4B,EAAC;YAC3B1F,aAAaxD,OAAOwD,WAAW;YAC/B2F,mBAAmB;gBAAC;gBAAQ;aAAc;YAC1CvE;QACF;KACD;IAED,qGAAqG;IACrG,MAAMwE,+BAA+BC,IAAAA,mDAA+B,EAClE9C,+BACA,CACE+C,kBACAtE,YACArE;YAmBwBmE;QAjBxB,MAAMA,UAA4C;YAChD,GAAGwE,gBAAgB;YACnBC,sBAAsB5I,aAAa;QACrC;QAEA,qEAAqE;QACrE,IACEqB,wBACA,sFAAsF;QACtF,8CAA8CX,IAAI,CAAC2D,aACnD;YACA,mGAAmG;YACnGF,QAAQ6B,gBAAgB,GAAG1D;YAC3B,yDAAyD;YACzD6B,QAAQ0E,yBAAyB,GAAG;QACtC;QAEA,IAAIvD,IAAAA,iCAAmB,GAACnB,iCAAAA,QAAQiB,qBAAqB,qBAA7BjB,+BAA+BoB,WAAW,GAAG;gBAWjEpB,iCAyBEA;YAnCJ,qFAAqF;YACrF,IAAIxD,2BAA2B,MAAM;gBACnCA,yBAAyB9B,oBAAoBsF,QAAQ2E,UAAU;YACjE;YACA3E,QAAQ2E,UAAU,GAAGnI;YAErBwD,QAAQ4E,6BAA6B,GAAG;YACxC5E,QAAQ6E,6BAA6B,GAAG,CAAC;YAEzC,MAAMC,0BACJ9E,EAAAA,kCAAAA,QAAQiB,qBAAqB,qBAA7BjB,gCAA+BoB,WAAW,MAAK;YAEjD,IAAI0D,yBAAyB;gBAC3B,uIAAuI;gBACvI,qGAAqG;gBACrG,IAAIjJ,aAAa,OAAO;oBACtB,gEAAgE;oBAChE,yEAAyE;oBACzEmE,QAAQ+E,UAAU,GAAG;wBAAC;wBAAU;qBAAO;gBACzC,OAAO;oBACL,qDAAqD;oBACrD/E,QAAQ+E,UAAU,GAAG;wBAAC;wBAAgB;wBAAU;qBAAO;gBACzD;YACF,OAAO;gBACL,IAAIlJ,aAAa,OAAO;oBACtB,gEAAgE;oBAChE,yEAAyE;oBACzEmE,QAAQ+E,UAAU,GAAG;wBAAC;wBAAQ;qBAAS;gBACzC,OAAO;oBACL,qDAAqD;oBACrD/E,QAAQ+E,UAAU,GAAG;wBAAC;wBAAgB;wBAAQ;qBAAS;gBACzD;YACF;YAEA,yCAAyC;YACzC,IAAI/E,EAAAA,kCAAAA,QAAQiB,qBAAqB,qBAA7BjB,gCAA+BoB,WAAW,MAAK,gBAAgB;gBACjEpB,QAAQgF,uBAAuB,GAAG;oBAAC;oBAAQ;oBAAgB;iBAAU;YACvE,OAAO;gBACLhF,QAAQgF,uBAAuB,GAAG;oBAAC;iBAAO;YAC5C;QACF,OAAO;YACL,qBAAqB;YAErB,IAAI,CAACC,QAAG,CAACC,iCAAiC,IAAIrJ,YAAYA,YAAY+C,qBAAqB;gBACzFoB,QAAQ+E,UAAU,GAAGnG,mBAAmB,CAAC/C,SAAS;YACpD;QACF;QAEA,OAAOmE;IACT;IAGF,OAAOmF,IAAAA,mDAA+B,EAACb;AACzC;AAGO,SAAS3J,kBACdyK,KAGC,EACDnC,KAA2C;QAIzCmC,eACOA;IAHT,OACEA,MAAMvJ,QAAQ,KAAKoH,MAAMpH,QAAQ,IACjCuJ,EAAAA,gBAAAA,MAAMjD,MAAM,qBAAZiD,cAAcvE,IAAI,MAAK,gBACvB,SAAOuE,iBAAAA,MAAMjD,MAAM,qBAAZiD,eAActE,QAAQ,MAAK,YAClC9E,iBAAiBoJ,MAAMjD,MAAM,CAACrB,QAAQ,EAAES,QAAQ,CAAC0B,MAAMoC,MAAM;AAEjE;AAGO,eAAexK,4BACpB6D,WAAmB,EACnB,EACExD,MAAM,EACNoK,GAAG,EACHC,gBAAgB,EAChBxI,sBAAsB,EACtBC,qBAAqB,EACrBC,WAAW,EACXC,oBAAoB,EACpBsI,sBAAsB,EACtBrI,8BAA8B,EAC9BhC,eAAe,EAYhB;IAED,IAAIqK,wBAAwB;QAC1BzK,MAAM;QACN,mFAAmF;QACnFC,QAAQ,sCAAsCyK,YAAY,GAAGzK,QAAQc,OAAO,CAC1E;IAEJ;IAEA,IAAI,CAACZ,OAAOwD,WAAW,EAAE;QACvB,oCAAoC;QACpCxD,OAAOwD,WAAW,GAAGA;IACvB;IAEA,sEAAsE;IACtE2C,QAAQ4D,GAAG,CAACS,wBAAwB,GAAGrE,QAAQ4D,GAAG,CAACS,wBAAwB,IAAIhH;IAE/E,0FAA0F;IAC1F,IAAI,CAACiH,cAAcC,WAAWlH,cAAc;QAC1C,IAAI,CAACxD,OAAO2K,YAAY,EAAE;YACxB,6CAA6C;YAC7C3K,OAAO2K,YAAY,GAAG,EAAE;QAC1B;QACA,6CAA6C;QAC7C3K,OAAO2K,YAAY,CAAClH,IAAI,CAACP,eAAI,CAACC,IAAI,CAACrD,QAAQc,OAAO,CAAC,+BAA+B;QAClF,6CAA6C;QAC7CZ,OAAO2K,YAAY,CAAClH,IAAI,CACtBP,eAAI,CAACC,IAAI,CAACrD,QAAQc,OAAO,CAAC,oCAAoC,UAC9D,sBAAsB;QACtBsC,eAAI,CAACC,IAAI,CAACrD,QAAQc,OAAO,CAAC,sBAAsB;QAElD,IAAIoB,sBAAsB;YACxB,6CAA6C;YAC7ChC,OAAO2K,YAAY,CAAClH,IAAI,CAACP,eAAI,CAACC,IAAI,CAACrD,QAAQc,OAAO,CAAC,2BAA2B;QAChF;IACF;IAEA,oBAAoB;IACpB,2FAA2F;IAC3FZ,OAAO4K,WAAW,CAACC,eAAe,GAAGvH,sBAAW,CAACC,MAAM,CAACC,aAAa;IAErE,IAAI5B,WAAiC;IAErC,IAAIC,wBAAwB;QAC1BD,WAAW,MAAMwC,IAAAA,yCAAsB,EAACZ;IAC1C;IAEA,IAAIsH,sBAAsBvG,OAAOwG,OAAO,CAACV,kBACtClJ,MAAM,CACL,CAAC,CAACR,UAAUgI,QAAQ;YAA4ByB;eAAvBzB,YAAY,aAAWyB,iBAAAA,IAAIY,SAAS,qBAAbZ,eAAehC,QAAQ,CAACzH;OAEzEsK,GAAG,CAAC,CAAC,CAACtK,SAAS,GAAKA;IAEvB,IAAIkC,MAAMC,OAAO,CAAC9C,OAAOyC,QAAQ,CAACuI,SAAS,GAAG;QAC5CF,sBAAsB;eAAI,IAAII,IAAIJ,oBAAoBK,MAAM,CAACnL,OAAOyC,QAAQ,CAACuI,SAAS;SAAG;IAC3F;IAEA,yCAAyC;IACzChL,OAAOyC,QAAQ,CAACuI,SAAS,GAAGF;IAE5B9K,SAASD,iBAAiBC,QAAQ;QAAEC;IAAgB;IAEpD,OAAOP,qBAAqBM,QAAQ;QAClC4B;QACAG;QACAF;QACAC;QACAE;QACAC;QACAhC;IACF;AACF;AAEA,SAASwK,cAAcW,UAAkB,EAAEC,QAAgB;IACzD,OAAOD,WAAWE,UAAU,CAACD,aAAaD,WAAW3G,MAAM,IAAI4G,SAAS5G,MAAM;AAChF"}