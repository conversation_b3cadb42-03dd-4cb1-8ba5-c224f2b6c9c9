// Copyright 2018-present 650 Industries. All rights reserved.

@protocol EXMagnetometerUncalibratedInterface

- (void)sensorModuleDidSubscribeForMagnetometerUncalibratedUpdates:(id)scopedSensorModule
                                                       with<PERSON>andler:(void (^)(NSDictionary *event))handlerBlock;
- (void)sensorModuleDidUnsubscribeForMagnetometerUncalibratedUpdates:(id)scopedSensorModule;
- (void)setMagnetometerUncalibratedUpdateInterval:(NSTimeInterval)intervalMs;
- (BOOL)isMagnetometerUncalibratedAvailable;

@end
