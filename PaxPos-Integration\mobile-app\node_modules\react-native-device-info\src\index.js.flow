// @flow

export type DeviceType = 'Handset' | 'Tablet' | 'Tv' | 'Desktop' | 'unknown';

export type BatteryState = 'unknown' | 'unplugged' | 'charging' | 'full';

export type PowerState = {
  batteryLevel: number,
  batteryState: BatteryState,
  lowPowerMode: boolean,
  [key: string]: any,
};

export type LocationProviderInfo = {
  [key: string]: boolean,
};

export type AsyncHookResult<T> = {
  loading: boolean,
  result: T,
};

// ExposedNativeMethods + DeviceInfoModule
declare module.exports: {
  getAndroidId: () => Promise<string>,
  getAndroidIdSync: () => string,
  getApiLevel: () => Promise<number>,
  getApiLevelSync: () => number,
  getApplicationName: () => string,
  getAvailableLocationProviders: () => Promise<LocationProviderInfo>,
  getAvailableLocationProvidersSync: () => LocationProviderInfo,
  getBaseOs: () => Promise<string>,
  getBaseOsSync: () => string,
  getBatteryLevel: () => Promise<number>,
  getBatteryLevelSync: () => number,
  getBootloader: () => Promise<string>,
  getBootloaderSync: () => string,
  getBrand: () => string,
  getBuildId: () => Promise<string>,
  getBuildIdSync: () => string,
  getBuildNumber: () => string,
  getBundleId: () => string,
  getCarrier: () => Promise<string>,
  getCarrierSync: () => string,
  getCodename: () => Promise<string>,
  getCodenameSync: () => string,
  getDevice: () => Promise<string>,
  getDeviceId: () => string,
  getDeviceName: () => Promise<string>,
  getDeviceNameSync: () => string,
  getDeviceSync: () => string,
  getDeviceType: () => string,
  getDisplay: () => Promise<string>,
  getDisplaySync: () => string,
  getFingerprint: () => Promise<string>,
  getFingerprintSync: () => string,
  getFirstInstallTime: () => Promise<number>,
  getFirstInstallTimeSync: () => number,
  getFontScale: () => Promise<number>,
  getFontScaleSync: () => number,
  getFreeDiskStorage: () => Promise<number>,
  getFreeDiskStorageOld: () => Promise<number>,
  getFreeDiskStorageSync: () => number,
  getFreeDiskStorageOldSync: () => number,
  getHardware: () => Promise<string>,
  getHardwareSync: () => string,
  getHost: () => Promise<string>,
  getHostSync: () => string,
  getHostNames: () => Promise<string[]>;
  getHostNamesSync: () => string[];
  getIncremental: () => Promise<string>,
  getIncrementalSync: () => string,
  getInstallerPackageName: () => Promise<string>,
  getInstallerPackageNameSync: () => string,
  getInstallReferrer: () => Promise<string>,
  getInstallReferrerSync: () => string,
  getInstanceId: () => Promise<string>,
  getInstanceIdSync: () => string,
  getIpAddress: () => Promise<string>,
  getIpAddressSync: () => string,
  getLastUpdateTime: () => Promise<number>,
  getLastUpdateTimeSync: () => number,
  getMacAddress: () => Promise<string>,
  getMacAddressSync: () => string,
  getManufacturer: () => Promise<string>,
  getManufacturerSync: () => string,
  getMaxMemory: () => Promise<number>,
  getMaxMemorySync: () => number,
  getModel: () => string,
  getPhoneNumber: () => Promise<string>,
  getPhoneNumberSync: () => string,
  getPowerState: () => Promise<PowerState | {}>,
  getPowerStateSync: () => PowerState | {},
  getPreviewSdkInt: () => Promise<number>,
  getPreviewSdkIntSync: () => number,
  getProduct: () => Promise<string>,
  getProductSync: () => string,
  getReadableVersion: () => string,
  getSecurityPatch: () => Promise<string>,
  getSecurityPatchSync: () => string,
  getSerialNumber: () => Promise<string>,
  getSerialNumberSync: () => string,
  getSystemAvailableFeatures: () => Promise<string[]>,
  getSystemAvailableFeaturesSync: () => string[],
  getSystemName: () => string,
  getSystemVersion: () => string,
  getTags: () => Promise<string>,
  getTagsSync: () => string,
  getTotalDiskCapacity: () => Promise<number>,
  getTotalDiskCapacityOld: () => Promise<number>,
  getTotalDiskCapacitySync: () => number,
  getTotalDiskCapacityOldSync: () => number,
  getTotalMemory: () => Promise<number>,
  getTotalMemorySync: () => number,
  getType: () => Promise<string>,
  getTypeSync: () => string,
  getUniqueId: () => Promise<string>,
  getUniqueIdSync: () => string,
  getUsedMemory: () => Promise<number>,
  getUsedMemorySync: () => number,
  getUserAgent: () => Promise<string>,
  getUserAgentSync: () => string,
  getVersion: () => string,
  getBrightness: () => Promise<number>,
  getBrightnessSync: () => number,
  hasNotch: () => boolean,
  hasDynamicIsland: () => boolean,
  hasSystemFeature: (feature: string) => Promise<boolean>,
  hasSystemFeature: (feature: string) => Promise<boolean>,
  hasSystemFeatureSync: (feature: string) => boolean,
  hasSystemFeatureSync: (feature: string) => boolean,
  hasGms: () => Promise<boolean>,
  hasGmsSync: () => boolean,
  hasHms: () => Promise<boolean>,
  hasHmsSync: () => boolean,
  isAirplaneMode: () => Promise<boolean>,
  isAirplaneModeSync: () => boolean,
  isBatteryCharging: () => Promise<boolean>,
  isBatteryChargingSync: () => boolean,
  isCameraPresent: () => Promise<boolean>,
  isCameraPresentSync: () => boolean,
  isEmulator: () => Promise<boolean>,
  isEmulatorSync: () => boolean,
  isHeadphonesConnected: () => Promise<boolean>,
  isHeadphonesConnectedSync: () => boolean,
  isWiredHeadphonesConnected: () => Promise<boolean>,
  isWiredHeadphonesConnectedSync: () => boolean,
  isBluetoothHeadphonesConnected: () => Promise<boolean>,
  isBluetoothHeadphonesConnectedSync: () => boolean,
  isLandscape: () => Promise<boolean>,
  isLandscapeSync: () => boolean,
  isLocationEnabled: () => Promise<boolean>,
  isLocationEnabledSync: () => boolean,
  isPinOrFingerprintSet: () => Promise<boolean>,
  isPinOrFingerprintSetSync: () => boolean,
  isMouseConnected: () => Promise<boolean>,
  isMouseConnectedSync: () => boolean,
  isKeyboardConnected: () => Promise<boolean>,
  isKeyboardConnectedSync: () => boolean,
  isTabletMode: () => Promise<boolean>,
  isTablet: () => boolean,
  isLowRamDevice: () => boolean,
  isDisplayZoomed: () => boolean,
  supported32BitAbis: () => Promise<string[]>,
  supported32BitAbisSync: () => string[],
  supported64BitAbis: () => Promise<string[]>,
  supported64BitAbisSync: () => string[],
  supportedAbis: () => Promise<string[]>,
  supportedAbisSync: () => string[],
  syncUniqueId: () => Promise<string>,
  useBatteryLevel: () => number | null,
  useBatteryLevelIsLow: () => number | null,
  useDeviceName: () => AsyncHookResult<string>,
  useFirstInstallTime: () => AsyncHookResult<number>,
  useHasSystemFeature: (feature: string) => AsyncHookResult<boolean>,
  useIsEmulator: () => AsyncHookResult<boolean>,
  usePowerState: () => PowerState | {},
  useManufacturer: () => AsyncHookResult<string>,
  useIsHeadphonesConnected: () => AsyncHookResult<boolean>,
  useIsWiredHeadphonesConnected: () => AsyncHookResult<boolean>,
  useIsBluetoothHeadphonesConnected: () => AsyncHookResult<boolean>,
  useBrightness: () => number | null,
};
