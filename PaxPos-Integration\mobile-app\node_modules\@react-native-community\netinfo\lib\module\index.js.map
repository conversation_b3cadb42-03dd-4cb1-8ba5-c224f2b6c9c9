{"version": 3, "sources": ["index.ts"], "names": ["useState", "useEffect", "useCallback", "Platform", "DEFAULT_CONFIGURATION", "NativeInterface", "State", "Types", "_configuration", "_state", "createState", "isRequestInProgress", "requestQueue", "configure", "configuration", "tearDown", "OS", "fetch", "requestedInterface", "latest", "refresh", "Promise", "resolve", "push", "_fetchCurrentState", "then", "result", "for<PERSON>ach", "finally", "addEventListener", "listener", "add", "remove", "useNetInfo", "netInfo", "setNetInfo", "type", "NetInfoStateType", "unknown", "isConnected", "isInternetReachable", "details", "unsubscribe", "useNetInfoInstance", "isPaused", "networkInfoManager", "setNetworkInfoManager", "config", "state"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,SAAQA,QAAR,EAAkBC,SAAlB,EAA6BC,WAA7B,QAA+C,OAA/C;AACA,SAAQC,QAAR,QAAuB,cAAvB;AACA,OAAOC,qBAAP,MAAkC,iCAAlC;AACA,OAAOC,eAAP,MAA4B,4BAA5B;AACA,OAAOC,KAAP,MAAkB,kBAAlB;AACA,OAAO,KAAKC,KAAZ,MAAuB,kBAAvB,C,CAEA;;AACA,IAAIC,cAAc,GAAGJ,qBAArB,C,CAEA;;AACA,IAAIK,MAAoB,GAAG,IAA3B;;AACA,MAAMC,WAAW,GAAG,MAAa;AAC/B,SAAO,IAAIJ,KAAJ,CAAUE,cAAV,CAAP;AACD,CAFD,C,CAIA;;;AACA,IAAIG,mBAAmB,GAAG,KAA1B;AACA,IAAIC,YAAqD,GAAG,EAA5D;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,SAAT,CACLC,aADK,EAEC;AACNN,EAAAA,cAAc,GAAG,EACf,GAAGJ,qBADY;AAEf,OAAGU;AAFY,GAAjB;;AAKA,MAAIL,MAAJ,EAAY;AACVA,IAAAA,MAAM,CAACM,QAAP;;AACAN,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AAED,MAAIP,QAAQ,CAACa,EAAT,KAAgB,KAApB,EAA2B;AACzBX,IAAAA,eAAe,CAACQ,SAAhB,CAA0BC,aAA1B;AACD;AACF;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASG,KAAT,CACLC,kBADK,EAEwB;AAC7B,MAAI,CAACT,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AACD,SAAOD,MAAM,CAACU,MAAP,CAAcD,kBAAd,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASE,OAAT,GAAgD;AACrD,MAAI,CAACX,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD,GAHoD,CAKtD;;;AACC,MAAIC,mBAAJ,EAAyB;AACvB,WAAO,IAAIU,OAAJ,CAAaC,OAAD,IAAa;AAC9BV,MAAAA,YAAY,CAACW,IAAb,CAAkBD,OAAlB;AACD,KAFM,CAAP;AAGD;;AAEDX,EAAAA,mBAAmB,GAAG,IAAtB;AAEA,SAAOF,MAAM,CAACe,kBAAP,GAA4BC,IAA5B,CAAkCC,MAAD,IAAY;AAClDd,IAAAA,YAAY,CAACe,OAAb,CAAsBL,OAAD,IAAaA,OAAO,CAACI,MAAD,CAAzC;AACAd,IAAAA,YAAY,GAAG,EAAf;AACA,WAAOc,MAAP;AACD,GAJM,EAIJE,OAJI,CAII,MAAM;AACfjB,IAAAA,mBAAmB,GAAG,KAAtB;AACD,GANM,CAAP;AAOD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASkB,gBAAT,CACLC,QADK,EAEsB;AAC3B,MAAI,CAACrB,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAGC,WAAW,EAApB;AACD;;AAEDD,EAAAA,MAAM,CAACsB,GAAP,CAAWD,QAAX;;AACA,SAAO,MAAY;AACjBrB,IAAAA,MAAM,IAAIA,MAAM,CAACuB,MAAP,CAAcF,QAAd,CAAV;AACD,GAFD;AAGD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASG,UAAT,CACLnB,aADK,EAEe;AACpB,MAAIA,aAAJ,EAAmB;AACjBD,IAAAA,SAAS,CAACC,aAAD,CAAT;AACD;;AAED,QAAM,CAACoB,OAAD,EAAUC,UAAV,IAAwBnC,QAAQ,CAAqB;AACzDoC,IAAAA,IAAI,EAAE7B,KAAK,CAAC8B,gBAAN,CAAuBC,OAD4B;AAEzDC,IAAAA,WAAW,EAAE,IAF4C;AAGzDC,IAAAA,mBAAmB,EAAE,IAHoC;AAIzDC,IAAAA,OAAO,EAAE;AAJgD,GAArB,CAAtC;AAOAxC,EAAAA,SAAS,CAAC,MAAoB;AAC5B,UAAMyC,WAAW,GAAGb,gBAAgB,CAACM,UAAD,CAApC;AACA,WAAO,MAAMO,WAAW,EAAxB;AACD,GAHQ,EAGN,EAHM,CAAT;AAKA,SAAOR,OAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASS,kBAAT,CACLC,QAAQ,GAAG,KADN,EAEL9B,aAFK,EAGL;AACA,QAAM,CAAC+B,kBAAD,EAAqBC,qBAArB,IAA8C9C,QAAQ,EAA5D;AACA,QAAM,CAACkC,OAAD,EAAUC,UAAV,IAAwBnC,QAAQ,CAAqB;AACzDoC,IAAAA,IAAI,EAAE7B,KAAK,CAAC8B,gBAAN,CAAuBC,OAD4B;AAEzDC,IAAAA,WAAW,EAAE,IAF4C;AAGzDC,IAAAA,mBAAmB,EAAE,IAHoC;AAIzDC,IAAAA,OAAO,EAAE;AAJgD,GAArB,CAAtC;AAOAxC,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI2C,QAAJ,EAAc;AACZ;AACD;;AACD,UAAMG,MAAM,GAAG,EACb,GAAG3C,qBADU;AAEb,SAAGU;AAFU,KAAf;AAIA,UAAMkC,KAAK,GAAG,IAAI1C,KAAJ,CAAUyC,MAAV,CAAd;AACAD,IAAAA,qBAAqB,CAACE,KAAD,CAArB;AACAA,IAAAA,KAAK,CAACjB,GAAN,CAAUI,UAAV;AACA,WAAOa,KAAK,CAACjC,QAAb;AACD,GAZQ,EAYN,CAAC6B,QAAD,EAAW9B,aAAX,CAZM,CAAT;AAcA,QAAMM,OAAO,GAAGlB,WAAW,CAAC,MAAM;AAChC,QAAI2C,kBAAkB,IAAI,CAAClC,mBAA3B,EAAgD;AAC9CA,MAAAA,mBAAmB,GAAG,IAAtB;;AACAkC,MAAAA,kBAAkB,CAACrB,kBAAnB,GAAwCI,OAAxC,CAAgD,MAAM;AACpDjB,QAAAA,mBAAmB,GAAG,KAAtB;AACD,OAFD;AAGD;AACF,GAP0B,EAOxB,CAACkC,kBAAD,CAPwB,CAA3B;AASA,SAAO;AACLX,IAAAA,OADK;AAELd,IAAAA;AAFK,GAAP;AAID;AAED,cAAc,kBAAd;AAEA,eAAe;AACbP,EAAAA,SADa;AAEbI,EAAAA,KAFa;AAGbG,EAAAA,OAHa;AAIbS,EAAAA,gBAJa;AAKbI,EAAAA,UALa;AAMbU,EAAAA;AANa,CAAf", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {useState, useEffect, useCallback} from 'react';\nimport {Platform} from 'react-native';\nimport DEFAULT_CONFIGURATION from './internal/defaultConfiguration';\nimport NativeInterface from './internal/nativeInterface';\nimport State from './internal/state';\nimport * as Types from './internal/types';\n\n// Stores the currently used configuration\nlet _configuration = DEFAULT_CONFIGURATION;\n\n// Stores the singleton reference to the state manager\nlet _state: State | null = null;\nconst createState = (): State => {\n  return new State(_configuration);\n};\n\n// Track ongoing requests\nlet isRequestInProgress = false;\nlet requestQueue: ((state: Types.NetInfoState) => void)[] = [];\n\n/**\n * Configures the library with the given configuration. Note that calling this will stop all\n * previously added listeners from being called again. It is best to call this right when your\n * application is started to avoid issues. The configuration sets up a global singleton instance.\n *\n * @param configuration The new configuration to set.\n */\nexport function configure(\n  configuration: Partial<Types.NetInfoConfiguration>,\n): void {\n  _configuration = {\n    ...DEFAULT_CONFIGURATION,\n    ...configuration,\n  };\n\n  if (_state) {\n    _state.tearDown();\n    _state = createState();\n  }\n\n  if (Platform.OS === 'ios') {\n    NativeInterface.configure(configuration);\n  }\n}\n\n/**\n * Returns a `Promise` that resolves to a `NetInfoState` object.\n * This function operates on the global singleton instance configured using `configure()`\n *\n * @param [requestedInterface] interface from which to obtain the information\n *\n * @returns A Promise which contains the current connection state.\n */\nexport function fetch(\n  requestedInterface?: string,\n): Promise<Types.NetInfoState> {\n  if (!_state) {\n    _state = createState();\n  }\n  return _state.latest(requestedInterface);\n}\n\n/**\n * Force-refreshes the internal state of the global singleton managed by this library.\n *\n * @returns A Promise which contains the updated connection state.\n */\nexport function refresh(): Promise<Types.NetInfoState> {\n  if (!_state) {\n    _state = createState();\n  }\n\n // If a request is already in progress, return a promise that will resolve when the current request finishes\n  if (isRequestInProgress) {\n    return new Promise((resolve) => {\n      requestQueue.push(resolve);\n    });\n  }\n\n  isRequestInProgress = true;\n\n  return _state._fetchCurrentState().then((result) => {\n    requestQueue.forEach((resolve) => resolve(result));\n    requestQueue = [];\n    return result;\n  }).finally(() => {\n    isRequestInProgress = false;\n  });\n}\n\n/**\n * Subscribe to the global singleton's connection information. The callback is called with a parameter of type\n * [`NetInfoState`](README.md#netinfostate) whenever the connection state changes. Your listener\n * will be called with the latest information soon after you subscribe and then with any\n * subsequent changes afterwards. You should not assume that the listener is called in the same\n * way across devices or platforms.\n *\n * @param listener The listener which is called when the network state changes.\n *\n * @returns A function which can be called to unsubscribe.\n */\nexport function addEventListener(\n  listener: Types.NetInfoChangeHandler,\n): Types.NetInfoSubscription {\n  if (!_state) {\n    _state = createState();\n  }\n\n  _state.add(listener);\n  return (): void => {\n    _state && _state.remove(listener);\n  };\n}\n\n/**\n * A React Hook into this library's singleton which updates when the connection state changes.\n *\n * @param {Partial<Types.NetInfoConfiguration>} configuration - Configure the isolated network checker managed by this hook\n *\n * @returns The connection state.\n */\nexport function useNetInfo(\n  configuration?: Partial<Types.NetInfoConfiguration>,\n): Types.NetInfoState {\n  if (configuration) {\n    configure(configuration);\n  }\n\n  const [netInfo, setNetInfo] = useState<Types.NetInfoState>({\n    type: Types.NetInfoStateType.unknown,\n    isConnected: null,\n    isInternetReachable: null,\n    details: null,\n  });\n\n  useEffect((): (() => void) => {\n    const unsubscribe = addEventListener(setNetInfo);\n    return () => unsubscribe();\n  }, []);\n\n  return netInfo;\n}\n\n/**\n * A React Hook which manages an isolated instance of the network info manager.\n * This is not a hook into a singleton shared state. NetInfo.configure, NetInfo.addEventListener,\n * NetInfo.fetch, NetInfo.refresh are performed on a global singleton and have no affect on this hook.\n * @param {boolean} isPaused - Pause the internal network checks.\n * @param {Partial<Types.NetInfoConfiguration>} configuration - Configure the isolated network checker managed by this hook\n *\n * @returns the netInfo state and a refresh function\n */\nexport function useNetInfoInstance(\n  isPaused = false,\n  configuration?: Partial<Types.NetInfoConfiguration>,\n) {\n  const [networkInfoManager, setNetworkInfoManager] = useState<State>();\n  const [netInfo, setNetInfo] = useState<Types.NetInfoState>({\n    type: Types.NetInfoStateType.unknown,\n    isConnected: null,\n    isInternetReachable: null,\n    details: null,\n  });\n\n  useEffect(() => {\n    if (isPaused) {\n      return;\n    }\n    const config = {\n      ...DEFAULT_CONFIGURATION,\n      ...configuration,\n    };\n    const state = new State(config);\n    setNetworkInfoManager(state);\n    state.add(setNetInfo);\n    return state.tearDown;\n  }, [isPaused, configuration]);\n\n  const refresh = useCallback(() => {\n    if (networkInfoManager && !isRequestInProgress) {\n      isRequestInProgress = true;\n      networkInfoManager._fetchCurrentState().finally(() => {\n        isRequestInProgress = false;\n      });\n    }\n  }, [networkInfoManager]);\n\n  return {\n    netInfo,\n    refresh,\n  };\n}\n\nexport * from './internal/types';\n\nexport default {\n  configure,\n  fetch,\n  refresh,\n  addEventListener,\n  useNetInfo,\n  useNetInfoInstance,\n};\n"]}