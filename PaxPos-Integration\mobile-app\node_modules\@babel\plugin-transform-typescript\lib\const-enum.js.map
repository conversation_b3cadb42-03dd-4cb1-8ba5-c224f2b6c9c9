{"version": 3, "names": ["_enum", "require", "EXPORTED_CONST_ENUMS_IN_NAMESPACE", "exports", "WeakSet", "transpileConstEnum", "path", "t", "name", "node", "id", "parentIsExport", "parentPath", "isExportNamedDeclaration", "isExported", "isProgram", "parent", "body", "some", "stmt", "exportKind", "source", "specifiers", "spec", "isExportSpecifier", "local", "enum<PERSON><PERSON><PERSON>", "entries", "translateEnumValues", "has", "obj", "objectExpression", "map", "value", "objectProperty", "isValidIdentifier", "identifier", "stringLiteral", "scope", "hasOwnBinding", "replaceWith", "expressionStatement", "callExpression", "memberExpression", "variableDeclaration", "variableDeclarator", "registerDeclaration", "entriesMap", "Map", "traverse", "<PERSON><PERSON>", "skip", "MemberExpression", "isIdentifier", "object", "key", "computed", "isStringLiteral", "property", "cloneNode", "get", "remove"], "sources": ["../src/const-enum.ts"], "sourcesContent": ["import type { NodePath, types as t } from \"@babel/core\";\n\nimport { translateEnumValues } from \"./enum.ts\";\n\nexport const EXPORTED_CONST_ENUMS_IN_NAMESPACE =\n  new WeakSet<t.TSEnumDeclaration>();\n\nexport type NodePathConstEnum = NodePath<t.TSEnumDeclaration & { const: true }>;\nexport default function transpileConstEnum(\n  path: NodePathConstEnum,\n  t: typeof import(\"@babel/types\"),\n) {\n  const { name } = path.node.id;\n\n  const parentIsExport = path.parentPath.isExportNamedDeclaration();\n  let isExported = parentIsExport;\n  if (!isExported && t.isProgram(path.parent)) {\n    isExported = path.parent.body.some(\n      stmt =>\n        t.isExportNamedDeclaration(stmt) &&\n        stmt.exportKind !== \"type\" &&\n        !stmt.source &&\n        stmt.specifiers.some(\n          spec =>\n            t.isExportSpecifier(spec) &&\n            spec.exportKind !== \"type\" &&\n            spec.local.name === name,\n        ),\n    );\n  }\n\n  const { enumValues: entries } = translateEnumValues(path, t);\n\n  if (isExported || EXPORTED_CONST_ENUMS_IN_NAMESPACE.has(path.node)) {\n    const obj = t.objectExpression(\n      entries.map(([name, value]) =>\n        t.objectProperty(\n          t.isValidIdentifier(name)\n            ? t.identifier(name)\n            : t.stringLiteral(name),\n          value,\n        ),\n      ),\n    );\n\n    if (path.scope.hasOwnBinding(name)) {\n      (parentIsExport ? path.parentPath : path).replaceWith(\n        t.expressionStatement(\n          t.callExpression(\n            t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\")),\n            [path.node.id, obj],\n          ),\n        ),\n      );\n    } else {\n      path.replaceWith(\n        t.variableDeclaration(process.env.BABEL_8_BREAKING ? \"const\" : \"var\", [\n          t.variableDeclarator(path.node.id, obj),\n        ]),\n      );\n      path.scope.registerDeclaration(path);\n    }\n\n    return;\n  }\n\n  const entriesMap = new Map(entries);\n\n  // TODO: After fixing https://github.com/babel/babel/pull/11065, we can\n  // use path.scope.getBinding(name).referencePaths rather than doing\n  // a full traversal.\n  path.scope.path.traverse({\n    Scope(path) {\n      if (path.scope.hasOwnBinding(name)) path.skip();\n    },\n    MemberExpression(path) {\n      if (!t.isIdentifier(path.node.object, { name })) return;\n\n      let key: string;\n      if (path.node.computed) {\n        if (t.isStringLiteral(path.node.property)) {\n          key = path.node.property.value;\n        } else {\n          return;\n        }\n      } else if (t.isIdentifier(path.node.property)) {\n        key = path.node.property.name;\n      } else {\n        return;\n      }\n      if (!entriesMap.has(key)) return;\n\n      path.replaceWith(t.cloneNode(entriesMap.get(key)));\n    },\n  });\n\n  path.remove();\n}\n"], "mappings": ";;;;;;;AAEA,IAAAA,KAAA,GAAAC,OAAA;AAEO,MAAMC,iCAAiC,GAAAC,OAAA,CAAAD,iCAAA,GAC5C,IAAIE,OAAO,CAAsB,CAAC;AAGrB,SAASC,kBAAkBA,CACxCC,IAAuB,EACvBC,CAAgC,EAChC;EACA,MAAM;IAAEC;EAAK,CAAC,GAAGF,IAAI,CAACG,IAAI,CAACC,EAAE;EAE7B,MAAMC,cAAc,GAAGL,IAAI,CAACM,UAAU,CAACC,wBAAwB,CAAC,CAAC;EACjE,IAAIC,UAAU,GAAGH,cAAc;EAC/B,IAAI,CAACG,UAAU,IAAIP,CAAC,CAACQ,SAAS,CAACT,IAAI,CAACU,MAAM,CAAC,EAAE;IAC3CF,UAAU,GAAGR,IAAI,CAACU,MAAM,CAACC,IAAI,CAACC,IAAI,CAChCC,IAAI,IACFZ,CAAC,CAACM,wBAAwB,CAACM,IAAI,CAAC,IAChCA,IAAI,CAACC,UAAU,KAAK,MAAM,IAC1B,CAACD,IAAI,CAACE,MAAM,IACZF,IAAI,CAACG,UAAU,CAACJ,IAAI,CAClBK,IAAI,IACFhB,CAAC,CAACiB,iBAAiB,CAACD,IAAI,CAAC,IACzBA,IAAI,CAACH,UAAU,KAAK,MAAM,IAC1BG,IAAI,CAACE,KAAK,CAACjB,IAAI,KAAKA,IACxB,CACJ,CAAC;EACH;EAEA,MAAM;IAAEkB,UAAU,EAAEC;EAAQ,CAAC,GAAG,IAAAC,yBAAmB,EAACtB,IAAI,EAAEC,CAAC,CAAC;EAE5D,IAAIO,UAAU,IAAIZ,iCAAiC,CAAC2B,GAAG,CAACvB,IAAI,CAACG,IAAI,CAAC,EAAE;IAClE,MAAMqB,GAAG,GAAGvB,CAAC,CAACwB,gBAAgB,CAC5BJ,OAAO,CAACK,GAAG,CAAC,CAAC,CAACxB,IAAI,EAAEyB,KAAK,CAAC,KACxB1B,CAAC,CAAC2B,cAAc,CACd3B,CAAC,CAAC4B,iBAAiB,CAAC3B,IAAI,CAAC,GACrBD,CAAC,CAAC6B,UAAU,CAAC5B,IAAI,CAAC,GAClBD,CAAC,CAAC8B,aAAa,CAAC7B,IAAI,CAAC,EACzByB,KACF,CACF,CACF,CAAC;IAED,IAAI3B,IAAI,CAACgC,KAAK,CAACC,aAAa,CAAC/B,IAAI,CAAC,EAAE;MAClC,CAACG,cAAc,GAAGL,IAAI,CAACM,UAAU,GAAGN,IAAI,EAAEkC,WAAW,CACnDjC,CAAC,CAACkC,mBAAmB,CACnBlC,CAAC,CAACmC,cAAc,CACdnC,CAAC,CAACoC,gBAAgB,CAACpC,CAAC,CAAC6B,UAAU,CAAC,QAAQ,CAAC,EAAE7B,CAAC,CAAC6B,UAAU,CAAC,QAAQ,CAAC,CAAC,EAClE,CAAC9B,IAAI,CAACG,IAAI,CAACC,EAAE,EAAEoB,GAAG,CACpB,CACF,CACF,CAAC;IACH,CAAC,MAAM;MACLxB,IAAI,CAACkC,WAAW,CACdjC,CAAC,CAACqC,mBAAmB,CAA0C,KAAK,EAAE,CACpErC,CAAC,CAACsC,kBAAkB,CAACvC,IAAI,CAACG,IAAI,CAACC,EAAE,EAAEoB,GAAG,CAAC,CACxC,CACH,CAAC;MACDxB,IAAI,CAACgC,KAAK,CAACQ,mBAAmB,CAACxC,IAAI,CAAC;IACtC;IAEA;EACF;EAEA,MAAMyC,UAAU,GAAG,IAAIC,GAAG,CAACrB,OAAO,CAAC;EAKnCrB,IAAI,CAACgC,KAAK,CAAChC,IAAI,CAAC2C,QAAQ,CAAC;IACvBC,KAAKA,CAAC5C,IAAI,EAAE;MACV,IAAIA,IAAI,CAACgC,KAAK,CAACC,aAAa,CAAC/B,IAAI,CAAC,EAAEF,IAAI,CAAC6C,IAAI,CAAC,CAAC;IACjD,CAAC;IACDC,gBAAgBA,CAAC9C,IAAI,EAAE;MACrB,IAAI,CAACC,CAAC,CAAC8C,YAAY,CAAC/C,IAAI,CAACG,IAAI,CAAC6C,MAAM,EAAE;QAAE9C;MAAK,CAAC,CAAC,EAAE;MAEjD,IAAI+C,GAAW;MACf,IAAIjD,IAAI,CAACG,IAAI,CAAC+C,QAAQ,EAAE;QACtB,IAAIjD,CAAC,CAACkD,eAAe,CAACnD,IAAI,CAACG,IAAI,CAACiD,QAAQ,CAAC,EAAE;UACzCH,GAAG,GAAGjD,IAAI,CAACG,IAAI,CAACiD,QAAQ,CAACzB,KAAK;QAChC,CAAC,MAAM;UACL;QACF;MACF,CAAC,MAAM,IAAI1B,CAAC,CAAC8C,YAAY,CAAC/C,IAAI,CAACG,IAAI,CAACiD,QAAQ,CAAC,EAAE;QAC7CH,GAAG,GAAGjD,IAAI,CAACG,IAAI,CAACiD,QAAQ,CAAClD,IAAI;MAC/B,CAAC,MAAM;QACL;MACF;MACA,IAAI,CAACuC,UAAU,CAAClB,GAAG,CAAC0B,GAAG,CAAC,EAAE;MAE1BjD,IAAI,CAACkC,WAAW,CAACjC,CAAC,CAACoD,SAAS,CAACZ,UAAU,CAACa,GAAG,CAACL,GAAG,CAAC,CAAC,CAAC;IACpD;EACF,CAAC,CAAC;EAEFjD,IAAI,CAACuD,MAAM,CAAC,CAAC;AACf", "ignoreList": []}