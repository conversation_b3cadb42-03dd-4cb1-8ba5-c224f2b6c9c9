<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta http-equiv="Content-Security-Policy" content="default-src * 'unsafe-inline' 'unsafe-eval' data: gap:; style-src * 'unsafe-inline'; media-src *; img-src * data: content:;">
    <title>MultiPOS Mobile</title>
    
    <!-- Capacitor Core -->
    <script type="module" src="https://unpkg.com/@capacitor/core@latest/dist/capacitor.js"></script>
    
    <!-- Capacitor Plugins -->
    <script type="module">
        import { Capacitor } from 'https://unpkg.com/@capacitor/core@latest/dist/capacitor.js';
        import { SplashScreen } from 'https://unpkg.com/@capacitor/splash-screen@latest/dist/esm/index.js';
        import { StatusBar, Style } from 'https://unpkg.com/@capacitor/status-bar@latest/dist/esm/index.js';
        import { App } from 'https://unpkg.com/@capacitor/app@latest/dist/esm/index.js';
        import { Network } from 'https://unpkg.com/@capacitor/network@latest/dist/esm/index.js';
        
        window.CapacitorPlugins = { SplashScreen, StatusBar, App, Network };
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body, html {
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }
        
        .loading-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .loading-text {
            font-size: 18px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .iframe-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }
        
        .error-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #dc3545;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .error-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .error-text {
            font-size: 16px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading" class="loading-container">
        <div class="loading-title">🏭 MultiPOS Mobile</div>
        <div class="loading-text">Loading POS System...</div>
        <div class="spinner"></div>
    </div>
    
    <!-- Error Screen -->
    <div id="error" class="error-container hidden">
        <div class="error-title">⚠️ Connection Error</div>
        <div class="error-text" id="error-message">Failed to load the POS system</div>
        <button class="retry-button" onclick="retryLoad()">Retry</button>
    </div>
    
    <!-- Main Content -->
    <div id="content" class="iframe-container hidden">
        <iframe 
            id="main-iframe"
            src="https://multiposfrontend.vercel.app/"
            allow="camera; microphone; geolocation; payment; usb; serial; bluetooth; accelerometer; gyroscope"
            allowfullscreen
            loading="eager">
        </iframe>
    </div>

    <script>
        class MultiPOSMobile {
            constructor() {
                this.iframe = null;
                this.isLoaded = false;
                this.retryCount = 0;
                this.maxRetries = 3;
                this.loadTimeout = 15000; // 15 seconds
                
                this.init();
            }
            
            async init() {
                console.log('Initializing MultiPOS Mobile...');
                
                // Wait for Capacitor to be ready
                if (window.Capacitor) {
                    await this.initializeCapacitor();
                }
                
                // Initialize iframe
                this.iframe = document.getElementById('main-iframe');
                this.setupIframeListeners();
                
                // Start loading
                this.startLoading();
            }
            
            async initializeCapacitor() {
                try {
                    const { SplashScreen, StatusBar, App, Network } = window.CapacitorPlugins;
                    
                    // Configure status bar
                    if (StatusBar) {
                        await StatusBar.setStyle({ style: 'DARK' });
                        await StatusBar.setBackgroundColor({ color: '#ffffff' });
                    }
                    
                    // Check network status
                    if (Network) {
                        const status = await Network.getStatus();
                        if (!status.connected) {
                            this.showError('No internet connection. Please check your network settings.');
                            return;
                        }
                        
                        // Listen for network changes
                        Network.addListener('networkStatusChange', (status) => {
                            if (!status.connected) {
                                this.showError('Connection lost. Please check your network.');
                            } else if (!this.isLoaded) {
                                this.retryLoad();
                            }
                        });
                    }
                    
                    // Handle app state changes
                    if (App) {
                        App.addListener('appStateChange', ({ isActive }) => {
                            console.log('App state changed. Active:', isActive);
                        });
                        
                        App.addListener('backButton', () => {
                            if (this.iframe && this.iframe.contentWindow) {
                                this.iframe.contentWindow.history.back();
                            }
                        });
                    }
                    
                    console.log('Capacitor initialized successfully');
                } catch (error) {
                    console.error('Capacitor initialization error:', error);
                }
            }
            
            setupIframeListeners() {
                // Set loading timeout
                const loadingTimeout = setTimeout(() => {
                    if (!this.isLoaded) {
                        this.showError('Loading timeout. The POS system is taking too long to respond.');
                    }
                }, this.loadTimeout);
                
                this.iframe.onload = () => {
                    clearTimeout(loadingTimeout);
                    this.onLoadSuccess();
                };
                
                this.iframe.onerror = () => {
                    clearTimeout(loadingTimeout);
                    this.onLoadError('Failed to load the POS system');
                };
                
                // Listen for messages from iframe
                window.addEventListener('message', (event) => {
                    this.handleIframeMessage(event);
                });
            }
            
            handleIframeMessage(event) {
                try {
                    // Only accept messages from our domain
                    if (!event.origin.includes('multiposfrontend.vercel.app')) {
                        return;
                    }
                    
                    const message = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
                    
                    switch (message.type) {
                        case 'PAX_PRINT_REQUEST':
                            this.handlePrintRequest(message.data);
                            break;
                        case 'APP_READY':
                            console.log('POS app is ready');
                            break;
                        default:
                            console.log('Unknown message from iframe:', message);
                    }
                } catch (error) {
                    console.error('Error handling iframe message:', error);
                }
            }
            
            async handlePrintRequest(printData) {
                try {
                    console.log('Print request received:', printData);
                    
                    // Send print request to PAX bridge
                    const response = await fetch('http://localhost:3002/api/print', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(printData)
                    });
                    
                    const result = await response.json();
                    
                    // Send response back to iframe
                    this.iframe.contentWindow.postMessage({
                        type: 'PAX_PRINT_RESPONSE',
                        data: result
                    }, '*');
                    
                } catch (error) {
                    console.error('Print request error:', error);
                    
                    // Send error response back to iframe
                    this.iframe.contentWindow.postMessage({
                        type: 'PAX_PRINT_RESPONSE',
                        data: { success: false, error: error.message }
                    }, '*');
                }
            }
            
            startLoading() {
                document.getElementById('loading').classList.remove('hidden');
                document.getElementById('error').classList.add('hidden');
                document.getElementById('content').classList.add('hidden');
            }
            
            onLoadSuccess() {
                console.log('POS system loaded successfully');
                this.isLoaded = true;
                this.retryCount = 0;
                
                // Hide loading and show content
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('error').classList.add('hidden');
                document.getElementById('content').classList.remove('hidden');
                
                // Hide splash screen if available
                if (window.CapacitorPlugins && window.CapacitorPlugins.SplashScreen) {
                    window.CapacitorPlugins.SplashScreen.hide();
                }
            }
            
            onLoadError(message) {
                console.error('Load error:', message);
                this.showError(message);
            }
            
            showError(message) {
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('content').classList.add('hidden');
                document.getElementById('error').classList.remove('hidden');
                document.getElementById('error-message').textContent = message;
            }
            
            retry() {
                if (this.retryCount < this.maxRetries) {
                    this.retryCount++;
                    console.log(`Retrying... Attempt ${this.retryCount}/${this.maxRetries}`);
                    
                    this.isLoaded = false;
                    this.startLoading();
                    
                    // Reload iframe
                    this.iframe.src = this.iframe.src;
                } else {
                    this.showError('Maximum retry attempts reached. Please check your internet connection and restart the app.');
                }
            }
        }
        
        // Global retry function
        function retryLoad() {
            if (window.multiPOSApp) {
                window.multiPOSApp.retry();
            }
        }
        
        // Initialize app when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            window.multiPOSApp = new MultiPOSMobile();
        });
        
        // Initialize immediately if DOM is already loaded
        if (document.readyState === 'loading') {
            // DOM is still loading
        } else {
            // DOM is already loaded
            window.multiPOSApp = new MultiPOSMobile();
        }
    </script>
</body>
</html>
