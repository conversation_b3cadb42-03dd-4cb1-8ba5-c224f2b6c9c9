{"name": "react-native-keychain", "version": "8.2.0", "description": "Keychain Access for React Native", "main": "index.js", "files": ["android", "!android/src/test", "RNKeychain.xcodeproj", "RNKeychainManager", "typings", "RNKeychain.podspec"], "scripts": {"format": "prettier '{,typings/,KeychainExample/}*.{md,js,json,ts,tsx}' --write", "lint": "eslint .", "flow": "flow", "test": "./gradlew test"}, "keywords": ["react-native", "react-component", "react-native-component", "react", "mobile", "ios", "android", "keychain"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/oblador/react-native-keychain", "bugs": {"url": "https://github.com/oblador/react-native-keychain/issues"}, "typescript": {"definition": "typings/react-native-keychain.d.ts"}, "types": "typings/react-native-keychain.d.ts", "repository": {"type": "git", "url": "git://github.com/oblador/react-native-keychain.git"}, "license": "MIT", "devDependencies": {"@react-native-community/eslint-config": "^3.0.1", "eslint": "^7.2.0", "flow-bin": "^0.176.3", "prettier": "^2.2.1", "react-native": "^0.69.0", "typescript": "^4.2.2"}, "volta": {"node": "16.14.2"}}