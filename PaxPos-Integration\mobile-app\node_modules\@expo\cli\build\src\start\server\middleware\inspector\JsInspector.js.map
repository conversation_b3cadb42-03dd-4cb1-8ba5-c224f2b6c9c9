{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/JsInspector.ts"], "sourcesContent": ["import type { CustomMessageHandlerConnection } from '@react-native/dev-middleware';\nimport chalk from 'chalk';\n\nimport { evaluateJsFromCdpAsync } from './CdpClient';\nimport { selectAsync } from '../../../../utils/prompts';\nimport { pageIsSupported } from '../../metro/debugging/pageIsSupported';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:inspector:jsInspector'\n) as typeof console.log;\n\nexport interface MetroInspectorProxyApp {\n  /** Unique device ID combined with the page ID */\n  id: string;\n  /** Information about the underlying CDP implementation, e.g. \"React Native Bridgeless [C++ connection]\" */\n  title: string;\n  /** The application ID that is currently running on the device, e.g. \"dev.expo.bareexpo\" */\n  appId: string;\n  /** The description of the runtime, e.g. \"React Native Bridgeless [C++ connection]\" */\n  description: string;\n  /** The CDP debugger type, which should always be \"node\" */\n  type: 'node';\n  /** The internal `devtools://..` URL for the debugger to connect to */\n  devtoolsFrontendUrl: string;\n  /** The websocket URL for the debugger to connect to */\n  webSocketDebuggerUrl: string;\n  /**\n   * Human-readable device name\n   * @since react-native@0.73\n   */\n  deviceName: string;\n  /**\n   * React Native specific information, like the unique device ID and native capabilities\n   * @since react-native@0.74\n   */\n  reactNative?: {\n    /** The unique device ID */\n    logicalDeviceId: string;\n    /** All supported native capabilities */\n    capabilities: CustomMessageHandlerConnection['page']['capabilities'];\n  };\n}\n\n/**\n * Launch the React Native DevTools by executing the `POST /open-debugger` request.\n * This endpoint is handled through `@react-native/dev-middleware`.\n */\nexport async function openJsInspector(metroBaseUrl: string, app: MetroInspectorProxyApp) {\n  if (!app.reactNative?.logicalDeviceId) {\n    debug('Failed to open React Native DevTools, target is missing device ID');\n    return false;\n  }\n\n  const url = new URL('/open-debugger', metroBaseUrl);\n  url.searchParams.set('target', app.id);\n\n  // Request to open the React Native DevTools, but limit it to 1s\n  // This is a workaround as this endpoint might not respond on some devices\n  const response = await fetch(url, {\n    method: 'POST',\n    signal: AbortSignal.timeout(1000),\n  }).catch((error) => {\n    // Only swallow timeout errors\n    if (error.name === 'TimeoutError') {\n      return null;\n    }\n\n    throw error;\n  });\n\n  if (!response) {\n    debug(`No response received from the React Native DevTools.`);\n  } else if (response.ok === false) {\n    debug('Failed to open React Native DevTools, received response:', response.status);\n  }\n\n  return response?.ok ?? true;\n}\n\nexport async function queryInspectorAppAsync(\n  metroServerOrigin: string,\n  appId: string\n): Promise<MetroInspectorProxyApp | null> {\n  const apps = await queryAllInspectorAppsAsync(metroServerOrigin);\n  return apps.find((app) => app.appId === appId) ?? null;\n}\n\nexport async function queryAllInspectorAppsAsync(\n  metroServerOrigin: string\n): Promise<MetroInspectorProxyApp[]> {\n  const resp = await fetch(`${metroServerOrigin}/json/list`);\n  // The newest runtime will be at the end of the list,\n  // reversing the result would save time from try-error.\n  const apps: MetroInspectorProxyApp[] = (await resp.json()).reverse();\n  const results: MetroInspectorProxyApp[] = [];\n  for (const app of apps) {\n    // Only use targets with better reloading support\n    if (!pageIsSupported(app)) {\n      continue;\n    }\n\n    try {\n      // Hide targets that are marked as hidden from the inspector, e.g. instances from expo-dev-menu and expo-dev-launcher.\n      if (await appShouldBeIgnoredAsync(app)) {\n        continue;\n      }\n    } catch (e: unknown) {\n      // If we can't evaluate the JS, we just ignore the error and skips the target.\n      debug(`Can't evaluate the JS on the app:`, JSON.stringify(e, null, 2));\n      continue;\n    }\n\n    results.push(app);\n  }\n  return results;\n}\n\nexport async function promptInspectorAppAsync(apps: MetroInspectorProxyApp[]) {\n  if (apps.length === 1) {\n    return apps[0];\n  }\n\n  // Check if multiple devices are connected with the same device names\n  // In this case, append the actual app id (device ID + page number) to the prompt\n  const hasDuplicateNames = apps.some(\n    (app, index) => index !== apps.findIndex((other) => app.deviceName === other.deviceName)\n  );\n\n  const choices = apps.map((app) => {\n    const name = app.deviceName ?? 'Unknown device';\n    return {\n      title: hasDuplicateNames ? chalk`${name}{dim  - ${app.id}}` : name,\n      value: app.id,\n      app,\n    };\n  });\n\n  const value = await selectAsync(chalk`Debug target {dim (Hermes only)}`, choices);\n\n  return choices.find((item) => item.value === value)?.app;\n}\n\nconst HIDE_FROM_INSPECTOR_ENV = 'globalThis.__expo_hide_from_inspector__';\n\nasync function appShouldBeIgnoredAsync(app: MetroInspectorProxyApp): Promise<boolean> {\n  const hideFromInspector = await evaluateJsFromCdpAsync(\n    app.webSocketDebuggerUrl,\n    HIDE_FROM_INSPECTOR_ENV\n  );\n  debug(\n    `[appShouldBeIgnoredAsync] webSocketDebuggerUrl[${app.webSocketDebuggerUrl}] hideFromInspector[${hideFromInspector}]`\n  );\n  return hideFromInspector !== undefined;\n}\n"], "names": ["openJsInspector", "promptInspectorAppAsync", "queryAllInspectorAppsAsync", "queryInspectorAppAsync", "debug", "require", "metroBaseUrl", "app", "reactNative", "logicalDeviceId", "url", "URL", "searchParams", "set", "id", "response", "fetch", "method", "signal", "AbortSignal", "timeout", "catch", "error", "name", "ok", "status", "metroServerOrigin", "appId", "apps", "find", "resp", "json", "reverse", "results", "pageIsSupported", "appShouldBeIgnoredAsync", "e", "JSON", "stringify", "push", "choices", "length", "hasDuplicateNames", "some", "index", "findIndex", "other", "deviceName", "map", "title", "chalk", "value", "selectAsync", "item", "HIDE_FROM_INSPECTOR_ENV", "hideFromInspector", "evaluateJsFromCdpAsync", "webSocketDebuggerUrl", "undefined"], "mappings": ";;;;;;;;;;;IA+CsBA,eAAe;eAAfA;;IAsEAC,uBAAuB;eAAvBA;;IA9BAC,0BAA0B;eAA1BA;;IARAC,sBAAsB;eAAtBA;;;;gEA9EJ;;;;;;2BAEqB;yBACX;iCACI;;;;;;AAEhC,MAAMC,QAAQC,QAAQ,SACpB;AAuCK,eAAeL,gBAAgBM,YAAoB,EAAEC,GAA2B;QAChFA;IAAL,IAAI,GAACA,mBAAAA,IAAIC,WAAW,qBAAfD,iBAAiBE,eAAe,GAAE;QACrCL,MAAM;QACN,OAAO;IACT;IAEA,MAAMM,MAAM,IAAIC,IAAI,kBAAkBL;IACtCI,IAAIE,YAAY,CAACC,GAAG,CAAC,UAAUN,IAAIO,EAAE;IAErC,gEAAgE;IAChE,0EAA0E;IAC1E,MAAMC,WAAW,MAAMC,MAAMN,KAAK;QAChCO,QAAQ;QACRC,QAAQC,YAAYC,OAAO,CAAC;IAC9B,GAAGC,KAAK,CAAC,CAACC;QACR,8BAA8B;QAC9B,IAAIA,MAAMC,IAAI,KAAK,gBAAgB;YACjC,OAAO;QACT;QAEA,MAAMD;IACR;IAEA,IAAI,CAACP,UAAU;QACbX,MAAM,CAAC,oDAAoD,CAAC;IAC9D,OAAO,IAAIW,SAASS,EAAE,KAAK,OAAO;QAChCpB,MAAM,4DAA4DW,SAASU,MAAM;IACnF;IAEA,OAAOV,CAAAA,4BAAAA,SAAUS,EAAE,KAAI;AACzB;AAEO,eAAerB,uBACpBuB,iBAAyB,EACzBC,KAAa;IAEb,MAAMC,OAAO,MAAM1B,2BAA2BwB;IAC9C,OAAOE,KAAKC,IAAI,CAAC,CAACtB,MAAQA,IAAIoB,KAAK,KAAKA,UAAU;AACpD;AAEO,eAAezB,2BACpBwB,iBAAyB;IAEzB,MAAMI,OAAO,MAAMd,MAAM,GAAGU,kBAAkB,UAAU,CAAC;IACzD,qDAAqD;IACrD,uDAAuD;IACvD,MAAME,OAAiC,AAAC,CAAA,MAAME,KAAKC,IAAI,EAAC,EAAGC,OAAO;IAClE,MAAMC,UAAoC,EAAE;IAC5C,KAAK,MAAM1B,OAAOqB,KAAM;QACtB,iDAAiD;QACjD,IAAI,CAACM,IAAAA,gCAAe,EAAC3B,MAAM;YACzB;QACF;QAEA,IAAI;YACF,sHAAsH;YACtH,IAAI,MAAM4B,wBAAwB5B,MAAM;gBACtC;YACF;QACF,EAAE,OAAO6B,GAAY;YACnB,8EAA8E;YAC9EhC,MAAM,CAAC,iCAAiC,CAAC,EAAEiC,KAAKC,SAAS,CAACF,GAAG,MAAM;YACnE;QACF;QAEAH,QAAQM,IAAI,CAAChC;IACf;IACA,OAAO0B;AACT;AAEO,eAAehC,wBAAwB2B,IAA8B;QAsBnEY;IArBP,IAAIZ,KAAKa,MAAM,KAAK,GAAG;QACrB,OAAOb,IAAI,CAAC,EAAE;IAChB;IAEA,qEAAqE;IACrE,iFAAiF;IACjF,MAAMc,oBAAoBd,KAAKe,IAAI,CACjC,CAACpC,KAAKqC,QAAUA,UAAUhB,KAAKiB,SAAS,CAAC,CAACC,QAAUvC,IAAIwC,UAAU,KAAKD,MAAMC,UAAU;IAGzF,MAAMP,UAAUZ,KAAKoB,GAAG,CAAC,CAACzC;QACxB,MAAMgB,OAAOhB,IAAIwC,UAAU,IAAI;QAC/B,OAAO;YACLE,OAAOP,oBAAoBQ,IAAAA,gBAAK,CAAA,CAAC,EAAE3B,KAAK,QAAQ,EAAEhB,IAAIO,EAAE,CAAC,CAAC,CAAC,GAAGS;YAC9D4B,OAAO5C,IAAIO,EAAE;YACbP;QACF;IACF;IAEA,MAAM4C,QAAQ,MAAMC,IAAAA,oBAAW,EAACF,IAAAA,gBAAK,CAAA,CAAC,gCAAgC,CAAC,EAAEV;IAEzE,QAAOA,gBAAAA,QAAQX,IAAI,CAAC,CAACwB,OAASA,KAAKF,KAAK,KAAKA,2BAAtCX,cAA8CjC,GAAG;AAC1D;AAEA,MAAM+C,0BAA0B;AAEhC,eAAenB,wBAAwB5B,GAA2B;IAChE,MAAMgD,oBAAoB,MAAMC,IAAAA,iCAAsB,EACpDjD,IAAIkD,oBAAoB,EACxBH;IAEFlD,MACE,CAAC,+CAA+C,EAAEG,IAAIkD,oBAAoB,CAAC,oBAAoB,EAAEF,kBAAkB,CAAC,CAAC;IAEvH,OAAOA,sBAAsBG;AAC/B"}