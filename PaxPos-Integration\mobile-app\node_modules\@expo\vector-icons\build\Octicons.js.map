{"version": 3, "file": "Octicons.js", "sourceRoot": "", "sources": ["../src/Octicons.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,uDAAuD,CAAC;AACzE,OAAO,QAAQ,MAAM,4DAA4D,CAAC;AAElF,eAAe,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["\"use client\";\n\nimport createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Octicons.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Octicons.json';\n\nexport default createIconSet(glyphMap, 'octicons', font);\n"]}