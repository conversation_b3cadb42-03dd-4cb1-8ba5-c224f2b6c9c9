{"name": "validate-npm-package-name", "version": "5.0.1", "description": "Give me a string and I'll tell you if it's a valid npm package name", "main": "lib/", "directories": {"test": "test"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "tap": "^16.0.1"}, "scripts": {"cov:test": "TAP_FLAGS='--cov' npm run test:code", "test:code": "tap ${TAP_FLAGS:-'--'} test/*.js", "test:style": "standard", "test": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/validate-npm-package-name.git"}, "keywords": ["npm", "package", "names", "validation"], "author": "GitHub Inc.", "license": "ISC", "bugs": {"url": "https://github.com/npm/validate-npm-package-name/issues"}, "homepage": "https://github.com/npm/validate-npm-package-name", "files": ["bin/", "lib/"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}