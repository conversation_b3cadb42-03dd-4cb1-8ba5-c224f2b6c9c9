# Render.com deployment configuration for PaxSoft POS System
# This file defines the services and their deployment settings

services:
  # Backend API Service
  - type: web
    name: paxsoft-pos-backend
    env: node
    plan: starter
    buildCommand: |
      cd backend &&
      npm ci &&
      npm run build
    startCommand: cd backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3001
      - key: MONGO_URI
        fromDatabase:
          name: paxsoft-pos-db
          property: connectionString
      # PAX POS Configuration
      - key: PAX_SIMULATION_MODE
        value: "true"
      - key: PAX_MERCHANT_NAME
        value: "PaxSoft POS"
      - key: PAX_RECEIPT_ENABLED
        value: "true"
      # Stripe Configuration (if needed)
      - key: STRIPE_SECRET_KEY
        fromSecret: STRIPE_SECRET_KEY
      - key: STRIPE_PUBLISHABLE_KEY
        fromSecret: STRIPE_PUBLISHABLE_KEY
      # Square Configuration (if needed)
      - key: SQUARE_APPLICATION_ID
        fromSecret: SQUARE_APPLICATION_ID
      - key: SQUARE_ACCESS_TOKEN
        fromSecret: SQUARE_ACCESS_TOKEN
      - key: SQUARE_LOCATION_ID
        fromSecret: SQUARE_LOCATION_ID
      # Move Payment Configuration (if needed)
      - key: MOVE_PAYMENT_API_KEY
        fromSecret: MOVE_PAYMENT_API_KEY
    healthCheckPath: /health
    
  # Frontend Web Service
  - type: web
    name: paxsoft-pos-frontend
    env: static
    plan: starter
    buildCommand: |
      cd frontend &&
      npm ci &&
      npm run build
    staticPublishPath: frontend/dist
    envVars:
      - key: VITE_API_URL
        value: https://paxsoft-pos-backend.onrender.com
      - key: VITE_API_BASE_URL
        value: https://paxsoft-pos-backend.onrender.com/api/v1
      - key: VITE_STRIPE_PUBLISHABLE_KEY
        fromSecret: STRIPE_PUBLISHABLE_KEY
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
    headers:
      - path: /*
        name: X-Frame-Options
        value: DENY
      - path: /*
        name: X-Content-Type-Options
        value: nosniff
      - path: /*
        name: Referrer-Policy
        value: strict-origin-when-cross-origin
      - path: /static/*
        name: Cache-Control
        value: public, max-age=31536000, immutable

# Database
databases:
  - name: paxsoft-pos-db
    databaseName: paxsoft_pos
    user: paxsoft_user
    plan: starter

# Environment Variables Documentation
# 
# Required Secrets (set in Render dashboard):
# - STRIPE_SECRET_KEY: Your Stripe secret key
# - STRIPE_PUBLISHABLE_KEY: Your Stripe publishable key
# - SQUARE_APPLICATION_ID: Square application ID
# - SQUARE_ACCESS_TOKEN: Square access token
# - SQUARE_LOCATION_ID: Square location ID
# - MOVE_PAYMENT_API_KEY: Move Payment API key
#
# Optional Environment Variables:
# - PAX_MERCHANT_ID: Merchant identifier for PAX transactions
# - PAX_TERMINAL_IP: IP address of PAX terminal (not used in cloud)
# - PAX_TERMINAL_PORT: Port of PAX terminal (not used in cloud)
# - LOG_LEVEL: Logging level (info, warn, error, debug)
#
# Notes:
# 1. PAX hardware integration is automatically disabled in cloud environments
# 2. The system will run in simulation mode for PAX transactions
# 3. All payment providers work normally in cloud deployment
# 4. Receipt generation is web-based and works in all environments
