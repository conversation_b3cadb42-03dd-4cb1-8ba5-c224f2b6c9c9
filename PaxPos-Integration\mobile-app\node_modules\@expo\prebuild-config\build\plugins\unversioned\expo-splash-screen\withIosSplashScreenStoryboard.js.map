{"version": 3, "file": "withIosSplashScreenStoryboard.js", "names": ["_configPlugins", "data", "require", "fs", "_interopRequireWildcard", "path", "_xml2js", "_InterfaceBuilder", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "STORYBOARD_FILE_PATH", "exports", "STORYBOARD_MOD_NAME", "withIosSplashScreenStoryboard", "config", "action", "with<PERSON><PERSON>", "platform", "mod", "withIosSplashScreenStoryboardBaseMod", "BaseMods", "withGeneratedBaseMods", "saveToInternal", "skipEmptyMod", "providers", "provider", "isIntrospective", "getFilePath", "modRequest", "join", "platformProjectRoot", "projectName", "read", "filePath", "contents", "promises", "readFile", "xml", "<PERSON><PERSON><PERSON>", "parseStringPromise", "getTemplateAsync", "write", "modResults", "introspect", "writeFile", "toString"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashScreenStoryboard.ts"], "sourcesContent": ["import { BaseMods, ConfigPlugin, Mod, withMod } from '@expo/config-plugins';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { Parser } from 'xml2js';\n\nimport { IBSplashScreenDocument, toString } from './InterfaceBuilder';\n\nexport const STORYBOARD_FILE_PATH = './SplashScreen.storyboard';\n\nconst STORYBOARD_MOD_NAME = 'splashScreenStoryboard';\n\n/**\n * Provides the SplashScreen `.storyboard` xml data for modification.\n *\n * @param config\n * @param action\n */\nexport const withIosSplashScreenStoryboard: ConfigPlugin<Mod<IBSplashScreenDocument>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: STORYBOARD_MOD_NAME,\n    action,\n  });\n};\n\n/** Append a custom rule to supply SplashScreen `.storyboard` xml data to mods on `mods.ios.splashScreenStoryboard` */\nexport const withIosSplashScreenStoryboardBaseMod: ConfigPlugin = (config) => {\n  return BaseMods.withGeneratedBaseMods(config, {\n    platform: 'ios',\n    saveToInternal: true,\n    skipEmptyMod: false,\n    providers: {\n      // Append a custom rule to supply .storyboard xml data to mods on `mods.ios.splashScreenStoryboard`\n      [STORYBOARD_MOD_NAME]: BaseMods.provider<IBSplashScreenDocument>({\n        isIntrospective: true,\n        async getFilePath({ modRequest }) {\n          //: [root]/myapp/ios/MyApp/SplashScreen.storyboard\n          return path.join(\n            //: myapp/ios\n            modRequest.platformProjectRoot,\n            // ./MyApp\n            modRequest.projectName!,\n            // ./SplashScreen.storyboard\n            STORYBOARD_FILE_PATH\n          );\n        },\n        async read(filePath) {\n          try {\n            const contents = await fs.promises.readFile(filePath, 'utf8');\n            const xml = await new Parser().parseStringPromise(contents);\n            return xml;\n          } catch {\n            return getTemplateAsync();\n          }\n        },\n        async write(filePath, { modResults, modRequest: { introspect } }) {\n          if (introspect) {\n            return;\n          }\n          await fs.promises.writeFile(filePath, toString(modResults));\n        },\n      }),\n    },\n  });\n};\n\n/** Get a template splash screen storyboard file. */\nexport async function getTemplateAsync(): Promise<IBSplashScreenDocument> {\n  const contents = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n  <document type=\"com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB\" version=\"3.0\" toolsVersion=\"32700.99.1234\" targetRuntime=\"iOS.CocoaTouch\" propertyAccessControl=\"none\" useAutolayout=\"YES\" launchScreen=\"YES\" useTraitCollections=\"YES\" useSafeAreas=\"YES\" colorMatched=\"YES\" initialViewController=\"EXPO-VIEWCONTROLLER-1\">\n      <device id=\"retina6_12\" orientation=\"portrait\" appearance=\"light\"/>\n      <dependencies>\n          <deployment identifier=\"iOS\"/>\n          <plugIn identifier=\"com.apple.InterfaceBuilder.IBCocoaTouchPlugin\" version=\"22685\"/>\n          <capability name=\"Safe area layout guides\" minToolsVersion=\"9.0\"/>\n          <capability name=\"documents saved in the Xcode 8 format\" minToolsVersion=\"8.0\"/>\n      </dependencies>\n      <scenes>\n          <!--View Controller-->\n          <scene sceneID=\"EXPO-SCENE-1\">\n              <objects>\n                  <viewController storyboardIdentifier=\"SplashScreenViewController\" id=\"EXPO-VIEWCONTROLLER-1\" sceneMemberID=\"viewController\">\n                      <view key=\"view\" userInteractionEnabled=\"NO\" contentMode=\"scaleToFill\" insetsLayoutMarginsFromSafeArea=\"NO\" id=\"EXPO-ContainerView\" userLabel=\"ContainerView\">\n                          <rect key=\"frame\" x=\"0.0\" y=\"0.0\" width=\"393\" height=\"852\"/>\n                          <autoresizingMask key=\"autoresizingMask\" flexibleMaxX=\"YES\" flexibleMaxY=\"YES\"/>\n                          <subviews>\n                              <imageView clipsSubviews=\"YES\" userInteractionEnabled=\"NO\" contentMode=\"scaleAspectFit\" horizontalHuggingPriority=\"251\" verticalHuggingPriority=\"251\" image=\"SplashScreen\" translatesAutoresizingMaskIntoConstraints=\"NO\" id=\"EXPO-SplashScreen\" userLabel=\"SplashScreen\">\n                                  <rect key=\"frame\" x=\"146.66666666666666\" y=\"381\" width=\"100\" height=\"90.333333333333314\"/>\n                              </imageView>\n                          </subviews>\n                          <viewLayoutGuide key=\"safeArea\" id=\"Rmq-lb-GrQ\"/>\n                          <constraints>\n                              <constraint firstItem=\"EXPO-SplashScreen\" firstAttribute=\"centerY\" secondItem=\"EXPO-ContainerView\" secondAttribute=\"centerY\" id=\"0VC-Wk-OaO\"/>\n                              <constraint firstItem=\"EXPO-SplashScreen\" firstAttribute=\"centerX\" secondItem=\"EXPO-ContainerView\" secondAttribute=\"centerX\" id=\"zR4-NK-mVN\"/>\n                          </constraints>\n                          <color key=\"backgroundColor\" name=\"SplashScreenBackground\"/>\n                      </view>\n                  </viewController>\n                  <placeholder placeholderIdentifier=\"IBFirstResponder\" id=\"EXPO-PLACEHOLDER-1\" userLabel=\"First Responder\" sceneMemberID=\"firstResponder\"/>\n              </objects>\n              <point key=\"canvasLocation\" x=\"140.625\" y=\"129.4921875\"/>\n          </scene>\n      </scenes>\n      <resources>\n          <image name=\"SplashScreenLogo\" width=\"100\" height=\"90.333335876464844\"/>\n          <namedColor name=\"SplashScreenBackground\">\n            <color alpha=\"1.000\" blue=\"1.00000000000000\" green=\"1.00000000000000\" red=\"1.00000000000000\" customColorSpace=\"sRGB\" colorSpace=\"custom\"/>\n          </namedColor>\n      </resources>\n  </document>`;\n  return await new Parser().parseStringPromise(contents);\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,GAAA;EAAA,MAAAF,IAAA,GAAAG,uBAAA,CAAAF,OAAA;EAAAC,EAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,KAAA;EAAA,MAAAJ,IAAA,GAAAG,uBAAA,CAAAF,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,kBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,iBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsE,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAE/D,MAAMW,oBAAoB,GAAAC,OAAA,CAAAD,oBAAA,GAAG,2BAA2B;AAE/D,MAAME,mBAAmB,GAAG,wBAAwB;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,6BAAwE,GAAGA,CACtFC,MAAM,EACNC,MAAM,KACH;EACH,OAAO,IAAAC,wBAAO,EAACF,MAAM,EAAE;IACrBG,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAEN,mBAAmB;IACxBG;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAJ,OAAA,CAAAE,6BAAA,GAAAA,6BAAA;AACO,MAAMM,oCAAkD,GAAIL,MAAM,IAAK;EAC5E,OAAOM,yBAAQ,CAACC,qBAAqB,CAACP,MAAM,EAAE;IAC5CG,QAAQ,EAAE,KAAK;IACfK,cAAc,EAAE,IAAI;IACpBC,YAAY,EAAE,KAAK;IACnBC,SAAS,EAAE;MACT;MACA,CAACZ,mBAAmB,GAAGQ,yBAAQ,CAACK,QAAQ,CAAyB;QAC/DC,eAAe,EAAE,IAAI;QACrB,MAAMC,WAAWA,CAAC;UAAEC;QAAW,CAAC,EAAE;UAChC;UACA,OAAOzC,IAAI,CAAD,CAAC,CAAC0C,IAAI;UACd;UACAD,UAAU,CAACE,mBAAmB;UAC9B;UACAF,UAAU,CAACG,WAAW;UACtB;UACArB,oBACF,CAAC;QACH,CAAC;QACD,MAAMsB,IAAIA,CAACC,QAAQ,EAAE;UACnB,IAAI;YACF,MAAMC,QAAQ,GAAG,MAAMjD,EAAE,CAAD,CAAC,CAACkD,QAAQ,CAACC,QAAQ,CAACH,QAAQ,EAAE,MAAM,CAAC;YAC7D,MAAMI,GAAG,GAAG,MAAM,KAAIC,gBAAM,EAAC,CAAC,CAACC,kBAAkB,CAACL,QAAQ,CAAC;YAC3D,OAAOG,GAAG;UACZ,CAAC,CAAC,MAAM;YACN,OAAOG,gBAAgB,CAAC,CAAC;UAC3B;QACF,CAAC;QACD,MAAMC,KAAKA,CAACR,QAAQ,EAAE;UAAES,UAAU;UAAEd,UAAU,EAAE;YAAEe;UAAW;QAAE,CAAC,EAAE;UAChE,IAAIA,UAAU,EAAE;YACd;UACF;UACA,MAAM1D,EAAE,CAAD,CAAC,CAACkD,QAAQ,CAACS,SAAS,CAACX,QAAQ,EAAE,IAAAY,4BAAQ,EAACH,UAAU,CAAC,CAAC;QAC7D;MACF,CAAC;IACH;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AAAA/B,OAAA,CAAAQ,oCAAA,GAAAA,oCAAA;AACO,eAAeqB,gBAAgBA,CAAA,EAAoC;EACxE,MAAMN,QAAQ,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;EACZ,OAAO,MAAM,KAAII,gBAAM,EAAC,CAAC,CAACC,kBAAkB,CAACL,QAAQ,CAAC;AACxD", "ignoreList": []}