{"version": 3, "sources": ["../../../src/export/resolveOptions.ts"], "sourcesContent": ["import { ExpoConfig, getConfig, Platform } from '@expo/config';\n\nimport { getPlatformBundlers, PlatformBundlers } from '../start/server/platformBundlers';\nimport { CommandError } from '../utils/errors';\n\nexport type Options = {\n  outputDir: string;\n  platforms: Platform[];\n  maxWorkers?: number;\n  dev: boolean;\n  clear: boolean;\n  minify: boolean;\n  bytecode: boolean;\n  dumpAssetmap: boolean;\n  sourceMaps: boolean;\n  skipSSG: boolean;\n};\n\n/** Returns an array of platforms based on the input platform identifier and runtime constraints. */\nexport function resolvePlatformOption(\n  exp: ExpoConfig,\n  platformBundlers: PlatformBundlers,\n  platform: string[] = ['all']\n): Platform[] {\n  const platformsAvailable: Partial<PlatformBundlers> = Object.fromEntries(\n    Object.entries(platformBundlers).filter(\n      ([platform, bundler]) => bundler === 'metro' && exp.platforms?.includes(platform as Platform)\n    )\n  );\n\n  if (!Object.keys(platformsAvailable).length) {\n    throw new CommandError(\n      `No platforms are configured to use the Metro bundler in the project Expo config.`\n    );\n  }\n\n  const assertPlatformBundler = (platform: Platform): Platform => {\n    if (!platformsAvailable[platform]) {\n      if (!exp.platforms?.includes(platform) && platform === 'web') {\n        // Pass through so the more robust error message is shown.\n        return platform;\n      }\n      throw new CommandError(\n        'BAD_ARGS',\n        `Platform \"${platform}\" is not configured to use the Metro bundler in the project Expo config, or is missing from the supported platforms in the platforms array: [${exp.platforms?.join(\n          ', '\n        )}].`\n      );\n    }\n\n    return platform;\n  };\n\n  const knownPlatforms = ['android', 'ios', 'web'] as Platform[];\n  const assertPlatformIsKnown = (platform: string): Platform => {\n    if (!knownPlatforms.includes(platform as Platform)) {\n      throw new CommandError(\n        `Unsupported platform \"${platform}\". Options are: ${knownPlatforms.join(',')},all`\n      );\n    }\n\n    return platform as Platform;\n  };\n\n  return (\n    platform\n      // Expand `all` to all available platforms.\n      .map((platform) => (platform === 'all' ? Object.keys(platformsAvailable) : platform))\n      .flat()\n      // Remove duplicated platforms\n      .filter((platform, index, list) => list.indexOf(platform) === index)\n      // Assert platforms are valid\n      .map((platform) => assertPlatformIsKnown(platform))\n      .map((platform) => assertPlatformBundler(platform))\n  );\n}\n\nexport async function resolveOptionsAsync(projectRoot: string, args: any): Promise<Options> {\n  const { exp } = getConfig(projectRoot, { skipPlugins: true, skipSDKVersionRequirement: true });\n  const platformBundlers = getPlatformBundlers(projectRoot, exp);\n\n  const platforms = resolvePlatformOption(exp, platformBundlers, args['--platform']);\n  return {\n    platforms,\n    outputDir: args['--output-dir'] ?? 'dist',\n    minify: !args['--no-minify'],\n    bytecode: !args['--no-bytecode'],\n    clear: !!args['--clear'],\n    dev: !!args['--dev'],\n    maxWorkers: args['--max-workers'],\n    dumpAssetmap: !!args['--dump-assetmap'],\n    sourceMaps: !!args['--source-maps'],\n    skipSSG: !!args['--no-ssg'],\n  };\n}\n"], "names": ["resolveOptionsAsync", "resolvePlatformOption", "exp", "platformBundlers", "platform", "platformsAvailable", "Object", "fromEntries", "entries", "filter", "bundler", "platforms", "includes", "keys", "length", "CommandError", "assertPlatformBundler", "join", "knownPlatforms", "assertPlatformIsKnown", "map", "flat", "index", "list", "indexOf", "projectRoot", "args", "getConfig", "skip<PERSON>lug<PERSON>", "skipSDKVersionRequirement", "getPlatformBundlers", "outputDir", "minify", "bytecode", "clear", "dev", "maxWorkers", "dumpAssetmap", "sourceMaps", "skipSSG"], "mappings": ";;;;;;;;;;;IA6EsBA,mBAAmB;eAAnBA;;IA1DNC,qBAAqB;eAArBA;;;;yBAnBgC;;;;;;kCAEM;wBACzB;AAgBtB,SAASA,sBACdC,GAAe,EACfC,gBAAkC,EAClCC,WAAqB;IAAC;CAAM;IAE5B,MAAMC,qBAAgDC,OAAOC,WAAW,CACtED,OAAOE,OAAO,CAACL,kBAAkBM,MAAM,CACrC,CAAC,CAACL,UAAUM,QAAQ;YAA4BR;eAAvBQ,YAAY,aAAWR,iBAAAA,IAAIS,SAAS,qBAAbT,eAAeU,QAAQ,CAACR;;IAI5E,IAAI,CAACE,OAAOO,IAAI,CAACR,oBAAoBS,MAAM,EAAE;QAC3C,MAAM,IAAIC,oBAAY,CACpB,CAAC,gFAAgF,CAAC;IAEtF;IAEA,MAAMC,wBAAwB,CAACZ;QAC7B,IAAI,CAACC,kBAAkB,CAACD,SAAS,EAAE;gBAC5BF,gBAMkKA;YANvK,IAAI,GAACA,iBAAAA,IAAIS,SAAS,qBAAbT,eAAeU,QAAQ,CAACR,cAAaA,aAAa,OAAO;gBAC5D,0DAA0D;gBAC1D,OAAOA;YACT;YACA,MAAM,IAAIW,oBAAY,CACpB,YACA,CAAC,UAAU,EAAEX,SAAS,6IAA6I,GAAEF,kBAAAA,IAAIS,SAAS,qBAAbT,gBAAee,IAAI,CACtL,MACA,EAAE,CAAC;QAET;QAEA,OAAOb;IACT;IAEA,MAAMc,iBAAiB;QAAC;QAAW;QAAO;KAAM;IAChD,MAAMC,wBAAwB,CAACf;QAC7B,IAAI,CAACc,eAAeN,QAAQ,CAACR,WAAuB;YAClD,MAAM,IAAIW,oBAAY,CACpB,CAAC,sBAAsB,EAAEX,SAAS,gBAAgB,EAAEc,eAAeD,IAAI,CAAC,KAAK,IAAI,CAAC;QAEtF;QAEA,OAAOb;IACT;IAEA,OACEA,QACE,2CAA2C;KAC1CgB,GAAG,CAAC,CAAChB,WAAcA,aAAa,QAAQE,OAAOO,IAAI,CAACR,sBAAsBD,UAC1EiB,IAAI,EACL,8BAA8B;KAC7BZ,MAAM,CAAC,CAACL,UAAUkB,OAAOC,OAASA,KAAKC,OAAO,CAACpB,cAAckB,MAC9D,6BAA6B;KAC5BF,GAAG,CAAC,CAAChB,WAAae,sBAAsBf,WACxCgB,GAAG,CAAC,CAAChB,WAAaY,sBAAsBZ;AAE/C;AAEO,eAAeJ,oBAAoByB,WAAmB,EAAEC,IAAS;IACtE,MAAM,EAAExB,GAAG,EAAE,GAAGyB,IAAAA,mBAAS,EAACF,aAAa;QAAEG,aAAa;QAAMC,2BAA2B;IAAK;IAC5F,MAAM1B,mBAAmB2B,IAAAA,qCAAmB,EAACL,aAAavB;IAE1D,MAAMS,YAAYV,sBAAsBC,KAAKC,kBAAkBuB,IAAI,CAAC,aAAa;IACjF,OAAO;QACLf;QACAoB,WAAWL,IAAI,CAAC,eAAe,IAAI;QACnCM,QAAQ,CAACN,IAAI,CAAC,cAAc;QAC5BO,UAAU,CAACP,IAAI,CAAC,gBAAgB;QAChCQ,OAAO,CAAC,CAACR,IAAI,CAAC,UAAU;QACxBS,KAAK,CAAC,CAACT,IAAI,CAAC,QAAQ;QACpBU,YAAYV,IAAI,CAAC,gBAAgB;QACjCW,cAAc,CAAC,CAACX,IAAI,CAAC,kBAAkB;QACvCY,YAAY,CAAC,CAACZ,IAAI,CAAC,gBAAgB;QACnCa,SAAS,CAAC,CAACb,IAAI,CAAC,WAAW;IAC7B;AACF"}