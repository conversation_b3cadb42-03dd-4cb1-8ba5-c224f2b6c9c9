{"version": 3, "sources": ["../../../../../src/start/server/middleware/FaviconMiddleware.ts"], "sourcesContent": ["import { ExpoMiddleware } from './ExpoMiddleware';\nimport { ServerNext, ServerRequest, ServerResponse } from './server.types';\nimport { getFaviconFromExpoConfigAsync } from '../../../export/favicon';\n\nconst debug = require('debug')('expo:start:server:middleware:favicon') as typeof console.log;\n\n/**\n * Middleware for generating a favicon.ico file for the current project if one doesn't exist.\n *\n * Test by making a get request with:\n * curl -v http://localhost:8081/favicon.ico\n */\nexport class FaviconMiddleware extends ExpoMiddleware {\n  constructor(protected projectRoot: string) {\n    super(projectRoot, ['/favicon.ico']);\n  }\n\n  async handleRequestAsync(\n    req: ServerRequest,\n    res: ServerResponse,\n    next: ServerNext\n  ): Promise<void> {\n    if (!['GET', 'HEAD'].includes(req.method || '')) {\n      return next();\n    }\n\n    let faviconImageData: Buffer | null;\n    try {\n      const data = await getFaviconFromExpoConfigAsync(this.projectRoot, { force: true });\n      if (!data) {\n        debug('No favicon defined in the Expo Config, skipping generation.');\n        return next();\n      }\n      faviconImageData = data.source;\n      debug('✅ Generated favicon successfully.');\n    } catch (error: any) {\n      // Pass through on ENOENT errors\n      debug('Failed to generate favicon from Expo config:', error);\n      if (error.code === 'ENOENT') {\n        return next();\n      }\n      return next(error);\n    }\n    // Respond with the generated favicon file\n    res.setHeader('Content-Type', 'image/x-icon');\n    res.end(faviconImageData);\n  }\n}\n"], "names": ["FaviconMiddleware", "debug", "require", "ExpoMiddleware", "constructor", "projectRoot", "handleRequestAsync", "req", "res", "next", "includes", "method", "faviconImageData", "data", "getFaviconFromExpoConfigAsync", "force", "source", "error", "code", "<PERSON><PERSON><PERSON><PERSON>", "end"], "mappings": ";;;;+BAYaA;;;eAAAA;;;gCAZkB;yBAEe;AAE9C,MAAMC,QAAQC,QAAQ,SAAS;AAQxB,MAAMF,0BAA0BG,8BAAc;IACnDC,YAAY,AAAUC,WAAmB,CAAE;QACzC,KAAK,CAACA,aAAa;YAAC;SAAe,QADfA,cAAAA;IAEtB;IAEA,MAAMC,mBACJC,GAAkB,EAClBC,GAAmB,EACnBC,IAAgB,EACD;QACf,IAAI,CAAC;YAAC;YAAO;SAAO,CAACC,QAAQ,CAACH,IAAII,MAAM,IAAI,KAAK;YAC/C,OAAOF;QACT;QAEA,IAAIG;QACJ,IAAI;YACF,MAAMC,OAAO,MAAMC,IAAAA,sCAA6B,EAAC,IAAI,CAACT,WAAW,EAAE;gBAAEU,OAAO;YAAK;YACjF,IAAI,CAACF,MAAM;gBACTZ,MAAM;gBACN,OAAOQ;YACT;YACAG,mBAAmBC,KAAKG,MAAM;YAC9Bf,MAAM;QACR,EAAE,OAAOgB,OAAY;YACnB,gCAAgC;YAChChB,MAAM,gDAAgDgB;YACtD,IAAIA,MAAMC,IAAI,KAAK,UAAU;gBAC3B,OAAOT;YACT;YACA,OAAOA,KAAKQ;QACd;QACA,0CAA0C;QAC1CT,IAAIW,SAAS,CAAC,gBAAgB;QAC9BX,IAAIY,GAAG,CAACR;IACV;AACF"}