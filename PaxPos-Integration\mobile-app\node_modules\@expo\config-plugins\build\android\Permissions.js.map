{"version": 3, "file": "Permissions.js", "names": ["_Manifest", "data", "require", "_androidPlugins", "USES_PERMISSION", "withPermissions", "config", "permissions", "Array", "isArray", "filter", "Boolean", "android", "Set", "concat", "withAndroidManifest", "modResults", "setAndroidPermissions", "exports", "withBlockedPermissions", "resolvedPermissions", "prefixAndroidPermissionsIfNecessary", "permission", "includes", "ensureToolsAvailable", "addBlockedPermissions", "withInternalBlockedPermissions", "blockedPermissions", "length", "androidManifest", "manifest", "ensureBlockedPermission", "manifestPermissions", "e", "$", "push", "map", "getAndroidPermissions", "providedPermissions", "permissionsToAdd", "hasOwnProperty", "for<PERSON>ach", "isPermissionAlreadyRequested", "addPermissionToManifest", "some", "removePermissions", "permissionNames", "targetNames", "ensurePermissionNameFormat", "nextPermissions", "attribute", "value", "name", "addPermission", "permissionName", "usesPermissions", "ensurePermissions", "getPermissions", "results", "targetName", "ensurePermission", "com", "split", "pop", "toUpperCase", "join", "permissionObject"], "sources": ["../../src/android/Permissions.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { AndroidManifest, ensureToolsAvailable, ManifestUsesPermission } from './Manifest';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAndroidManifest } from '../plugins/android-plugins';\n\nconst USES_PERMISSION = 'uses-permission';\n\nexport const withPermissions: ConfigPlugin<string[] | void> = (config, permissions) => {\n  if (Array.isArray(permissions)) {\n    permissions = permissions.filter(Boolean);\n    if (!config.android) config.android = {};\n    if (!config.android.permissions) config.android.permissions = [];\n    config.android.permissions = [\n      // @ts-ignore\n      ...new Set(config.android.permissions.concat(permissions)),\n    ];\n  }\n  return withAndroidManifest(config, async (config) => {\n    config.modResults = await setAndroidPermissions(config, config.modResults);\n    return config;\n  });\n};\n\n/** Given a permission or list of permissions, block permissions in the final `AndroidManifest.xml` to ensure no installed library or plugin can add them. */\nexport const withBlockedPermissions: ConfigPlugin<string[] | string> = (config, permissions) => {\n  const resolvedPermissions = prefixAndroidPermissionsIfNecessary(\n    (Array.isArray(permissions) ? permissions : [permissions]).filter(Boolean)\n  );\n\n  if (config?.android?.permissions && Array.isArray(config.android.permissions)) {\n    // Remove any static config permissions\n    config.android.permissions = prefixAndroidPermissionsIfNecessary(\n      config.android.permissions\n    ).filter((permission) => !resolvedPermissions.includes(permission));\n  }\n\n  return withAndroidManifest(config, async (config) => {\n    config.modResults = ensureToolsAvailable(config.modResults);\n    config.modResults = addBlockedPermissions(config.modResults, resolvedPermissions);\n    return config;\n  });\n};\n\nexport const withInternalBlockedPermissions: ConfigPlugin = (config) => {\n  // Only add permissions if the user defined the property and added some values\n  // this ensures we don't add the `tools:*` namespace extraneously.\n  if (config.android?.blockedPermissions?.length) {\n    return withBlockedPermissions(config, config.android.blockedPermissions);\n  }\n\n  return config;\n};\n\nexport function addBlockedPermissions(androidManifest: AndroidManifest, permissions: string[]) {\n  if (!Array.isArray(androidManifest.manifest['uses-permission'])) {\n    androidManifest.manifest['uses-permission'] = [];\n  }\n\n  for (const permission of prefixAndroidPermissionsIfNecessary(permissions)) {\n    androidManifest.manifest['uses-permission'] = ensureBlockedPermission(\n      androidManifest.manifest['uses-permission'],\n      permission\n    );\n  }\n\n  return androidManifest;\n}\n\n/**\n * Filter any existing permissions matching the provided permission name, then add a\n * restricted permission to overwrite any extra permissions that may be added in a\n * third-party package's AndroidManifest.xml.\n *\n * @param manifestPermissions manifest `uses-permissions` array.\n * @param permission `android:name` of the permission to restrict\n * @returns\n */\nfunction ensureBlockedPermission(\n  manifestPermissions: ManifestUsesPermission[],\n  permission: string\n) {\n  // Remove permission if it currently exists\n  manifestPermissions = manifestPermissions.filter((e) => e.$['android:name'] !== permission);\n\n  // Add a permission with tools:node to overwrite any existing permission and ensure it's removed upon building.\n  manifestPermissions.push({\n    $: { 'android:name': permission, 'tools:node': 'remove' },\n  });\n  return manifestPermissions;\n}\n\nfunction prefixAndroidPermissionsIfNecessary(permissions: string[]): string[] {\n  return permissions.map((permission) => {\n    if (!permission.includes('.')) {\n      return `android.permission.${permission}`;\n    }\n    return permission;\n  });\n}\n\nexport function getAndroidPermissions(config: Pick<ExpoConfig, 'android'>): string[] {\n  return config.android?.permissions ?? [];\n}\n\nexport function setAndroidPermissions(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidManifest\n) {\n  const permissions = getAndroidPermissions(config);\n  const providedPermissions = prefixAndroidPermissionsIfNecessary(permissions);\n  const permissionsToAdd = [...providedPermissions];\n\n  if (!androidManifest.manifest.hasOwnProperty('uses-permission')) {\n    androidManifest.manifest['uses-permission'] = [];\n  }\n  // manifest.manifest['uses-permission'] = [];\n\n  const manifestPermissions = androidManifest.manifest['uses-permission'] ?? [];\n\n  permissionsToAdd.forEach((permission) => {\n    if (!isPermissionAlreadyRequested(permission, manifestPermissions)) {\n      addPermissionToManifest(permission, manifestPermissions);\n    }\n  });\n\n  return androidManifest;\n}\n\nexport function isPermissionAlreadyRequested(\n  permission: string,\n  manifestPermissions: ManifestUsesPermission[]\n): boolean {\n  return manifestPermissions.some((e) => e.$['android:name'] === permission);\n}\n\nexport function addPermissionToManifest(\n  permission: string,\n  manifestPermissions: ManifestUsesPermission[]\n) {\n  manifestPermissions.push({ $: { 'android:name': permission } });\n  return manifestPermissions;\n}\n\nexport function removePermissions(androidManifest: AndroidManifest, permissionNames?: string[]) {\n  const targetNames = permissionNames ? permissionNames.map(ensurePermissionNameFormat) : null;\n  const permissions = androidManifest.manifest[USES_PERMISSION] || [];\n  const nextPermissions = [];\n  for (const attribute of permissions) {\n    if (targetNames) {\n      // @ts-ignore: name isn't part of the type\n      const value = attribute.$['android:name'] || attribute.$.name;\n      if (!targetNames.includes(value)) {\n        nextPermissions.push(attribute);\n      }\n    }\n  }\n\n  androidManifest.manifest[USES_PERMISSION] = nextPermissions;\n}\n\nexport function addPermission(androidManifest: AndroidManifest, permissionName: string): void {\n  const usesPermissions: ManifestUsesPermission[] = androidManifest.manifest[USES_PERMISSION] || [];\n  usesPermissions.push({\n    $: { 'android:name': permissionName },\n  });\n  androidManifest.manifest[USES_PERMISSION] = usesPermissions;\n}\n\nexport function ensurePermissions(\n  androidManifest: AndroidManifest,\n  permissionNames: string[]\n): { [permission: string]: boolean } {\n  const permissions = getPermissions(androidManifest);\n\n  const results: { [permission: string]: boolean } = {};\n  for (const permissionName of permissionNames) {\n    const targetName = ensurePermissionNameFormat(permissionName);\n    if (!permissions.includes(targetName)) {\n      addPermission(androidManifest, targetName);\n      results[permissionName] = true;\n    } else {\n      results[permissionName] = false;\n    }\n  }\n  return results;\n}\n\nexport function ensurePermission(\n  androidManifest: AndroidManifest,\n  permissionName: string\n): boolean {\n  const permissions = getPermissions(androidManifest);\n  const targetName = ensurePermissionNameFormat(permissionName);\n\n  if (!permissions.includes(targetName)) {\n    addPermission(androidManifest, targetName);\n    return true;\n  }\n  return false;\n}\n\nexport function ensurePermissionNameFormat(permissionName: string): string {\n  if (permissionName.includes('.')) {\n    const com = permissionName.split('.');\n    const name = com.pop() as string;\n    return [...com, name.toUpperCase()].join('.');\n  } else {\n    // If shorthand form like `WRITE_CONTACTS` is provided, expand it to `android.permission.WRITE_CONTACTS`.\n    return ensurePermissionNameFormat(`android.permission.${permissionName}`);\n  }\n}\n\nexport function getPermissions(androidManifest: AndroidManifest): string[] {\n  const usesPermissions: { [key: string]: any }[] = androidManifest.manifest[USES_PERMISSION] || [];\n  const permissions = usesPermissions.map((permissionObject) => {\n    return permissionObject.$['android:name'] || permissionObject.$.name;\n  });\n  return permissions;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,gBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,eAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMG,eAAe,GAAG,iBAAiB;AAElC,MAAMC,eAA8C,GAAGA,CAACC,MAAM,EAAEC,WAAW,KAAK;EACrF,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;IAC9BA,WAAW,GAAGA,WAAW,CAACG,MAAM,CAACC,OAAO,CAAC;IACzC,IAAI,CAACL,MAAM,CAACM,OAAO,EAAEN,MAAM,CAACM,OAAO,GAAG,CAAC,CAAC;IACxC,IAAI,CAACN,MAAM,CAACM,OAAO,CAACL,WAAW,EAAED,MAAM,CAACM,OAAO,CAACL,WAAW,GAAG,EAAE;IAChED,MAAM,CAACM,OAAO,CAACL,WAAW,GAAG;IAC3B;IACA,GAAG,IAAIM,GAAG,CAACP,MAAM,CAACM,OAAO,CAACL,WAAW,CAACO,MAAM,CAACP,WAAW,CAAC,CAAC,CAC3D;EACH;EACA,OAAO,IAAAQ,qCAAmB,EAACT,MAAM,EAAE,MAAOA,MAAM,IAAK;IACnDA,MAAM,CAACU,UAAU,GAAG,MAAMC,qBAAqB,CAACX,MAAM,EAAEA,MAAM,CAACU,UAAU,CAAC;IAC1E,OAAOV,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAY,OAAA,CAAAb,eAAA,GAAAA,eAAA;AACO,MAAMc,sBAAuD,GAAGA,CAACb,MAAM,EAAEC,WAAW,KAAK;EAC9F,MAAMa,mBAAmB,GAAGC,mCAAmC,CAC7D,CAACb,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC,EAAEG,MAAM,CAACC,OAAO,CAC3E,CAAC;EAED,IAAIL,MAAM,EAAEM,OAAO,EAAEL,WAAW,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACM,OAAO,CAACL,WAAW,CAAC,EAAE;IAC7E;IACAD,MAAM,CAACM,OAAO,CAACL,WAAW,GAAGc,mCAAmC,CAC9Df,MAAM,CAACM,OAAO,CAACL,WACjB,CAAC,CAACG,MAAM,CAAEY,UAAU,IAAK,CAACF,mBAAmB,CAACG,QAAQ,CAACD,UAAU,CAAC,CAAC;EACrE;EAEA,OAAO,IAAAP,qCAAmB,EAACT,MAAM,EAAE,MAAOA,MAAM,IAAK;IACnDA,MAAM,CAACU,UAAU,GAAG,IAAAQ,gCAAoB,EAAClB,MAAM,CAACU,UAAU,CAAC;IAC3DV,MAAM,CAACU,UAAU,GAAGS,qBAAqB,CAACnB,MAAM,CAACU,UAAU,EAAEI,mBAAmB,CAAC;IACjF,OAAOd,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACY,OAAA,CAAAC,sBAAA,GAAAA,sBAAA;AAEK,MAAMO,8BAA4C,GAAIpB,MAAM,IAAK;EACtE;EACA;EACA,IAAIA,MAAM,CAACM,OAAO,EAAEe,kBAAkB,EAAEC,MAAM,EAAE;IAC9C,OAAOT,sBAAsB,CAACb,MAAM,EAAEA,MAAM,CAACM,OAAO,CAACe,kBAAkB,CAAC;EAC1E;EAEA,OAAOrB,MAAM;AACf,CAAC;AAACY,OAAA,CAAAQ,8BAAA,GAAAA,8BAAA;AAEK,SAASD,qBAAqBA,CAACI,eAAgC,EAAEtB,WAAqB,EAAE;EAC7F,IAAI,CAACC,KAAK,CAACC,OAAO,CAACoB,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;IAC/DD,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,EAAE;EAClD;EAEA,KAAK,MAAMR,UAAU,IAAID,mCAAmC,CAACd,WAAW,CAAC,EAAE;IACzEsB,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,GAAGC,uBAAuB,CACnEF,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAC3CR,UACF,CAAC;EACH;EAEA,OAAOO,eAAe;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAC9BC,mBAA6C,EAC7CV,UAAkB,EAClB;EACA;EACAU,mBAAmB,GAAGA,mBAAmB,CAACtB,MAAM,CAAEuB,CAAC,IAAKA,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,KAAKZ,UAAU,CAAC;;EAE3F;EACAU,mBAAmB,CAACG,IAAI,CAAC;IACvBD,CAAC,EAAE;MAAE,cAAc,EAAEZ,UAAU;MAAE,YAAY,EAAE;IAAS;EAC1D,CAAC,CAAC;EACF,OAAOU,mBAAmB;AAC5B;AAEA,SAASX,mCAAmCA,CAACd,WAAqB,EAAY;EAC5E,OAAOA,WAAW,CAAC6B,GAAG,CAAEd,UAAU,IAAK;IACrC,IAAI,CAACA,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7B,OAAO,sBAAsBD,UAAU,EAAE;IAC3C;IACA,OAAOA,UAAU;EACnB,CAAC,CAAC;AACJ;AAEO,SAASe,qBAAqBA,CAAC/B,MAAmC,EAAY;EACnF,OAAOA,MAAM,CAACM,OAAO,EAAEL,WAAW,IAAI,EAAE;AAC1C;AAEO,SAASU,qBAAqBA,CACnCX,MAAmC,EACnCuB,eAAgC,EAChC;EACA,MAAMtB,WAAW,GAAG8B,qBAAqB,CAAC/B,MAAM,CAAC;EACjD,MAAMgC,mBAAmB,GAAGjB,mCAAmC,CAACd,WAAW,CAAC;EAC5E,MAAMgC,gBAAgB,GAAG,CAAC,GAAGD,mBAAmB,CAAC;EAEjD,IAAI,CAACT,eAAe,CAACC,QAAQ,CAACU,cAAc,CAAC,iBAAiB,CAAC,EAAE;IAC/DX,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,EAAE;EAClD;EACA;;EAEA,MAAME,mBAAmB,GAAGH,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,EAAE;EAE7ES,gBAAgB,CAACE,OAAO,CAAEnB,UAAU,IAAK;IACvC,IAAI,CAACoB,4BAA4B,CAACpB,UAAU,EAAEU,mBAAmB,CAAC,EAAE;MAClEW,uBAAuB,CAACrB,UAAU,EAAEU,mBAAmB,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF,OAAOH,eAAe;AACxB;AAEO,SAASa,4BAA4BA,CAC1CpB,UAAkB,EAClBU,mBAA6C,EACpC;EACT,OAAOA,mBAAmB,CAACY,IAAI,CAAEX,CAAC,IAAKA,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,KAAKZ,UAAU,CAAC;AAC5E;AAEO,SAASqB,uBAAuBA,CACrCrB,UAAkB,EAClBU,mBAA6C,EAC7C;EACAA,mBAAmB,CAACG,IAAI,CAAC;IAAED,CAAC,EAAE;MAAE,cAAc,EAAEZ;IAAW;EAAE,CAAC,CAAC;EAC/D,OAAOU,mBAAmB;AAC5B;AAEO,SAASa,iBAAiBA,CAAChB,eAAgC,EAAEiB,eAA0B,EAAE;EAC9F,MAAMC,WAAW,GAAGD,eAAe,GAAGA,eAAe,CAACV,GAAG,CAACY,0BAA0B,CAAC,GAAG,IAAI;EAC5F,MAAMzC,WAAW,GAAGsB,eAAe,CAACC,QAAQ,CAAC1B,eAAe,CAAC,IAAI,EAAE;EACnE,MAAM6C,eAAe,GAAG,EAAE;EAC1B,KAAK,MAAMC,SAAS,IAAI3C,WAAW,EAAE;IACnC,IAAIwC,WAAW,EAAE;MACf;MACA,MAAMI,KAAK,GAAGD,SAAS,CAAChB,CAAC,CAAC,cAAc,CAAC,IAAIgB,SAAS,CAAChB,CAAC,CAACkB,IAAI;MAC7D,IAAI,CAACL,WAAW,CAACxB,QAAQ,CAAC4B,KAAK,CAAC,EAAE;QAChCF,eAAe,CAACd,IAAI,CAACe,SAAS,CAAC;MACjC;IACF;EACF;EAEArB,eAAe,CAACC,QAAQ,CAAC1B,eAAe,CAAC,GAAG6C,eAAe;AAC7D;AAEO,SAASI,aAAaA,CAACxB,eAAgC,EAAEyB,cAAsB,EAAQ;EAC5F,MAAMC,eAAyC,GAAG1B,eAAe,CAACC,QAAQ,CAAC1B,eAAe,CAAC,IAAI,EAAE;EACjGmD,eAAe,CAACpB,IAAI,CAAC;IACnBD,CAAC,EAAE;MAAE,cAAc,EAAEoB;IAAe;EACtC,CAAC,CAAC;EACFzB,eAAe,CAACC,QAAQ,CAAC1B,eAAe,CAAC,GAAGmD,eAAe;AAC7D;AAEO,SAASC,iBAAiBA,CAC/B3B,eAAgC,EAChCiB,eAAyB,EACU;EACnC,MAAMvC,WAAW,GAAGkD,cAAc,CAAC5B,eAAe,CAAC;EAEnD,MAAM6B,OAA0C,GAAG,CAAC,CAAC;EACrD,KAAK,MAAMJ,cAAc,IAAIR,eAAe,EAAE;IAC5C,MAAMa,UAAU,GAAGX,0BAA0B,CAACM,cAAc,CAAC;IAC7D,IAAI,CAAC/C,WAAW,CAACgB,QAAQ,CAACoC,UAAU,CAAC,EAAE;MACrCN,aAAa,CAACxB,eAAe,EAAE8B,UAAU,CAAC;MAC1CD,OAAO,CAACJ,cAAc,CAAC,GAAG,IAAI;IAChC,CAAC,MAAM;MACLI,OAAO,CAACJ,cAAc,CAAC,GAAG,KAAK;IACjC;EACF;EACA,OAAOI,OAAO;AAChB;AAEO,SAASE,gBAAgBA,CAC9B/B,eAAgC,EAChCyB,cAAsB,EACb;EACT,MAAM/C,WAAW,GAAGkD,cAAc,CAAC5B,eAAe,CAAC;EACnD,MAAM8B,UAAU,GAAGX,0BAA0B,CAACM,cAAc,CAAC;EAE7D,IAAI,CAAC/C,WAAW,CAACgB,QAAQ,CAACoC,UAAU,CAAC,EAAE;IACrCN,aAAa,CAACxB,eAAe,EAAE8B,UAAU,CAAC;IAC1C,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAEO,SAASX,0BAA0BA,CAACM,cAAsB,EAAU;EACzE,IAAIA,cAAc,CAAC/B,QAAQ,CAAC,GAAG,CAAC,EAAE;IAChC,MAAMsC,GAAG,GAAGP,cAAc,CAACQ,KAAK,CAAC,GAAG,CAAC;IACrC,MAAMV,IAAI,GAAGS,GAAG,CAACE,GAAG,CAAC,CAAW;IAChC,OAAO,CAAC,GAAGF,GAAG,EAAET,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC/C,CAAC,MAAM;IACL;IACA,OAAOjB,0BAA0B,CAAC,sBAAsBM,cAAc,EAAE,CAAC;EAC3E;AACF;AAEO,SAASG,cAAcA,CAAC5B,eAAgC,EAAY;EACzE,MAAM0B,eAAyC,GAAG1B,eAAe,CAACC,QAAQ,CAAC1B,eAAe,CAAC,IAAI,EAAE;EACjG,MAAMG,WAAW,GAAGgD,eAAe,CAACnB,GAAG,CAAE8B,gBAAgB,IAAK;IAC5D,OAAOA,gBAAgB,CAAChC,CAAC,CAAC,cAAc,CAAC,IAAIgC,gBAAgB,CAAChC,CAAC,CAACkB,IAAI;EACtE,CAAC,CAAC;EACF,OAAO7C,WAAW;AACpB", "ignoreList": []}