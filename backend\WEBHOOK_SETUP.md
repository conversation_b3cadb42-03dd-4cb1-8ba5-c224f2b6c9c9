# Viva Wallet Webhook Setup Guide

This guide helps you set up webhook notifications for Viva Wallet payments.

## Quick Start

### 1. Set Environment Variables

```bash
export VIVA_MERCHANT_ID="your_merchant_id"
export VIVA_API_KEY="your_api_key"
export VIVA_ENVIRONMENT="demo"  # or "production"
```

### 2. Generate Verification Key

```bash
# Using npm script
npm run viva:webhook

# Or directly with node
node webhookurl.js
```

### 3. Test Webhook Endpoint

```bash
# Test if your webhook endpoint is accessible
npm run viva:webhook:test

# Or directly
node webhookurl.js --test
```

## Webhook URL

Your webhook endpoint is configured as:
```
https://multiposbackend.onrender.com/api/v1/viva/webhook
```

## Environment Configuration

### Demo Environment
- **Merchant Portal**: https://demo.vivapayments.com/
- **API Endpoint**: https://demo.vivapayments.com/api/messages/config/token

### Production Environment  
- **Merchant Portal**: https://www.vivapayments.com/
- **API Endpoint**: https://www.vivapayments.com/api/messages/config/token

## Webhook Setup Steps

1. **Generate Verification Key**
   ```bash
   npm run viva:webhook
   ```

2. **Log in to Viva Self Care**
   - Demo: https://demo.vivapayments.com/
   - Production: https://www.vivapayments.com/

3. **Navigate to Webhooks**
   - Go to Settings > API Access > Webhooks

4. **Create New Webhook**
   - Click "Create Webhook"
   - Enter URL: `https://multiposbackend.onrender.com/api/v1/viva/webhook`
   - Click "Verify" (should return the verification key)

5. **Configure Event Types**
   - ✅ Transaction Payment Created (1796)
   - ✅ Transaction Failed (1798)  
   - ✅ Transaction Reversal Created (1797)

6. **Activate Webhook**
   - Check "Active" checkbox
   - Click "Save"

## Supported Event Types

| Event Type | ID | Description |
|------------|----|-----------| 
| Transaction Payment Created | 1796 | Payment successfully completed |
| Transaction Failed | 1798 | Payment failed or declined |
| Transaction Reversal Created | 1797 | Payment refunded/reversed |
| POS ECR Session Created | 1802 | POS session started |
| POS ECR Session Failed | 1803 | POS session failed |

## Webhook Response Format

Your webhook endpoint will receive POST requests with this structure:

```json
{
  "EventTypeId": 1796,
  "EventData": {
    "TransactionId": "252b950e-27f2-4300-ada1-4dedd7c17904",
    "OrderCode": *********,
    "StatusId": "F",
    "Amount": 100,
    "CurrencyCode": "978",
    "MerchantId": "your-merchant-id",
    "MerchantTrns": "Payment description",
    "CustomerTrns": "Customer reference",
    "AuthorizationId": "123456",
    "ResponseCode": "00",
    "ReferenceNumber": "************",
    "CardNumber": "414746XXXXXX0133",
    "CardToken": "token123",
    "BankId": "VISA",
    "InsDate": "2024-01-01T12:00:00Z"
  },
  "Created": "2024-01-01T12:00:00Z",
  "CorrelationId": "correlation-123",
  "MessageId": "message-456",
  "RetryCount": 0
}
```

## Troubleshooting

### Authentication Errors (401)
- Verify your Merchant ID and API Key
- Ensure you're using the correct environment (demo/production)
- Check that credentials match the environment

### Webhook Verification Failed
- Ensure your webhook endpoint is publicly accessible
- Check that the endpoint returns the verification key in JSON format
- Verify TLS 1.2 support on your server

### No Webhook Notifications
- Confirm webhook is marked as "Active" in Viva Self Care
- Check that event types are properly selected
- Verify webhook URL is correct and accessible
- Check server logs for incoming requests

## Testing

You can test webhook functionality by:

1. **Testing endpoint accessibility**:
   ```bash
   curl https://multiposbackend.onrender.com/api/v1/viva/webhook
   ```

2. **Making a test payment** in the Viva demo environment

3. **Checking webhook logs** in your application

## Documentation

- [Viva Wallet Webhooks Documentation](https://developer.vivawallet.com/webhooks/)
- [Webhook Events Reference](https://developer.vivawallet.com/webhooks/webhook-events/)
- [API Authentication](https://developer.vivawallet.com/api-reference-guide/authentication/)
