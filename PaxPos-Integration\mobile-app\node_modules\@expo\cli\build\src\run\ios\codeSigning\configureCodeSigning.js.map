{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/configureCodeSigning.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport * as Security from './Security';\nimport { resolveCertificateSigningIdentityAsync } from './resolveCertificateSigningIdentity';\nimport { getCodeSigningInfoForPbxproj, setAutoCodeSigningInfoForPbxproj } from './xcodeCodeSigning';\nimport * as Log from '../../../log';\n\nexport async function ensureDeviceIsCodeSignedForDeploymentAsync(\n  projectRoot: string\n): Promise<string | null> {\n  if (isCodeSigningConfigured(projectRoot)) {\n    return null;\n  }\n  return configureCodeSigningAsync(projectRoot);\n}\n\nfunction isCodeSigningConfigured(projectRoot: string): boolean {\n  // Check if the app already has a development team defined.\n  const signingInfo = getCodeSigningInfoForPbxproj(projectRoot);\n\n  const allTargetsHaveTeams = Object.values(signingInfo).reduce((prev, curr) => {\n    return prev && !!curr.developmentTeams.length;\n  }, true);\n\n  if (allTargetsHaveTeams) {\n    const teamList = Object.values(signingInfo).reduce<string[]>((prev, curr) => {\n      return prev.concat([curr.developmentTeams[0]]);\n    }, []);\n    Log.log(chalk.dim`\\u203A Auto signing app using team(s): ${teamList.join(', ')}`);\n    return true;\n  }\n\n  const allTargetsHaveProfiles = Object.values(signingInfo).reduce((prev, curr) => {\n    return prev && !!curr.provisioningProfiles.length;\n  }, true);\n\n  if (allTargetsHaveProfiles) {\n    // this indicates that the user has manual code signing setup (possibly for production).\n    return true;\n  }\n  return false;\n}\n\nasync function configureCodeSigningAsync(projectRoot: string) {\n  const ids = await Security.findIdentitiesAsync();\n\n  const id = await resolveCertificateSigningIdentityAsync(projectRoot, ids);\n\n  Log.log(`\\u203A Signing and building iOS app with: ${id.codeSigningInfo}`);\n\n  setAutoCodeSigningInfoForPbxproj(projectRoot, {\n    appleTeamId: id.appleTeamId!,\n  });\n  return id.appleTeamId!;\n}\n"], "names": ["ensureDeviceIsCodeSignedForDeploymentAsync", "projectRoot", "isCodeSigningConfigured", "configureCodeSigningAsync", "signingInfo", "getCodeSigningInfoForPbxproj", "allTargetsHaveTeams", "Object", "values", "reduce", "prev", "curr", "developmentTeams", "length", "teamList", "concat", "Log", "log", "chalk", "dim", "join", "allTargetsHaveProfiles", "provisioningProfiles", "ids", "Security", "findIdentitiesAsync", "id", "resolveCertificateSigningIdentityAsync", "codeSigningInfo", "setAutoCodeSigningInfoForPbxproj", "appleTeamId"], "mappings": ";;;;+BAOsBA;;;eAAAA;;;;gEAPJ;;;;;;kEAEQ;mDAC6B;kCACwB;6DAC1D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,eAAeA,2CACpBC,WAAmB;IAEnB,IAAIC,wBAAwBD,cAAc;QACxC,OAAO;IACT;IACA,OAAOE,0BAA0BF;AACnC;AAEA,SAASC,wBAAwBD,WAAmB;IAClD,2DAA2D;IAC3D,MAAMG,cAAcC,IAAAA,8CAA4B,EAACJ;IAEjD,MAAMK,sBAAsBC,OAAOC,MAAM,CAACJ,aAAaK,MAAM,CAAC,CAACC,MAAMC;QACnE,OAAOD,QAAQ,CAAC,CAACC,KAAKC,gBAAgB,CAACC,MAAM;IAC/C,GAAG;IAEH,IAAIP,qBAAqB;QACvB,MAAMQ,WAAWP,OAAOC,MAAM,CAACJ,aAAaK,MAAM,CAAW,CAACC,MAAMC;YAClE,OAAOD,KAAKK,MAAM,CAAC;gBAACJ,KAAKC,gBAAgB,CAAC,EAAE;aAAC;QAC/C,GAAG,EAAE;QACLI,KAAIC,GAAG,CAACC,gBAAK,CAACC,GAAG,CAAC,uCAAuC,EAAEL,SAASM,IAAI,CAAC,MAAM,CAAC;QAChF,OAAO;IACT;IAEA,MAAMC,yBAAyBd,OAAOC,MAAM,CAACJ,aAAaK,MAAM,CAAC,CAACC,MAAMC;QACtE,OAAOD,QAAQ,CAAC,CAACC,KAAKW,oBAAoB,CAACT,MAAM;IACnD,GAAG;IAEH,IAAIQ,wBAAwB;QAC1B,wFAAwF;QACxF,OAAO;IACT;IACA,OAAO;AACT;AAEA,eAAelB,0BAA0BF,WAAmB;IAC1D,MAAMsB,MAAM,MAAMC,UAASC,mBAAmB;IAE9C,MAAMC,KAAK,MAAMC,IAAAA,yEAAsC,EAAC1B,aAAasB;IAErEP,KAAIC,GAAG,CAAC,CAAC,0CAA0C,EAAES,GAAGE,eAAe,EAAE;IAEzEC,IAAAA,kDAAgC,EAAC5B,aAAa;QAC5C6B,aAAaJ,GAAGI,WAAW;IAC7B;IACA,OAAOJ,GAAGI,WAAW;AACvB"}