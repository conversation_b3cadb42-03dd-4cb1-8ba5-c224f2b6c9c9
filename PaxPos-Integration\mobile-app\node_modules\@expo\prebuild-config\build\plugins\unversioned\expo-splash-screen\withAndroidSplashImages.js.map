{"version": 3, "file": "withAndroidSplashImages.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "_fs", "_interopRequireDefault", "_path", "_getAndroidSplashConfig", "e", "__esModule", "default", "IMAGE_CACHE_NAME", "SPLASH_SCREEN_FILENAME", "SPLASH_SCREEN_DRAWABLE_NAME", "DRAWABLES_CONFIGS", "modes", "light", "path", "dark", "dimensionsMultiplier", "mdpi", "hdpi", "xhdpi", "xxhdpi", "xxxhdpi", "withAndroidSplashImages", "config", "splash", "withDangerousMod", "setSplashImageDrawablesAsync", "modRequest", "projectRoot", "imageWidth", "exports", "props", "clearAllExistingSplashImagesAsync", "getAndroidSplashConfig", "darkSplash", "getAndroidDarkSplashConfig", "Promise", "all", "setSplashImageDrawablesForThemeAsync", "and<PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "Object", "values", "map", "filePath", "fs", "promises", "rm", "resolve", "force", "recursive", "theme", "drawable", "writeSplashScreenDrawablesAsync", "sizes", "image<PERSON>ey", "image", "multiplier", "size", "canvasSize", "background", "generateImageBackgroundAsync", "width", "height", "backgroundColor", "resizeMode", "source", "foreground", "generateImageAsync", "cacheType", "src", "composedImage", "compositeImagesAsync", "x", "y", "outputPath", "folder", "dirname", "mkdir", "writeFile", "drawablePath", "lightDrawablePath", "darkDrawablePath", "lightFolder", "copyFile", "icon", "darkIcon", "darkFolder"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withAndroidSplashImages.ts"], "sourcesContent": ["import { ConfigPlugin, withDangerousMod } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport {\n  generateImageAsync,\n  compositeImagesAsync,\n  generateImageBackgroundAsync,\n} from '@expo/image-utils';\nimport fs from 'fs';\nimport path from 'path';\n\nimport {\n  AndroidSplashConfig,\n  getAndroidDarkSplashConfig,\n  getAndroidSplashConfig,\n  SplashScreenConfig,\n} from './getAndroidSplashConfig';\n\ntype DRAWABLE_SIZE = 'default' | 'mdpi' | 'hdpi' | 'xhdpi' | 'xxhdpi' | 'xxxhdpi';\ntype THEME = 'light' | 'dark';\n\nconst IMAGE_CACHE_NAME = 'splash-android';\nconst SPLASH_SCREEN_FILENAME = 'splashscreen_logo.png';\nconst SPLASH_SCREEN_DRAWABLE_NAME = 'splashscreen_logo.xml';\n\nconst DRAWABLES_CONFIGS: {\n  [key in DRAWABLE_SIZE]: {\n    modes: {\n      [key in THEME]: {\n        path: string;\n      };\n    };\n    dimensionsMultiplier: number;\n  };\n} = {\n  default: {\n    modes: {\n      light: {\n        path: `./res/drawable/${SPLASH_SCREEN_DRAWABLE_NAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night/${SPLASH_SCREEN_DRAWABLE_NAME}`,\n      },\n    },\n    dimensionsMultiplier: 1,\n  },\n  mdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-mdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-mdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 1,\n  },\n  hdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-hdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-hdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 1.5,\n  },\n  xhdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-xhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-xhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 2,\n  },\n  xxhdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-xxhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-xxhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 3,\n  },\n  xxxhdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-xxxhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-xxxhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 4,\n  },\n};\n\nexport const withAndroidSplashImages: ConfigPlugin<AndroidSplashConfig | null> = (\n  config,\n  splash\n) => {\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      if (splash) {\n        await setSplashImageDrawablesAsync(\n          config,\n          splash,\n          config.modRequest.projectRoot,\n          splash?.imageWidth ?? 200\n        );\n      }\n      return config;\n    },\n  ]);\n};\n\n/**\n * Deletes all previous splash_screen_images and copies new one to desired drawable directory.\n * If path isn't provided then no new image is placed in drawable directories.\n * @see https://developer.android.com/training/multiscreen/screendensities\n *\n * @param androidMainPath Absolute path to the main directory containing code and resources in Android project. In general that would be `android/app/src/main`.\n */\nexport async function setSplashImageDrawablesAsync(\n  config: Pick<ExpoConfig, 'android' | 'splash'>,\n  props: AndroidSplashConfig | null,\n  projectRoot: string,\n  imageWidth: number\n) {\n  await clearAllExistingSplashImagesAsync(projectRoot);\n\n  const splash = getAndroidSplashConfig(config, props);\n  const darkSplash = getAndroidDarkSplashConfig(config, props);\n\n  await Promise.all([\n    setSplashImageDrawablesForThemeAsync(splash, 'light', projectRoot, imageWidth),\n    setSplashImageDrawablesForThemeAsync(darkSplash, 'dark', projectRoot, imageWidth),\n  ]);\n}\n\nasync function clearAllExistingSplashImagesAsync(projectRoot: string) {\n  const androidMainPath = path.join(projectRoot, 'android/app/src/main');\n\n  await Promise.all(\n    Object.values(DRAWABLES_CONFIGS).map(async ({ modes }) => {\n      await Promise.all(\n        Object.values(modes).map(async ({ path: filePath }) => {\n          await fs.promises.rm(path.resolve(androidMainPath, filePath), {\n            force: true,\n            recursive: true,\n          });\n        })\n      );\n    })\n  );\n}\n\nexport async function setSplashImageDrawablesForThemeAsync(\n  config: SplashScreenConfig | null,\n  theme: 'dark' | 'light',\n  projectRoot: string,\n  imageWidth: number = 100\n) {\n  if (!config) return;\n  const androidMainPath = path.join(projectRoot, 'android/app/src/main');\n\n  if (config.drawable) {\n    await writeSplashScreenDrawablesAsync(androidMainPath, projectRoot, config.drawable);\n    return;\n  }\n\n  const sizes: DRAWABLE_SIZE[] = ['mdpi', 'hdpi', 'xhdpi', 'xxhdpi', 'xxxhdpi'];\n\n  await Promise.all(\n    sizes.map(async (imageKey) => {\n      // @ts-ignore\n      const image = config[imageKey];\n\n      if (image) {\n        const multiplier = DRAWABLES_CONFIGS[imageKey].dimensionsMultiplier;\n        const size = imageWidth * multiplier; // \"imageWidth\" must be replaced by the logo width chosen by the user in its config file\n        const canvasSize = 288 * multiplier;\n\n        const background = await generateImageBackgroundAsync({\n          width: canvasSize,\n          height: canvasSize,\n          backgroundColor: config.backgroundColor ?? 'transparent',\n          resizeMode: 'cover',\n        });\n\n        const { source: foreground } = await generateImageAsync(\n          {\n            projectRoot,\n            cacheType: IMAGE_CACHE_NAME,\n          },\n          {\n            src: image,\n            resizeMode: 'contain',\n            width: size,\n            height: size,\n          }\n        );\n\n        const composedImage = await compositeImagesAsync({\n          background,\n          foreground,\n          x: (canvasSize - size) / 2,\n          y: (canvasSize - size) / 2,\n        });\n\n        // Get output path for drawable.\n        const outputPath = path.join(\n          androidMainPath,\n          DRAWABLES_CONFIGS[imageKey].modes[theme].path\n        );\n\n        const folder = path.dirname(outputPath);\n        // Ensure directory exists.\n        await fs.promises.mkdir(folder, { recursive: true });\n        await fs.promises.writeFile(outputPath, composedImage);\n      }\n      return null;\n    })\n  );\n}\n\nasync function writeSplashScreenDrawablesAsync(\n  drawablePath: string,\n  projectRoot: string,\n  drawable: SplashScreenConfig['drawable']\n) {\n  if (!drawable) {\n    return;\n  }\n\n  const lightDrawablePath = path.join(drawablePath, DRAWABLES_CONFIGS.default.modes.light.path);\n  const darkDrawablePath = path.join(drawablePath, DRAWABLES_CONFIGS.default.modes.dark.path);\n\n  const lightFolder = path.dirname(lightDrawablePath);\n  await fs.promises.mkdir(lightFolder, { recursive: true });\n  await fs.promises.copyFile(path.join(projectRoot, drawable.icon), lightDrawablePath);\n\n  if (drawable.darkIcon) {\n    const darkFolder = path.dirname(darkDrawablePath);\n    await fs.promises.mkdir(darkFolder, { recursive: true });\n    await fs.promises.copyFile(path.join(projectRoot, drawable.darkIcon), darkDrawablePath);\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,wBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,uBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKkC,SAAAI,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAKlC,MAAMG,gBAAgB,GAAG,gBAAgB;AACzC,MAAMC,sBAAsB,GAAG,uBAAuB;AACtD,MAAMC,2BAA2B,GAAG,uBAAuB;AAE3D,MAAMC,iBASL,GAAG;EACFJ,OAAO,EAAE;IACPK,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAE,kBAAkBJ,2BAA2B;MACrD,CAAC;MACDK,IAAI,EAAE;QACJD,IAAI,EAAE,wBAAwBJ,2BAA2B;MAC3D;IACF,CAAC;IACDM,oBAAoB,EAAE;EACxB,CAAC;EACDC,IAAI,EAAE;IACJL,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAE,uBAAuBL,sBAAsB;MACrD,CAAC;MACDM,IAAI,EAAE;QACJD,IAAI,EAAE,6BAA6BL,sBAAsB;MAC3D;IACF,CAAC;IACDO,oBAAoB,EAAE;EACxB,CAAC;EACDE,IAAI,EAAE;IACJN,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAE,uBAAuBL,sBAAsB;MACrD,CAAC;MACDM,IAAI,EAAE;QACJD,IAAI,EAAE,6BAA6BL,sBAAsB;MAC3D;IACF,CAAC;IACDO,oBAAoB,EAAE;EACxB,CAAC;EACDG,KAAK,EAAE;IACLP,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAE,wBAAwBL,sBAAsB;MACtD,CAAC;MACDM,IAAI,EAAE;QACJD,IAAI,EAAE,8BAA8BL,sBAAsB;MAC5D;IACF,CAAC;IACDO,oBAAoB,EAAE;EACxB,CAAC;EACDI,MAAM,EAAE;IACNR,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAE,yBAAyBL,sBAAsB;MACvD,CAAC;MACDM,IAAI,EAAE;QACJD,IAAI,EAAE,+BAA+BL,sBAAsB;MAC7D;IACF,CAAC;IACDO,oBAAoB,EAAE;EACxB,CAAC;EACDK,OAAO,EAAE;IACPT,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAE,0BAA0BL,sBAAsB;MACxD,CAAC;MACDM,IAAI,EAAE;QACJD,IAAI,EAAE,gCAAgCL,sBAAsB;MAC9D;IACF,CAAC;IACDO,oBAAoB,EAAE;EACxB;AACF,CAAC;AAEM,MAAMM,uBAAiE,GAAGA,CAC/EC,MAAM,EACNC,MAAM,KACH;EACH,OAAO,IAAAC,iCAAgB,EAACF,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChB,IAAIC,MAAM,EAAE;MACV,MAAME,4BAA4B,CAChCH,MAAM,EACNC,MAAM,EACND,MAAM,CAACI,UAAU,CAACC,WAAW,EAC7BJ,MAAM,EAAEK,UAAU,IAAI,GACxB,CAAC;IACH;IACA,OAAON,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANAO,OAAA,CAAAR,uBAAA,GAAAA,uBAAA;AAOO,eAAeI,4BAA4BA,CAChDH,MAA8C,EAC9CQ,KAAiC,EACjCH,WAAmB,EACnBC,UAAkB,EAClB;EACA,MAAMG,iCAAiC,CAACJ,WAAW,CAAC;EAEpD,MAAMJ,MAAM,GAAG,IAAAS,gDAAsB,EAACV,MAAM,EAAEQ,KAAK,CAAC;EACpD,MAAMG,UAAU,GAAG,IAAAC,oDAA0B,EAACZ,MAAM,EAAEQ,KAAK,CAAC;EAE5D,MAAMK,OAAO,CAACC,GAAG,CAAC,CAChBC,oCAAoC,CAACd,MAAM,EAAE,OAAO,EAAEI,WAAW,EAAEC,UAAU,CAAC,EAC9ES,oCAAoC,CAACJ,UAAU,EAAE,MAAM,EAAEN,WAAW,EAAEC,UAAU,CAAC,CAClF,CAAC;AACJ;AAEA,eAAeG,iCAAiCA,CAACJ,WAAmB,EAAE;EACpE,MAAMW,eAAe,GAAGzB,eAAI,CAAC0B,IAAI,CAACZ,WAAW,EAAE,sBAAsB,CAAC;EAEtE,MAAMQ,OAAO,CAACC,GAAG,CACfI,MAAM,CAACC,MAAM,CAAC/B,iBAAiB,CAAC,CAACgC,GAAG,CAAC,OAAO;IAAE/B;EAAM,CAAC,KAAK;IACxD,MAAMwB,OAAO,CAACC,GAAG,CACfI,MAAM,CAACC,MAAM,CAAC9B,KAAK,CAAC,CAAC+B,GAAG,CAAC,OAAO;MAAE7B,IAAI,EAAE8B;IAAS,CAAC,KAAK;MACrD,MAAMC,aAAE,CAACC,QAAQ,CAACC,EAAE,CAACjC,eAAI,CAACkC,OAAO,CAACT,eAAe,EAAEK,QAAQ,CAAC,EAAE;QAC5DK,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CACH,CAAC;EACH,CAAC,CACH,CAAC;AACH;AAEO,eAAeZ,oCAAoCA,CACxDf,MAAiC,EACjC4B,KAAuB,EACvBvB,WAAmB,EACnBC,UAAkB,GAAG,GAAG,EACxB;EACA,IAAI,CAACN,MAAM,EAAE;EACb,MAAMgB,eAAe,GAAGzB,eAAI,CAAC0B,IAAI,CAACZ,WAAW,EAAE,sBAAsB,CAAC;EAEtE,IAAIL,MAAM,CAAC6B,QAAQ,EAAE;IACnB,MAAMC,+BAA+B,CAACd,eAAe,EAAEX,WAAW,EAAEL,MAAM,CAAC6B,QAAQ,CAAC;IACpF;EACF;EAEA,MAAME,KAAsB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;EAE7E,MAAMlB,OAAO,CAACC,GAAG,CACfiB,KAAK,CAACX,GAAG,CAAC,MAAOY,QAAQ,IAAK;IAC5B;IACA,MAAMC,KAAK,GAAGjC,MAAM,CAACgC,QAAQ,CAAC;IAE9B,IAAIC,KAAK,EAAE;MACT,MAAMC,UAAU,GAAG9C,iBAAiB,CAAC4C,QAAQ,CAAC,CAACvC,oBAAoB;MACnE,MAAM0C,IAAI,GAAG7B,UAAU,GAAG4B,UAAU,CAAC,CAAC;MACtC,MAAME,UAAU,GAAG,GAAG,GAAGF,UAAU;MAEnC,MAAMG,UAAU,GAAG,MAAM,IAAAC,0CAA4B,EAAC;QACpDC,KAAK,EAAEH,UAAU;QACjBI,MAAM,EAAEJ,UAAU;QAClBK,eAAe,EAAEzC,MAAM,CAACyC,eAAe,IAAI,aAAa;QACxDC,UAAU,EAAE;MACd,CAAC,CAAC;MAEF,MAAM;QAAEC,MAAM,EAAEC;MAAW,CAAC,GAAG,MAAM,IAAAC,gCAAkB,EACrD;QACExC,WAAW;QACXyC,SAAS,EAAE7D;MACb,CAAC,EACD;QACE8D,GAAG,EAAEd,KAAK;QACVS,UAAU,EAAE,SAAS;QACrBH,KAAK,EAAEJ,IAAI;QACXK,MAAM,EAAEL;MACV,CACF,CAAC;MAED,MAAMa,aAAa,GAAG,MAAM,IAAAC,kCAAoB,EAAC;QAC/CZ,UAAU;QACVO,UAAU;QACVM,CAAC,EAAE,CAACd,UAAU,GAAGD,IAAI,IAAI,CAAC;QAC1BgB,CAAC,EAAE,CAACf,UAAU,GAAGD,IAAI,IAAI;MAC3B,CAAC,CAAC;;MAEF;MACA,MAAMiB,UAAU,GAAG7D,eAAI,CAAC0B,IAAI,CAC1BD,eAAe,EACf5B,iBAAiB,CAAC4C,QAAQ,CAAC,CAAC3C,KAAK,CAACuC,KAAK,CAAC,CAACrC,IAC3C,CAAC;MAED,MAAM8D,MAAM,GAAG9D,eAAI,CAAC+D,OAAO,CAACF,UAAU,CAAC;MACvC;MACA,MAAM9B,aAAE,CAACC,QAAQ,CAACgC,KAAK,CAACF,MAAM,EAAE;QAAE1B,SAAS,EAAE;MAAK,CAAC,CAAC;MACpD,MAAML,aAAE,CAACC,QAAQ,CAACiC,SAAS,CAACJ,UAAU,EAAEJ,aAAa,CAAC;IACxD;IACA,OAAO,IAAI;EACb,CAAC,CACH,CAAC;AACH;AAEA,eAAelB,+BAA+BA,CAC5C2B,YAAoB,EACpBpD,WAAmB,EACnBwB,QAAwC,EACxC;EACA,IAAI,CAACA,QAAQ,EAAE;IACb;EACF;EAEA,MAAM6B,iBAAiB,GAAGnE,eAAI,CAAC0B,IAAI,CAACwC,YAAY,EAAErE,iBAAiB,CAACJ,OAAO,CAACK,KAAK,CAACC,KAAK,CAACC,IAAI,CAAC;EAC7F,MAAMoE,gBAAgB,GAAGpE,eAAI,CAAC0B,IAAI,CAACwC,YAAY,EAAErE,iBAAiB,CAACJ,OAAO,CAACK,KAAK,CAACG,IAAI,CAACD,IAAI,CAAC;EAE3F,MAAMqE,WAAW,GAAGrE,eAAI,CAAC+D,OAAO,CAACI,iBAAiB,CAAC;EACnD,MAAMpC,aAAE,CAACC,QAAQ,CAACgC,KAAK,CAACK,WAAW,EAAE;IAAEjC,SAAS,EAAE;EAAK,CAAC,CAAC;EACzD,MAAML,aAAE,CAACC,QAAQ,CAACsC,QAAQ,CAACtE,eAAI,CAAC0B,IAAI,CAACZ,WAAW,EAAEwB,QAAQ,CAACiC,IAAI,CAAC,EAAEJ,iBAAiB,CAAC;EAEpF,IAAI7B,QAAQ,CAACkC,QAAQ,EAAE;IACrB,MAAMC,UAAU,GAAGzE,eAAI,CAAC+D,OAAO,CAACK,gBAAgB,CAAC;IACjD,MAAMrC,aAAE,CAACC,QAAQ,CAACgC,KAAK,CAACS,UAAU,EAAE;MAAErC,SAAS,EAAE;IAAK,CAAC,CAAC;IACxD,MAAML,aAAE,CAACC,QAAQ,CAACsC,QAAQ,CAACtE,eAAI,CAAC0B,IAAI,CAACZ,WAAW,EAAEwB,QAAQ,CAACkC,QAAQ,CAAC,EAAEJ,gBAAgB,CAAC;EACzF;AACF", "ignoreList": []}