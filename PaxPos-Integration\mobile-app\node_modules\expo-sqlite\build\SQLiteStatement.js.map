{"version": 3, "file": "SQLiteStatement.js", "sourceRoot": "", "sources": ["../src/SQLiteStatement.ts"], "names": [], "mappings": "AAWA,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAMxE;;GAEG;AACH,MAAM,OAAO,eAAe;IAEP;IACA;IAFnB,YACmB,cAA8B,EAC9B,eAAgC;QADhC,mBAAc,GAAd,cAAc,CAAgB;QAC9B,oBAAe,GAAf,eAAe,CAAiB;IAChD,CAAC;IAaG,KAAK,CAAC,YAAY,CAAI,GAAG,MAAiB;QAC/C,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CACtF,IAAI,CAAC,cAAc,EACnB,GAAG,eAAe,CAAC,GAAG,MAAM,CAAC,CAC9B,CAAC;QACF,OAAO,8BAA8B,CACnC,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,eAAe,EACpB,cAAc,EACd;YACE,SAAS,EAAE,KAAK;YAChB,eAAe;YACf,OAAO;SACR,CACF,CAAC;IACJ,CAAC;IAeM,KAAK,CAAC,wBAAwB,CACnC,GAAG,MAAiB;QAEpB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CACtF,IAAI,CAAC,cAAc,EACnB,GAAG,eAAe,CAAC,GAAG,MAAM,CAAC,CAC9B,CAAC;QACF,OAAO,8BAA8B,CACnC,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,eAAe,EACpB,cAAc,EACd;YACE,SAAS,EAAE,IAAI;YACf,eAAe;YACf,OAAO;SACR,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC;IACpD,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,aAAa;QACxB,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAChE,CAAC;IAgBM,WAAW,CAAI,GAAG,MAAiB;QACxC,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAC/E,IAAI,CAAC,cAAc,EACnB,GAAG,eAAe,CAAC,GAAG,MAAM,CAAC,CAC9B,CAAC;QACF,OAAO,6BAA6B,CAClC,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,eAAe,EACpB,cAAc,EACd;YACE,SAAS,EAAE,KAAK;YAChB,eAAe;YACf,OAAO;SACR,CACF,CAAC;IACJ,CAAC;IAeM,uBAAuB,CAC5B,GAAG,MAAiB;QAEpB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAC/E,IAAI,CAAC,cAAc,EACnB,GAAG,eAAe,CAAC,GAAG,MAAM,CAAC,CAC9B,CAAC;QACF,OAAO,6BAA6B,CAClC,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,eAAe,EACpB,cAAc,EACd;YACE,SAAS,EAAE,IAAI;YACf,eAAe;YACf,OAAO;SACR,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;IACnD,CAAC;IAED;;;;;;;;OAQG;IACI,YAAY;QACjB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACzD,CAAC;CAGF;AA6JD;;;;;GAKG;AACH,KAAK,UAAU,8BAA8B,CAC3C,QAA2B,EAC3B,SAA0B,EAC1B,cAAyC,EACzC,OAAmC;IAEnC,MAAM,QAAQ,GAAG,IAAI,4BAA4B,CAC/C,QAAQ,EACR,SAAS,EACT,cAAc,EACd,OAAO,CACR,CAAC;IACF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;IAC5C,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE;QACjC,eAAe,EAAE;YACf,KAAK,EAAE,OAAO,CAAC,eAAe;YAC9B,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;SACnB;QACD,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE;QAC1F,aAAa,EAAE;YACb,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC5C,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;SACnB;QACD,WAAW,EAAE;YACX,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC1C,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;SACnB;QACD,UAAU,EAAE;YACV,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;YACzC,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;SACnB;KACF,CAAC,CAAC;IAEH,OAAO,SAAwC,CAAC;AAClD,CAAC;AAED;;GAEG;AACH,SAAS,6BAA6B,CACpC,QAA2B,EAC3B,SAA0B,EAC1B,cAAyC,EACzC,OAAmC;IAEnC,MAAM,QAAQ,GAAG,IAAI,2BAA2B,CAAI,QAAQ,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAClG,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;IAC3C,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE;QACjC,eAAe,EAAE;YACf,KAAK,EAAE,OAAO,CAAC,eAAe;YAC9B,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;SACnB;QACD,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE;QAC1F,YAAY,EAAE;YACZ,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC3C,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;SACnB;QACD,UAAU,EAAE;YACV,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;YACzC,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;SACnB;QACD,SAAS,EAAE;YACT,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;YACxC,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;SACnB;KACF,CAAC,CAAC;IAEH,OAAO,SAAuC,CAAC;AACjD,CAAC;AAED,MAAM,4BAA4B;IAKb;IACA;IACT;IACQ;IAPV,WAAW,GAAoB,IAAI,CAAC;IACpC,YAAY,GAAG,KAAK,CAAC;IAE7B,YACmB,QAA2B,EAC3B,SAA0B,EACnC,cAAyC,EACjC,OAAmC;QAHlC,aAAQ,GAAR,QAAQ,CAAmB;QAC3B,cAAS,GAAT,SAAS,CAAiB;QACnC,mBAAc,GAAd,cAAc,CAA2B;QACjC,YAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ,KAAK,CAAC,aAAa;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CACb,wLAAwL,CACzL,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACrD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,OAAO,kBAAkB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;QACpF,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,OAAO,QAAQ,IAAI,IAAI;YACrB,CAAC,CAAC,kBAAkB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC;YACtE,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CACb,8KAA8K,CAC/K,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,mIAAmI;YACnI,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACrD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChE,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,OAAO,mBAAmB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE;gBACjE,cAAc;gBACd,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;QACD,OAAO,mBAAmB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IAC9E,CAAC;IAED,KAAK,CAAC,CAAC,cAAc;QACnB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACrD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,kBAAkB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,MAAM,CAAC;QACX,GAAG,CAAC;YACF,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvD,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACnB,MAAM,kBAAkB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC,QAAQ,MAAM,IAAI,IAAI,EAAE;IAC3B,CAAC;IAED,UAAU;QACR,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,iBAAiB;QACvB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;YAChC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;YAC3C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,OAAO,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3D,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;QAChE,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF;AAED,MAAM,2BAA2B;IAKZ;IACA;IACT;IACQ;IAPV,WAAW,GAAoB,IAAI,CAAC;IACpC,YAAY,GAAG,KAAK,CAAC;IAE7B,YACmB,QAA2B,EAC3B,SAA0B,EACnC,cAAyC,EACjC,OAAmC;QAHlC,aAAQ,GAAR,QAAQ,CAAmB;QAC3B,cAAS,GAAT,SAAS,CAAiB;QACnC,mBAAc,GAAd,cAAc,CAA2B;QACjC,YAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ,YAAY;QACV,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CACb,uLAAuL,CACxL,CAAC;QACJ,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,OAAO,kBAAkB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;QACpF,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,OAAO,QAAQ,IAAI,IAAI;YACrB,CAAC,CAAC,kBAAkB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC;YACtE,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CACb,6KAA6K,CAC9K,CAAC;QACJ,CAAC;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,mIAAmI;YACnI,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,OAAO,mBAAmB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE;gBACjE,cAAc;gBACd,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;QACD,OAAO,mBAAmB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IAC9E,CAAC;IAED,CAAC,aAAa;QACZ,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,kBAAkB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;QACnF,CAAC;QACD,IAAI,MAAM,CAAC;QACX,GAAG,CAAC;YACF,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChD,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACnB,MAAM,kBAAkB,CAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC,QAAQ,MAAM,IAAI,IAAI,EAAE;IAC3B,CAAC;IAED,SAAS;QACP,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,iBAAiB;QACvB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;YAChC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;YAC3C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,OAAO,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3D,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB;QACxB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;QACzD,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF;AAED,SAAS,kBAAkB,CACzB,SAAkB,EAClB,WAA8B,EAC9B,YAAgC;IAEhC,OAAO,SAAS;QACd,CAAC,CAAE,YAAkB,CAAC,sCAAsC;QAC5D,CAAC,CAAC,UAAU,CAAI,WAAW,EAAE,YAAY,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,mBAAmB,CAC1B,SAAkB,EAClB,WAA8B,EAC9B,gBAAsC;IAEtC,OAAO,SAAS;QACd,CAAC,CAAE,gBAAwB,CAAC,0CAA0C;QACtE,CAAC,CAAC,WAAW,CAAI,WAAW,EAAE,gBAAgB,CAAC,CAAC;AACpD,CAAC;AAED,YAAY", "sourcesContent": ["import { NativeDatabase } from './NativeDatabase';\nimport {\n  SQLiteBindParams,\n  SQLiteBindValue,\n  NativeStatement,\n  SQLiteVariadicBindParams,\n  type SQLiteAnyDatabase,\n  type SQLiteColumnNames,\n  type SQLiteColumnValues,\n  type SQLiteRunResult,\n} from './NativeStatement';\nimport { composeRow, composeRows, normalizeParams } from './paramUtils';\n\nexport { SQLiteBindParams, SQLiteBindValue, SQLiteRunResult, SQLiteVariadicBindParams };\n\ntype ValuesOf<T extends object> = T[keyof T][];\n\n/**\n * A prepared statement returned by [`SQLiteDatabase.prepareAsync()`](#prepareasyncsource) or [`SQLiteDatabase.prepareSync()`](#preparesyncsource) that can be binded with parameters and executed.\n */\nexport class SQLiteStatement {\n  constructor(\n    private readonly nativeDatabase: NativeDatabase,\n    private readonly nativeStatement: NativeStatement\n  ) {}\n\n  //#region Asynchronous API\n\n  /**\n   * Run the prepared statement and return the [`SQLiteExecuteAsyncResult`](#sqliteexecuteasyncresult) instance.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   */\n  public executeAsync<T>(params: SQLiteBindParams): Promise<SQLiteExecuteAsyncResult<T>>;\n  /**\n   * @hidden\n   */\n  public executeAsync<T>(...params: SQLiteVariadicBindParams): Promise<SQLiteExecuteAsyncResult<T>>;\n  public async executeAsync<T>(...params: unknown[]): Promise<SQLiteExecuteAsyncResult<T>> {\n    const { lastInsertRowId, changes, firstRowValues } = await this.nativeStatement.runAsync(\n      this.nativeDatabase,\n      ...normalizeParams(...params)\n    );\n    return createSQLiteExecuteAsyncResult<T>(\n      this.nativeDatabase,\n      this.nativeStatement,\n      firstRowValues,\n      {\n        rawResult: false,\n        lastInsertRowId,\n        changes,\n      }\n    );\n  }\n\n  /**\n   * Similar to [`executeAsync()`](#executeasyncparams) but returns the raw value array result instead of the row objects.\n   * @hidden Advanced use only.\n   */\n  public executeForRawResultAsync<T extends object>(\n    params: SQLiteBindParams\n  ): Promise<SQLiteExecuteAsyncResult<ValuesOf<T>>>;\n  /**\n   * @hidden\n   */\n  public executeForRawResultAsync<T extends object>(\n    ...params: SQLiteVariadicBindParams\n  ): Promise<SQLiteExecuteAsyncResult<ValuesOf<T>>>;\n  public async executeForRawResultAsync<T extends object>(\n    ...params: unknown[]\n  ): Promise<SQLiteExecuteAsyncResult<ValuesOf<T>>> {\n    const { lastInsertRowId, changes, firstRowValues } = await this.nativeStatement.runAsync(\n      this.nativeDatabase,\n      ...normalizeParams(...params)\n    );\n    return createSQLiteExecuteAsyncResult<ValuesOf<T>>(\n      this.nativeDatabase,\n      this.nativeStatement,\n      firstRowValues,\n      {\n        rawResult: true,\n        lastInsertRowId,\n        changes,\n      }\n    );\n  }\n\n  /**\n   * Get the column names of the prepared statement.\n   */\n  public getColumnNamesAsync(): Promise<string[]> {\n    return this.nativeStatement.getColumnNamesAsync();\n  }\n\n  /**\n   * Finalize the prepared statement. This will call the [`sqlite3_finalize()`](https://www.sqlite.org/c3ref/finalize.html) C function under the hood.\n   *\n   * Attempting to access a finalized statement will result in an error.\n   * > **Note:** While `expo-sqlite` will automatically finalize any orphaned prepared statements upon closing the database, it is considered best practice\n   * > to manually finalize prepared statements as soon as they are no longer needed. This helps to prevent resource leaks.\n   * > You can use the `try...finally` statement to ensure that prepared statements are finalized even if an error occurs.\n   */\n  public async finalizeAsync(): Promise<void> {\n    await this.nativeStatement.finalizeAsync(this.nativeDatabase);\n  }\n\n  //#endregion\n\n  //#region Synchronous API\n\n  /**\n   * Run the prepared statement and return the [`SQLiteExecuteSyncResult`](#sqliteexecutesyncresult) instance.\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   * @param params The parameters to bind to the prepared statement. You can pass values in array, object, or variadic arguments. See [`SQLiteBindValue`](#sqlitebindvalue) for more information about binding values.\n   */\n  public executeSync<T>(params: SQLiteBindParams): SQLiteExecuteSyncResult<T>;\n  /**\n   * @hidden\n   */\n  public executeSync<T>(...params: SQLiteVariadicBindParams): SQLiteExecuteSyncResult<T>;\n  public executeSync<T>(...params: unknown[]): SQLiteExecuteSyncResult<T> {\n    const { lastInsertRowId, changes, firstRowValues } = this.nativeStatement.runSync(\n      this.nativeDatabase,\n      ...normalizeParams(...params)\n    );\n    return createSQLiteExecuteSyncResult<T>(\n      this.nativeDatabase,\n      this.nativeStatement,\n      firstRowValues,\n      {\n        rawResult: false,\n        lastInsertRowId,\n        changes,\n      }\n    );\n  }\n\n  /**\n   * Similar to [`executeSync()`](#executesyncparams) but returns the raw value array result instead of the row objects.\n   * @hidden Advanced use only.\n   */\n  public executeForRawResultSync<T extends object>(\n    params: SQLiteBindParams\n  ): SQLiteExecuteSyncResult<ValuesOf<T>>;\n  /**\n   * @hidden\n   */\n  public executeForRawResultSync<T extends object>(\n    ...params: SQLiteVariadicBindParams\n  ): SQLiteExecuteSyncResult<ValuesOf<T>>;\n  public executeForRawResultSync<T extends object>(\n    ...params: unknown[]\n  ): SQLiteExecuteSyncResult<ValuesOf<T>> {\n    const { lastInsertRowId, changes, firstRowValues } = this.nativeStatement.runSync(\n      this.nativeDatabase,\n      ...normalizeParams(...params)\n    );\n    return createSQLiteExecuteSyncResult<ValuesOf<T>>(\n      this.nativeDatabase,\n      this.nativeStatement,\n      firstRowValues,\n      {\n        rawResult: true,\n        lastInsertRowId,\n        changes,\n      }\n    );\n  }\n\n  /**\n   * Get the column names of the prepared statement.\n   */\n  public getColumnNamesSync(): string[] {\n    return this.nativeStatement.getColumnNamesSync();\n  }\n\n  /**\n   * Finalize the prepared statement. This will call the [`sqlite3_finalize()`](https://www.sqlite.org/c3ref/finalize.html) C function under the hood.\n   *\n   * Attempting to access a finalized statement will result in an error.\n   *\n   * > **Note:** While `expo-sqlite` will automatically finalize any orphaned prepared statements upon closing the database, it is considered best practice\n   * > to manually finalize prepared statements as soon as they are no longer needed. This helps to prevent resource leaks.\n   * > You can use the `try...finally` statement to ensure that prepared statements are finalized even if an error occurs.\n   */\n  public finalizeSync(): void {\n    this.nativeStatement.finalizeSync(this.nativeDatabase);\n  }\n\n  //#endregion\n}\n\n/**\n * A result returned by [`SQLiteStatement.executeAsync()`](#executeasyncparams).\n *\n * @example\n * The result includes the [`lastInsertRowId`](https://www.sqlite.org/c3ref/last_insert_rowid.html) and [`changes`](https://www.sqlite.org/c3ref/changes.html) properties. You can get the information from the write operations.\n * ```ts\n * const statement = await db.prepareAsync('INSERT INTO test (value) VALUES (?)');\n * try {\n *   const result = await statement.executeAsync(101);\n *   console.log('lastInsertRowId:', result.lastInsertRowId);\n *   console.log('changes:', result.changes);\n * } finally {\n *   await statement.finalizeAsync();\n * }\n * ```\n *\n * @example\n * The result implements the [`AsyncIterator`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol/asyncIterator) interface, so you can use it in `for await...of` loops.\n * ```ts\n * const statement = await db.prepareAsync('SELECT value FROM test WHERE value > ?');\n * try {\n *   const result = await statement.executeAsync<{ value: number }>(100);\n *   for await (const row of result) {\n *     console.log('row value:', row.value);\n *   }\n * } finally {\n *   await statement.finalizeAsync();\n * }\n * ```\n *\n * @example\n * If your write operations also return values, you can mix all of them together.\n * ```ts\n * const statement = await db.prepareAsync('INSERT INTO test (name, value) VALUES (?, ?) RETURNING name');\n * try {\n *   const result = await statement.executeAsync<{ name: string }>('John Doe', 101);\n *   console.log('lastInsertRowId:', result.lastInsertRowId);\n *   console.log('changes:', result.changes);\n *   for await (const row of result) {\n *     console.log('name:', row.name);\n *   }\n * } finally {\n *   await statement.finalizeAsync();\n * }\n * ```\n */\nexport interface SQLiteExecuteAsyncResult<T> extends AsyncIterableIterator<T> {\n  /**\n   * The last inserted row ID. Returned from the [`sqlite3_last_insert_rowid()`](https://www.sqlite.org/c3ref/last_insert_rowid.html) function.\n   */\n  readonly lastInsertRowId: number;\n\n  /**\n   * The number of rows affected. Returned from the [`sqlite3_changes()`](https://www.sqlite.org/c3ref/changes.html) function.\n   */\n  readonly changes: number;\n\n  /**\n   * Get the first row of the result set. This requires the SQLite cursor to be in its initial state. If you have already retrieved rows from the result set, you need to reset the cursor first by calling [`resetAsync()`](#resetasync). Otherwise, an error will be thrown.\n   */\n  getFirstAsync(): Promise<T | null>;\n\n  /**\n   * Get all rows of the result set. This requires the SQLite cursor to be in its initial state. If you have already retrieved rows from the result set, you need to reset the cursor first by calling [`resetAsync()`](#resetasync). Otherwise, an error will be thrown.\n   */\n  getAllAsync(): Promise<T[]>;\n\n  /**\n   * Reset the prepared statement cursor. This will call the [`sqlite3_reset()`](https://www.sqlite.org/c3ref/reset.html) C function under the hood.\n   */\n  resetAsync(): Promise<void>;\n}\n\n/**\n * A result returned by [`SQLiteStatement.executeSync()`](#executesyncparams).\n * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n\n * @example\n * The result includes the [`lastInsertRowId`](https://www.sqlite.org/c3ref/last_insert_rowid.html) and [`changes`](https://www.sqlite.org/c3ref/changes.html) properties. You can get the information from the write operations.\n * ```ts\n * const statement = db.prepareSync('INSERT INTO test (value) VALUES (?)');\n * try {\n *   const result = statement.executeSync(101);\n *   console.log('lastInsertRowId:', result.lastInsertRowId);\n *   console.log('changes:', result.changes);\n * } finally {\n *   statement.finalizeSync();\n * }\n * ```\n *\n * @example\n * The result implements the [`Iterator`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Iterator) interface, so you can use it in `for...of` loops.\n * ```ts\n * const statement = db.prepareSync('SELECT value FROM test WHERE value > ?');\n * try {\n *   const result = statement.executeSync<{ value: number }>(100);\n *   for (const row of result) {\n *     console.log('row value:', row.value);\n *   }\n * } finally {\n *   statement.finalizeSync();\n * }\n * ```\n *\n * @example\n * If your write operations also return values, you can mix all of them together.\n * ```ts\n * const statement = db.prepareSync('INSERT INTO test (name, value) VALUES (?, ?) RETURNING name');\n * try {\n *   const result = statement.executeSync<{ name: string }>('John Doe', 101);\n *   console.log('lastInsertRowId:', result.lastInsertRowId);\n *   console.log('changes:', result.changes);\n *   for (const row of result) {\n *     console.log('name:', row.name);\n *   }\n * } finally {\n *   statement.finalizeSync();\n * }\n * ```\n */\nexport interface SQLiteExecuteSyncResult<T> extends IterableIterator<T> {\n  /**\n   * The last inserted row ID. Returned from the [`sqlite3_last_insert_rowid()`](https://www.sqlite.org/c3ref/last_insert_rowid.html) function.\n   */\n  readonly lastInsertRowId: number;\n\n  /**\n   * The number of rows affected. Returned from the [`sqlite3_changes()`](https://www.sqlite.org/c3ref/changes.html) function.\n   */\n  readonly changes: number;\n\n  /**\n   * Get the first row of the result set. This requires the SQLite cursor to be in its initial state. If you have already retrieved rows from the result set, you need to reset the cursor first by calling [`resetSync()`](#resetsync). Otherwise, an error will be thrown.\n   */\n  getFirstSync(): T | null;\n\n  /**\n   * Get all rows of the result set. This requires the SQLite cursor to be in its initial state. If you have already retrieved rows from the result set, you need to reset the cursor first by calling [`resetSync()`](#resetsync). Otherwise, an error will be thrown.\n   */\n  getAllSync(): T[];\n\n  /**\n   * Reset the prepared statement cursor. This will call the [`sqlite3_reset()`](https://www.sqlite.org/c3ref/reset.html) C function under the hood.\n   */\n  resetSync(): void;\n}\n\n//#region Internals for SQLiteExecuteAsyncResult and SQLiteExecuteSyncResult\n\ninterface SQLiteExecuteResultOptions {\n  rawResult: boolean;\n  lastInsertRowId: number;\n  changes: number;\n}\n\n/**\n * Create the `SQLiteExecuteAsyncResult` instance.\n *\n * NOTE: Since Hermes does not support the `Symbol.asyncIterator` feature, we have to use an AsyncGenerator to implement the `AsyncIterableIterator` interface.\n * This is done by `Object.defineProperties` to add the properties to the AsyncGenerator.\n */\nasync function createSQLiteExecuteAsyncResult<T>(\n  database: SQLiteAnyDatabase,\n  statement: NativeStatement,\n  firstRowValues: SQLiteColumnValues | null,\n  options: SQLiteExecuteResultOptions\n): Promise<SQLiteExecuteAsyncResult<T>> {\n  const instance = new SQLiteExecuteAsyncResultImpl<T>(\n    database,\n    statement,\n    firstRowValues,\n    options\n  );\n  const generator = instance.generatorAsync();\n  Object.defineProperties(generator, {\n    lastInsertRowId: {\n      value: options.lastInsertRowId,\n      enumerable: true,\n      writable: false,\n      configurable: true,\n    },\n    changes: { value: options.changes, enumerable: true, writable: false, configurable: true },\n    getFirstAsync: {\n      value: instance.getFirstAsync.bind(instance),\n      enumerable: true,\n      writable: false,\n      configurable: true,\n    },\n    getAllAsync: {\n      value: instance.getAllAsync.bind(instance),\n      enumerable: true,\n      writable: false,\n      configurable: true,\n    },\n    resetAsync: {\n      value: instance.resetAsync.bind(instance),\n      enumerable: true,\n      writable: false,\n      configurable: true,\n    },\n  });\n\n  return generator as SQLiteExecuteAsyncResult<T>;\n}\n\n/**\n * Create the `SQLiteExecuteSyncResult` instance.\n */\nfunction createSQLiteExecuteSyncResult<T>(\n  database: SQLiteAnyDatabase,\n  statement: NativeStatement,\n  firstRowValues: SQLiteColumnValues | null,\n  options: SQLiteExecuteResultOptions\n): SQLiteExecuteSyncResult<T> {\n  const instance = new SQLiteExecuteSyncResultImpl<T>(database, statement, firstRowValues, options);\n  const generator = instance.generatorSync();\n  Object.defineProperties(generator, {\n    lastInsertRowId: {\n      value: options.lastInsertRowId,\n      enumerable: true,\n      writable: false,\n      configurable: true,\n    },\n    changes: { value: options.changes, enumerable: true, writable: false, configurable: true },\n    getFirstSync: {\n      value: instance.getFirstSync.bind(instance),\n      enumerable: true,\n      writable: false,\n      configurable: true,\n    },\n    getAllSync: {\n      value: instance.getAllSync.bind(instance),\n      enumerable: true,\n      writable: false,\n      configurable: true,\n    },\n    resetSync: {\n      value: instance.resetSync.bind(instance),\n      enumerable: true,\n      writable: false,\n      configurable: true,\n    },\n  });\n\n  return generator as SQLiteExecuteSyncResult<T>;\n}\n\nclass SQLiteExecuteAsyncResultImpl<T> {\n  private columnNames: string[] | null = null;\n  private isStepCalled = false;\n\n  constructor(\n    private readonly database: SQLiteAnyDatabase,\n    private readonly statement: NativeStatement,\n    private firstRowValues: SQLiteColumnValues | null,\n    public readonly options: SQLiteExecuteResultOptions\n  ) {}\n\n  async getFirstAsync(): Promise<T | null> {\n    if (this.isStepCalled) {\n      throw new Error(\n        'The SQLite cursor has been shifted and is unable to retrieve the first row without being reset. Invoke `resetAsync()` to reset the cursor first if you want to retrieve the first row.'\n      );\n    }\n    this.isStepCalled = true;\n    const columnNames = await this.getColumnNamesAsync();\n    const firstRowValues = this.popFirstRowValues();\n    if (firstRowValues != null) {\n      return composeRowIfNeeded<T>(this.options.rawResult, columnNames, firstRowValues);\n    }\n    const firstRow = await this.statement.stepAsync(this.database);\n    return firstRow != null\n      ? composeRowIfNeeded<T>(this.options.rawResult, columnNames, firstRow)\n      : null;\n  }\n\n  async getAllAsync(): Promise<T[]> {\n    if (this.isStepCalled) {\n      throw new Error(\n        'The SQLite cursor has been shifted and is unable to retrieve all rows without being reset. Invoke `resetAsync()` to reset the cursor first if you want to retrieve all rows.'\n      );\n    }\n    this.isStepCalled = true;\n    const firstRowValues = this.popFirstRowValues();\n    if (firstRowValues == null) {\n      // If the first row is empty, this SQL query may be a write operation. We should not call `statement.getAllAsync()` to write again.\n      return [];\n    }\n    const columnNames = await this.getColumnNamesAsync();\n    const allRows = await this.statement.getAllAsync(this.database);\n    if (firstRowValues != null && firstRowValues.length > 0) {\n      return composeRowsIfNeeded<T>(this.options.rawResult, columnNames, [\n        firstRowValues,\n        ...allRows,\n      ]);\n    }\n    return composeRowsIfNeeded<T>(this.options.rawResult, columnNames, allRows);\n  }\n\n  async *generatorAsync(): AsyncIterableIterator<T> {\n    this.isStepCalled = true;\n    const columnNames = await this.getColumnNamesAsync();\n    const firstRowValues = this.popFirstRowValues();\n    if (firstRowValues != null) {\n      yield composeRowIfNeeded<T>(this.options.rawResult, columnNames, firstRowValues);\n    }\n\n    let result;\n    do {\n      result = await this.statement.stepAsync(this.database);\n      if (result != null) {\n        yield composeRowIfNeeded<T>(this.options.rawResult, columnNames, result);\n      }\n    } while (result != null);\n  }\n\n  resetAsync(): Promise<void> {\n    const result = this.statement.resetAsync(this.database);\n    this.isStepCalled = false;\n    return result;\n  }\n\n  private popFirstRowValues(): SQLiteColumnValues | null {\n    if (this.firstRowValues != null) {\n      const firstRowValues = this.firstRowValues;\n      this.firstRowValues = null;\n      return firstRowValues.length > 0 ? firstRowValues : null;\n    }\n    return null;\n  }\n\n  private async getColumnNamesAsync(): Promise<string[]> {\n    if (this.columnNames == null) {\n      this.columnNames = await this.statement.getColumnNamesAsync();\n    }\n    return this.columnNames;\n  }\n}\n\nclass SQLiteExecuteSyncResultImpl<T> {\n  private columnNames: string[] | null = null;\n  private isStepCalled = false;\n\n  constructor(\n    private readonly database: SQLiteAnyDatabase,\n    private readonly statement: NativeStatement,\n    private firstRowValues: SQLiteColumnValues | null,\n    public readonly options: SQLiteExecuteResultOptions\n  ) {}\n\n  getFirstSync(): T | null {\n    if (this.isStepCalled) {\n      throw new Error(\n        'The SQLite cursor has been shifted and is unable to retrieve the first row without being reset. Invoke `resetSync()` to reset the cursor first if you want to retrieve the first row.'\n      );\n    }\n    const columnNames = this.getColumnNamesSync();\n    const firstRowValues = this.popFirstRowValues();\n    if (firstRowValues != null) {\n      return composeRowIfNeeded<T>(this.options.rawResult, columnNames, firstRowValues);\n    }\n    const firstRow = this.statement.stepSync(this.database);\n    return firstRow != null\n      ? composeRowIfNeeded<T>(this.options.rawResult, columnNames, firstRow)\n      : null;\n  }\n\n  getAllSync(): T[] {\n    if (this.isStepCalled) {\n      throw new Error(\n        'The SQLite cursor has been shifted and is unable to retrieve all rows without being reset. Invoke `resetSync()` to reset the cursor first if you want to retrieve all rows.'\n      );\n    }\n    const firstRowValues = this.popFirstRowValues();\n    if (firstRowValues == null) {\n      // If the first row is empty, this SQL query may be a write operation. We should not call `statement.getAllAsync()` to write again.\n      return [];\n    }\n    const columnNames = this.getColumnNamesSync();\n    const allRows = this.statement.getAllSync(this.database);\n    if (firstRowValues != null && firstRowValues.length > 0) {\n      return composeRowsIfNeeded<T>(this.options.rawResult, columnNames, [\n        firstRowValues,\n        ...allRows,\n      ]);\n    }\n    return composeRowsIfNeeded<T>(this.options.rawResult, columnNames, allRows);\n  }\n\n  *generatorSync(): IterableIterator<T> {\n    const columnNames = this.getColumnNamesSync();\n    const firstRowValues = this.popFirstRowValues();\n    if (firstRowValues != null) {\n      yield composeRowIfNeeded<T>(this.options.rawResult, columnNames, firstRowValues);\n    }\n    let result;\n    do {\n      result = this.statement.stepSync(this.database);\n      if (result != null) {\n        yield composeRowIfNeeded<T>(this.options.rawResult, columnNames, result);\n      }\n    } while (result != null);\n  }\n\n  resetSync(): void {\n    const result = this.statement.resetSync(this.database);\n    this.isStepCalled = false;\n    return result;\n  }\n\n  private popFirstRowValues(): SQLiteColumnValues | null {\n    if (this.firstRowValues != null) {\n      const firstRowValues = this.firstRowValues;\n      this.firstRowValues = null;\n      return firstRowValues.length > 0 ? firstRowValues : null;\n    }\n    return null;\n  }\n\n  private getColumnNamesSync(): string[] {\n    if (this.columnNames == null) {\n      this.columnNames = this.statement.getColumnNamesSync();\n    }\n    return this.columnNames;\n  }\n}\n\nfunction composeRowIfNeeded<T>(\n  rawResult: boolean,\n  columnNames: SQLiteColumnNames,\n  columnValues: SQLiteColumnValues\n): T {\n  return rawResult\n    ? (columnValues as T) // T would be a ValuesOf<> from caller\n    : composeRow<T>(columnNames, columnValues);\n}\n\nfunction composeRowsIfNeeded<T>(\n  rawResult: boolean,\n  columnNames: SQLiteColumnNames,\n  columnValuesList: SQLiteColumnValues[]\n): T[] {\n  return rawResult\n    ? (columnValuesList as T[]) // T[] would be a ValuesOf<>[] from caller\n    : composeRows<T>(columnNames, columnValuesList);\n}\n\n//#endregion\n"]}