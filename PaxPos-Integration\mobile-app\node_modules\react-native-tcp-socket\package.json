{"name": "react-native-tcp-socket", "title": "React Native Tcp Socket", "version": "6.3.0", "description": "React Native TCP socket API for Android & iOS with SSL/TLS support", "main": "src/index.js", "types": "lib/types/index.d.ts", "scripts": {"ci": "yarn install --frozen-lockfile && yarn lint && yarn declaration:build && yarn checkjs && yarn test", "lint": "eslint .", "checkjs": "tsc && tsc -p ./__tests__/tsconfig.json", "test": "jest ./__tests__", "declaration:build": "tsc -p ./declaration.tsconfig.json", "prepublishOnly": "yarn declaration:build && yarn checkjs"}, "files": ["/android/src/", "/android/build.gradle", "/ios", "!Podfile*", "/windows", "/src", "/lib", "/*.podspec", "/jest"], "repository": {"type": "git", "url": "git+https://github.com/Rapsssito/react-native-tcp-socket.git", "baseUrl": "https://github.com/Rapsssito/react-native-tcp-socket"}, "keywords": ["react-native", "iOS", "Android", "tcp-socket", "tls", "ssl", "tcp-server", "tcp-client", "tcp", "react-native-library"], "funding": {"type": "individual", "url": "https://github.com/sponsors/Rapsssito"}, "author": {"name": "Rapsssito", "email": "<EMAIL>"}, "license": "MIT", "licenseFilename": "LICENSE", "readmeFilename": "README.md", "peerDependencies": {"react-native": ">=0.60.0"}, "devDependencies": {"@babel/core": "^7.7.7", "@semantic-release/changelog": "^5.0.0", "@semantic-release/git": "^9.0.0", "@semantic-release/github": "^7.0.0", "@semantic-release/npm": "^7.0.0", "@types/eventemitter3": "^2.0.2", "@types/jest": "^25.1.3", "@types/react-native": "^0.61.17", "babel-eslint": "^10.1.0", "babel-jest": "^24.9.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-jest": "^23.6.0", "eslint-plugin-jsdoc": "^21.0.0", "eslint-plugin-prettier": "^3.1.1", "jest": "^26.6.3", "metro-react-native-babel-preset": "^0.58.0", "prettier": "^1.18.2", "react": "16.9.0", "react-native": "0.60.5", "semantic-release": "^17.0.1", "typescript": "^4.1.3"}, "dependencies": {"buffer": "^5.4.3", "eventemitter3": "^4.0.7"}}