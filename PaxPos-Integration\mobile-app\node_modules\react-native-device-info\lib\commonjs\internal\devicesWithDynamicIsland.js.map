{"version": 3, "sources": ["devicesWithDynamicIsland.ts"], "names": ["devicesWithDynamicIsland", "brand", "model"], "mappings": ";;;;;;AAEA,MAAMA,wBAAuC,GAAG,CAC9C;AACEC,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAD8C,EAK9C;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAL8C,EAS9C;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAT8C,EAa9C;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAb8C,EAiB9C;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CAjB8C,EAqB9C;AACED,EAAAA,KAAK,EAAE,OADT;AAEEC,EAAAA,KAAK,EAAE;AAFT,CArB8C,CAAhD;eA2BeF,wB", "sourcesContent": ["import { NotchDevice } from './privateTypes';\n\nconst devicesWithDynamicIsland: NotchDevice[] = [\n  {\n    brand: 'Apple',\n    model: 'iPhone 15',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 15 Plus',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 15 Pro',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 15 Pro Max',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 14 Pro',\n  },\n  {\n    brand: 'Apple',\n    model: 'iPhone 14 Pro Max',\n  },\n];\n\nexport default devicesWithDynamicIsland;\n"]}