{"version": 3, "sources": ["../../../src/utils/url.ts"], "sourcesContent": ["import dns from 'dns';\nimport { URL } from 'url';\n\nimport { fetchAsync } from '../api/rest/client';\n\n/** Check if a server is available based on the URL. */\nexport function isUrlAvailableAsync(url: string): Promise<boolean> {\n  return new Promise<boolean>((resolve) => {\n    dns.lookup(url, (err) => {\n      resolve(!err);\n    });\n  });\n}\n\n/** Check if a request to the given URL is `ok` (status 200). */\nexport async function isUrlOk(url: string): Promise<boolean> {\n  try {\n    const res = await fetchAsync(url);\n    return res.ok;\n  } catch {\n    return false;\n  }\n}\n\n/** Determine if a string is a valid URL, can optionally ensure certain protocols (like `https` or `exp`) are adhered to. */\nexport function validateUrl(\n  urlString: string,\n  {\n    protocols,\n    requireProtocol,\n  }: {\n    /** Set of allowed protocols for the string to adhere to. @example ['exp', 'https'] */\n    protocols?: string[];\n    /** Ensure the URL has a protocol component (prefix before `://`). */\n    requireProtocol?: boolean;\n  } = {}\n) {\n  try {\n    const results = new URL(urlString);\n    if (!results.protocol && !requireProtocol) {\n      return true;\n    }\n    return protocols\n      ? results.protocol\n        ? protocols.map((x) => `${x.toLowerCase()}:`).includes(results.protocol)\n        : false\n      : true;\n  } catch {\n    return false;\n  }\n}\n\n/** Remove the port from a given `host` URL string. */\nexport function stripPort(host?: string): string | null {\n  return coerceUrl(host)?.hostname ?? null;\n}\n\nfunction coerceUrl(urlString?: string): URL | null {\n  if (!urlString) {\n    return null;\n  }\n  try {\n    return new URL('/', urlString);\n  } catch {\n    return new URL('/', `http://${urlString}`);\n  }\n}\n\n/** Strip a given extension from a URL string. */\nexport function stripExtension(url: string, extension: string): string {\n  return url.replace(new RegExp(`.${extension}$`), '');\n}\n"], "names": ["isUrlAvailableAsync", "isUrlOk", "stripExtension", "stripPort", "validateUrl", "url", "Promise", "resolve", "dns", "lookup", "err", "res", "fetchAsync", "ok", "urlString", "protocols", "requireProtocol", "results", "URL", "protocol", "map", "x", "toLowerCase", "includes", "host", "coerceUrl", "hostname", "extension", "replace", "RegExp"], "mappings": ";;;;;;;;;;;IAMgBA,mBAAmB;eAAnBA;;IASMC,OAAO;eAAPA;;IAsDNC,cAAc;eAAdA;;IAhBAC,SAAS;eAATA;;IA5BAC,WAAW;eAAXA;;;;gEAzBA;;;;;;;yBACI;;;;;;wBAEO;;;;;;AAGpB,SAASJ,oBAAoBK,GAAW;IAC7C,OAAO,IAAIC,QAAiB,CAACC;QAC3BC,cAAG,CAACC,MAAM,CAACJ,KAAK,CAACK;YACfH,QAAQ,CAACG;QACX;IACF;AACF;AAGO,eAAeT,QAAQI,GAAW;IACvC,IAAI;QACF,MAAMM,MAAM,MAAMC,IAAAA,kBAAU,EAACP;QAC7B,OAAOM,IAAIE,EAAE;IACf,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAST,YACdU,SAAiB,EACjB,EACEC,SAAS,EACTC,eAAe,EAMhB,GAAG,CAAC,CAAC;IAEN,IAAI;QACF,MAAMC,UAAU,IAAIC,CAAAA,MAAE,KAAC,CAACJ;QACxB,IAAI,CAACG,QAAQE,QAAQ,IAAI,CAACH,iBAAiB;YACzC,OAAO;QACT;QACA,OAAOD,YACHE,QAAQE,QAAQ,GACdJ,UAAUK,GAAG,CAAC,CAACC,IAAM,GAAGA,EAAEC,WAAW,GAAG,CAAC,CAAC,EAAEC,QAAQ,CAACN,QAAQE,QAAQ,IACrE,QACF;IACN,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAShB,UAAUqB,IAAa;QAC9BC;IAAP,OAAOA,EAAAA,aAAAA,UAAUD,0BAAVC,WAAiBC,QAAQ,KAAI;AACtC;AAEA,SAASD,UAAUX,SAAkB;IACnC,IAAI,CAACA,WAAW;QACd,OAAO;IACT;IACA,IAAI;QACF,OAAO,IAAII,CAAAA,MAAE,KAAC,CAAC,KAAKJ;IACtB,EAAE,OAAM;QACN,OAAO,IAAII,CAAAA,MAAE,KAAC,CAAC,KAAK,CAAC,OAAO,EAAEJ,WAAW;IAC3C;AACF;AAGO,SAASZ,eAAeG,GAAW,EAAEsB,SAAiB;IAC3D,OAAOtB,IAAIuB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEF,UAAU,CAAC,CAAC,GAAG;AACnD"}